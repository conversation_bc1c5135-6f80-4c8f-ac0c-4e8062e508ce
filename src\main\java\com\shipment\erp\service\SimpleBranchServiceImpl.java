package com.shipment.erp.service;

import java.util.List;
import java.util.Optional;
import com.shipment.erp.model.Branch;
import com.shipment.erp.repository.SimpleBranchRepository;

/**
 * تنفيذ مبسط لخدمة إدارة الفروع Simple Branch Management Service Implementation
 */
public class SimpleBranchServiceImpl extends SimpleBaseServiceImpl<Branch>
        implements SimpleBranchService {

    private final SimpleBranchRepository branchRepository;

    public SimpleBranchServiceImpl(SimpleBranchRepository branchRepository) {
        super(branchRepository);
        this.branchRepository = branchRepository;
    }

    @Override
    public List<Branch> findByCompanyId(Long companyId) {
        return branchRepository.findByCompanyId(companyId);
    }

    @Override
    public List<Branch> findActiveByCompanyId(Long companyId) {
        return branchRepository.findByCompanyIdAndIsActiveTrue(companyId);
    }

    @Override
    public Branch findByCode(String code) {
        Optional<Branch> branch = branchRepository.findByCode(code);
        return branch.orElse(null);
    }

    @Override
    public List<Branch> findByNameContaining(String name) {
        return branchRepository.findByNameContainingIgnoreCase(name);
    }

    @Override
    public boolean existsByCode(String code) {
        return branchRepository.existsByCode(code);
    }

    @Override
    public void toggleActive(Long branchId) {
        Optional<Branch> branchOpt = branchRepository.findById(branchId);
        if (branchOpt.isPresent()) {
            Branch branch = branchOpt.get();
            branch.setActive(!branch.isActive());
            branchRepository.save(branch);
        }
    }

    @Override
    public long getActiveBranchesCount(Long companyId) {
        return branchRepository.countByCompanyIdAndIsActiveTrue(companyId);
    }

    @Override
    public Branch save(Branch branch) {
        // التحقق من عدم تكرار الكود
        if (branch.getId() == null && existsByCode(branch.getCode())) {
            throw new RuntimeException("كود الفرع موجود مسبقاً: " + branch.getCode());
        }

        // التحقق من عدم تكرار الكود عند التحديث
        if (branch.getId() != null) {
            Branch existingBranch = findByCode(branch.getCode());
            if (existingBranch != null && !existingBranch.getId().equals(branch.getId())) {
                throw new RuntimeException("كود الفرع موجود مسبقاً: " + branch.getCode());
            }
        }

        return super.save(branch);
    }
}
