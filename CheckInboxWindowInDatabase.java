import java.sql.*;

/**
 * فحص تسجيل نافذة صندوق البريد الوارد في قاعدة البيانات
 */
public class CheckInboxWindowInDatabase {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  CHECKING INBOX WINDOW IN DATABASE");
        System.out.println("  فحص نافذة صندوق البريد الوارد في قاعدة البيانات");
        System.out.println("========================================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";
            
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة بيانات ship_erp بنجاح");
            
            // البحث عن نوافذ البريد الإلكتروني
            searchEmailWindows(connection);
            
            // البحث عن نافذة صندوق البريد الوارد تحديداً
            searchInboxWindow(connection);
            
            // إضافة نافذة صندوق البريد الوارد إذا لم تكن موجودة
            addInboxWindowIfMissing(connection);
            
            connection.close();
            System.out.println("\n🎉 تم فحص وإضافة نافذة صندوق البريد الوارد بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فحص النافذة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void searchEmailWindows(Connection connection) {
        try {
            System.out.println("\n🔍 البحث عن نوافذ البريد الإلكتروني...");
            
            String sql = """
                SELECT NODE_ID, NODE_NAME_AR, NODE_NAME_EN, WINDOW_CLASS, DESCRIPTION
                FROM ERP_SYSTEM_TREE 
                WHERE NODE_NAME_AR LIKE '%بريد%' 
                OR NODE_NAME_EN LIKE '%Email%'
                OR NODE_NAME_EN LIKE '%Mail%'
                OR WINDOW_CLASS LIKE '%Email%'
                ORDER BY NODE_NAME_AR
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                System.out.println("📧 نوافذ البريد الإلكتروني الموجودة:");
                System.out.println("=" .repeat(80));
                
                boolean found = false;
                while (rs.next()) {
                    found = true;
                    System.out.println("🔹 المعرف: " + rs.getInt("NODE_ID"));
                    System.out.println("   الاسم العربي: " + rs.getString("NODE_NAME_AR"));
                    System.out.println("   الاسم الإنجليزي: " + rs.getString("NODE_NAME_EN"));
                    System.out.println("   كلاس النافذة: " + rs.getString("WINDOW_CLASS"));
                    System.out.println("   الوصف: " + rs.getString("DESCRIPTION"));
                    System.out.println("-" .repeat(60));
                }
                
                if (!found) {
                    System.out.println("❌ لم يتم العثور على نوافذ البريد الإلكتروني");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن نوافذ البريد الإلكتروني: " + e.getMessage());
        }
    }
    
    private static void searchInboxWindow(Connection connection) {
        try {
            System.out.println("\n🔍 البحث عن نافذة صندوق البريد الوارد...");
            
            String sql = """
                SELECT NODE_ID, NODE_NAME_AR, NODE_NAME_EN, WINDOW_CLASS, DESCRIPTION
                FROM ERP_SYSTEM_TREE 
                WHERE NODE_NAME_AR LIKE '%صندوق%' 
                OR NODE_NAME_AR LIKE '%وارد%'
                OR NODE_NAME_EN LIKE '%Inbox%'
                OR WINDOW_CLASS LIKE '%Inbox%'
                ORDER BY NODE_NAME_AR
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                System.out.println("📥 نوافذ صندوق البريد الوارد:");
                System.out.println("=" .repeat(80));
                
                boolean found = false;
                while (rs.next()) {
                    found = true;
                    System.out.println("🔹 المعرف: " + rs.getInt("NODE_ID"));
                    System.out.println("   الاسم العربي: " + rs.getString("NODE_NAME_AR"));
                    System.out.println("   الاسم الإنجليزي: " + rs.getString("NODE_NAME_EN"));
                    System.out.println("   كلاس النافذة: " + rs.getString("WINDOW_CLASS"));
                    System.out.println("   الوصف: " + rs.getString("DESCRIPTION"));
                    System.out.println("-" .repeat(60));
                }
                
                if (!found) {
                    System.out.println("❌ لم يتم العثور على نافذة صندوق البريد الوارد");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن نافذة صندوق البريد الوارد: " + e.getMessage());
        }
    }
    
    private static void addInboxWindowIfMissing(Connection connection) {
        try {
            System.out.println("\n📝 إضافة نافذة صندوق البريد الوارد إذا لم تكن موجودة...");
            
            // التحقق من وجود النافذة
            String checkSql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = 'EmailInboxWindow'";
            try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
                 ResultSet rs = checkStmt.executeQuery()) {
                
                if (rs.next() && rs.getInt(1) > 0) {
                    System.out.println("⚠️ نافذة صندوق البريد الوارد موجودة مسبقاً");
                    return;
                }
            }
            
            // البحث عن عقدة نظام البريد الإلكتروني
            String findParentSql = "SELECT NODE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'Email Management System'";
            int parentId = -1;
            try (PreparedStatement stmt = connection.prepareStatement(findParentSql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    parentId = rs.getInt("NODE_ID");
                } else {
                    // إذا لم توجد عقدة نظام البريد الإلكتروني، نبحث عن أي عقدة مناسبة
                    String findAnyParentSql = "SELECT NODE_ID FROM ERP_SYSTEM_TREE WHERE ROWNUM = 1 ORDER BY NODE_ID";
                    try (PreparedStatement anyStmt = connection.prepareStatement(findAnyParentSql);
                         ResultSet anyRs = anyStmt.executeQuery()) {
                        
                        if (anyRs.next()) {
                            parentId = anyRs.getInt("NODE_ID");
                        }
                    }
                }
            }
            
            if (parentId == -1) {
                System.err.println("❌ لم يتم العثور على عقدة مناسبة للإضافة");
                return;
            }
            
            // الحصول على أكبر NODE_ID موجود
            String maxIdSql = "SELECT NVL(MAX(NODE_ID), 0) + 1 FROM ERP_SYSTEM_TREE";
            int newNodeId = 1;
            try (PreparedStatement stmt = connection.prepareStatement(maxIdSql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    newNodeId = rs.getInt(1);
                }
            }
            
            // إدراج نافذة صندوق البريد الوارد
            String insertSql = """
                INSERT INTO ERP_SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, NODE_ORDER, WINDOW_CLASS, DESCRIPTION, IS_ACTIVE, CREATED_DATE, CREATED_BY)
                VALUES (?, 'صندوق البريد الوارد الشامل', 'Comprehensive Email Inbox', ?, 3, 2, 'EmailInboxWindow', 'صندوق البريد الوارد الشامل لعرض وإدارة جميع الرسائل الواردة', 'Y', SYSDATE, USER)
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(insertSql)) {
                stmt.setInt(1, newNodeId);
                stmt.setInt(2, parentId);
                stmt.executeUpdate();
                System.out.println("✅ تم إدراج نافذة صندوق البريد الوارد الشامل (ID: " + newNodeId + ")");
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إضافة نافذة صندوق البريد الوارد: " + e.getMessage());
        }
    }
}
