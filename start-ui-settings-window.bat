@echo off
echo ========================================
echo   ADVANCED UI SETTINGS WINDOW
echo   نافذة إعدادات الواجهة المتطورة
echo ========================================

cd /d "d:\java\java"

echo.
echo Starting Advanced UI Settings Window...
echo تشغيل نافذة إعدادات الواجهة المتطورة...
echo.

REM Compile if needed
echo [1] Compiling files...
javac -encoding UTF-8 -cp "lib\*" AdvancedUISettingsWindow.java EnhancedSettingsManager.java

if %errorlevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Compilation successful!

echo.
echo [2] Starting UI Settings Window...

REM Start the window
java -cp "lib\*;." AdvancedUISettingsWindow

echo.
echo UI Settings Window closed.
echo.

echo ========================================
echo   FEATURES AVAILABLE
echo   الميزات المتاحة
echo ========================================
echo.
echo 🎨 19+ Themes Available:
echo    19+ ثيم متاح:
echo    • FlatLaf (Light, Dark, IntelliJ, Darcula)
echo    • JTattoo Collection (11 themes)
echo    • SeaGlass
echo    • System Default themes
echo.
echo 🔤 Font Customization:
echo    تخصيص الخطوط:
echo    • All system fonts available
echo    • Size adjustment (8-72)
echo    • Arabic support with RTL
echo.
echo 🖥️ Interface Options:
echo    خيارات الواجهة:
echo    • Animations control
echo    • Sounds control
echo    • Tooltips control
echo    • RTL support
echo    • Transparency control
echo.
echo 🌈 Color Customization:
echo    تخصيص الألوان:
echo    • Full color picker
echo    • Accent color selection
echo    • Real-time preview
echo.
echo 💾 Database Integration:
echo    تكامل قاعدة البيانات:
echo    • Settings saved to Oracle DB
echo    • Automatic backup to file
echo    • Persistent across sessions
echo.

pause
