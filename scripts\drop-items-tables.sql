-- سكريبت حذف جداول الأصناف من قاعدة البيانات
-- Drop Items Tables Script
-- Oracle SID: orcl

-- الاتصال بمستخدم التطبيق
-- sqlplus ship_erp/ship_erp_password@localhost:1521/orcl

-- حذف الجداول بالترتيب الصحيح (مع مراعاة Foreign Keys)

-- 1. حذف جدول الأصناف أولاً (يحتوي على مراجع للجداول الأخرى)
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE ITEMS CASCADE CONSTRAINTS';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف جدول ITEMS بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -942 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  جدول ITEMS غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف جدول ITEMS: ' || SQLERRM);
        END IF;
END;
/

-- 2. حذف جدول مجموعات الأصناف
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE ITEM_CATEGORIES CASCADE CONSTRAINTS';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف جدول ITEM_CATEGORIES بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -942 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  جدول ITEM_CATEGORIES غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف جدول ITEM_CATEGORIES: ' || SQLERRM);
        END IF;
END;
/

-- 3. حذف جدول وحدات القياس
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE UNITS_OF_MEASURE CASCADE CONSTRAINTS';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف جدول UNITS_OF_MEASURE بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -942 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  جدول UNITS_OF_MEASURE غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف جدول UNITS_OF_MEASURE: ' || SQLERRM);
        END IF;
END;
/

-- حذف المتسلسلات المتعلقة بالأصناف

-- 4. حذف متسلسل الأصناف
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_ITEM';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف متسلسل SEQ_ITEM بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -2289 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  متسلسل SEQ_ITEM غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف متسلسل SEQ_ITEM: ' || SQLERRM);
        END IF;
END;
/

-- 5. حذف متسلسل مجموعات الأصناف
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_ITEM_CATEGORY';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف متسلسل SEQ_ITEM_CATEGORY بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -2289 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  متسلسل SEQ_ITEM_CATEGORY غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف متسلسل SEQ_ITEM_CATEGORY: ' || SQLERRM);
        END IF;
END;
/

-- 6. حذف متسلسل وحدات القياس
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_UNIT_OF_MEASURE';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف متسلسل SEQ_UNIT_OF_MEASURE بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -2289 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  متسلسل SEQ_UNIT_OF_MEASURE غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف متسلسل SEQ_UNIT_OF_MEASURE: ' || SQLERRM);
        END IF;
END;
/

-- حذف الفهارس المتعلقة بالأصناف (إذا كانت موجودة)

-- 7. حذف فهارس الأصناف
BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_ITEM_CATEGORY';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف فهرس IDX_ITEM_CATEGORY بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  فهرس IDX_ITEM_CATEGORY غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف فهرس IDX_ITEM_CATEGORY: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_ITEM_UOM';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف فهرس IDX_ITEM_UOM بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  فهرس IDX_ITEM_UOM غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف فهرس IDX_ITEM_UOM: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_CATEGORY_PARENT';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف فهرس IDX_CATEGORY_PARENT بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  فهرس IDX_CATEGORY_PARENT غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف فهرس IDX_CATEGORY_PARENT: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_UOM_BASE_UNIT';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف فهرس IDX_UOM_BASE_UNIT بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  فهرس IDX_UOM_BASE_UNIT غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف فهرس IDX_UOM_BASE_UNIT: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_ITEM_CODE';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف فهرس IDX_ITEM_CODE بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  فهرس IDX_ITEM_CODE غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف فهرس IDX_ITEM_CODE: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_ITEM_BARCODE';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف فهرس IDX_ITEM_BARCODE بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  فهرس IDX_ITEM_BARCODE غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف فهرس IDX_ITEM_BARCODE: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_ITEM_NAME_AR';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف فهرس IDX_ITEM_NAME_AR بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  فهرس IDX_ITEM_NAME_AR غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف فهرس IDX_ITEM_NAME_AR: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_CATEGORY_CODE';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف فهرس IDX_CATEGORY_CODE بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  فهرس IDX_CATEGORY_CODE غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف فهرس IDX_CATEGORY_CODE: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_UOM_CODE';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف فهرس IDX_UOM_CODE بنجاح');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  فهرس IDX_UOM_CODE غير موجود');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف فهرس IDX_UOM_CODE: ' || SQLERRM);
        END IF;
END;
/

-- حذف الصلاحيات المتعلقة بالأصناف
BEGIN
    DELETE FROM PERMISSIONS WHERE MODULE_NAME = 'ITEMS';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف صلاحيات الأصناف من جدول PERMISSIONS');
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('❌ خطأ في حذف صلاحيات الأصناف: ' || SQLERRM);
        ROLLBACK;
END;
/

-- رسالة نجاح
DBMS_OUTPUT.PUT_LINE('');
DBMS_OUTPUT.PUT_LINE('========================================');
DBMS_OUTPUT.PUT_LINE('✅ تم حذف جميع جداول الأصناف بنجاح!');
DBMS_OUTPUT.PUT_LINE('========================================');
DBMS_OUTPUT.PUT_LINE('الجداول المحذوفة:');
DBMS_OUTPUT.PUT_LINE('• ITEMS - جدول الأصناف');
DBMS_OUTPUT.PUT_LINE('• ITEM_CATEGORIES - جدول مجموعات الأصناف');
DBMS_OUTPUT.PUT_LINE('• UNITS_OF_MEASURE - جدول وحدات القياس');
DBMS_OUTPUT.PUT_LINE('');
DBMS_OUTPUT.PUT_LINE('المتسلسلات المحذوفة:');
DBMS_OUTPUT.PUT_LINE('• SEQ_ITEM');
DBMS_OUTPUT.PUT_LINE('• SEQ_ITEM_CATEGORY');
DBMS_OUTPUT.PUT_LINE('• SEQ_UNIT_OF_MEASURE');
DBMS_OUTPUT.PUT_LINE('');
DBMS_OUTPUT.PUT_LINE('الفهارس المحذوفة: جميع الفهارس المتعلقة');
DBMS_OUTPUT.PUT_LINE('الصلاحيات المحذوفة: صلاحيات وحدة ITEMS');
DBMS_OUTPUT.PUT_LINE('========================================');
