import java.sql.*;
import java.util.Properties;

/**
 * تسجيل النوافذ والأنظمة الموجودة في شجرة الأنظمة
 * Register Existing Windows and Systems in System Tree
 */
public class RegisterExistingWindows {
    
    private Connection connection;
    private SystemTreeManager treeManager;
    
    public static void main(String[] args) {
        try {
            RegisterExistingWindows registrar = new RegisterExistingWindows();
            registrar.initialize();
            registrar.registerAllExistingWindows();
            registrar.close();
            
            System.out.println("✅ تم تسجيل جميع النوافذ الموجودة بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تسجيل النوافذ الموجودة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void initialize() throws SQLException {
        // الاتصال بقاعدة البيانات
        connection = getConnection();
        
        // تهيئة مدير شجرة النظام
        treeManager = SystemTreeManager.getInstance();
        
        System.out.println("✅ تم تهيئة نظام تسجيل النوافذ");
    }
    
    private Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    private void registerAllExistingWindows() {
        System.out.println("\n🔄 تسجيل النوافذ والأنظمة الموجودة...");
        System.out.println("=" + "=".repeat(60));
        
        // تنظيف البيانات الموجودة (اختياري)
        // clearExistingData();
        
        // تسجيل نوافذ إدارة الأصناف
        registerItemManagementWindows();
        
        // تسجيل نوافذ إدارة المستخدمين
        registerUserManagementWindows();
        
        // تسجيل نوافذ الإعدادات
        registerSettingsWindows();
        
        // تسجيل نوافذ التقارير
        registerReportsWindows();
        
        // تسجيل أدوات النظام
        registerSystemTools();
        
        // تسجيل نوافذ إضافية
        registerAdditionalWindows();
        
        System.out.println("=" + "=".repeat(60));
        System.out.println("✅ تم الانتهاء من تسجيل جميع النوافذ");
    }
    
    private void registerItemManagementWindows() {
        System.out.println("\n📦 تسجيل نوافذ إدارة الأصناف...");
        
        // نافذة بيانات الأصناف الحقيقية
        registerWindow(
            "RealItemDataWindow",
            "بيانات الأصناف الحقيقية",
            "Real Item Data",
            "إدارة وعرض بيانات الأصناف الحقيقية من قاعدة البيانات مع إمكانية البحث والتصفية",
            "Items Management",
            "icons/items_real.png"
        );
        
        // نافذة بيانات الأصناف الشاملة
        registerWindow(
            "ComprehensiveItemDataWindow",
            "بيانات الأصناف الشاملة",
            "Comprehensive Item Data",
            "عرض شامل لجميع بيانات الأصناف مع التفاصيل الكاملة والإحصائيات",
            "Items Management",
            "icons/items_comprehensive.png"
        );
        
        // نافذة مجموعات الأصناف
        registerWindow(
            "ItemGroupsManagementWindow",
            "مجموعات الأصناف",
            "Item Groups Management",
            "إدارة وتنظيم مجموعات الأصناف والتصنيفات الهرمية",
            "Items Management",
            "icons/item_groups.png"
        );
        
        // نافذة وحدات القياس
        registerWindow(
            "MeasurementUnitsWindow",
            "وحدات القياس",
            "Measurement Units",
            "إدارة وحدات القياس والتحويلات بين الوحدات المختلفة",
            "Items Management",
            "icons/measurement_units.png"
        );
        
        System.out.println("✅ تم تسجيل نوافذ إدارة الأصناف (4 نوافذ)");
    }
    
    private void registerUserManagementWindows() {
        System.out.println("\n👥 تسجيل نوافذ إدارة المستخدمين...");
        
        // نافذة إدارة المستخدمين
        registerWindow(
            "UserManagementWindow",
            "إدارة المستخدمين",
            "User Management",
            "إضافة وتعديل وحذف المستخدمين وإدارة الصلاحيات والأدوار",
            "User Management",
            "icons/users.png"
        );
        
        // نافذة صلاحيات المستخدمين
        registerWindow(
            "UserPermissionsWindow",
            "صلاحيات المستخدمين",
            "User Permissions",
            "إدارة صلاحيات المستخدمين والتحكم في الوصول للنوافذ والوظائف",
            "User Management",
            "icons/permissions.png"
        );
        
        // نافذة مجموعات المستخدمين
        registerWindow(
            "UserGroupsWindow",
            "مجموعات المستخدمين",
            "User Groups",
            "إنشاء وإدارة مجموعات المستخدمين وتعيين الصلاحيات الجماعية",
            "User Management",
            "icons/user_groups.png"
        );
        
        System.out.println("✅ تم تسجيل نوافذ إدارة المستخدمين (3 نوافذ)");
    }
    
    private void registerSettingsWindows() {
        System.out.println("\n⚙️ تسجيل نوافذ الإعدادات...");
        
        // نافذة الإعدادات العامة
        registerWindow(
            "GeneralSettingsWindow",
            "الإعدادات العامة",
            "General Settings",
            "إعدادات النظام العامة مثل اللغة والمظهر والتفضيلات",
            "General Settings",
            "icons/settings.png"
        );
        
        // نافذة إعدادات قاعدة البيانات
        registerWindow(
            "DatabaseSettingsWindow",
            "إعدادات قاعدة البيانات",
            "Database Settings",
            "تكوين اتصالات قاعدة البيانات والنسخ الاحتياطي",
            "General Settings",
            "icons/database_settings.png"
        );
        
        // نافذة إعدادات الأمان
        registerWindow(
            "SecuritySettingsWindow",
            "إعدادات الأمان",
            "Security Settings",
            "إعدادات الأمان والتشفير وسياسات كلمات المرور",
            "General Settings",
            "icons/security_settings.png"
        );
        
        // نافذة إعدادات النظام
        registerWindow(
            "SystemConfigurationWindow",
            "تكوين النظام",
            "System Configuration",
            "تكوين متقدم للنظام والمعاملات التشغيلية",
            "General Settings",
            "icons/system_config.png"
        );
        
        System.out.println("✅ تم تسجيل نوافذ الإعدادات (4 نوافذ)");
    }
    
    private void registerReportsWindows() {
        System.out.println("\n📊 تسجيل نوافذ التقارير...");
        
        // تقرير الأصناف
        registerWindow(
            "ItemsReportWindow",
            "تقرير الأصناف",
            "Items Report",
            "تقارير شاملة عن الأصناف والمخزون والحركات",
            "Reports",
            "icons/items_report.png"
        );
        
        // تقرير المستخدمين
        registerWindow(
            "UsersReportWindow",
            "تقرير المستخدمين",
            "Users Report",
            "تقارير عن نشاط المستخدمين والصلاحيات والجلسات",
            "Reports",
            "icons/users_report.png"
        );
        
        // تقرير النظام
        registerWindow(
            "SystemReportWindow",
            "تقرير النظام",
            "System Report",
            "تقارير عن أداء النظام والأخطاء والإحصائيات",
            "Reports",
            "icons/system_report.png"
        );
        
        // تقرير مخصص
        registerWindow(
            "CustomReportWindow",
            "التقارير المخصصة",
            "Custom Reports",
            "إنشاء وتخصيص التقارير حسب الحاجة",
            "Reports",
            "icons/custom_report.png"
        );
        
        System.out.println("✅ تم تسجيل نوافذ التقارير (4 نوافذ)");
    }
    
    private void registerSystemTools() {
        System.out.println("\n🔧 تسجيل أدوات النظام...");
        
        // فحص النظام الشامل
        registerTool(
            "CompleteOracleSystemTest",
            "فحص النظام الشامل",
            "Complete System Audit",
            "فحص شامل لحالة النظام وقواعد البيانات والاتصالات",
            "System Tools",
            "icons/system_audit.png"
        );
        
        // مراقب الأداء
        registerTool(
            "PerformanceMonitor",
            "مراقب الأداء",
            "Performance Monitor",
            "مراقبة أداء النظام والذاكرة والاتصالات في الوقت الفعلي",
            "System Tools",
            "icons/performance_monitor.png"
        );
        
        // إدارة الاتصالات
        registerTool(
            "TNSConnectionManager",
            "إدارة الاتصالات",
            "Connection Manager",
            "إدارة واختبار اتصالات قواعد البيانات وTNS",
            "System Tools",
            "icons/connection_manager.png"
        );
        
        // مدير الأمان
        registerTool(
            "SecurityManager",
            "مدير الأمان",
            "Security Manager",
            "إدارة التشفير وكلمات المرور والأحداث الأمنية",
            "System Tools",
            "icons/security_manager.png"
        );
        
        // مدير التكوين
        registerTool(
            "EnhancedConfigManager",
            "مدير التكوين",
            "Configuration Manager",
            "إدارة ملفات التكوين والإعدادات والنسخ الاحتياطي",
            "System Tools",
            "icons/config_manager.png"
        );
        
        System.out.println("✅ تم تسجيل أدوات النظام (5 أدوات)");
    }
    
    private void registerAdditionalWindows() {
        System.out.println("\n🔍 تسجيل نوافذ إضافية...");
        
        // نافذة البحث المتقدم
        registerWindow(
            "AdvancedSearchWindow",
            "البحث المتقدم",
            "Advanced Search",
            "بحث متقدم في جميع بيانات النظام مع فلاتر متعددة",
            "System Tools",
            "icons/advanced_search.png"
        );
        
        // نافذة النسخ الاحتياطي
        registerWindow(
            "BackupRestoreWindow",
            "النسخ الاحتياطي والاستعادة",
            "Backup & Restore",
            "إنشاء واستعادة النسخ الاحتياطية لقاعدة البيانات",
            "System Tools",
            "icons/backup_restore.png"
        );
        
        // نافذة سجل النظام
        registerWindow(
            "SystemLogWindow",
            "سجل النظام",
            "System Log",
            "عرض وتحليل سجلات النظام والأخطاء والأحداث",
            "System Tools",
            "icons/system_log.png"
        );
        
        // نافذة الإشعارات
        registerWindow(
            "NotificationCenterWindow",
            "مركز الإشعارات",
            "Notification Center",
            "إدارة وعرض الإشعارات والتنبيهات النظام",
            "System Tools",
            "icons/notifications.png"
        );
        
        System.out.println("✅ تم تسجيل النوافذ الإضافية (4 نوافذ)");
    }
    
    private void registerWindow(String windowClass, String nameAr, String nameEn, 
                              String description, String category, String iconPath) {
        try {
            boolean success = treeManager.registerNewWindow(windowClass, nameAr, nameEn, description, category);
            
            if (success) {
                // تحديث مسار الأيقونة إذا تم التسجيل بنجاح
                updateIconPath(windowClass, iconPath);
                System.out.println("  ✅ " + nameAr + " (" + windowClass + ")");
            } else {
                System.out.println("  ⚠️ " + nameAr + " (موجود مسبقاً أو فشل التسجيل)");
            }
        } catch (Exception e) {
            System.err.println("  ❌ خطأ في تسجيل " + nameAr + ": " + e.getMessage());
        }
    }
    
    private void registerTool(String toolClass, String nameAr, String nameEn, 
                            String description, String category, String iconPath) {
        try {
            // البحث عن الفئة الأب
            SystemTreeManager.SystemTreeNode parentNode = treeManager.findNodeByName(category);
            if (parentNode == null) {
                System.err.println("❌ لم يتم العثور على الفئة الأب: " + category);
                return;
            }
            
            // التحقق من عدم وجود الأداة مسبقاً
            SystemTreeManager.SystemTreeNode existingNode = treeManager.findNodeByName(nameEn);
            if (existingNode != null) {
                System.out.println("  ⚠️ " + nameAr + " (موجود مسبقاً)");
                return;
            }
            
            // إنشاء عقدة جديدة للأداة
            SystemTreeManager.SystemTreeNode newNode = new SystemTreeManager.SystemTreeNode();
            newNode.parentId = parentNode.treeId;
            newNode.nodeNameAr = nameAr;
            newNode.nodeNameEn = nameEn;
            newNode.nodeDescription = description;
            newNode.nodeType = SystemTreeManager.NodeType.TOOL;
            newNode.windowClass = toolClass;
            newNode.iconPath = iconPath;
            newNode.displayOrder = getNextDisplayOrder(parentNode.treeId);
            newNode.treeLevel = parentNode.treeLevel + 1;
            
            boolean success = treeManager.addNode(newNode);
            
            if (success) {
                System.out.println("  ✅ " + nameAr + " (" + toolClass + ")");
            } else {
                System.out.println("  ❌ فشل في تسجيل " + nameAr);
            }
            
        } catch (Exception e) {
            System.err.println("  ❌ خطأ في تسجيل " + nameAr + ": " + e.getMessage());
        }
    }
    
    private void updateIconPath(String windowClass, String iconPath) {
        try {
            String sql = "UPDATE ERP_SYSTEM_TREE SET ICON_PATH = ? WHERE WINDOW_CLASS = ?";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, iconPath);
                stmt.setString(2, windowClass);
                
                stmt.executeUpdate();
                connection.commit();
            }
        } catch (SQLException e) {
            System.err.println("⚠️ تحذير: فشل في تحديث مسار الأيقونة لـ " + windowClass);
        }
    }
    
    private int getNextDisplayOrder(int parentId) {
        try {
            String sql = "SELECT MAX(DISPLAY_ORDER) FROM ERP_SYSTEM_TREE WHERE PARENT_ID = ?";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setInt(1, parentId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getInt(1) + 1;
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في الحصول على الترتيب التالي: " + e.getMessage());
        }
        return 1;
    }
    
    private void clearExistingData() {
        System.out.println("🗑️ تنظيف البيانات الموجودة...");
        
        try {
            // حذف النوافذ والأدوات فقط، الاحتفاظ بالفئات
            String sql = "DELETE FROM ERP_SYSTEM_TREE WHERE NODE_TYPE IN ('WINDOW', 'TOOL')";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                int deleted = stmt.executeUpdate();
                connection.commit();
                System.out.println("✅ تم حذف " + deleted + " عنصر من البيانات السابقة");
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تنظيف البيانات: " + e.getMessage());
        }
    }
    
    private void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("✅ تم إغلاق اتصال قاعدة البيانات");
            }
            
            if (treeManager != null) {
                treeManager.close();
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إغلاق الاتصال: " + e.getMessage());
        }
    }
}
