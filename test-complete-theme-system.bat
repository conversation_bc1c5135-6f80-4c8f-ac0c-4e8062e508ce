@echo off
chcp 65001 > nul
echo ========================================
echo   🧪 اختبار نظام المظاهر الكامل
echo   Complete Theme System Test
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] اختبار قاعدة البيانات...
echo Testing database...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CreateCompleteThemeDatabase.java
if %errorlevel% neq 0 (
    echo ❌ فشل في تجميع منشئ قاعدة البيانات
    goto :error
)

java -cp "lib\*;." CreateCompleteThemeDatabase
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء قاعدة البيانات
    goto :error
)

echo.
echo [2] اختبار مدير المظاهر...
echo Testing theme manager...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CompleteThemeManager.java
if %errorlevel% neq 0 (
    echo ❌ فشل في تجميع مدير المظاهر
    goto :error
)

echo.
echo [3] اختبار نافذة المظاهر...
echo Testing theme window...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CompleteThemeWindow.java
if %errorlevel% neq 0 (
    echo ❌ فشل في تجميع نافذة المظاهر
    goto :error
)

echo.
echo [4] تحديث جدول النظام...
echo Updating system tree...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\UpdateSystemTreeForCompleteThemes.java
if %errorlevel% neq 0 (
    echo ❌ فشل في تجميع محدث جدول النظام
    goto :error
)

java -cp "lib\*;." UpdateSystemTreeForCompleteThemes
if %errorlevel% neq 0 (
    echo ❌ فشل في تحديث جدول النظام
    goto :error
)

echo.
echo [5] تحديث TreeMenuPanel...
echo Updating TreeMenuPanel...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo ❌ فشل في تجميع TreeMenuPanel
    goto :error
)

echo.
echo [6] اختبار النظام الكامل...
echo Testing complete system...

echo.
echo 🎨 تشغيل نافذة المظاهر الكاملة...
start /min java -cp "lib\*;." CompleteThemeWindow
timeout /t 3 /nobreak > nul

echo.
echo 🏠 تشغيل النظام الرئيسي...
start /min .\start-ship-erp.bat
timeout /t 5 /nobreak > nul

echo.
echo ✅ تم اختبار النظام بنجاح!
echo ✅ System tested successfully!
echo.
echo 📋 ملخص النظام:
echo System Summary:
echo   • مدير المظاهر الكامل: CompleteThemeManager
echo   • نافذة المظاهر الشاملة: CompleteThemeWindow  
echo   • قاعدة البيانات: ERP_COMPLETE_THEMES
echo   • عدد المظاهر المتاحة: 40+ مظهر
echo   • الفئات: 8 فئات مختلفة
echo   • التكامل: مع النظام الرئيسي
echo.
echo 🚀 طرق التشغيل:
echo Launch Methods:
echo   1. من النظام الرئيسي: الإعدادات العامة → إعدادات الواجهة والمظهر
echo   2. مباشرة: .\start-complete-theme-system.bat
echo   3. يدوياً: java -cp "lib\*;." CompleteThemeWindow
echo.
goto :success

:error
echo.
echo ❌ حدث خطأ أثناء الاختبار!
echo ❌ Error occurred during testing!
pause
exit /b 1

:success
echo 🎉 تم إكمال الاختبار بنجاح!
echo 🎉 Testing completed successfully!
pause
