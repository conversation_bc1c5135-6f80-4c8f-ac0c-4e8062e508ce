import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * استعادة الأنظمة الحرجة المفقودة Restore Critical Missing Systems
 */
public class RestoreCriticalSystems {

    public static void main(String[] args) {
        System.out.println("🚨 استعادة الأنظمة الحرجة المفقودة...");

        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();

            // استعادة إدارة الأصناف والمخزون
            restoreItemsManagement(connection);

            // استعادة الإعدادات العامة
            restoreGeneralSettings(connection);

            // استعادة أدوات النظام
            restoreSystemTools(connection);

            // استعادة التقارير
            restoreReports(connection);

            connection.close();
            System.out.println("✅ تم استعادة جميع الأنظمة الحرجة بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ في استعادة الأنظمة الحرجة: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void restoreItemsManagement(Connection connection) throws SQLException {
        System.out.println("📦 استعادة نظام إدارة الأصناف والمخزون...");

        // البحث عن فئة إدارة الأصناف
        int itemsCategoryId = getCategoryId(connection, "إدارة الأصناف");

        if (itemsCategoryId == 0) {
            // إنشاء فئة إدارة الأصناف
            itemsCategoryId = createCategory(connection, "إدارة الأصناف", "Items Management",
                    "إدارة شاملة للأصناف والمنتجات والمخزون");
        }

        if (itemsCategoryId > 0) {
            // إضافة نوافذ إدارة الأصناف
            addItemsWindows(connection, itemsCategoryId);
        }
    }

    private static void restoreGeneralSettings(Connection connection) throws SQLException {
        System.out.println("⚙️ استعادة نظام الإعدادات العامة...");

        // البحث عن فئة الإعدادات العامة
        int settingsCategoryId = getCategoryId(connection, "الإعدادات العامة");

        if (settingsCategoryId == 0) {
            // إنشاء فئة الإعدادات العامة
            settingsCategoryId = createCategory(connection, "الإعدادات العامة", "General Settings",
                    "الإعدادات العامة للنظام والتكوينات الأساسية");
        }

        if (settingsCategoryId > 0) {
            // إضافة نوافذ الإعدادات العامة
            addGeneralSettingsWindows(connection, settingsCategoryId);
        }
    }

    private static void restoreSystemTools(Connection connection) throws SQLException {
        System.out.println("🔧 استعادة أدوات النظام...");

        // البحث عن فئة أدوات النظام
        int toolsCategoryId = getCategoryId(connection, "أدوات النظام");

        if (toolsCategoryId == 0) {
            // إنشاء فئة أدوات النظام
            toolsCategoryId = createCategory(connection, "أدوات النظام", "System Tools",
                    "أدوات النظام والصيانة والنسخ الاحتياطي");
        }

        if (toolsCategoryId > 0) {
            // إضافة نوافذ أدوات النظام
            addSystemToolsWindows(connection, toolsCategoryId);
        }
    }

    private static void restoreReports(Connection connection) throws SQLException {
        System.out.println("📊 استعادة نظام التقارير...");

        // البحث عن فئة التقارير
        int reportsCategoryId = getCategoryId(connection, "التقارير");

        if (reportsCategoryId == 0) {
            // إنشاء فئة التقارير
            reportsCategoryId = createCategory(connection, "التقارير", "Reports",
                    "التقارير والإحصائيات التفصيلية");
        }

        if (reportsCategoryId > 0) {
            // إضافة نوافذ التقارير
            addReportsWindows(connection, reportsCategoryId);
        }
    }

    private static void addItemsWindows(Connection connection, int parentId) throws SQLException {
        System.out.println("📦 إضافة نوافذ إدارة الأصناف...");

        String[][] windows = {
                {"إدارة الأصناف", "Items Management", "ItemsManagementWindow",
                        "إدارة شاملة للأصناف والمنتجات"},
                {"تصنيفات الأصناف", "Item Categories", "ItemCategoriesWindow",
                        "إدارة تصنيفات ومجموعات الأصناف"},
                {"وحدات القياس", "Units of Measure", "UnitsOfMeasureWindow",
                        "إدارة وحدات القياس والتحويلات"},
                {"أسعار الأصناف", "Item Prices", "ItemPricesWindow",
                        "إدارة أسعار الأصناف وقوائم الأسعار"},
                {"مخزون الأصناف", "Item Inventory", "ItemInventoryWindow",
                        "متابعة مخزون الأصناف والكميات"},
                {"حركة المخزون", "Inventory Transactions", "InventoryTransactionsWindow",
                        "تتبع حركة دخول وخروج المخزون"},
                {"تقييم المخزون", "Inventory Valuation", "InventoryValuationWindow",
                        "تقييم المخزون والتكلفة"},
                {"إعدادات المخزون", "Inventory Settings", "InventorySettingsWindow",
                        "إعدادات نظام المخزون"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static void addGeneralSettingsWindows(Connection connection, int parentId)
            throws SQLException {
        System.out.println("⚙️ إضافة نوافذ الإعدادات العامة...");

        String[][] windows = {
                {"إعدادات النظام العامة", "General System Settings", "GeneralSystemSettingsWindow",
                        "الإعدادات العامة للنظام"},
                {"إعدادات الشركات الشاملة", "Advanced Company Settings",
                        "AdvancedCompanySettingsWindow", "إعدادات الشركات الشاملة والمتقدمة"},
                {"إعدادات الواجهة والمظهر", "Interface & Theme Settings", "WorkingThemeWindow",
                        "إعدادات واجهة النظام والمظاهر"},
                {"المتغيرات العامة", "Global Variables", "GlobalVariablesWindow",
                        "إدارة المتغيرات العامة للنظام"},
                {"إعدادات قاعدة البيانات", "Database Settings", "DatabaseSettingsWindow",
                        "إعدادات الاتصال بقاعدة البيانات"},
                {"إعدادات الأمان", "Security Settings", "SecuritySettingsWindow",
                        "إعدادات الأمان والصلاحيات"},
                {"إعدادات النسخ الاحتياطي", "Backup Settings", "BackupSettingsWindow",
                        "إعدادات النسخ الاحتياطي"},
                {"إعدادات التقارير", "Reports Settings", "ReportsSettingsWindow",
                        "إعدادات التقارير والطباعة"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static void addSystemToolsWindows(Connection connection, int parentId)
            throws SQLException {
        System.out.println("🔧 إضافة نوافذ أدوات النظام...");

        String[][] windows = {
                {"مراقب النظام", "System Monitor", "SystemMonitorWindow",
                        "مراقبة أداء النظام والموارد"},
                {"سجلات النظام", "System Logs", "SystemLogsWindow", "عرض سجلات النظام والأخطاء"},
                {"النسخ الاحتياطي", "Backup Manager", "BackupManagerWindow",
                        "إدارة النسخ الاحتياطي"},
                {"استعادة البيانات", "Data Recovery", "DataRecoveryWindow",
                        "استعادة البيانات من النسخ الاحتياطية"},
                {"صيانة قاعدة البيانات", "Database Maintenance", "DatabaseMaintenanceWindow",
                        "صيانة وتحسين قاعدة البيانات"},
                {"مدير المهام", "Task Manager", "TaskManagerWindow", "إدارة المهام والعمليات"},
                {"أدوات التشخيص", "Diagnostic Tools", "DiagnosticToolsWindow",
                        "أدوات تشخيص مشاكل النظام"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static void addReportsWindows(Connection connection, int parentId) throws SQLException {
        System.out.println("📊 إضافة نوافذ التقارير...");

        String[][] windows = {
                {"مصمم التقارير", "Report Designer", "ReportDesignerWindow",
                        "تصميم وإنشاء التقارير المخصصة"},
                {"مكتبة التقارير", "Reports Library", "ReportsLibraryWindow",
                        "مكتبة التقارير الجاهزة"},
                {"جدولة التقارير", "Report Scheduler", "ReportSchedulerWindow",
                        "جدولة التقارير التلقائية"},
                {"توزيع التقارير", "Report Distribution", "ReportDistributionWindow",
                        "توزيع التقارير عبر البريد الإلكتروني"},
                {"أرشيف التقارير", "Reports Archive", "ReportsArchiveWindow",
                        "أرشيف التقارير المنجزة"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static int getCategoryId(Connection connection, String categoryName)
            throws SQLException {
        String sql =
                "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("TREE_ID");
                }
            }
        }
        return 0;
    }

    private static int createCategory(Connection connection, String nameAr, String nameEn,
            String description) throws SQLException {
        int nextOrder = getNextRootOrder(connection);

        String sql = """
                INSERT INTO ERP_SYSTEM_TREE
                (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE,
                 DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE,
                 CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
                VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, NULL, ?, ?, ?, 'CATEGORY',
                        ?, 1, 'Y', 'Y',
                        SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, nameAr);
            stmt.setString(2, nameEn);
            stmt.setString(3, description);
            stmt.setInt(4, nextOrder);

            int result = stmt.executeUpdate();
            if (result > 0) {
                System.out.println("✅ تم إنشاء الفئة: " + nameAr);
                return getLastInsertedId(connection);
            }
        }
        return 0;
    }

    private static int getNextRootOrder(Connection connection) throws SQLException {
        String sql =
                "SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID IS NULL";

        try (PreparedStatement stmt = connection.prepareStatement(sql);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        return 1;
    }

    private static int getLastInsertedId(Connection connection) throws SQLException {
        String sql = "SELECT ERP_SYSTEM_TREE_SEQ.CURRVAL FROM DUAL";

        try (PreparedStatement stmt = connection.prepareStatement(sql);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        return 0;
    }

    private static void addWindow(Connection connection, int parentId, String nameAr, String nameEn,
            String windowClass, String description, int order) throws SQLException {

        // التحقق من وجود النافذة مسبقاً
        String checkSQL =
                "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = ? OR (NODE_NAME_AR = ? AND PARENT_ID = ?)";

        try (PreparedStatement stmt = connection.prepareStatement(checkSQL)) {
            stmt.setString(1, windowClass);
            stmt.setString(2, nameAr);
            stmt.setInt(3, parentId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next() && rs.getInt(1) > 0) {
                    System.out.println("ℹ️ النافذة موجودة مسبقاً: " + nameAr);
                    return;
                }
            }
        }

        String sql = """
                INSERT INTO ERP_SYSTEM_TREE
                (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE,
                 WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE,
                 CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
                VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, ?, ?, ?, ?, 'WINDOW',
                        ?, ?, 2, 'Y', 'Y',
                        SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            stmt.setString(2, nameAr);
            stmt.setString(3, nameEn);
            stmt.setString(4, description);
            stmt.setString(5, windowClass);
            stmt.setInt(6, order);

            int result = stmt.executeUpdate();
            if (result > 0) {
                System.out.println("✅ تم إضافة النافذة: " + nameAr);
            }
        }
    }
}
