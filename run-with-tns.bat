@echo off
echo ========================================
echo    RUN SYSTEM WITH TNS SUPPORT
echo    تشغيل النظام مع دعم TNS
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Starting Ship ERP System with TNS support...
echo.

REM إعداد متغيرات البيئة
set TNS_ADMIN=%CD%\network\admin
set ORACLE_NET_TNS_ADMIN=%TNS_ADMIN%
set JAVA_TNS_OPTS=-Doracle.net.tns_admin="%TNS_ADMIN%" -Doracle.jdbc.defaultNChar=true -Dfile.encoding=UTF-8

echo [1] TNS Environment configured:
echo    TNS_ADMIN: %TNS_ADMIN%
echo.

REM التحقق من ملفات TNS
echo [2] Checking TNS files:
if exist "%TNS_ADMIN%\tnsnames.ora" (
    echo    OK: tnsnames.ora found
) else (
    echo    ERROR: tnsnames.ora not found
    goto :error
)

if exist "%TNS_ADMIN%\sqlnet.ora" (
    echo    OK: sqlnet.ora found
) else (
    echo    WARNING: sqlnet.ora not found
)

echo.

REM إعداد CLASSPATH
echo [3] Setting up classpath...
set CLASSPATH=.
for %%f in (lib\*.jar) do (
    set CLASSPATH=!CLASSPATH!;%%f
)

echo    Classpath configured with libraries

echo.

REM تجميع TNSConnectionManager إذا لم يكن مجمعاً
echo [4] Compiling TNS Connection Manager...
if not exist "TNSConnectionManager.class" (
    echo    Compiling TNSConnectionManager...
    javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\TNSConnectionManager.java
    if errorlevel 1 (
        echo    ERROR: Failed to compile TNSConnectionManager
        goto :error
    )
    echo    OK: TNSConnectionManager compiled
) else (
    echo    OK: TNSConnectionManager already compiled
)

echo.

REM اختبار اتصالات TNS
echo [5] Testing TNS connections...
java %JAVA_TNS_OPTS% -cp "%CLASSPATH%" TNSConnectionManager
if errorlevel 1 (
    echo    WARNING: TNS connection test failed
    echo    Continuing with system startup...
) else (
    echo    OK: TNS connections tested successfully
)

echo.

REM تجميع النظام الرئيسي
echo [6] Compiling main system...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\CompleteOracleSystemTest.java
if errorlevel 1 (
    echo    ERROR: Failed to compile main system
    goto :error
)

echo    OK: Main system compiled

echo.

REM تشغيل النظام
echo [7] Starting Ship ERP System...
echo ========================================
echo    SYSTEM STARTUP
echo    بدء تشغيل النظام
echo ========================================

java %JAVA_TNS_OPTS% -cp "%CLASSPATH%" CompleteOracleSystemTest

echo.
echo ========================================
echo    SYSTEM SHUTDOWN
echo    إغلاق النظام
echo ========================================

goto :end

:error
echo.
echo ========================================
echo    ERROR OCCURRED
echo    حدث خطأ
echo ========================================
echo.
echo [ERROR] Failed to start system with TNS support
echo [خطأ] فشل في تشغيل النظام مع دعم TNS
echo.
echo Troubleshooting:
echo 1. Check if TNS files exist in network\admin\
echo 2. Verify Oracle database is running
echo 3. Check Java compilation errors
echo.
goto :end

:end
echo.
echo System execution completed.
echo اكتمل تنفيذ النظام.
pause
