@echo off
echo ========================================
echo    ESSENTIAL LIBRARIES DOWNLOADER
echo    تحميل المكتبات الأساسية
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Downloading essential libraries for security and performance...
echo.

if not exist "lib-backup" mkdir lib-backup
if exist "lib\*.jar" copy "lib\*.jar" "lib-backup\" >nul 2>&1

echo [1] Downloading HikariCP for connection pooling...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar' -OutFile 'lib\HikariCP-5.0.1.jar'"

echo [2] Downloading Jasypt for password encryption...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/jasypt/jasypt/1.9.3/jasypt-1.9.3.jar' -OutFile 'lib\jasypt-1.9.3.jar'"

echo [3] Downloading Commons Codec for encoding...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/commons-codec/commons-codec/1.15/commons-codec-1.15.jar' -OutFile 'lib\commons-codec-1.15.jar'"

echo [4] Downloading Logback for advanced logging...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.4.8/logback-classic-1.4.8.jar' -OutFile 'lib\logback-classic-1.4.8.jar'"
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/ch/qos/logback/logback-core/1.4.8/logback-core-1.4.8.jar' -OutFile 'lib\logback-core-1.4.8.jar'"

echo [5] Downloading Commons Configuration...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/apache/commons/commons-configuration2/2.9.0/commons-configuration2-2.9.0.jar' -OutFile 'lib\commons-configuration2-2.9.0.jar'"

echo [6] Downloading Commons Lang3...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar' -OutFile 'lib\commons-lang3-3.12.0.jar'"

echo [7] Downloading Caffeine for caching...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.1.7/caffeine-3.1.7.jar' -OutFile 'lib\caffeine-3.1.7.jar'"

echo.
echo [SUCCESS] Essential libraries downloaded!
echo.

echo Verifying downloads...
dir lib\*.jar

echo.
echo Libraries downloaded:
echo - HikariCP-5.0.1.jar (Connection Pooling)
echo - jasypt-1.9.3.jar (Password Encryption)
echo - commons-codec-1.15.jar (Encoding)
echo - logback-classic-1.4.8.jar (Logging)
echo - logback-core-1.4.8.jar (Logging Core)
echo - commons-configuration2-2.9.0.jar (Configuration)
echo - commons-lang3-3.12.0.jar (Utilities)
echo - caffeine-3.1.7.jar (Caching)
echo.

pause
