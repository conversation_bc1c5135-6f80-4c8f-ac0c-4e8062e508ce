import java.sql.*;
import java.util.Properties;

/**
 * إصلاح شامل لشجرة النظام
 * Complete System Tree Fix
 */
public class CompleteSystemTreeFix {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔧 إصلاح شامل لشجرة النظام...");
            
            Connection connection = getConnection();
            
            // إصلاح شامل
            completeTreeFix(connection);
            
            connection.close();
            
            System.out.println("✅ تم الإصلاح الشامل لشجرة النظام بنجاح!");
            
            // اختبار النتيجة
            testResult();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في الإصلاح الشامل: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    private static void completeTreeFix(Connection conn) throws SQLException {
        System.out.println("📊 عرض البيانات الحالية...");
        showCurrentData(conn);
        
        System.out.println("\n🔧 إصلاح النوافذ المفقودة...");
        fixMissingWindows(conn);
        
        System.out.println("\n📋 إعادة ترتيب الشجرة...");
        reorganizeTree(conn);
        
        System.out.println("✅ تم الإصلاح الشامل");
    }
    
    private static void showCurrentData(Connection conn) throws SQLException {
        String sql = """
            SELECT 
                TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_TYPE, TREE_LEVEL, DISPLAY_ORDER
            FROM ERP_SYSTEM_TREE
            WHERE IS_ACTIVE = 'Y' AND IS_VISIBLE = 'Y'
            ORDER BY TREE_LEVEL, DISPLAY_ORDER
        """;
        
        System.out.println("البيانات الحالية:");
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                System.out.println("  " + rs.getInt("TREE_ID") + " | " + 
                                 rs.getObject("PARENT_ID") + " | " + 
                                 rs.getString("NODE_NAME_AR") + " | " + 
                                 rs.getString("NODE_TYPE") + " | " + 
                                 rs.getInt("TREE_LEVEL") + " | " + 
                                 rs.getInt("DISPLAY_ORDER"));
            }
        }
    }
    
    private static void fixMissingWindows(Connection conn) throws SQLException {
        // إضافة النوافذ المفقودة لإدارة المستخدمين
        int usersId = getCategoryId(conn, "إدارة المستخدمين");
        if (usersId > 0) {
            addWindowIfNotExists(conn, usersId, "إدارة المستخدمين", "User Management", 
                               "إضافة وتعديل وحذف المستخدمين", "UserManagementWindow", 1);
        }
        
        // إضافة النوافذ المفقودة للإعدادات العامة
        int settingsId = getCategoryId(conn, "الإعدادات العامة");
        if (settingsId > 0) {
            addWindowIfNotExists(conn, settingsId, "الإعدادات العامة", "General Settings", 
                               "إعدادات النظام العامة", "GeneralSettingsWindow", 1);
        }
    }
    
    private static void reorganizeTree(Connection conn) throws SQLException {
        // إعادة ترتيب الفئات الرئيسية
        updateNode(conn, "إدارة الأصناف", 1, 1, 1);
        updateNode(conn, "إدارة المستخدمين", 1, 2, 1);
        updateNode(conn, "الإعدادات العامة", 1, 3, 1);
        updateNode(conn, "التقارير", 1, 4, 1);
        updateNode(conn, "أدوات النظام", 1, 5, 1);
        
        // إعادة ترتيب نوافذ إدارة الأصناف
        int itemsId = getCategoryId(conn, "إدارة الأصناف");
        if (itemsId > 0) {
            moveWindowToParent(conn, "بيانات الأصناف الحقيقية", itemsId, 2, 1);
            moveWindowToParent(conn, "بيانات الأصناف الشاملة", itemsId, 2, 2);
            moveWindowToParent(conn, "مجموعات الأصناف", itemsId, 2, 3);
            moveWindowToParent(conn, "وحدات القياس", itemsId, 2, 4);
        }
        
        // إعادة ترتيب نوافذ إدارة المستخدمين
        int usersId = getCategoryId(conn, "إدارة المستخدمين");
        if (usersId > 0) {
            moveWindowToParent(conn, "إدارة المستخدمين", usersId, 2, 1);
            moveWindowToParent(conn, "صلاحيات المستخدمين", usersId, 2, 2);
            moveWindowToParent(conn, "مجموعات المستخدمين", usersId, 2, 3);
        }
        
        // إعادة ترتيب نوافذ الإعدادات
        int settingsId = getCategoryId(conn, "الإعدادات العامة");
        if (settingsId > 0) {
            moveWindowToParent(conn, "الإعدادات العامة", settingsId, 2, 1);
            moveWindowToParent(conn, "إعدادات قاعدة البيانات", settingsId, 2, 2);
            moveWindowToParent(conn, "إعدادات الأمان", settingsId, 2, 3);
            moveWindowToParent(conn, "تكوين النظام", settingsId, 2, 4);
        }
        
        // إعادة ترتيب نوافذ التقارير
        int reportsId = getCategoryId(conn, "التقارير");
        if (reportsId > 0) {
            moveWindowToParent(conn, "تقرير الأصناف", reportsId, 2, 1);
            moveWindowToParent(conn, "تقرير المستخدمين", reportsId, 2, 2);
            moveWindowToParent(conn, "تقرير النظام", reportsId, 2, 3);
            moveWindowToParent(conn, "التقارير المخصصة", reportsId, 2, 4);
        }
        
        // إعادة ترتيب أدوات النظام
        int toolsId = getCategoryId(conn, "أدوات النظام");
        if (toolsId > 0) {
            moveWindowToParent(conn, "فحص النظام الشامل", toolsId, 2, 1);
            moveWindowToParent(conn, "مراقب الأداء", toolsId, 2, 2);
            moveWindowToParent(conn, "إدارة الاتصالات", toolsId, 2, 3);
            moveWindowToParent(conn, "مدير الأمان", toolsId, 2, 4);
            moveWindowToParent(conn, "مدير التكوين", toolsId, 2, 5);
            moveWindowToParent(conn, "البحث المتقدم", toolsId, 2, 6);
            moveWindowToParent(conn, "النسخ الاحتياطي والاستعادة", toolsId, 2, 7);
            moveWindowToParent(conn, "سجل النظام", toolsId, 2, 8);
            moveWindowToParent(conn, "مركز الإشعارات", toolsId, 2, 9);
        }
    }
    
    private static int getCategoryId(Connection conn, String categoryName) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }
    
    private static void addWindowIfNotExists(Connection conn, int parentId, String nameAr, String nameEn, 
                                           String description, String windowClass, int order) throws SQLException {
        // التحقق من وجود النافذة
        String checkSql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(checkSql)) {
            stmt.setString(1, nameAr);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next() && rs.getInt(1) > 0) {
                    System.out.println("  ⚠️ " + nameAr + " (موجود مسبقاً)");
                    return;
                }
            }
        }
        
        // إضافة النافذة
        String insertSql = """
            INSERT INTO ERP_SYSTEM_TREE (
                PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
                NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
            ) VALUES (?, ?, ?, ?, 'WINDOW', ?, ?, 2)
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(insertSql)) {
            stmt.setInt(1, parentId);
            stmt.setString(2, nameAr);
            stmt.setString(3, nameEn);
            stmt.setString(4, description);
            stmt.setString(5, windowClass);
            stmt.setInt(6, order);
            
            stmt.executeUpdate();
            System.out.println("  ✅ تم إضافة: " + nameAr);
        }
    }
    
    private static void updateNode(Connection conn, String nodeName, int level, int order, int parentId) throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET TREE_LEVEL = ?, DISPLAY_ORDER = ?, PARENT_ID = ? WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, level);
            stmt.setInt(2, order);
            stmt.setInt(3, parentId);
            stmt.setString(4, nodeName);
            
            int updated = stmt.executeUpdate();
            if (updated > 0) {
                System.out.println("  ✅ تم تحديث: " + nodeName);
            }
        }
    }
    
    private static void moveWindowToParent(Connection conn, String windowName, int parentId, int level, int order) throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET PARENT_ID = ?, TREE_LEVEL = ?, DISPLAY_ORDER = ? WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            stmt.setInt(2, level);
            stmt.setInt(3, order);
            stmt.setString(4, windowName);
            
            int updated = stmt.executeUpdate();
            if (updated > 0) {
                System.out.println("  ✅ تم نقل: " + windowName + " -> الفئة " + parentId);
            }
        }
    }
    
    private static void testResult() {
        System.out.println("\n🔍 اختبار النتيجة النهائية...");
        
        try {
            SystemTreeManager manager = SystemTreeManager.getInstance();
            manager.printSystemTree();
            manager.close();
        } catch (Exception e) {
            System.err.println("❌ خطأ في اختبار النتيجة: " + e.getMessage());
        }
    }
}
