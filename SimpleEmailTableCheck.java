import java.sql.*;
import java.util.Properties;

/**
 * فحص بسيط لجداول البريد الإلكتروني
 */
public class SimpleEmailTableCheck {
    
    private static final String DB_URL = "*************************************";
    private static final String DB_USER = "ias20251";
    private static final String DB_PASSWORD = "ys123";
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  CHECKING EMAIL TABLES");
        System.out.println("  فحص جداول البريد الإلكتروني");
        System.out.println("========================================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", DB_USER);
            props.setProperty("password", DB_PASSWORD);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            Connection connection = DriverManager.getConnection(DB_URL, props);
            System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح");
            
            // فحص الجداول المطلوبة
            System.out.println("\n📋 فحص الجداول:");
            checkTable(connection, "EMAIL_ACCOUNTS");
            checkTable(connection, "ERP_EMAIL_ACCOUNTS");
            checkTable(connection, "EMAIL_MESSAGES");
            checkTable(connection, "EMAIL_TEMPLATES");
            
            // فحص المتسلسلات
            System.out.println("\n🔢 فحص المتسلسلات:");
            checkSequence(connection, "EMAIL_ACCOUNTS_SEQ");
            checkSequence(connection, "ERP_EMAIL_ACCOUNTS_SEQ");
            
            // محاولة تشغيل الاستعلام الذي تستخدمه النافذة
            System.out.println("\n🔍 اختبار استعلام النافذة:");
            testWindowQuery(connection);
            
            connection.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فحص الجداول: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkTable(Connection connection, String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, tableName.toUpperCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        System.out.println("✅ جدول " + tableName + " موجود");
                        
                        // فحص عدد السجلات
                        try {
                            String countSql = "SELECT COUNT(*) FROM " + tableName;
                            try (PreparedStatement countStmt = connection.prepareStatement(countSql);
                                 ResultSet countRs = countStmt.executeQuery()) {
                                if (countRs.next()) {
                                    System.out.println("   📊 عدد السجلات: " + countRs.getInt(1));
                                }
                            }
                        } catch (SQLException e) {
                            System.out.println("   ⚠️ لا يمكن قراءة السجلات: " + e.getMessage());
                        }
                        
                    } else {
                        System.out.println("❌ جدول " + tableName + " غير موجود");
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في فحص جدول " + tableName + ": " + e.getMessage());
        }
    }
    
    private static void checkSequence(Connection connection, String sequenceName) {
        try {
            String sql = "SELECT COUNT(*) FROM USER_SEQUENCES WHERE SEQUENCE_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, sequenceName.toUpperCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        System.out.println("✅ متسلسل " + sequenceName + " موجود");
                    } else {
                        System.out.println("❌ متسلسل " + sequenceName + " غير موجود");
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في فحص متسلسل " + sequenceName + ": " + e.getMessage());
        }
    }
    
    private static void testWindowQuery(Connection connection) {
        try {
            // هذا هو الاستعلام الذي تستخدمه النافذة
            String sql = """
                    SELECT a.ACCOUNT_ID, a.ACCOUNT_NAME, a.EMAIL_ADDRESS, a.ACCOUNT_TYPE,
                           CASE WHEN a.IS_CONNECTED = 'Y' THEN 'متصل' ELSE 'غير متصل' END AS STATUS,
                           TO_CHAR(a.LAST_CHECK_DATE, 'DD/MM/YYYY HH24:MI') AS LAST_CHECK,
                           CASE WHEN a.LAST_ERROR IS NOT NULL THEN 'نعم' ELSE 'لا' END AS HAS_ERRORS,
                           (SELECT COUNT(*) FROM EMAIL_MESSAGES m WHERE m.ACCOUNT_ID = a.ACCOUNT_ID) AS MESSAGE_COUNT
                    FROM EMAIL_ACCOUNTS a
                    WHERE a.IS_ACTIVE = 'Y'
                    ORDER BY a.DEFAULT_ACCOUNT DESC, a.ACCOUNT_NAME
                    """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                System.out.println("✅ استعلام النافذة يعمل بنجاح");
                
                int count = 0;
                while (rs.next() && count < 5) {
                    System.out.println("   📧 حساب: " + rs.getString("ACCOUNT_NAME") + 
                                     " (" + rs.getString("EMAIL_ADDRESS") + ")");
                    count++;
                }
                
                if (count == 0) {
                    System.out.println("   ⚠️ لا توجد حسابات بريد إلكتروني في قاعدة البيانات");
                }
                
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في استعلام النافذة: " + e.getMessage());
            System.err.println("   السبب المحتمل: جدول EMAIL_ACCOUNTS غير موجود أو لا يحتوي على الأعمدة المطلوبة");
        }
    }
}
