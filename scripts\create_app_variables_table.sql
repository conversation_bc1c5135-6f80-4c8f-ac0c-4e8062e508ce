-- إنشاء جدول المتغيرات العامة للتطبيق
-- Create Application Global Variables Table
-- Ship ERP System - Global Variables Management

-- حذ<PERSON> الجدول إذا كان موجوداً
DROP TABLE ERP_APP_VARIABLES CASCADE CONSTRAINTS;

-- إنشاء sequence للمعرف الفريد
DROP SEQUENCE ERP_APP_VARIABLES_SEQ;
CREATE SEQUENCE ERP_APP_VARIABLES_SEQ START WITH 1 INCREMENT BY 1;

-- إنشاء الجدول الرئيسي للمتغيرات العامة
CREATE TABLE ERP_APP_VARIABLES (
    -- الم<PERSON>ر<PERSON> الفريد
    VAR_ID NUMBER(10) PRIMARY KEY,
    
    -- اسم المتغير (فريد)
    VAR_NAME VARCHAR2(100) NOT NULL UNIQUE,
    
    -- اسم المتغير بالعربية
    VAR_NAME_AR NVARCHAR2(200) NOT NULL,
    
    -- اسم المتغير بالإنجليزية
    VAR_NAME_EN VARCHAR2(200) NOT NULL,
    
    -- وصف المتغير
    VAR_DESCRIPTION NVARCHAR2(1000),
    
    -- قيمة المتغير
    VAR_VALUE NVARCHAR2(4000),
    
    -- القيمة الافتراضية
    DEFAULT_VALUE NVARCHAR2(4000),
    
    -- نوع البيانات (STRING, NUMBER, BOOLEAN, DATE, JSON, XML)
    DATA_TYPE VARCHAR2(20) DEFAULT 'STRING' NOT NULL,
    
    -- فئة المتغير (SYSTEM, UI, DATABASE, SECURITY, BUSINESS)
    VAR_CATEGORY VARCHAR2(50) DEFAULT 'SYSTEM' NOT NULL,
    
    -- مجموعة المتغير (للتنظيم)
    VAR_GROUP VARCHAR2(100),
    
    -- مستوى الأولوية (1=عالي، 2=متوسط، 3=منخفض)
    PRIORITY_LEVEL NUMBER(1) DEFAULT 2 CHECK (PRIORITY_LEVEL IN (1, 2, 3)),
    
    -- هل المتغير مطلوب
    IS_REQUIRED CHAR(1) DEFAULT 'N' CHECK (IS_REQUIRED IN ('Y', 'N')),
    
    -- هل المتغير للقراءة فقط
    IS_READONLY CHAR(1) DEFAULT 'N' CHECK (IS_READONLY IN ('Y', 'N')),
    
    -- هل المتغير مشفر
    IS_ENCRYPTED CHAR(1) DEFAULT 'N' CHECK (IS_ENCRYPTED IN ('Y', 'N')),
    
    -- هل المتغير نشط
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    
    -- هل المتغير مرئي في الواجهة
    IS_VISIBLE CHAR(1) DEFAULT 'Y' CHECK (IS_VISIBLE IN ('Y', 'N')),
    
    -- القيم المسموحة (مفصولة بفواصل أو JSON)
    ALLOWED_VALUES CLOB,
    
    -- قيمة دنيا (للأرقام)
    MIN_VALUE NUMBER,
    
    -- قيمة عليا (للأرقام)
    MAX_VALUE NUMBER,
    
    -- طول أدنى (للنصوص)
    MIN_LENGTH NUMBER,
    
    -- طول أقصى (للنصوص)
    MAX_LENGTH NUMBER,
    
    -- نمط التحقق (Regular Expression)
    VALIDATION_PATTERN VARCHAR2(500),
    
    -- رسالة خطأ التحقق
    VALIDATION_MESSAGE NVARCHAR2(500),
    
    -- وحدة القياس
    UNIT_OF_MEASURE VARCHAR2(50),
    
    -- تنسيق العرض
    DISPLAY_FORMAT VARCHAR2(100),
    
    -- ترتيب العرض
    DISPLAY_ORDER NUMBER(5) DEFAULT 0,
    
    -- مساعدة المستخدم
    HELP_TEXT NVARCHAR2(2000),
    
    -- رابط المساعدة
    HELP_URL VARCHAR2(500),
    
    -- معلومات إضافية (JSON format)
    ADDITIONAL_INFO CLOB,
    
    -- تاريخ الإنشاء
    CREATED_DATE DATE DEFAULT SYSDATE,
    
    -- المستخدم المنشئ
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    
    -- تاريخ آخر تحديث
    LAST_UPDATED DATE DEFAULT SYSDATE,
    
    -- المستخدم المحدث
    UPDATED_BY VARCHAR2(50) DEFAULT USER,
    
    -- تاريخ آخر وصول
    LAST_ACCESSED DATE,
    
    -- المستخدم الذي وصل أخيراً
    LAST_ACCESSED_BY VARCHAR2(50),
    
    -- عدد مرات الوصول
    ACCESS_COUNT NUMBER(10) DEFAULT 0,
    
    -- إصدار السجل (للتحكم في التزامن)
    VERSION_NUMBER NUMBER(10) DEFAULT 1,
    
    -- ملاحظات
    NOTES NVARCHAR2(2000)
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IDX_APP_VAR_NAME ON ERP_APP_VARIABLES(VAR_NAME);
CREATE INDEX IDX_APP_VAR_CATEGORY ON ERP_APP_VARIABLES(VAR_CATEGORY);
CREATE INDEX IDX_APP_VAR_GROUP ON ERP_APP_VARIABLES(VAR_GROUP);
CREATE INDEX IDX_APP_VAR_TYPE ON ERP_APP_VARIABLES(DATA_TYPE);
CREATE INDEX IDX_APP_VAR_ACTIVE ON ERP_APP_VARIABLES(IS_ACTIVE);
CREATE INDEX IDX_APP_VAR_PRIORITY ON ERP_APP_VARIABLES(PRIORITY_LEVEL);

-- إنشاء trigger لتحديث التاريخ والإصدار تلقائياً
CREATE OR REPLACE TRIGGER TRG_APP_VAR_UPDATE
    BEFORE UPDATE ON ERP_APP_VARIABLES
    FOR EACH ROW
BEGIN
    :NEW.LAST_UPDATED := SYSDATE;
    :NEW.UPDATED_BY := USER;
    :NEW.VERSION_NUMBER := :OLD.VERSION_NUMBER + 1;
END;
/

-- إنشاء trigger لتعيين المعرف الفريد تلقائياً
CREATE OR REPLACE TRIGGER TRG_APP_VAR_ID
    BEFORE INSERT ON ERP_APP_VARIABLES
    FOR EACH ROW
    WHEN (NEW.VAR_ID IS NULL)
BEGIN
    :NEW.VAR_ID := ERP_APP_VARIABLES_SEQ.NEXTVAL;
END;
/

-- إنشاء trigger لتتبع الوصول
CREATE OR REPLACE TRIGGER TRG_APP_VAR_ACCESS
    AFTER SELECT ON ERP_APP_VARIABLES
    FOR EACH ROW
BEGIN
    UPDATE ERP_APP_VARIABLES 
    SET LAST_ACCESSED = SYSDATE,
        LAST_ACCESSED_BY = USER,
        ACCESS_COUNT = NVL(ACCESS_COUNT, 0) + 1
    WHERE VAR_ID = :NEW.VAR_ID;
END;
/

-- إدراج المتغيرات الأساسية للنظام
INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
) VALUES (
    'APP_NAME', 'اسم التطبيق', 'Application Name', 'اسم التطبيق الرئيسي',
    'نظام إدارة الشحنات', 'نظام إدارة الشحنات', 'STRING', 'SYSTEM', 'APPLICATION',
    1, 'Y', 1
);

INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
) VALUES (
    'APP_VERSION', 'إصدار التطبيق', 'Application Version', 'رقم إصدار التطبيق الحالي',
    '1.0.0', '1.0.0', 'STRING', 'SYSTEM', 'APPLICATION',
    1, 'Y', 2
);

INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
) VALUES (
    'DEFAULT_LANGUAGE', 'اللغة الافتراضية', 'Default Language', 'اللغة الافتراضية للتطبيق',
    'ar', 'ar', 'STRING', 'UI', 'LOCALIZATION',
    2, 'Y', 3
);

INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER,
    ALLOWED_VALUES
) VALUES (
    'DATE_FORMAT', 'تنسيق التاريخ', 'Date Format', 'تنسيق عرض التاريخ في التطبيق',
    'dd/MM/yyyy', 'dd/MM/yyyy', 'STRING', 'UI', 'FORMATTING',
    2, 'Y', 4,
    'dd/MM/yyyy,MM/dd/yyyy,yyyy-MM-dd,dd-MM-yyyy'
);

INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
) VALUES (
    'SESSION_TIMEOUT', 'انتهاء الجلسة', 'Session Timeout', 'مدة انتهاء الجلسة بالدقائق',
    '30', '30', 'NUMBER', 'SECURITY', 'SESSION',
    1, 'Y', 5
);

INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
) VALUES (
    'MAX_LOGIN_ATTEMPTS', 'محاولات تسجيل الدخول', 'Max Login Attempts', 'عدد محاولات تسجيل الدخول المسموحة',
    '3', '3', 'NUMBER', 'SECURITY', 'AUTHENTICATION',
    1, 'Y', 6
);

INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
) VALUES (
    'BACKUP_ENABLED', 'تفعيل النسخ الاحتياطي', 'Backup Enabled', 'تفعيل النسخ الاحتياطي التلقائي',
    'true', 'true', 'BOOLEAN', 'SYSTEM', 'BACKUP',
    2, 'Y', 7
);

INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
) VALUES (
    'LOG_LEVEL', 'مستوى السجلات', 'Log Level', 'مستوى تفصيل السجلات',
    'INFO', 'INFO', 'STRING', 'SYSTEM', 'LOGGING',
    2, 'Y', 8
);

INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
) VALUES (
    'COMPANY_NAME', 'اسم الشركة', 'Company Name', 'اسم الشركة المستخدمة للنظام',
    'شركة الشحنات المتقدمة', 'شركة الشحنات المتقدمة', 'STRING', 'BUSINESS', 'COMPANY',
    1, 'Y', 9
);

INSERT INTO ERP_APP_VARIABLES (
    VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
    VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
    PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
) VALUES (
    'CURRENCY_CODE', 'رمز العملة', 'Currency Code', 'رمز العملة الافتراضية',
    'SAR', 'SAR', 'STRING', 'BUSINESS', 'FINANCIAL',
    1, 'Y', 10
);

-- حفظ التغييرات
COMMIT;

-- عرض البيانات المدرجة
SELECT 
    VAR_ID,
    VAR_NAME,
    VAR_NAME_AR,
    VAR_VALUE,
    DATA_TYPE,
    VAR_CATEGORY,
    VAR_GROUP,
    PRIORITY_LEVEL
FROM ERP_APP_VARIABLES
ORDER BY VAR_CATEGORY, VAR_GROUP, DISPLAY_ORDER;

-- إنشاء view لعرض المتغيرات بشكل منظم
CREATE OR REPLACE VIEW VW_APP_VARIABLES_ORGANIZED AS
SELECT 
    VAR_ID,
    VAR_NAME,
    VAR_NAME_AR,
    VAR_NAME_EN,
    VAR_DESCRIPTION,
    VAR_VALUE,
    DEFAULT_VALUE,
    DATA_TYPE,
    VAR_CATEGORY,
    VAR_GROUP,
    PRIORITY_LEVEL,
    IS_REQUIRED,
    IS_READONLY,
    IS_ENCRYPTED,
    IS_ACTIVE,
    IS_VISIBLE,
    DISPLAY_ORDER,
    CASE VAR_CATEGORY
        WHEN 'SYSTEM' THEN 'إعدادات النظام'
        WHEN 'UI' THEN 'إعدادات الواجهة'
        WHEN 'DATABASE' THEN 'إعدادات قاعدة البيانات'
        WHEN 'SECURITY' THEN 'إعدادات الأمان'
        WHEN 'BUSINESS' THEN 'إعدادات العمل'
        ELSE VAR_CATEGORY
    END AS CATEGORY_NAME_AR,
    CASE PRIORITY_LEVEL
        WHEN 1 THEN 'عالي'
        WHEN 2 THEN 'متوسط'
        WHEN 3 THEN 'منخفض'
        ELSE 'غير محدد'
    END AS PRIORITY_NAME_AR
FROM ERP_APP_VARIABLES
WHERE IS_ACTIVE = 'Y'
ORDER BY VAR_CATEGORY, VAR_GROUP, DISPLAY_ORDER;

PROMPT 'Application Variables Table created successfully!'
PROMPT 'جدول المتغيرات العامة تم إنشاؤه بنجاح!'
