import java.sql.*;
import java.util.Properties;

/**
 * إنشاء جدول شجرة النظام من Java
 * Create System Tree Table from Java
 */
public class CreateSystemTreeTable {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔄 إنشاء جدول شجرة النظام...");
            
            // الاتصال بقاعدة البيانات
            Connection connection = getConnection();
            
            // إنشاء الجدول
            createSystemTreeTable(connection);
            
            // إدراج البيانات الأساسية
            insertInitialData(connection);
            
            // إنشاء الفهارس
            createIndexes(connection);
            
            // إنشاء View
            createView(connection);
            
            connection.close();
            
            System.out.println("✅ تم إنشاء جدول شجرة النظام بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء جدول شجرة النظام: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    private static void createSystemTreeTable(Connection conn) throws SQLException {
        System.out.println("📊 إنشاء جدول ERP_SYSTEM_TREE...");
        
        // حذف الجدول إذا كان موجوداً
        try {
            conn.createStatement().execute("DROP TABLE ERP_SYSTEM_TREE CASCADE CONSTRAINTS");
            System.out.println("⚠️ تم حذف الجدول الموجود");
        } catch (SQLException e) {
            // تجاهل الخطأ إذا لم يكن الجدول موجوداً
        }
        
        // حذف Sequence إذا كان موجوداً
        try {
            conn.createStatement().execute("DROP SEQUENCE ERP_SYSTEM_TREE_SEQ");
        } catch (SQLException e) {
            // تجاهل الخطأ
        }
        
        // إنشاء Sequence
        String createSeq = "CREATE SEQUENCE ERP_SYSTEM_TREE_SEQ START WITH 1 INCREMENT BY 1";
        conn.createStatement().execute(createSeq);
        
        // إنشاء الجدول
        String createTable = """
            CREATE TABLE ERP_SYSTEM_TREE (
                TREE_ID NUMBER(10) PRIMARY KEY,
                PARENT_ID NUMBER(10),
                NODE_NAME_AR NVARCHAR2(100) NOT NULL,
                NODE_NAME_EN VARCHAR2(100) NOT NULL,
                NODE_DESCRIPTION NVARCHAR2(500),
                NODE_TYPE VARCHAR2(20) NOT NULL,
                WINDOW_CLASS VARCHAR2(200),
                ICON_PATH VARCHAR2(500),
                DISPLAY_ORDER NUMBER(5) DEFAULT 0,
                TREE_LEVEL NUMBER(3) DEFAULT 0,
                IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                IS_VISIBLE CHAR(1) DEFAULT 'Y' CHECK (IS_VISIBLE IN ('Y', 'N')),
                ACCESS_PERMISSIONS VARCHAR2(500),
                ADDITIONAL_INFO CLOB,
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50) DEFAULT USER,
                LAST_UPDATED DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(50) DEFAULT USER,
                VERSION_NUMBER NUMBER(10) DEFAULT 1,
                NOTES NVARCHAR2(1000)
            )
        """;
        
        conn.createStatement().execute(createTable);
        System.out.println("✅ تم إنشاء جدول ERP_SYSTEM_TREE");
        
        // إضافة قيود المرجعية
        String addFK = """
            ALTER TABLE ERP_SYSTEM_TREE 
            ADD CONSTRAINT FK_SYSTEM_TREE_PARENT 
            FOREIGN KEY (PARENT_ID) REFERENCES ERP_SYSTEM_TREE(TREE_ID)
        """;
        conn.createStatement().execute(addFK);
        
        // إنشاء Triggers
        createTriggers(conn);
    }
    
    private static void createTriggers(Connection conn) throws SQLException {
        System.out.println("🔧 إنشاء Triggers...");
        
        // Trigger لتحديث التاريخ والإصدار
        String updateTrigger = """
            CREATE OR REPLACE TRIGGER TRG_SYSTEM_TREE_UPDATE
                BEFORE UPDATE ON ERP_SYSTEM_TREE
                FOR EACH ROW
            BEGIN
                :NEW.LAST_UPDATED := SYSDATE;
                :NEW.UPDATED_BY := USER;
                :NEW.VERSION_NUMBER := :OLD.VERSION_NUMBER + 1;
            END;
        """;
        conn.createStatement().execute(updateTrigger);
        
        // Trigger لتعيين المعرف الفريد
        String idTrigger = """
            CREATE OR REPLACE TRIGGER TRG_SYSTEM_TREE_ID
                BEFORE INSERT ON ERP_SYSTEM_TREE
                FOR EACH ROW
                WHEN (NEW.TREE_ID IS NULL)
            BEGIN
                :NEW.TREE_ID := ERP_SYSTEM_TREE_SEQ.NEXTVAL;
            END;
        """;
        conn.createStatement().execute(idTrigger);
        
        System.out.println("✅ تم إنشاء Triggers");
    }
    
    private static void insertInitialData(Connection conn) throws SQLException {
        System.out.println("📝 إدراج البيانات الأساسية...");
        
        // إدراج العقدة الجذرية
        String insertRoot = """
            INSERT INTO ERP_SYSTEM_TREE (
                TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
                NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL
            ) VALUES (
                1, NULL, 'نظام إدارة الشحنات', 'Ship ERP System', 'النظام الرئيسي لإدارة الشحنات',
                'CATEGORY', 1, 0
            )
        """;
        conn.createStatement().execute(insertRoot);
        
        // إدراج الفئات الرئيسية
        String[] categories = {
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (1, 'إدارة الأصناف', 'Items Management', 'إدارة وتصنيف الأصناف والمنتجات', 'CATEGORY', 1, 1)",
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (1, 'إدارة المستخدمين', 'User Management', 'إدارة المستخدمين والصلاحيات', 'CATEGORY', 2, 1)",
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (1, 'الإعدادات العامة', 'General Settings', 'الإعدادات العامة للنظام', 'CATEGORY', 3, 1)",
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (1, 'التقارير', 'Reports', 'التقارير والإحصائيات', 'CATEGORY', 4, 1)",
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (1, 'أدوات النظام', 'System Tools', 'أدوات مساعدة ومراقبة النظام', 'CATEGORY', 5, 1)"
        };
        
        for (String sql : categories) {
            conn.createStatement().execute(sql);
        }
        
        // إدراج النوافذ
        insertWindows(conn);
        
        conn.commit();
        System.out.println("✅ تم إدراج البيانات الأساسية");
    }
    
    private static void insertWindows(Connection conn) throws SQLException {
        // الحصول على معرفات الفئات
        String getItemsId = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'Items Management'";
        String getUsersId = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'User Management'";
        String getSettingsId = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'General Settings'";
        String getToolsId = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'System Tools'";
        
        ResultSet rs;
        int itemsId = 0, usersId = 0, settingsId = 0, toolsId = 0;
        
        rs = conn.createStatement().executeQuery(getItemsId);
        if (rs.next()) itemsId = rs.getInt(1);
        
        rs = conn.createStatement().executeQuery(getUsersId);
        if (rs.next()) usersId = rs.getInt(1);
        
        rs = conn.createStatement().executeQuery(getSettingsId);
        if (rs.next()) settingsId = rs.getInt(1);
        
        rs = conn.createStatement().executeQuery(getToolsId);
        if (rs.next()) toolsId = rs.getInt(1);
        
        // إدراج نوافذ إدارة الأصناف
        String[] itemWindows = {
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (" + itemsId + ", 'بيانات الأصناف الحقيقية', 'Real Item Data', 'إدارة بيانات الأصناف الحقيقية', 'WINDOW', 'RealItemDataWindow', 1, 2)",
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (" + itemsId + ", 'بيانات الأصناف الشاملة', 'Comprehensive Item Data', 'إدارة بيانات الأصناف الشاملة', 'WINDOW', 'ComprehensiveItemDataWindow', 2, 2)",
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (" + itemsId + ", 'مجموعات الأصناف', 'Item Groups', 'إدارة مجموعات وتصنيفات الأصناف', 'WINDOW', 'ItemGroupsManagementWindow', 3, 2)",
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (" + itemsId + ", 'وحدات القياس', 'Measurement Units', 'إدارة وحدات القياس والتحويلات', 'WINDOW', 'MeasurementUnitsWindow', 4, 2)"
        };
        
        for (String sql : itemWindows) {
            conn.createStatement().execute(sql);
        }
        
        // إدراج نوافذ إدارة المستخدمين
        String userWindow = "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (" + usersId + ", 'إدارة المستخدمين', 'User Management', 'إضافة وتعديل وحذف المستخدمين', 'WINDOW', 'UserManagementWindow', 1, 2)";
        conn.createStatement().execute(userWindow);
        
        // إدراج نوافذ الإعدادات
        String settingsWindow = "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (" + settingsId + ", 'الإعدادات العامة', 'General Settings', 'إعدادات النظام العامة', 'WINDOW', 'GeneralSettingsWindow', 1, 2)";
        conn.createStatement().execute(settingsWindow);
        
        // إدراج أدوات النظام
        String[] toolWindows = {
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (" + toolsId + ", 'فحص النظام الشامل', 'System Audit', 'فحص شامل لحالة النظام', 'TOOL', 'CompleteOracleSystemTest', 1, 2)",
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (" + toolsId + ", 'مراقب الأداء', 'Performance Monitor', 'مراقبة أداء النظام والاتصالات', 'TOOL', 'PerformanceMonitor', 2, 2)",
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (" + toolsId + ", 'إدارة الاتصالات', 'Connection Manager', 'إدارة اتصالات قواعد البيانات', 'TOOL', 'TNSConnectionManager', 3, 2)"
        };
        
        for (String sql : toolWindows) {
            conn.createStatement().execute(sql);
        }
    }
    
    private static void createIndexes(Connection conn) throws SQLException {
        System.out.println("🔍 إنشاء الفهارس...");
        
        String[] indexes = {
            "CREATE INDEX IDX_SYSTEM_TREE_PARENT ON ERP_SYSTEM_TREE(PARENT_ID)",
            "CREATE INDEX IDX_SYSTEM_TREE_TYPE ON ERP_SYSTEM_TREE(NODE_TYPE)",
            "CREATE INDEX IDX_SYSTEM_TREE_ORDER ON ERP_SYSTEM_TREE(DISPLAY_ORDER)",
            "CREATE INDEX IDX_SYSTEM_TREE_LEVEL ON ERP_SYSTEM_TREE(TREE_LEVEL)",
            "CREATE INDEX IDX_SYSTEM_TREE_ACTIVE ON ERP_SYSTEM_TREE(IS_ACTIVE)"
        };
        
        for (String sql : indexes) {
            try {
                conn.createStatement().execute(sql);
            } catch (SQLException e) {
                System.err.println("⚠️ تحذير في إنشاء فهرس: " + e.getMessage());
            }
        }
        
        System.out.println("✅ تم إنشاء الفهارس");
    }
    
    private static void createView(Connection conn) throws SQLException {
        System.out.println("👁️ إنشاء View...");
        
        String createView = """
            CREATE OR REPLACE VIEW VW_SYSTEM_TREE_HIERARCHY AS
            SELECT 
                TREE_ID,
                PARENT_ID,
                NODE_NAME_AR,
                NODE_NAME_EN,
                NODE_DESCRIPTION,
                NODE_TYPE,
                WINDOW_CLASS,
                ICON_PATH,
                DISPLAY_ORDER,
                TREE_LEVEL,
                IS_ACTIVE,
                IS_VISIBLE,
                LPAD(' ', (TREE_LEVEL * 4)) || NODE_NAME_AR AS TREE_DISPLAY,
                SYS_CONNECT_BY_PATH(NODE_NAME_EN, '/') AS FULL_PATH
            FROM ERP_SYSTEM_TREE
            WHERE IS_ACTIVE = 'Y' AND IS_VISIBLE = 'Y'
            START WITH PARENT_ID IS NULL
            CONNECT BY PRIOR TREE_ID = PARENT_ID
            ORDER SIBLINGS BY DISPLAY_ORDER
        """;
        
        conn.createStatement().execute(createView);
        System.out.println("✅ تم إنشاء View");
    }
}
