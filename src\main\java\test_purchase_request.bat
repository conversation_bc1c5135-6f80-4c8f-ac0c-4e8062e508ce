@echo off
echo ========================================
echo تشغيل نافذة طلبات الشراء
echo Testing Purchase Request Window
echo ========================================

echo.
echo تجميع النافذة...
echo Compiling Purchase Request Window...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\PurchaseRequestWindow.java

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في التجميع!
    echo Compilation Error!
    pause
    exit /b 1
)

echo.
echo تم التجميع بنجاح!
echo Compilation Successful!

echo.
echo تشغيل النافذة...
echo Running Purchase Request Window...
java -cp "lib\*;." PurchaseRequestWindow

echo.
echo انتهى التشغيل
echo Execution Completed
pause
