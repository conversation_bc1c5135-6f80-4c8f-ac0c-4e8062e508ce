import java.sql.*;
import java.util.Properties;

/**
 * فحص مفصل لجداول البريد الإلكتروني
 */
public class DetailedTableCheck {
    
    private static final String DB_URL = "*************************************";
    private static final String DB_USER = "ias20251";
    private static final String DB_PASSWORD = "ys123";
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  DETAILED EMAIL TABLES CHECK");
        System.out.println("  فحص مفصل لجداول البريد الإلكتروني");
        System.out.println("========================================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", DB_USER);
            props.setProperty("password", DB_PASSWORD);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            Connection connection = DriverManager.getConnection(DB_URL, props);
            System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح");
            
            // فحص جميع الجداول في المخطط الحالي
            System.out.println("\n📋 جميع الجداول في المخطط الحالي:");
            listAllTables(connection);
            
            // فحص الجداول المرتبطة بالبريد الإلكتروني
            System.out.println("\n📧 البحث عن جداول البريد الإلكتروني:");
            searchEmailTables(connection);
            
            // فحص الأعمدة في الجداول الموجودة
            System.out.println("\n🔍 فحص أعمدة الجداول:");
            checkTableColumns(connection, "EMAIL_ACCOUNTS");
            checkTableColumns(connection, "ERP_EMAIL_ACCOUNTS");
            
            // فحص المتسلسلات
            System.out.println("\n🔢 فحص المتسلسلات:");
            listSequences(connection);
            
            connection.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فحص الجداول: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void listAllTables(Connection connection) {
        try {
            String sql = "SELECT TABLE_NAME FROM USER_TABLES ORDER BY TABLE_NAME";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                int count = 0;
                while (rs.next()) {
                    String tableName = rs.getString("TABLE_NAME");
                    System.out.println("   📋 " + tableName);
                    count++;
                }
                System.out.println("إجمالي الجداول: " + count);
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في قراءة قائمة الجداول: " + e.getMessage());
        }
    }
    
    private static void searchEmailTables(Connection connection) {
        try {
            String sql = "SELECT TABLE_NAME FROM USER_TABLES WHERE TABLE_NAME LIKE '%EMAIL%' ORDER BY TABLE_NAME";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                boolean found = false;
                while (rs.next()) {
                    String tableName = rs.getString("TABLE_NAME");
                    System.out.println("   📧 " + tableName);
                    found = true;
                }
                
                if (!found) {
                    System.out.println("   ⚠️ لم يتم العثور على أي جداول تحتوي على كلمة EMAIL");
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في البحث عن جداول البريد الإلكتروني: " + e.getMessage());
        }
    }
    
    private static void checkTableColumns(Connection connection, String tableName) {
        try {
            String sql = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ? ORDER BY COLUMN_ID";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, tableName.toUpperCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    
                    System.out.println("   📋 أعمدة جدول " + tableName + ":");
                    boolean hasColumns = false;
                    while (rs.next()) {
                        String columnName = rs.getString("COLUMN_NAME");
                        String dataType = rs.getString("DATA_TYPE");
                        String length = rs.getString("DATA_LENGTH");
                        String nullable = rs.getString("NULLABLE");
                        
                        System.out.println("      🔹 " + columnName + " (" + dataType + 
                                         (length != null ? "(" + length + ")" : "") + 
                                         ", " + (nullable.equals("Y") ? "NULL" : "NOT NULL") + ")");
                        hasColumns = true;
                    }
                    
                    if (!hasColumns) {
                        System.out.println("      ⚠️ الجدول " + tableName + " غير موجود");
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في فحص أعمدة جدول " + tableName + ": " + e.getMessage());
        }
    }
    
    private static void listSequences(Connection connection) {
        try {
            String sql = "SELECT SEQUENCE_NAME FROM USER_SEQUENCES WHERE SEQUENCE_NAME LIKE '%EMAIL%' ORDER BY SEQUENCE_NAME";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                boolean found = false;
                while (rs.next()) {
                    String sequenceName = rs.getString("SEQUENCE_NAME");
                    System.out.println("   🔢 " + sequenceName);
                    found = true;
                }
                
                if (!found) {
                    System.out.println("   ⚠️ لم يتم العثور على أي متسلسلات تحتوي على كلمة EMAIL");
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في البحث عن المتسلسلات: " + e.getMessage());
        }
    }
}
