-- ========================================
-- فحص بنية الجدول V_DETAILS من قاعدة IAS20251
-- Check V_DETAILS Table Structure from IAS20251
-- ========================================

-- الاتصال بقاعدة بيانات IAS20251
CONNECT ias20251/ias20251_password@localhost:1521/ORCL;

-- فحص بنية الجدول V_DETAILS
DESCRIBE V_DETAILS;

-- عرض معلومات تفصيلية عن الأعمدة
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    DATA_PRECISION,
    DATA_SCALE,
    NULLABLE,
    DATA_DEFAULT,
    COLUMN_ID
FROM USER_TAB_COLUMNS 
WHERE TABLE_NAME = 'V_DETAILS'
ORDER BY COLUMN_ID;

-- فحص المفاتيح الأساسية
SELECT 
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE,
    COLUMN_NAME,
    POSITION
FROM USER_CONS_COLUMNS ucc
JOIN USER_CONSTRAINTS uc ON ucc.CONSTRAINT_NAME = uc.CONSTRAINT_NAME
WHERE ucc.TABLE_NAME = 'V_DETAILS'
AND uc.CONSTRAINT_TYPE = 'P'
ORDER BY POSITION;

-- فحص الفهارس
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    COLUMN_POSITION,
    DESCEND
FROM USER_IND_COLUMNS
WHERE TABLE_NAME = 'V_DETAILS'
ORDER BY INDEX_NAME, COLUMN_POSITION;

-- فحص القيود (Constraints)
SELECT 
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE,
    SEARCH_CONDITION,
    STATUS
FROM USER_CONSTRAINTS
WHERE TABLE_NAME = 'V_DETAILS';

-- عرض عينة من البيانات لفهم التنسيق
SELECT * FROM V_DETAILS WHERE ROWNUM <= 5;

-- حفظ النتائج في ملف
SPOOL v_details_structure.txt
DESCRIBE V_DETAILS;
SPOOL OFF

EXIT;
