import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTextArea;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;
import javax.swing.UIManager;

/**
 * أداة إصلاح متقدمة لجدول شجرة الأنظمة
 * Advanced System Tree Fixer Tool
 */
public class AdvancedSystemTreeFixer extends JFrame {
    
    private Connection dbConnection;
    private Font arabicFont;
    private JTabbedPane tabbedPane;
    private JTextArea logArea;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    
    // خيارات الإصلاح
    private JCheckBox fixDuplicateNamesCheck;
    private JCheckBox addDescriptionsCheck;
    private JCheckBox addIconsCheck;
    private JCheckBox optimizeStructureCheck;
    private JCheckBox createBackupCheck;
    
    // بيانات المشاكل
    private List<DuplicateNameIssue> duplicateNames;
    private List<MissingDescriptionIssue> missingDescriptions;
    private List<MissingIconIssue> missingIcons;
    
    public AdvancedSystemTreeFixer() {
        initializeDatabase();
        initializeComponents();
        setupLayout();
        loadIssues();
        
        setTitle("🔧 أداة الإصلاح المتقدمة لشجرة الأنظمة - Advanced System Tree Fixer");
        setSize(1200, 800);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تطبيق مظهر حديث
        try {
            FinalThemeManager.initializeDefaultTheme();
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
        }
    }
    
    /**
     * تهيئة الاتصال بقاعدة البيانات
     */
    private void initializeDatabase() {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            dbConnection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات للإصلاح المتقدم");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, 
                "خطأ في الاتصال بقاعدة البيانات:\n" + e.getMessage(),
                "خطأ في الاتصال", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * تهيئة مكونات الواجهة
     */
    private void initializeComponents() {
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        // تهيئة البيانات
        duplicateNames = new ArrayList<>();
        missingDescriptions = new ArrayList<>();
        missingIcons = new ArrayList<>();
        
        // إنشاء التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicFont);
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تبويب خيارات الإصلاح
        createFixOptionsTab();
        
        // تبويب السجلات
        createLogTab();
        
        // تبويب التقرير
        createReportTab();
        
        // شريط التقدم والحالة
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setFont(arabicFont);
        
        statusLabel = new JLabel("جاهز للإصلاح");
        statusLabel.setFont(arabicFont);
        statusLabel.setHorizontalAlignment(SwingConstants.CENTER);
        statusLabel.setBorder(BorderFactory.createEtchedBorder());
    }
    
    /**
     * إنشاء تبويب خيارات الإصلاح
     */
    private void createFixOptionsTab() {
        JPanel optionsPanel = new JPanel(new GridBagLayout());
        optionsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        GridBagConstraints gbc = new GridBagConstraints();
        
        // عنوان
        JLabel titleLabel = new JLabel("🔧 خيارات الإصلاح المتاحة");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2; gbc.insets = new Insets(10, 10, 20, 10);
        optionsPanel.add(titleLabel, gbc);
        
        // خيارات الإصلاح
        fixDuplicateNamesCheck = new JCheckBox("إصلاح الأسماء المكررة (12 مشكلة)", true);
        fixDuplicateNamesCheck.setFont(arabicFont);
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 2; gbc.anchor = GridBagConstraints.EAST;
        optionsPanel.add(fixDuplicateNamesCheck, gbc);
        
        addDescriptionsCheck = new JCheckBox("إضافة الأوصاف المفقودة (13 عقدة)", true);
        addDescriptionsCheck.setFont(arabicFont);
        gbc.gridy = 2;
        optionsPanel.add(addDescriptionsCheck, gbc);
        
        addIconsCheck = new JCheckBox("إضافة الأيقونات المفقودة (90 عقدة)", true);
        addIconsCheck.setFont(arabicFont);
        gbc.gridy = 3;
        optionsPanel.add(addIconsCheck, gbc);
        
        optimizeStructureCheck = new JCheckBox("تحسين هيكل الشجرة والترتيب", true);
        optimizeStructureCheck.setFont(arabicFont);
        gbc.gridy = 4;
        optionsPanel.add(optimizeStructureCheck, gbc);
        
        createBackupCheck = new JCheckBox("إنشاء نسخة احتياطية قبل الإصلاح", true);
        createBackupCheck.setFont(arabicFont);
        gbc.gridy = 5;
        optionsPanel.add(createBackupCheck, gbc);
        
        // أزرار التحكم
        JPanel buttonPanel = new JPanel();
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JButton startFixButton = new JButton("🚀 بدء الإصلاح");
        startFixButton.setFont(arabicFont);
        startFixButton.setPreferredSize(new Dimension(150, 35));
        startFixButton.addActionListener(e -> startFixing());
        
        JButton previewButton = new JButton("👁️ معاينة التغييرات");
        previewButton.setFont(arabicFont);
        previewButton.setPreferredSize(new Dimension(150, 35));
        previewButton.addActionListener(e -> previewChanges());
        
        JButton refreshButton = new JButton("🔄 تحديث البيانات");
        refreshButton.setFont(arabicFont);
        refreshButton.setPreferredSize(new Dimension(150, 35));
        refreshButton.addActionListener(e -> loadIssues());
        
        buttonPanel.add(startFixButton);
        buttonPanel.add(previewButton);
        buttonPanel.add(refreshButton);
        
        gbc.gridx = 0; gbc.gridy = 6; gbc.gridwidth = 2; gbc.insets = new Insets(20, 10, 10, 10);
        optionsPanel.add(buttonPanel, gbc);
        
        tabbedPane.addTab("🔧 خيارات الإصلاح", optionsPanel);
    }
    
    /**
     * إنشاء تبويب السجلات
     */
    private void createLogTab() {
        logArea = new JTextArea();
        logArea.setFont(new Font("Courier New", Font.PLAIN, 12));
        logArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        logArea.setEditable(false);
        logArea.setBackground(new Color(248, 249, 250));
        
        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        tabbedPane.addTab("📋 سجل العمليات", scrollPane);
    }
    
    /**
     * إنشاء تبويب التقرير
     */
    private void createReportTab() {
        JTextArea reportArea = new JTextArea();
        reportArea.setFont(new Font("Courier New", Font.PLAIN, 12));
        reportArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        reportArea.setEditable(false);
        
        JScrollPane scrollPane = new JScrollPane(reportArea);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تحديث التقرير
        updateReport(reportArea);
        
        tabbedPane.addTab("📊 تقرير المشاكل", scrollPane);
    }
    
    /**
     * إعداد تخطيط الواجهة
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);
        
        // لوحة سفلية للتقدم والحالة
        JPanel bottomPanel = new JPanel(new BorderLayout());
        bottomPanel.add(progressBar, BorderLayout.CENTER);
        bottomPanel.add(statusLabel, BorderLayout.SOUTH);
        
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    /**
     * تحميل المشاكل من قاعدة البيانات
     */
    private void loadIssues() {
        SwingUtilities.invokeLater(() -> {
            statusLabel.setText("جاري تحميل المشاكل...");
            
            try {
                loadDuplicateNames();
                loadMissingDescriptions();
                loadMissingIcons();
                
                statusLabel.setText("تم تحميل المشاكل - جاهز للإصلاح");
                logMessage("✅ تم تحميل جميع المشاكل بنجاح");
                
            } catch (Exception e) {
                statusLabel.setText("خطأ في تحميل المشاكل");
                logMessage("❌ خطأ في تحميل المشاكل: " + e.getMessage());
            }
        });
    }
    
    /**
     * تحميل الأسماء المكررة
     */
    private void loadDuplicateNames() throws SQLException {
        duplicateNames.clear();
        
        String sql = """
            SELECT NODE_NAME_AR, COUNT(*) as COUNT
            FROM ERP_SYSTEM_TREE
            GROUP BY NODE_NAME_AR
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                DuplicateNameIssue issue = new DuplicateNameIssue();
                issue.nodeName = rs.getString("NODE_NAME_AR");
                issue.count = rs.getInt("COUNT");
                issue.nodeIds = getDuplicateNodeIds(issue.nodeName);
                duplicateNames.add(issue);
            }
        }
        
        logMessage("📋 تم تحميل " + duplicateNames.size() + " اسم مكرر");
    }
    
    /**
     * الحصول على معرفات العقد المكررة
     */
    private List<Integer> getDuplicateNodeIds(String nodeName) throws SQLException {
        List<Integer> nodeIds = new ArrayList<>();
        
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? ORDER BY TREE_ID";
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            stmt.setString(1, nodeName);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    nodeIds.add(rs.getInt("TREE_ID"));
                }
            }
        }
        
        return nodeIds;
    }
    
    /**
     * تحميل العقد بدون أوصاف
     */
    private void loadMissingDescriptions() throws SQLException {
        missingDescriptions.clear();
        
        String sql = """
            SELECT TREE_ID, NODE_NAME_AR, NODE_TYPE
            FROM ERP_SYSTEM_TREE
            WHERE NODE_DESCRIPTION IS NULL OR TRIM(NODE_DESCRIPTION) = ''
            ORDER BY NODE_TYPE, TREE_ID
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                MissingDescriptionIssue issue = new MissingDescriptionIssue();
                issue.treeId = rs.getInt("TREE_ID");
                issue.nodeName = rs.getString("NODE_NAME_AR");
                issue.nodeType = rs.getString("NODE_TYPE");
                issue.suggestedDescription = generateDescription(issue.nodeName, issue.nodeType);
                missingDescriptions.add(issue);
            }
        }
        
        logMessage("📝 تم تحميل " + missingDescriptions.size() + " عقدة بدون وصف");
    }
    
    /**
     * تحميل العقد بدون أيقونات
     */
    private void loadMissingIcons() throws SQLException {
        missingIcons.clear();
        
        String sql = """
            SELECT TREE_ID, NODE_NAME_AR, NODE_TYPE
            FROM ERP_SYSTEM_TREE
            WHERE ICON_PATH IS NULL OR TRIM(ICON_PATH) = ''
            ORDER BY NODE_TYPE, TREE_ID
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                MissingIconIssue issue = new MissingIconIssue();
                issue.treeId = rs.getInt("TREE_ID");
                issue.nodeName = rs.getString("NODE_NAME_AR");
                issue.nodeType = rs.getString("NODE_TYPE");
                issue.suggestedIcon = generateIconPath(issue.nodeType);
                missingIcons.add(issue);
            }
        }
        
        logMessage("🎨 تم تحميل " + missingIcons.size() + " عقدة بدون أيقونة");
    }

    /**
     * إنشاء وصف مقترح للعقدة
     */
    private String generateDescription(String nodeName, String nodeType) {
        if (nodeType == null) return "وصف العقدة";

        return switch (nodeType) {
            case "CATEGORY" -> "فئة " + nodeName + " - تحتوي على النوافذ والأدوات المتعلقة بـ" + nodeName;
            case "WINDOW" -> "نافذة " + nodeName + " - واجهة لإدارة وعرض بيانات " + nodeName;
            case "TOOL" -> "أداة " + nodeName + " - أداة مساعدة لتنفيذ مهام " + nodeName;
            case "REPORT" -> "تقرير " + nodeName + " - تقرير مفصل حول " + nodeName;
            default -> "عقدة " + nodeName + " من نوع " + nodeType;
        };
    }

    /**
     * إنشاء مسار أيقونة مقترح
     */
    private String generateIconPath(String nodeType) {
        if (nodeType == null) return "icons/default.png";

        return switch (nodeType) {
            case "CATEGORY" -> "icons/folder.png";
            case "WINDOW" -> "icons/window.png";
            case "TOOL" -> "icons/tool.png";
            case "REPORT" -> "icons/report.png";
            default -> "icons/default.png";
        };
    }

    /**
     * معاينة التغييرات
     */
    private void previewChanges() {
        StringBuilder preview = new StringBuilder();
        preview.append("📋 معاينة التغييرات المقترحة:\n");
        preview.append("=====================================\n\n");

        if (fixDuplicateNamesCheck.isSelected()) {
            preview.append("🔄 إصلاح الأسماء المكررة:\n");
            for (DuplicateNameIssue issue : duplicateNames) {
                preview.append("• ").append(issue.nodeName).append(" (").append(issue.count).append(" مرات)\n");
                for (int i = 1; i < issue.nodeIds.size(); i++) {
                    preview.append("  └─ سيتم تغيير العقدة ").append(issue.nodeIds.get(i))
                           .append(" إلى: ").append(issue.nodeName).append(" (").append(i + 1).append(")\n");
                }
            }
            preview.append("\n");
        }

        if (addDescriptionsCheck.isSelected()) {
            preview.append("📝 إضافة الأوصاف:\n");
            for (int i = 0; i < Math.min(5, missingDescriptions.size()); i++) {
                MissingDescriptionIssue issue = missingDescriptions.get(i);
                preview.append("• [").append(issue.treeId).append("] ").append(issue.nodeName)
                       .append("\n  الوصف: ").append(issue.suggestedDescription).append("\n");
            }
            if (missingDescriptions.size() > 5) {
                preview.append("... و ").append(missingDescriptions.size() - 5).append(" عقدة أخرى\n");
            }
            preview.append("\n");
        }

        if (addIconsCheck.isSelected()) {
            preview.append("🎨 إضافة الأيقونات:\n");
            Map<String, Integer> iconCounts = new HashMap<>();
            for (MissingIconIssue issue : missingIcons) {
                iconCounts.put(issue.suggestedIcon, iconCounts.getOrDefault(issue.suggestedIcon, 0) + 1);
            }
            for (Map.Entry<String, Integer> entry : iconCounts.entrySet()) {
                preview.append("• ").append(entry.getKey()).append(": ").append(entry.getValue()).append(" عقدة\n");
            }
            preview.append("\n");
        }

        JTextArea previewArea = new JTextArea(preview.toString());
        previewArea.setFont(new Font("Courier New", Font.PLAIN, 12));
        previewArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        previewArea.setEditable(false);

        JScrollPane scrollPane = new JScrollPane(previewArea);
        scrollPane.setPreferredSize(new Dimension(600, 400));

        JOptionPane.showMessageDialog(this, scrollPane, "معاينة التغييرات", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * بدء عملية الإصلاح
     */
    private void startFixing() {
        int result = JOptionPane.showConfirmDialog(this,
            "هل أنت متأكد من بدء عملية الإصلاح؟\n" +
            "سيتم تطبيق جميع التغييرات المحددة على قاعدة البيانات.",
            "تأكيد الإصلاح", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // تشغيل الإصلاح في خيط منفصل
            SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
                @Override
                protected Void doInBackground() throws Exception {
                    performFixes();
                    return null;
                }

                @Override
                protected void process(List<String> chunks) {
                    for (String message : chunks) {
                        logMessage(message);
                    }
                }

                @Override
                protected void done() {
                    try {
                        get();
                        statusLabel.setText("تم إكمال الإصلاح بنجاح");
                        progressBar.setValue(100);
                        JOptionPane.showMessageDialog(AdvancedSystemTreeFixer.this,
                            "تم إكمال عملية الإصلاح بنجاح!\n" +
                            "يُنصح بإعادة تشغيل النظام لرؤية التغييرات.",
                            "إصلاح مكتمل", JOptionPane.INFORMATION_MESSAGE);
                    } catch (Exception e) {
                        statusLabel.setText("خطأ في الإصلاح");
                        logMessage("❌ خطأ في الإصلاح: " + e.getMessage());
                        JOptionPane.showMessageDialog(AdvancedSystemTreeFixer.this,
                            "حدث خطأ أثناء الإصلاح:\n" + e.getMessage(),
                            "خطأ في الإصلاح", JOptionPane.ERROR_MESSAGE);
                    }
                }
            };

            worker.execute();
        }
    }

    /**
     * تنفيذ عمليات الإصلاح
     */
    private void performFixes() throws SQLException {
        progressBar.setValue(0);
        int totalSteps = 0;
        int currentStep = 0;

        // حساب عدد الخطوات
        if (createBackupCheck.isSelected()) totalSteps++;
        if (fixDuplicateNamesCheck.isSelected()) totalSteps++;
        if (addDescriptionsCheck.isSelected()) totalSteps++;
        if (addIconsCheck.isSelected()) totalSteps++;
        if (optimizeStructureCheck.isSelected()) totalSteps++;

        // إنشاء نسخة احتياطية
        if (createBackupCheck.isSelected()) {
            statusLabel.setText("إنشاء نسخة احتياطية...");
            createBackup();
            currentStep++;
            progressBar.setValue((currentStep * 100) / totalSteps);
        }

        // إصلاح الأسماء المكررة
        if (fixDuplicateNamesCheck.isSelected()) {
            statusLabel.setText("إصلاح الأسماء المكررة...");
            fixDuplicateNames();
            currentStep++;
            progressBar.setValue((currentStep * 100) / totalSteps);
        }

        // إضافة الأوصاف
        if (addDescriptionsCheck.isSelected()) {
            statusLabel.setText("إضافة الأوصاف المفقودة...");
            addMissingDescriptions();
            currentStep++;
            progressBar.setValue((currentStep * 100) / totalSteps);
        }

        // إضافة الأيقونات
        if (addIconsCheck.isSelected()) {
            statusLabel.setText("إضافة الأيقونات المفقودة...");
            addMissingIcons();
            currentStep++;
            progressBar.setValue((currentStep * 100) / totalSteps);
        }

        // تحسين الهيكل
        if (optimizeStructureCheck.isSelected()) {
            statusLabel.setText("تحسين هيكل الشجرة...");
            optimizeTreeStructure();
            currentStep++;
            progressBar.setValue((currentStep * 100) / totalSteps);
        }

        // تأكيد التغييرات
        dbConnection.commit();
        logMessage("✅ تم تأكيد جميع التغييرات في قاعدة البيانات");
    }

    /**
     * إنشاء نسخة احتياطية
     */
    private void createBackup() throws SQLException {
        String backupTableName = "ERP_SYSTEM_TREE_BACKUP_" +
            new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());

        String sql = "CREATE TABLE " + backupTableName + " AS SELECT * FROM ERP_SYSTEM_TREE";
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            stmt.executeUpdate();
            logMessage("✅ تم إنشاء نسخة احتياطية: " + backupTableName);
        }
    }

    /**
     * إصلاح الأسماء المكررة
     */
    private void fixDuplicateNames() throws SQLException {
        int fixedCount = 0;

        for (DuplicateNameIssue issue : duplicateNames) {
            // ترك العقدة الأولى كما هي وتغيير الباقي
            for (int i = 1; i < issue.nodeIds.size(); i++) {
                String newName = issue.nodeName + " (" + (i + 1) + ")";

                String sql = "UPDATE ERP_SYSTEM_TREE SET NODE_NAME_AR = ? WHERE TREE_ID = ?";
                try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
                    stmt.setString(1, newName);
                    stmt.setInt(2, issue.nodeIds.get(i));
                    stmt.executeUpdate();

                    logMessage("🔄 تم تغيير اسم العقدة " + issue.nodeIds.get(i) + " إلى: " + newName);
                    fixedCount++;
                }
            }
        }

        logMessage("✅ تم إصلاح " + fixedCount + " اسم مكرر");
    }

    /**
     * إضافة الأوصاف المفقودة
     */
    private void addMissingDescriptions() throws SQLException {
        int addedCount = 0;

        String sql = "UPDATE ERP_SYSTEM_TREE SET NODE_DESCRIPTION = ? WHERE TREE_ID = ?";
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            for (MissingDescriptionIssue issue : missingDescriptions) {
                stmt.setString(1, issue.suggestedDescription);
                stmt.setInt(2, issue.treeId);
                stmt.executeUpdate();

                logMessage("📝 تم إضافة وصف للعقدة " + issue.treeId + ": " + issue.nodeName);
                addedCount++;
            }
        }

        logMessage("✅ تم إضافة " + addedCount + " وصف");
    }

    /**
     * إضافة الأيقونات المفقودة
     */
    private void addMissingIcons() throws SQLException {
        int addedCount = 0;

        String sql = "UPDATE ERP_SYSTEM_TREE SET ICON_PATH = ? WHERE TREE_ID = ?";
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            for (MissingIconIssue issue : missingIcons) {
                stmt.setString(1, issue.suggestedIcon);
                stmt.setInt(2, issue.treeId);
                stmt.executeUpdate();

                logMessage("🎨 تم إضافة أيقونة للعقدة " + issue.treeId + ": " + issue.suggestedIcon);
                addedCount++;
            }
        }

        logMessage("✅ تم إضافة " + addedCount + " أيقونة");
    }

    /**
     * تحسين هيكل الشجرة
     */
    private void optimizeTreeStructure() throws SQLException {
        // إعادة ترقيم ترتيب العرض
        reorderDisplayOrder();

        // تحديث مستويات الشجرة
        updateTreeLevels();

        // تحسين الأداء
        updateStatistics();

        logMessage("✅ تم تحسين هيكل الشجرة");
    }

    /**
     * إعادة ترقيم ترتيب العرض
     */
    private void reorderDisplayOrder() throws SQLException {
        String sql = """
            UPDATE ERP_SYSTEM_TREE SET DISPLAY_ORDER = (
                SELECT ROW_NUMBER() OVER (
                    PARTITION BY NVL(PARENT_ID, 0)
                    ORDER BY DISPLAY_ORDER, TREE_ID
                ) * 10
                FROM ERP_SYSTEM_TREE t2
                WHERE t2.TREE_ID = ERP_SYSTEM_TREE.TREE_ID
            )
        """;

        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            int updated = stmt.executeUpdate();
            logMessage("🔢 تم إعادة ترقيم " + updated + " عقدة");
        }
    }

    /**
     * تحديث مستويات الشجرة
     */
    private void updateTreeLevels() throws SQLException {
        String sql = """
            UPDATE ERP_SYSTEM_TREE SET TREE_LEVEL = (
                SELECT LEVEL - 1
                FROM ERP_SYSTEM_TREE t2
                WHERE t2.TREE_ID = ERP_SYSTEM_TREE.TREE_ID
                START WITH t2.PARENT_ID IS NULL
                CONNECT BY PRIOR t2.TREE_ID = t2.PARENT_ID
            )
        """;

        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            int updated = stmt.executeUpdate();
            logMessage("📏 تم تحديث مستويات " + updated + " عقدة");
        }
    }

    /**
     * تحديث الإحصائيات
     */
    private void updateStatistics() throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET LAST_UPDATED = SYSDATE, UPDATED_BY = 'SYSTEM_FIXER'";
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            stmt.executeUpdate();
            logMessage("📊 تم تحديث الإحصائيات");
        }
    }

    /**
     * تحديث التقرير
     */
    private void updateReport(JTextArea reportArea) {
        StringBuilder report = new StringBuilder();
        report.append("📊 تقرير المشاكل المكتشفة\n");
        report.append("========================\n\n");

        report.append("🔄 الأسماء المكررة: ").append(duplicateNames.size()).append(" مشكلة\n");
        report.append("📝 العقد بدون أوصاف: ").append(missingDescriptions.size()).append(" عقدة\n");
        report.append("🎨 العقد بدون أيقونات: ").append(missingIcons.size()).append(" عقدة\n\n");

        report.append("تفاصيل الأسماء المكررة:\n");
        report.append("======================\n");
        for (DuplicateNameIssue issue : duplicateNames) {
            report.append("• ").append(issue.nodeName).append(" (").append(issue.count).append(" مرات)\n");
        }

        reportArea.setText(report.toString());
    }

    /**
     * إضافة رسالة إلى السجل
     */
    private void logMessage(String message) {
        SwingUtilities.invokeLater(() -> {
            String timestamp = new SimpleDateFormat("HH:mm:ss").format(new Date());
            logArea.append("[" + timestamp + "] " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
    }

    /**
     * إغلاق الاتصال بقاعدة البيانات
     */
    private void closeConnection() {
        try {
            if (dbConnection != null && !dbConnection.isClosed()) {
                dbConnection.close();
                System.out.println("🔌 تم إغلاق الاتصال بقاعدة البيانات");
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
        }
    }

    // ===== الكلاسات المساعدة =====

    /**
     * كلاس مشكلة الاسم المكرر
     */
    public static class DuplicateNameIssue {
        public String nodeName;
        public int count;
        public List<Integer> nodeIds;
    }

    /**
     * كلاس مشكلة الوصف المفقود
     */
    public static class MissingDescriptionIssue {
        public int treeId;
        public String nodeName;
        public String nodeType;
        public String suggestedDescription;
    }

    /**
     * كلاس مشكلة الأيقونة المفقودة
     */
    public static class MissingIconIssue {
        public int treeId;
        public String nodeName;
        public String nodeType;
        public String suggestedIcon;
    }

    /**
     * نقطة الدخول الرئيسية
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                FinalThemeManager.initializeDefaultTheme();
            } catch (Exception e) {
                System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
            }

            System.out.println("🔧 تشغيل أداة الإصلاح المتقدمة لشجرة الأنظمة");
            AdvancedSystemTreeFixer fixer = new AdvancedSystemTreeFixer();
            fixer.setVisible(true);

            // إضافة مستمع لإغلاق النافذة
            fixer.addWindowListener(new java.awt.event.WindowAdapter() {
                @Override
                public void windowClosing(java.awt.event.WindowEvent windowEvent) {
                    fixer.closeConnection();
                    System.exit(0);
                }
            });
        });
    }
}
