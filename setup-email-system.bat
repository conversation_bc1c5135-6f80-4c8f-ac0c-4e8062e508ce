@echo off
echo ========================================
echo   Setup Email Management System
echo   إعداد نظام إدارة البريد الإلكتروني
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Creating Email System Database Tables...
echo إنشاء جداول نظام البريد الإلكتروني...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CreateEmailSystemTables.java
if %errorlevel% neq 0 (
    echo Failed to compile CreateEmailSystemTables
    pause
    exit /b 1
)

java -cp "lib\*;." CreateEmailSystemTables
if %errorlevel% neq 0 (
    echo Failed to create email system tables
    pause
    exit /b 1
)

echo.
echo [2] Adding Email System to System Tree...
echo إضافة نظام البريد الإلكتروني إلى شجرة الأنظمة...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\AddEmailSystemToTree.java
if %errorlevel% neq 0 (
    echo Failed to compile AddEmailSystemToTree
    pause
    exit /b 1
)

java -cp "lib\*;." AddEmailSystemToTree
if %errorlevel% neq 0 (
    echo Failed to add email system to tree
    pause
    exit /b 1
)

echo.
echo [3] Compiling Email Windows...
echo تجميع نوافذ البريد الإلكتروني...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\EmailAccountsWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile EmailAccountsWindow
    pause
    exit /b 1
)

echo.
echo [4] Updating TreeMenuPanel...
echo تحديث لوحة القائمة الرئيسية...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo Failed to compile TreeMenuPanel
    pause
    exit /b 1
)

echo.
echo [5] Testing Email Accounts Window...
echo اختبار نافذة حسابات البريد الإلكتروني...
start /min java -cp "lib\*;." EmailAccountsWindow

echo.
echo [6] Starting Main System...
echo تشغيل النظام الرئيسي...
start .\start-ship-erp.bat

echo.
echo ========================================
echo   Email System Setup Complete!
echo   تم إكمال إعداد نظام البريد الإلكتروني!
echo ========================================
echo.
echo SYSTEM FEATURES:
echo ✅ 10 Database Tables Created
echo ✅ Email System Category Added to Tree
echo ✅ 11 Email Windows Added to System
echo ✅ Full Arabic Language Support
echo ✅ Internet-Connected Email Management
echo.
echo DATABASE TABLES:
echo - ERP_EMAIL_ACCOUNTS (Email Accounts)
echo - ERP_EMAIL_TEMPLATES (Email Templates)
echo - ERP_EMAIL_CAMPAIGNS (Email Campaigns)
echo - ERP_EMAIL_MESSAGES (Email Messages)
echo - ERP_EMAIL_ATTACHMENTS (Email Attachments)
echo - ERP_EMAIL_RECIPIENTS (Email Recipients)
echo - ERP_EMAIL_LOGS (Email Logs)
echo - ERP_EMAIL_SETTINGS (Email Settings)
echo - ERP_EMAIL_FILTERS (Email Filters)
echo - ERP_EMAIL_SIGNATURES (Email Signatures)
echo.
echo SYSTEM WINDOWS:
echo 1. إدارة حسابات البريد - Email Accounts Management
echo 2. صندوق الوارد - Inbox
echo 3. إنشاء رسالة جديدة - Compose New Message
echo 4. إدارة القوالب - Templates Management
echo 5. إدارة الحملات - Campaigns Management
echo 6. دفتر العناوين - Address Book
echo 7. المرشحات والقواعد - Filters ^& Rules
echo 8. التوقيعات - Signatures
echo 9. الإحصائيات والتقارير - Statistics ^& Reports
echo 10. سجلات النظام - System Logs
echo 11. إعدادات النظام - System Settings
echo.
echo ACCESS INSTRUCTIONS:
echo 1. Open main system (already started)
echo 2. Navigate to: نظام إدارة البريد الإلكتروني
echo 3. Click on any email management window
echo 4. All windows support full Arabic orientation
echo.
echo NEXT STEPS:
echo - Download required email libraries (JavaMail, etc.)
echo - Configure email accounts
echo - Set up email templates
echo - Test email sending/receiving
echo.
pause
