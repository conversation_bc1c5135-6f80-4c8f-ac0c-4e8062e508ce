package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.text.SimpleDateFormat;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSeparator;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumnModel;
import com.shipment.erp.model.Branch;
import com.shipment.erp.model.Company;

/**
 * نافذة إدارة الفروع Branch Management Window
 */
public class BranchManagementWindow extends JDialog {

    private Font arabicFont;
    private JTable branchesTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> statusFilterCombo;
    private JComboBox<Company> companyFilterCombo;

    // private BranchService branchService; // TODO: Implement BranchService
    // private CompanyService companyService; // TODO: Implement CompanyService
    private List<Branch> branchesList;
    private List<Company> companiesList;

    public BranchManagementWindow(JFrame parent) {
        super(parent, "إدارة الفروع", true);

        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeServices();
        initializeComponents();
        setupLayout();
        loadData();

        setSize(1400, 800);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(true);
        setMinimumSize(new Dimension(1200, 600));
    }

    private void initializeServices() {
        // TODO: Initialize services through dependency injection
        // branchService = ApplicationContext.getBean(BranchService.class);
        // companyService = ApplicationContext.getBean(CompanyService.class);
    }

    private void initializeComponents() {
        // شريط الأدوات العلوي
        JPanel toolbarPanel = createToolbarPanel();

        // جدول الفروع
        createBranchesTable();
        JScrollPane tableScrollPane = new JScrollPane(branchesTable);
        tableScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();

        // تخطيط النافذة
        setLayout(new BorderLayout());
        add(toolbarPanel, BorderLayout.NORTH);
        add(tableScrollPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }

    private JPanel createToolbarPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // أزرار العمليات
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addButton = new JButton("إضافة فرع جديد");
        addButton.setFont(arabicFont);
        addButton.addActionListener(e -> addBranch());

        JButton editButton = new JButton("تعديل");
        editButton.setFont(arabicFont);
        editButton.addActionListener(e -> editBranch());

        JButton deleteButton = new JButton("حذف");
        deleteButton.setFont(arabicFont);
        deleteButton.addActionListener(e -> deleteBranch());

        JButton refreshButton = new JButton("تحديث");
        refreshButton.setFont(arabicFont);
        refreshButton.addActionListener(e -> loadData());

        buttonsPanel.add(addButton);
        buttonsPanel.add(editButton);
        buttonsPanel.add(deleteButton);
        buttonsPanel.add(new JSeparator(SwingConstants.VERTICAL));
        buttonsPanel.add(refreshButton);

        // شريط البحث والفلترة
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);

        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                filterBranches();
            }
        });

        JLabel companyLabel = new JLabel("الشركة:");
        companyLabel.setFont(arabicFont);

        companyFilterCombo = new JComboBox<>();
        companyFilterCombo.setFont(arabicFont);
        companyFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        companyFilterCombo.addActionListener(e -> filterBranches());

        JLabel statusLabel = new JLabel("الحالة:");
        statusLabel.setFont(arabicFont);

        statusFilterCombo = new JComboBox<>(new String[] {"الكل", "نشط", "غير نشط"});
        statusFilterCombo.setFont(arabicFont);
        statusFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusFilterCombo.addActionListener(e -> filterBranches());

        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        searchPanel.add(Box.createHorizontalStrut(10));
        searchPanel.add(companyLabel);
        searchPanel.add(companyFilterCombo);
        searchPanel.add(Box.createHorizontalStrut(10));
        searchPanel.add(statusLabel);
        searchPanel.add(statusFilterCombo);

        panel.add(buttonsPanel, BorderLayout.EAST);
        panel.add(searchPanel, BorderLayout.WEST);

        return panel;
    }

    private void createBranchesTable() {
        String[] columnNames = {"الرقم", "الكود", "اسم الفرع", "الشركة", "المدير", "الهاتف",
                "البريد الإلكتروني", "العنوان", "الحالة", "تاريخ الإنشاء"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        branchesTable = new JTable(tableModel);
        branchesTable.setFont(arabicFont);
        branchesTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        branchesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        branchesTable.setRowHeight(25);

        // تخصيص عرض الأعمدة
        TableColumnModel columnModel = branchesTable.getColumnModel();
        columnModel.getColumn(0).setPreferredWidth(60); // الرقم
        columnModel.getColumn(1).setPreferredWidth(80); // الكود
        columnModel.getColumn(2).setPreferredWidth(150); // اسم الفرع
        columnModel.getColumn(3).setPreferredWidth(150); // الشركة
        columnModel.getColumn(4).setPreferredWidth(120); // المدير
        columnModel.getColumn(5).setPreferredWidth(120); // الهاتف
        columnModel.getColumn(6).setPreferredWidth(150); // البريد الإلكتروني
        columnModel.getColumn(7).setPreferredWidth(200); // العنوان
        columnModel.getColumn(8).setPreferredWidth(80); // الحالة
        columnModel.getColumn(9).setPreferredWidth(120); // تاريخ الإنشاء

        // تخصيص رأس الجدول
        JTableHeader header = branchesTable.getTableHeader();
        header.setFont(arabicFont);
        header.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إضافة مستمع النقر المزدوج
        branchesTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    editBranch();
                }
            }
        });
    }

    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);

        panel.add(statusLabel);
        return panel;
    }

    private void setupLayout() {
        // تم تنفيذه في initializeComponents
    }

    private void loadData() {
        loadCompanies();
        loadBranches();
    }

    private void loadCompanies() {
        // TODO: Load from CompanyService
        // companiesList = companyService.findActiveCompanies();

        // بيانات تجريبية
        companiesList = createSampleCompanies();

        companyFilterCombo.removeAllItems();
        companyFilterCombo.addItem(null); // خيار "الكل"
        for (Company company : companiesList) {
            companyFilterCombo.addItem(company);
        }
    }

    private void loadBranches() {
        // TODO: Load from BranchService
        // branchesList = branchService.findAll();

        // بيانات تجريبية
        branchesList = createSampleBranches();
        updateTableData();
    }

    private List<Company> createSampleCompanies() {
        Company company1 = new Company();
        company1.setId(1L);
        company1.setName("شركة الشحن المتحدة");

        Company company2 = new Company();
        company2.setId(2L);
        company2.setName("شركة النقل السريع");

        return java.util.Arrays.asList(company1, company2);
    }

    private List<Branch> createSampleBranches() {
        // إنشاء شركة وهمية
        Company sampleCompany = new Company();
        sampleCompany.setId(1L);
        sampleCompany.setName("شركة الشحن المتقدم");

        Branch branch1 = new Branch();
        branch1.setId(1L);
        branch1.setCode("MAIN");
        branch1.setName("الفرع الرئيسي");
        branch1.setCompany(sampleCompany);
        branch1.setManagerName("أحمد محمد");
        branch1.setPhone("966-11-1234567");
        branch1.setEmail("<EMAIL>");
        branch1.setAddress("الرياض، المملكة العربية السعودية");
        branch1.setIsActive(true);
        branch1.setCreatedDate(java.time.LocalDateTime.now());

        Branch branch2 = new Branch();
        branch2.setId(2L);
        branch2.setCode("JED");
        branch2.setName("فرع جدة");
        branch2.setCompany(sampleCompany);
        branch2.setManagerName("محمد أحمد");
        branch2.setPhone("966-12-7654321");
        branch2.setEmail("<EMAIL>");
        branch2.setAddress("جدة، المملكة العربية السعودية");
        branch2.setIsActive(true);
        branch2.setCreatedDate(java.time.LocalDateTime.now());

        return java.util.Arrays.asList(branch1, branch2);
    }

    private void updateTableData() {
        tableModel.setRowCount(0);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (Branch branch : branchesList) {
            String companyName =
                    branch.getCompany() != null ? branch.getCompany().getName() : "غير محدد";

            Object[] row = {branch.getId(), branch.getCode(), branch.getName(), companyName,
                    branch.getManagerName(), branch.getPhone(), branch.getEmail(),
                    branch.getAddress(), branch.getIsActive() ? "نشط" : "غير نشط",
                    branch.getCreatedDate() != null ? dateFormat.format(branch.getCreatedDate())
                            : ""};
            tableModel.addRow(row);
        }
    }

    // طريقة محذوفة - لم تعد مطلوبة

    private void filterBranches() {
        // TODO: Implement filtering logic
        updateTableData();
    }

    private void addBranch() {
        BranchFormDialog dialog = new BranchFormDialog(this, "إضافة فرع جديد", null, companiesList);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            Branch newBranch = dialog.getBranch();
            // TODO: Save using BranchService
            // branchService.save(newBranch);
            loadBranches();
        }
    }

    private void editBranch() {
        int selectedRow = branchesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار فرع للتعديل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Branch selectedBranch = branchesList.get(selectedRow);
        BranchFormDialog dialog =
                new BranchFormDialog(this, "تعديل الفرع", selectedBranch, companiesList);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            Branch updatedBranch = dialog.getBranch();
            // TODO: Update using BranchService
            // branchService.save(updatedBranch);
            loadBranches();
        }
    }

    private void deleteBranch() {
        int selectedRow = branchesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار فرع للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Branch selectedBranch = branchesList.get(selectedRow);
        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف الفرع: " + selectedBranch.getName() + "؟", "تأكيد الحذف",
                JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Delete using BranchService
            // branchService.delete(selectedBranch.getId());
            loadBranches();
        }
    }
}
