import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.GridLayout;
import java.util.Locale;
import javax.swing.BorderFactory;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JWindow;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.Timer;

/**
 * النظام الكامل المحسن مع دعم Oracle Complete Enhanced System with Oracle Support
 */
public class CompleteOracleSystemTest {

    public static void main(String[] args) {
        // تعيين الترميز والمحلية العربية
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("oracle.jdbc.defaultNChar", "true");
        Locale.setDefault(new Locale("ar", "SA"));

        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق المظهر المحفوظ
                SettingsManager.applyStoredTheme();

                // عرض شاشة البداية
                showSplashScreen();

                // إنشاء النافذة الرئيسية المحسنة
                EnhancedMainWindow mainWindow = new EnhancedMainWindow();
                mainWindow.setVisible(true);

                System.out.println("=== تم تشغيل النظام الكامل المحسن مع Oracle بنجاح! ===");
                System.out.println("الميزات المتاحة:");
                System.out.println("• النافذة الرئيسية المحسنة مع القائمة الشجرية");
                System.out.println("• نظام إدارة الأصناف الشامل");
                System.out.println("• نافذة الأصناف الشاملة المتقدمة");
                System.out.println("• نافذة الأصناف الحقيقية");
                System.out.println("• إدارة مجموعات الأصناف");
                System.out.println("• إدارة المستخدمين والصلاحيات");
                System.out.println("• الإعدادات العامة المتقدمة");
                System.out.println("• نظام التكامل مع قواعد البيانات");
                System.out.println("• تحليل قواعد البيانات");
                System.out.println("• دعم كامل لـ Oracle مع الترميز العربي");
                System.out.println("• واجهة عربية كاملة RTL");

                // فحص الاتصال التلقائي بقاعدة البيانات
                System.out.println("\n=== فحص الاتصال بقواعد البيانات Oracle ===");
                testOracleConnections();

            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("حدث خطأ في تشغيل النظام الكامل: " + e.getMessage());
            }
        });
    }

    /**
     * عرض شاشة البداية المحسنة
     */
    private static void showSplashScreen() {
        JWindow splash = new JWindow();
        splash.setSize(600, 350);
        splash.setLocationRelativeTo(null);

        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(new Color(52, 152, 219));
        panel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));

        // العنوان
        JLabel titleLabel = new JLabel("نظام إدارة الشحنات المحسن مع Oracle");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // العنوان الفرعي
        JLabel subtitleLabel = new JLabel("الإصدار 3.0 - نظام كامل مع دعم Oracle");
        subtitleLabel.setFont(new Font("Tahoma", Font.PLAIN, 16));
        subtitleLabel.setForeground(new Color(236, 240, 241));
        subtitleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        subtitleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط التقدم
        JProgressBar progressBar = new JProgressBar();
        progressBar.setIndeterminate(true);
        progressBar.setStringPainted(true);
        progressBar.setString("جاري تحميل النظام الكامل...");
        progressBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الميزات
        JPanel featuresPanel = new JPanel(new GridLayout(4, 2, 10, 5));
        featuresPanel.setOpaque(false);

        String[] features = {"✅ دعم Oracle كامل", "✅ واجهة عربية RTL", "✅ إدارة الأصناف",
                "✅ إدارة المستخدمين", "✅ التكامل مع قواعد البيانات", "✅ تحليل البيانات",
                "✅ الترميز العربي", "✅ النوافذ المتقدمة"};

        for (String feature : features) {
            JLabel featureLabel = new JLabel(feature);
            featureLabel.setFont(new Font("Tahoma", Font.PLAIN, 12));
            featureLabel.setForeground(Color.WHITE);
            featureLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            featuresPanel.add(featureLabel);
        }

        panel.add(titleLabel, BorderLayout.NORTH);
        panel.add(subtitleLabel, BorderLayout.CENTER);
        panel.add(featuresPanel, BorderLayout.SOUTH);

        JPanel bottomPanel = new JPanel(new BorderLayout());
        bottomPanel.setOpaque(false);
        bottomPanel.add(progressBar, BorderLayout.SOUTH);
        panel.add(bottomPanel, BorderLayout.PAGE_END);

        splash.add(panel);
        splash.setVisible(true);

        // إخفاء شاشة البداية بعد 3 ثوان
        Timer timer = new Timer(3000, e -> splash.dispose());
        timer.setRepeats(false);
        timer.start();

        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * فحص الاتصال بقواعد البيانات Oracle
     */
    private static void testOracleConnections() {
        try {
            System.out.println("🔄 فحص مكتبات Oracle...");

            // فحص تحميل مكتبة Oracle JDBC
            try {
                Class.forName("oracle.jdbc.driver.OracleDriver");
                System.out.println("✅ Oracle JDBC Driver محمل بنجاح");
            } catch (ClassNotFoundException e) {
                System.out.println("❌ Oracle JDBC Driver غير محمل!");
                System.out.println("الحل: تأكد من وجود ojdbc11.jar في مجلد lib");
                return;
            }

            // فحص وجود مكتبة orai18n.jar
            String classpath = System.getProperty("java.class.path");
            if (classpath.contains("orai18n.jar")) {
                System.out.println("✅ مكتبة orai18n.jar محملة (دعم الأحرف العربية)");
            } else {
                System.out.println(
                        "⚠️ مكتبة orai18n.jar غير محملة - قد تواجه مشاكل مع الأحرف العربية");
            }

            System.out.println("🔄 اختبار الاتصال بقواعد البيانات Oracle...");

            // اختبار الاتصال بقاعدة بيانات SHIP_ERP
            testSingleOracleConnection("SHIP_ERP", "ship_erp_password", "localhost", "1521",
                    "ORCL");

            // اختبار الاتصال بقاعدة بيانات IAS20251
            testSingleOracleConnection("ias20251", "ys123", "localhost", "1521", "ORCL");

        } catch (Exception e) {
            System.out.println("❌ خطأ في فحص قواعد البيانات: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * اختبار الاتصال بقاعدة بيانات Oracle محددة
     */
    private static void testSingleOracleConnection(String username, String password, String host,
            String port, String sid) {
        try {
            System.out.println("🔄 اختبار الاتصال بـ " + username + "...");

            // إعداد خصائص Oracle لدعم الترميز العربي
            java.util.Properties props = new java.util.Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");

            String url = "jdbc:oracle:thin:@" + host + ":" + port + ":" + sid;

            try (java.sql.Connection connection =
                    java.sql.DriverManager.getConnection(url, props)) {
                System.out.println("✅ تم الاتصال بقاعدة البيانات " + username
                        + " بنجاح مع دعم الترميز العربي");

                // فحص وجود جداول مهمة
                checkImportantTables(connection, username);

            } catch (java.sql.SQLException e) {
                System.out.println(
                        "❌ فشل الاتصال بقاعدة البيانات " + username + ": " + e.getMessage());
                if (e.getMessage().contains("ORA-01017")) {
                    System.out.println("💡 تأكد من صحة اسم المستخدم وكلمة المرور");
                } else if (e.getMessage().contains("ORA-12505")) {
                    System.out.println("💡 تأكد من تشغيل Oracle Database وصحة SID");
                }
            }

        } catch (Exception e) {
            System.out.println("❌ خطأ في اختبار الاتصال بـ " + username + ": " + e.getMessage());
        }
    }

    /**
     * فحص وجود الجداول المهمة
     */
    private static void checkImportantTables(java.sql.Connection connection, String schema) {
        try {
            System.out.println("🔄 فحص الجداول في " + schema + "...");

            String[] importantTables;

            // تحديد الجداول المناسبة لكل مستخدم
            if ("SHIP_ERP".equalsIgnoreCase(schema)) {
                // جداول SHIP_ERP
                importantTables = new String[] {"IAS_ITM_MST", "IAS_ITM_DTL", "ERP_MEASUREMENT",
                        "ERP_SUB_GRP_DTL", "ERP_ASSISTANT_GROUP", "ERP_DETAIL_GROUP",
                        "ERP_GROUP_DETAILS", "ERP_MAINSUB_GRP_DTL"};
            } else if ("ias20251".equalsIgnoreCase(schema)) {
                // جداول IAS20251
                importantTables = new String[] {"IAS_ITM_MST", "IAS_ITM_DTL", "MEASUREMENT",
                        "IAS_SUB_GRP_DTL", "IAS_ASSISTANT_GROUP", "IAS_DETAIL_GROUP",
                        "GROUP_DETAILS", "IAS_MAINSUB_GRP_DTL"};
            } else {
                // جداول افتراضية
                importantTables = new String[] {"IAS_ITM_MST", "IAS_ITM_DTL"};
            }

            for (String tableName : importantTables) {
                try (java.sql.PreparedStatement stmt = connection.prepareStatement(
                        "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = ?")) {
                    stmt.setString(1, tableName);
                    try (java.sql.ResultSet rs = stmt.executeQuery()) {
                        if (rs.next() && rs.getInt(1) > 0) {
                            System.out.println("  ✅ جدول " + tableName + " موجود");

                            // عد الصفوف
                            try (java.sql.PreparedStatement countStmt = connection
                                    .prepareStatement("SELECT COUNT(*) FROM " + tableName)) {
                                try (java.sql.ResultSet countRs = countStmt.executeQuery()) {
                                    if (countRs.next()) {
                                        int rowCount = countRs.getInt(1);
                                        System.out.println("    📊 عدد الصفوف: " + rowCount);
                                    }
                                }
                            } catch (Exception e) {
                                System.out.println("    ⚠️ لا يمكن عد الصفوف: " + e.getMessage());
                            }
                        } else {
                            System.out.println("  ❌ جدول " + tableName + " غير موجود");
                        }
                    }
                } catch (Exception e) {
                    System.out.println("  ❌ خطأ في فحص جدول " + tableName + ": " + e.getMessage());
                }
            }

        } catch (Exception e) {
            System.out.println("❌ خطأ في فحص الجداول: " + e.getMessage());
        }
    }
}
