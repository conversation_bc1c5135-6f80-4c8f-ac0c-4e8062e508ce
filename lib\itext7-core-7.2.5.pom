<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.itextpdf</groupId>
  <artifactId>itext7-core</artifactId>
  <version>7.2.5</version>
  <packaging>pom</packaging>
  <name>iText 7 Core</name>
  <description>A Free Java-PDF library</description>
  <url>https://itextpdf.com/</url>
  <inceptionYear>1998</inceptionYear>
  <organization>
    <name>iText Group NV</name>
    <url>https://itextpdf.com/</url>
  </organization>
  <licenses>
    <license>
      <name>GNU Affero General Public License v3</name>
      <url>http://www.fsf.org/licensing/licenses/agpl-3.0.html</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>itext</id>
      <name>iText Software</name>
      <email><EMAIL></email>
      <url>https://www.itextpdf.com</url>
    </developer>
  </developers>
  <mailingLists>
    <mailingList>
      <name>iText on StackOverflow</name>
      <subscribe>https://stackoverflow.com/questions/tagged/itext7</subscribe>
      <archive>https://stackoverflow.com/questions/tagged/itext7</archive>
      <otherArchives>
        <otherArchive>http://news.gmane.org/gmane.comp.java.lib.itext.general</otherArchive>
        <otherArchive>http://itext-general.2136553.n4.nabble.com/</otherArchive>
        <otherArchive>http://www.junlu.com/2.html</otherArchive>
        <otherArchive>http://sourceforge.net/mailarchive/forum.php?forum_id=3273</otherArchive>
        <otherArchive>http://www.mail-archive.com/itext-questions%40lists.sourceforge.net/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>
  <scm>
    <connection>ssh://************************:7999/i7j/itextcore.git</connection>
    <url>ssh://************************:7999/i7j/itextcore.git</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://jira.itextsupport.com/</url>
  </issueManagement>
  <ciManagement>
    <system>jenkins-ci</system>
    <url>https://jenkins.itextsupport.com/</url>
  </ciManagement>
  <repositories>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>itext-snapshot</id>
      <name>iText Repository - snapshots</name>
      <url>https://repo.itextsupport.com/snapshot</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>itext-releases</id>
      <name>iText Repository - releases</name>
      <url>https://repo.itextsupport.com/releases</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>barcodes</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>font-asian</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>forms</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>hyph</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>io</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>kernel</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>layout</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>pdfa</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>sign</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>styled-xml-parser</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>svg</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>
</project>