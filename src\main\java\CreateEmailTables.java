import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء جداول نظام البريد الإلكتروني Create Email System Tables
 */
public class CreateEmailTables {

    public static void main(String[] args) {
        System.out.println("📧 إنشاء جداول نظام البريد الإلكتروني...");

        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();

            // إنشاء جدول حسابات البريد الإلكتروني
            createEmailAccountsTable(connection);

            // إنشاء جدول إعدادات الخوادم
            createEmailServersTable(connection);

            // إنشاء جدول الرسائل
            createEmailMessagesTable(connection);

            // إنشاء جدول المرفقات
            createEmailAttachmentsTable(connection);

            // إنشاء جدول دفتر العناوين
            createAddressBookTable(connection);

            // إنشاء جدول المجلدات
            createEmailFoldersTable(connection);

            // إنشاء جدول القوالب
            createEmailTemplatesTable(connection);

            // إنشاء جدول سجلات النشاط
            createEmailLogsTable(connection);

            // إدراج بيانات تجريبية
            insertSampleData(connection);

            connection.close();
            System.out.println("🎉 تم إنشاء جداول نظام البريد الإلكتروني بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء جداول البريد الإلكتروني: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void createEmailAccountsTable(Connection connection) throws SQLException {
        System.out.println("📧 إنشاء جدول حسابات البريد الإلكتروني...");

        String createTableSQL =
                """
                        CREATE TABLE EMAIL_ACCOUNTS (
                            ACCOUNT_ID NUMBER(10) NOT NULL,
                            ACCOUNT_NAME NVARCHAR2(200) NOT NULL,
                            EMAIL_ADDRESS VARCHAR2(255) NOT NULL UNIQUE,
                            DISPLAY_NAME NVARCHAR2(200),
                            ACCOUNT_TYPE VARCHAR2(20) DEFAULT 'IMAP' CHECK (ACCOUNT_TYPE IN ('IMAP', 'POP3', 'EXCHANGE')),

                            -- إعدادات الخادم الوارد
                            INCOMING_SERVER VARCHAR2(255) NOT NULL,
                            INCOMING_PORT NUMBER(5) DEFAULT 993,
                            INCOMING_SECURITY VARCHAR2(10) DEFAULT 'SSL' CHECK (INCOMING_SECURITY IN ('NONE', 'SSL', 'TLS')),
                            INCOMING_USERNAME VARCHAR2(255),
                            INCOMING_PASSWORD VARCHAR2(500),

                            -- إعدادات الخادم الصادر
                            OUTGOING_SERVER VARCHAR2(255) NOT NULL,
                            OUTGOING_PORT NUMBER(5) DEFAULT 587,
                            OUTGOING_SECURITY VARCHAR2(10) DEFAULT 'TLS' CHECK (OUTGOING_SECURITY IN ('NONE', 'SSL', 'TLS')),
                            OUTGOING_USERNAME VARCHAR2(255),
                            OUTGOING_PASSWORD VARCHAR2(500),
                            OUTGOING_AUTH CHAR(1) DEFAULT 'Y' CHECK (OUTGOING_AUTH IN ('Y', 'N')),

                            -- إعدادات إضافية
                            DEFAULT_ACCOUNT CHAR(1) DEFAULT 'N' CHECK (DEFAULT_ACCOUNT IN ('Y', 'N')),
                            AUTO_CHECK_INTERVAL NUMBER(3) DEFAULT 15, -- بالدقائق
                            KEEP_MESSAGES_ON_SERVER CHAR(1) DEFAULT 'Y' CHECK (KEEP_MESSAGES_ON_SERVER IN ('Y', 'N')),
                            DELETE_AFTER_DAYS NUMBER(3) DEFAULT 30,

                            -- معلومات الحالة
                            IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                            IS_CONNECTED CHAR(1) DEFAULT 'N' CHECK (IS_CONNECTED IN ('Y', 'N')),
                            LAST_CHECK_DATE DATE,
                            LAST_ERROR NVARCHAR2(1000),

                            -- معلومات التدقيق
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,
                            LAST_UPDATED DATE DEFAULT SYSDATE,
                            UPDATED_BY VARCHAR2(50) DEFAULT USER,
                            VERSION_NUMBER NUMBER(10) DEFAULT 1,

                            CONSTRAINT PK_EMAIL_ACCOUNTS PRIMARY KEY (ACCOUNT_ID)
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول EMAIL_ACCOUNTS");
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
            System.out.println("ℹ️ جدول EMAIL_ACCOUNTS موجود مسبقاً");
        }

        // إنشاء المتسلسل
        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(
                    "CREATE SEQUENCE EMAIL_ACCOUNTS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء متسلسل EMAIL_ACCOUNTS_SEQ");
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                System.err.println("تحذير: " + e.getMessage());
            }
        }
    }

    private static void createEmailServersTable(Connection connection) throws SQLException {
        System.out.println("🖥️ إنشاء جدول خوادم البريد الإلكتروني...");

        String createTableSQL = """
                CREATE TABLE EMAIL_SERVERS (
                    SERVER_ID NUMBER(10) NOT NULL,
                    SERVER_NAME NVARCHAR2(200) NOT NULL,
                    PROVIDER_NAME NVARCHAR2(100),

                    -- إعدادات IMAP
                    IMAP_SERVER VARCHAR2(255),
                    IMAP_PORT NUMBER(5) DEFAULT 993,
                    IMAP_SECURITY VARCHAR2(10) DEFAULT 'SSL',

                    -- إعدادات SMTP
                    SMTP_SERVER VARCHAR2(255),
                    SMTP_PORT NUMBER(5) DEFAULT 587,
                    SMTP_SECURITY VARCHAR2(10) DEFAULT 'TLS',

                    -- إعدادات POP3
                    POP3_SERVER VARCHAR2(255),
                    POP3_PORT NUMBER(5) DEFAULT 995,
                    POP3_SECURITY VARCHAR2(10) DEFAULT 'SSL',

                    IS_PREDEFINED CHAR(1) DEFAULT 'N' CHECK (IS_PREDEFINED IN ('Y', 'N')),
                    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),

                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,

                    CONSTRAINT PK_EMAIL_SERVERS PRIMARY KEY (SERVER_ID)
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول EMAIL_SERVERS");
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
            System.out.println("ℹ️ جدول EMAIL_SERVERS موجود مسبقاً");
        }

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(
                    "CREATE SEQUENCE EMAIL_SERVERS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
        } catch (SQLException e) {
            // تجاهل إذا كان موجود
        }
    }

    private static void createEmailMessagesTable(Connection connection) throws SQLException {
        System.out.println("📨 إنشاء جدول الرسائل...");

        String createTableSQL =
                """
                        CREATE TABLE EMAIL_MESSAGES (
                            MESSAGE_ID NUMBER(10) NOT NULL,
                            ACCOUNT_ID NUMBER(10) NOT NULL,
                            FOLDER_ID NUMBER(10),

                            -- معلومات الرسالة
                            MESSAGE_UID VARCHAR2(100),
                            SUBJECT NVARCHAR2(500),
                            FROM_ADDRESS VARCHAR2(255),
                            FROM_NAME NVARCHAR2(200),
                            TO_ADDRESSES CLOB,
                            CC_ADDRESSES CLOB,
                            BCC_ADDRESSES CLOB,
                            REPLY_TO VARCHAR2(255),

                            -- محتوى الرسالة
                            MESSAGE_BODY CLOB,
                            MESSAGE_HTML CLOB,
                            MESSAGE_SIZE NUMBER(10),

                            -- معلومات التوقيت
                            SENT_DATE DATE,
                            RECEIVED_DATE DATE DEFAULT SYSDATE,

                            -- حالة الرسالة
                            IS_READ CHAR(1) DEFAULT 'N' CHECK (IS_READ IN ('Y', 'N')),
                            IS_FLAGGED CHAR(1) DEFAULT 'N' CHECK (IS_FLAGGED IN ('Y', 'N')),
                            IS_DELETED CHAR(1) DEFAULT 'N' CHECK (IS_DELETED IN ('Y', 'N')),
                            IS_DRAFT CHAR(1) DEFAULT 'N' CHECK (IS_DRAFT IN ('Y', 'N')),
                            IS_SENT CHAR(1) DEFAULT 'N' CHECK (IS_SENT IN ('Y', 'N')),

                            -- معلومات إضافية
                            PRIORITY VARCHAR2(10) DEFAULT 'NORMAL' CHECK (PRIORITY IN ('LOW', 'NORMAL', 'HIGH')),
                            HAS_ATTACHMENTS CHAR(1) DEFAULT 'N' CHECK (HAS_ATTACHMENTS IN ('Y', 'N')),
                            MESSAGE_HEADERS CLOB,

                            CREATED_DATE DATE DEFAULT SYSDATE,

                            CONSTRAINT PK_EMAIL_MESSAGES PRIMARY KEY (MESSAGE_ID),
                            CONSTRAINT FK_EMAIL_MESSAGES_ACCOUNT FOREIGN KEY (ACCOUNT_ID) REFERENCES EMAIL_ACCOUNTS(ACCOUNT_ID)
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول EMAIL_MESSAGES");
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
            System.out.println("ℹ️ جدول EMAIL_MESSAGES موجود مسبقاً");
        }

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(
                    "CREATE SEQUENCE EMAIL_MESSAGES_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
        } catch (SQLException e) {
            // تجاهل إذا كان موجود
        }
    }

    private static void createEmailAttachmentsTable(Connection connection) throws SQLException {
        System.out.println("📎 إنشاء جدول المرفقات...");

        String createTableSQL =
                """
                        CREATE TABLE EMAIL_ATTACHMENTS (
                            ATTACHMENT_ID NUMBER(10) NOT NULL,
                            MESSAGE_ID NUMBER(10) NOT NULL,

                            FILENAME NVARCHAR2(500) NOT NULL,
                            CONTENT_TYPE VARCHAR2(100),
                            FILE_SIZE NUMBER(10),
                            ATTACHMENT_DATA BLOB,

                            IS_INLINE CHAR(1) DEFAULT 'N' CHECK (IS_INLINE IN ('Y', 'N')),
                            CONTENT_ID VARCHAR2(100),

                            CREATED_DATE DATE DEFAULT SYSDATE,

                            CONSTRAINT PK_EMAIL_ATTACHMENTS PRIMARY KEY (ATTACHMENT_ID),
                            CONSTRAINT FK_EMAIL_ATTACHMENTS_MESSAGE FOREIGN KEY (MESSAGE_ID) REFERENCES EMAIL_MESSAGES(MESSAGE_ID) ON DELETE CASCADE
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول EMAIL_ATTACHMENTS");
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
            System.out.println("ℹ️ جدول EMAIL_ATTACHMENTS موجود مسبقاً");
        }

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(
                    "CREATE SEQUENCE EMAIL_ATTACHMENTS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
        } catch (SQLException e) {
            // تجاهل إذا كان موجود
        }
    }

    private static void createAddressBookTable(Connection connection) throws SQLException {
        System.out.println("📇 إنشاء جدول دفتر العناوين...");

        String createTableSQL = """
                CREATE TABLE EMAIL_ADDRESS_BOOK (
                    CONTACT_ID NUMBER(10) NOT NULL,

                    -- معلومات الاتصال الأساسية
                    FIRST_NAME NVARCHAR2(100),
                    LAST_NAME NVARCHAR2(100),
                    DISPLAY_NAME NVARCHAR2(200),
                    EMAIL_ADDRESS VARCHAR2(255) NOT NULL,

                    -- معلومات إضافية
                    COMPANY NVARCHAR2(200),
                    DEPARTMENT NVARCHAR2(100),
                    JOB_TITLE NVARCHAR2(100),
                    PHONE VARCHAR2(50),
                    MOBILE VARCHAR2(50),

                    -- معلومات التجميع
                    CATEGORY NVARCHAR2(100),
                    NOTES NVARCHAR2(1000),

                    -- إعدادات
                    IS_FAVORITE CHAR(1) DEFAULT 'N' CHECK (IS_FAVORITE IN ('Y', 'N')),
                    IS_BLOCKED CHAR(1) DEFAULT 'N' CHECK (IS_BLOCKED IN ('Y', 'N')),

                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    LAST_UPDATED DATE DEFAULT SYSDATE,
                    UPDATED_BY VARCHAR2(50) DEFAULT USER,

                    CONSTRAINT PK_EMAIL_ADDRESS_BOOK PRIMARY KEY (CONTACT_ID)
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول EMAIL_ADDRESS_BOOK");
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
            System.out.println("ℹ️ جدول EMAIL_ADDRESS_BOOK موجود مسبقاً");
        }

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(
                    "CREATE SEQUENCE EMAIL_ADDRESS_BOOK_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
        } catch (SQLException e) {
            // تجاهل إذا كان موجود
        }
    }

    private static void createEmailFoldersTable(Connection connection) throws SQLException {
        System.out.println("📁 إنشاء جدول المجلدات...");

        String createTableSQL =
                """
                        CREATE TABLE EMAIL_FOLDERS (
                            FOLDER_ID NUMBER(10) NOT NULL,
                            ACCOUNT_ID NUMBER(10) NOT NULL,
                            PARENT_FOLDER_ID NUMBER(10),

                            FOLDER_NAME NVARCHAR2(200) NOT NULL,
                            FOLDER_TYPE VARCHAR2(20) DEFAULT 'CUSTOM' CHECK (FOLDER_TYPE IN ('INBOX', 'SENT', 'DRAFTS', 'TRASH', 'SPAM', 'CUSTOM')),
                            FOLDER_PATH VARCHAR2(500),

                            MESSAGE_COUNT NUMBER(10) DEFAULT 0,
                            UNREAD_COUNT NUMBER(10) DEFAULT 0,

                            IS_SYSTEM CHAR(1) DEFAULT 'N' CHECK (IS_SYSTEM IN ('Y', 'N')),
                            IS_SUBSCRIBED CHAR(1) DEFAULT 'Y' CHECK (IS_SUBSCRIBED IN ('Y', 'N')),

                            CREATED_DATE DATE DEFAULT SYSDATE,

                            CONSTRAINT PK_EMAIL_FOLDERS PRIMARY KEY (FOLDER_ID),
                            CONSTRAINT FK_EMAIL_FOLDERS_ACCOUNT FOREIGN KEY (ACCOUNT_ID) REFERENCES EMAIL_ACCOUNTS(ACCOUNT_ID) ON DELETE CASCADE,
                            CONSTRAINT FK_EMAIL_FOLDERS_PARENT FOREIGN KEY (PARENT_FOLDER_ID) REFERENCES EMAIL_FOLDERS(FOLDER_ID)
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول EMAIL_FOLDERS");
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
            System.out.println("ℹ️ جدول EMAIL_FOLDERS موجود مسبقاً");
        }

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(
                    "CREATE SEQUENCE EMAIL_FOLDERS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
        } catch (SQLException e) {
            // تجاهل إذا كان موجود
        }
    }

    private static void createEmailTemplatesTable(Connection connection) throws SQLException {
        System.out.println("📝 إنشاء جدول القوالب...");

        String createTableSQL = """
                CREATE TABLE EMAIL_TEMPLATES (
                    TEMPLATE_ID NUMBER(10) NOT NULL,

                    TEMPLATE_NAME NVARCHAR2(200) NOT NULL,
                    TEMPLATE_SUBJECT NVARCHAR2(500),
                    TEMPLATE_BODY CLOB,
                    TEMPLATE_HTML CLOB,

                    CATEGORY NVARCHAR2(100),
                    IS_SHARED CHAR(1) DEFAULT 'N' CHECK (IS_SHARED IN ('Y', 'N')),

                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    LAST_UPDATED DATE DEFAULT SYSDATE,
                    UPDATED_BY VARCHAR2(50) DEFAULT USER,

                    CONSTRAINT PK_EMAIL_TEMPLATES PRIMARY KEY (TEMPLATE_ID)
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول EMAIL_TEMPLATES");
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
            System.out.println("ℹ️ جدول EMAIL_TEMPLATES موجود مسبقاً");
        }

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(
                    "CREATE SEQUENCE EMAIL_TEMPLATES_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
        } catch (SQLException e) {
            // تجاهل إذا كان موجود
        }
    }

    private static void createEmailLogsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول سجلات النشاط...");

        String createTableSQL =
                """
                        CREATE TABLE EMAIL_LOGS (
                            LOG_ID NUMBER(10) NOT NULL,
                            ACCOUNT_ID NUMBER(10),

                            LOG_TYPE VARCHAR2(20) NOT NULL CHECK (LOG_TYPE IN ('SEND', 'RECEIVE', 'DELETE', 'ERROR', 'LOGIN', 'LOGOUT')),
                            LOG_MESSAGE NVARCHAR2(1000),
                            LOG_DETAILS CLOB,

                            IP_ADDRESS VARCHAR2(45),
                            USER_AGENT VARCHAR2(500),

                            LOG_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,

                            CONSTRAINT PK_EMAIL_LOGS PRIMARY KEY (LOG_ID),
                            CONSTRAINT FK_EMAIL_LOGS_ACCOUNT FOREIGN KEY (ACCOUNT_ID) REFERENCES EMAIL_ACCOUNTS(ACCOUNT_ID)
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول EMAIL_LOGS");
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
            System.out.println("ℹ️ جدول EMAIL_LOGS موجود مسبقاً");
        }

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(
                    "CREATE SEQUENCE EMAIL_LOGS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
        } catch (SQLException e) {
            // تجاهل إذا كان موجود
        }
    }

    private static void insertSampleData(Connection connection) throws SQLException {
        System.out.println("📊 إدراج بيانات تجريبية...");

        // إدراج خوادم البريد المشهورة
        insertPredefinedServers(connection);

        // إدراج حساب تجريبي
        insertSampleAccount(connection);
    }

    private static void insertPredefinedServers(Connection connection) throws SQLException {
        String[][] servers = {
                {"Gmail", "Google", "imap.gmail.com", "993", "SSL", "smtp.gmail.com", "587", "TLS",
                        "pop.gmail.com", "995", "SSL"},
                {"Outlook/Hotmail", "Microsoft", "outlook.office365.com", "993", "SSL",
                        "smtp-mail.outlook.com", "587", "TLS", "outlook.office365.com", "995",
                        "SSL"},
                {"Yahoo Mail", "Yahoo", "imap.mail.yahoo.com", "993", "SSL", "smtp.mail.yahoo.com",
                        "587", "TLS", "pop.mail.yahoo.com", "995", "SSL"},
                {"iCloud", "Apple", "imap.mail.me.com", "993", "SSL", "smtp.mail.me.com", "587",
                        "TLS", "", "0", ""},
                {"AOL Mail", "AOL", "imap.aol.com", "993", "SSL", "smtp.aol.com", "587", "TLS",
                        "pop.aol.com", "995", "SSL"}};

        for (String[] server : servers) {
            try {
                String insertSQL =
                        """
                                INSERT INTO EMAIL_SERVERS
                                (SERVER_ID, SERVER_NAME, PROVIDER_NAME, IMAP_SERVER, IMAP_PORT, IMAP_SECURITY,
                                 SMTP_SERVER, SMTP_PORT, SMTP_SECURITY, POP3_SERVER, POP3_PORT, POP3_SECURITY, IS_PREDEFINED)
                                VALUES (EMAIL_SERVERS_SEQ.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Y')
                                """;

                try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
                    stmt.setString(1, server[0]);
                    stmt.setString(2, server[1]);
                    stmt.setString(3, server[2]);
                    stmt.setInt(4, Integer.parseInt(server[3]));
                    stmt.setString(5, server[4]);
                    stmt.setString(6, server[5]);
                    stmt.setInt(7, Integer.parseInt(server[6]));
                    stmt.setString(8, server[7]);
                    stmt.setString(9, server[8].isEmpty() ? null : server[8]);
                    stmt.setInt(10, server[9].isEmpty() ? 0 : Integer.parseInt(server[9]));
                    stmt.setString(11, server[10].isEmpty() ? null : server[10]);
                    stmt.executeUpdate();
                }
            } catch (SQLException e) {
                // تجاهل إذا كان موجود
            }
        }

        System.out.println("✅ تم إدراج خوادم البريد المشهورة");
    }

    private static void insertSampleAccount(Connection connection) throws SQLException {
        try {
            String insertSQL =
                    """
                            INSERT INTO EMAIL_ACCOUNTS
                            (ACCOUNT_ID, ACCOUNT_NAME, EMAIL_ADDRESS, DISPLAY_NAME, ACCOUNT_TYPE,
                             INCOMING_SERVER, INCOMING_PORT, INCOMING_SECURITY, INCOMING_USERNAME, INCOMING_PASSWORD,
                             OUTGOING_SERVER, OUTGOING_PORT, OUTGOING_SECURITY, OUTGOING_USERNAME, OUTGOING_PASSWORD,
                             DEFAULT_ACCOUNT)
                            VALUES (EMAIL_ACCOUNTS_SEQ.NEXTVAL, 'حساب تجريبي', '<EMAIL>', 'حساب تجريبي للشركة', 'IMAP',
                                    'imap.gmail.com', 993, 'SSL', '<EMAIL>', 'encrypted_password',
                                    'smtp.gmail.com', 587, 'TLS', '<EMAIL>', 'encrypted_password',
                                    'Y')
                            """;

            try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
                stmt.executeUpdate();
                System.out.println("✅ تم إدراج حساب تجريبي");
            }
        } catch (SQLException e) {
            // تجاهل إذا كان موجود
        }
    }
}
