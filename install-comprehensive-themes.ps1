Write-Host "========================================" -ForegroundColor Green
Write-Host "  COMPREHENSIVE THEME LIBRARIES INSTALLER" -ForegroundColor Green
Write-Host "  مثبت مكتبات المظاهر الشامل" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Set-Location "e:\ship_erp\java\lib"

# Function to download with retry
function Download-WithRetry {
    param(
        [string]$Url,
        [string]$OutputFile,
        [int]$MaxRetries = 3
    )
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            Write-Host "  Downloading $OutputFile (attempt $i/$MaxRetries)..." -ForegroundColor Cyan
            Invoke-WebRequest -Uri $Url -OutFile $OutputFile -TimeoutSec 60
            
            if (Test-Path $OutputFile) {
                $size = (Get-Item $OutputFile).Length
                if ($size -gt 1000) {
                    Write-Host "    SUCCESS: $OutputFile ($size bytes)" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "    FAILED: File too small ($size bytes)" -ForegroundColor Red
                    Remove-Item $OutputFile -Force -ErrorAction SilentlyContinue
                }
            }
        }
        catch {
            Write-Host "    ERROR: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        if ($i -lt $MaxRetries) {
            Write-Host "    Retrying in 2 seconds..." -ForegroundColor Yellow
            Start-Sleep -Seconds 2
        }
    }
    return $false
}

Write-Host "`n[PHASE 1] FlatLaf Theme Libraries" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow

$flatLafLibraries = @(
    @{
        Name = "FlatLaf Core"
        Url = "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar"
        File = "flatlaf-3.2.5.jar"
    },
    @{
        Name = "FlatLaf Extras"
        Url = "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar"
        File = "flatlaf-extras-3.2.5.jar"
    },
    @{
        Name = "FlatLaf IntelliJ Themes"
        Url = "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-intellij-themes/3.2.5/flatlaf-intellij-themes-3.2.5.jar"
        File = "flatlaf-intellij-themes-3.2.5.jar"
    },
    @{
        Name = "FlatLaf Fonts Roboto"
        Url = "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-fonts-roboto/2.137/flatlaf-fonts-roboto-2.137.jar"
        File = "flatlaf-fonts-roboto-2.137.jar"
    },
    @{
        Name = "FlatLaf Fonts JetBrains Mono"
        Url = "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-fonts-jetbrains-mono/2.304/flatlaf-fonts-jetbrains-mono-2.304.jar"
        File = "flatlaf-fonts-jetbrains-mono-2.304.jar"
    }
)

foreach ($lib in $flatLafLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n[PHASE 2] JTattoo Theme Libraries" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow

$jTattooLibraries = @(
    @{
        Name = "JTattoo Look and Feel"
        Url = "https://search.maven.org/remotecontent?filepath=com/jtattoo/JTattoo/1.6.13/JTattoo-1.6.13.jar"
        File = "jtattoo-1.6.13.jar"
    }
)

foreach ($lib in $jTattooLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n[PHASE 3] Substance Theme Libraries" -ForegroundColor Yellow
Write-Host "====================================" -ForegroundColor Yellow

$substanceLibraries = @(
    @{
        Name = "Substance Look and Feel"
        Url = "https://search.maven.org/remotecontent?filepath=org/pushingpixels/substance/8.0.02/substance-8.0.02.jar"
        File = "substance-8.0.02.jar"
    },
    @{
        Name = "Trident Animation Library"
        Url = "https://search.maven.org/remotecontent?filepath=org/pushingpixels/trident/1.5.00/trident-1.5.00.jar"
        File = "trident-1.5.00.jar"
    }
)

foreach ($lib in $substanceLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n[PHASE 4] WebLaF Theme Libraries" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow

$webLafLibraries = @(
    @{
        Name = "WebLaF Core"
        Url = "https://search.maven.org/remotecontent?filepath=com/weblookandfeel/weblaf-core/2.2.1/weblaf-core-2.2.1.jar"
        File = "weblaf-core-2.2.1.jar"
    },
    @{
        Name = "WebLaF UI"
        Url = "https://search.maven.org/remotecontent?filepath=com/weblookandfeel/weblaf-ui/2.2.1/weblaf-ui-2.2.1.jar"
        File = "weblaf-ui-2.2.1.jar"
    }
)

foreach ($lib in $webLafLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n[PHASE 5] DarkLaf Theme Libraries" -ForegroundColor Yellow
Write-Host "==================================" -ForegroundColor Yellow

$darkLafLibraries = @(
    @{
        Name = "DarkLaf Core"
        Url = "https://search.maven.org/remotecontent?filepath=com/github/weisj/darklaf-core/3.0.2/darklaf-core-3.0.2.jar"
        File = "darklaf-core-3.0.2.jar"
    },
    @{
        Name = "DarkLaf Theme"
        Url = "https://search.maven.org/remotecontent?filepath=com/github/weisj/darklaf-theme/3.0.2/darklaf-theme-3.0.2.jar"
        File = "darklaf-theme-3.0.2.jar"
    }
)

foreach ($lib in $darkLafLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n[PHASE 6] Material Design Theme Libraries" -ForegroundColor Yellow
Write-Host "==========================================" -ForegroundColor Yellow

$materialLibraries = @(
    @{
        Name = "Material UI Swing"
        Url = "https://search.maven.org/remotecontent?filepath=io/github/vincenzopalazzo/material-ui-swing/1.1.4/material-ui-swing-1.1.4.jar"
        File = "material-ui-swing-1.1.4.jar"
    }
)

foreach ($lib in $materialLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n[PHASE 7] Synthetica Theme Libraries" -ForegroundColor Yellow
Write-Host "=====================================" -ForegroundColor Yellow

$syntheticaLibraries = @(
    @{
        Name = "Synthetica Look and Feel"
        Url = "https://search.maven.org/remotecontent?filepath=de/javasoft/synthetica/2.30.0/synthetica-2.30.0.jar"
        File = "synthetica-2.30.0.jar"
    }
)

foreach ($lib in $syntheticaLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n[PHASE 8] BeautyEye Theme Libraries" -ForegroundColor Yellow
Write-Host "====================================" -ForegroundColor Yellow

$beautyEyeLibraries = @(
    @{
        Name = "BeautyEye Look and Feel"
        Url = "https://search.maven.org/remotecontent?filepath=org/jb2011/beautyeye/3.7/beautyeye-3.7.jar"
        File = "beautyeye-3.7.jar"
    }
)

foreach ($lib in $beautyEyeLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n[PHASE 9] Seaglass Theme Libraries" -ForegroundColor Yellow
Write-Host "===================================" -ForegroundColor Yellow

$seaglassLibraries = @(
    @{
        Name = "Seaglass Look and Feel"
        Url = "https://search.maven.org/remotecontent?filepath=com/seaglasslookandfeel/seaglasslookandfeel/0.2.1/seaglasslookandfeel-0.2.1.jar"
        File = "seaglass-0.2.1.jar"
    }
)

foreach ($lib in $seaglassLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n[PHASE 10] Utility Libraries" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

$utilityLibraries = @(
    @{
        Name = "JSON Processing"
        Url = "https://search.maven.org/remotecontent?filepath=org/json/json/20230227/json-20230227.jar"
        File = "json-20230227.jar"
    },
    @{
        Name = "Apache Commons IO"
        Url = "https://search.maven.org/remotecontent?filepath=commons-io/commons-io/2.11.0/commons-io-2.11.0.jar"
        File = "commons-io-2.11.0.jar"
    },
    @{
        Name = "Apache Commons Lang"
        Url = "https://search.maven.org/remotecontent?filepath=org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar"
        File = "commons-lang3-3.12.0.jar"
    }
)

foreach ($lib in $utilityLibraries) {
    Write-Host "`n[$($lib.Name)]" -ForegroundColor White
    Download-WithRetry -Url $lib.Url -OutputFile $lib.File
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "  INSTALLATION VERIFICATION" -ForegroundColor Green
Write-Host "  التحقق من التثبيت" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

$allLibraries = $flatLafLibraries + $jTattooLibraries + $substanceLibraries + $webLafLibraries + $darkLafLibraries + $materialLibraries + $syntheticaLibraries + $beautyEyeLibraries + $seaglassLibraries + $utilityLibraries

$successCount = 0
$failCount = 0

Write-Host "`nVerifying all downloaded libraries:" -ForegroundColor White
Write-Host "===================================" -ForegroundColor White

foreach ($lib in $allLibraries) {
    if (Test-Path $lib.File) {
        $size = (Get-Item $lib.File).Length
        if ($size -gt 1000) {
            Write-Host "  ✓ $($lib.Name): $($lib.File) ($size bytes)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "  ✗ $($lib.Name): $($lib.File) (TOO SMALL: $size bytes)" -ForegroundColor Red
            $failCount++
        }
    } else {
        Write-Host "  ✗ $($lib.Name): $($lib.File) (MISSING)" -ForegroundColor Red
        $failCount++
    }
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "  INSTALLATION SUMMARY" -ForegroundColor Green
Write-Host "  ملخص التثبيت" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "Total Libraries: $($allLibraries.Count)" -ForegroundColor White
Write-Host "Successfully Installed: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red

if ($failCount -eq 0) {
    Write-Host "`n🎉 ALL THEME LIBRARIES INSTALLED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "🎉 تم تثبيت جميع مكتبات المظاهر بنجاح!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some libraries failed to install. Check the errors above." -ForegroundColor Yellow
    Write-Host "⚠️  فشل تثبيت بعض المكتبات. تحقق من الأخطاء أعلاه." -ForegroundColor Yellow
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
