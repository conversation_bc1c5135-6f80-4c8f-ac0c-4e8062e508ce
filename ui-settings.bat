@echo off
echo ========================================
echo   UI SETTINGS WINDOW
echo ========================================

cd /d "d:\java\java"

echo.
echo [1] Compiling files...
javac -encoding UTF-8 -cp "lib\*" EnhancedSettingsManager.java AdvancedUISettingsWindow.java

if %errorlevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Compilation successful!

echo.
echo [2] Starting UI Settings Window...

java -cp "lib\*;." -Djava.awt.headless=false -Dfile.encoding=UTF-8 AdvancedUISettingsWindow

echo.
echo UI Settings Window closed.
pause
