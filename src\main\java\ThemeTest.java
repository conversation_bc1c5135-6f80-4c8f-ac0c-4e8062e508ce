import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * اختبار سريع للمظاهر
 * Quick Theme Test
 */
public class ThemeTest extends JFrame {
    
    private JComboBox<String> themeSelector;
    private JButton applyButton;
    private JLabel statusLabel;
    
    public ThemeTest() {
        setTitle("اختبار المظاهر - Theme Test");
        setSize(500, 300);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        
        initComponents();
        setupLayout();
        setupEvents();
    }
    
    private void initComponents() {
        themeSelector = new JComboBox<>(ThemeApplier.getAvailableThemes());
        applyButton = new JButton("تطبيق المظهر");
        statusLabel = new JLabel("اختر مظهراً وانقر تطبيق", JLabel.CENTER);
        
        // إعداد الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        themeSelector.setFont(arabicFont);
        applyButton.setFont(arabicFont);
        statusLabel.setFont(arabicFont);
        
        // إعداد الاتجاه
        themeSelector.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        applyButton.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        JPanel topPanel = new JPanel(new FlowLayout());
        topPanel.add(new JLabel("المظهر:"));
        topPanel.add(themeSelector);
        topPanel.add(applyButton);
        
        JPanel centerPanel = new JPanel(new GridLayout(4, 2, 10, 10));
        centerPanel.setBorder(BorderFactory.createTitledBorder("عناصر للاختبار"));
        
        // إضافة عناصر للاختبار
        centerPanel.add(new JLabel("تسمية:"));
        centerPanel.add(new JTextField("نص تجريبي"));
        
        centerPanel.add(new JLabel("زر:"));
        centerPanel.add(new JButton("زر تجريبي"));
        
        centerPanel.add(new JLabel("قائمة:"));
        centerPanel.add(new JComboBox<>(new String[]{"خيار 1", "خيار 2", "خيار 3"}));
        
        centerPanel.add(new JLabel("مربع اختيار:"));
        centerPanel.add(new JCheckBox("خيار"));
        
        add(topPanel, BorderLayout.NORTH);
        add(centerPanel, BorderLayout.CENTER);
        add(statusLabel, BorderLayout.SOUTH);
    }
    
    private void setupEvents() {
        applyButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String selectedTheme = (String) themeSelector.getSelectedItem();
                if (selectedTheme != null) {
                    statusLabel.setText("جاري تطبيق المظهر: " + selectedTheme);
                    
                    SwingUtilities.invokeLater(() -> {
                        boolean success = ThemeApplier.applyTheme(selectedTheme, ThemeTest.this);
                        if (success) {
                            statusLabel.setText("تم تطبيق المظهر: " + selectedTheme);
                        } else {
                            statusLabel.setText("فشل في تطبيق المظهر: " + selectedTheme);
                        }
                    });
                }
            }
        });
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق مظهر افتراضي
                com.formdev.flatlaf.FlatLightLaf.setup();
            } catch (Exception e) {
                e.printStackTrace();
            }
            
            new ThemeTest().setVisible(true);
        });
    }
}
