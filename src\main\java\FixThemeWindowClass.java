import java.sql.*;

/**
 * إصلاح اسم كلاس نافذة المظاهر في قاعدة البيانات
 * Fix Theme Window Class Name in Database
 */
public class FixThemeWindowClass {
    
    public static void main(String[] args) {
        System.out.println("🔧 إصلاح اسم كلاس نافذة المظاهر في قاعدة البيانات...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            fixThemeWindowClass(connection);
            
            connection.close();
            System.out.println("✅ تم إصلاح اسم كلاس نافذة المظاهر بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إصلاح اسم كلاس نافذة المظاهر: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * إصلاح اسم كلاس نافذة المظاهر
     */
    private static void fixThemeWindowClass(Connection connection) throws SQLException {
        System.out.println("📋 تحديث اسم كلاس نافذة إعدادات الواجهة والمظهر...");
        
        // تحديث النافذة المحددة (ID = 38)
        String updateSQL = "UPDATE ERP_SYSTEM_TREE SET WINDOW_CLASS = ? WHERE TREE_ID = 38";
        
        try (PreparedStatement updateStmt = connection.prepareStatement(updateSQL)) {
            updateStmt.setString(1, "ComprehensiveThemeSettingsWindow");
            
            int updated = updateStmt.executeUpdate();
            
            if (updated > 0) {
                System.out.println("✅ تم تحديث كلاس النافذة إلى: ComprehensiveThemeSettingsWindow");
            } else {
                System.err.println("❌ فشل في تحديث كلاس النافذة");
            }
        }
        
        // التحقق من النتيجة
        System.out.println("\n📋 التحقق من النتيجة:");
        String checkSQL = "SELECT TREE_ID, NODE_NAME_AR, WINDOW_CLASS FROM ERP_SYSTEM_TREE WHERE TREE_ID = 38";
        
        try (PreparedStatement checkStmt = connection.prepareStatement(checkSQL);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next()) {
                int treeId = rs.getInt("TREE_ID");
                String nodeName = rs.getString("NODE_NAME_AR");
                String windowClass = rs.getString("WINDOW_CLASS");
                
                System.out.println("  ID: " + treeId);
                System.out.println("  اسم النافذة: " + nodeName);
                System.out.println("  كلاس النافذة: " + windowClass);
                
                if ("ComprehensiveThemeSettingsWindow".equals(windowClass)) {
                    System.out.println("✅ تم التحديث بنجاح!");
                } else {
                    System.err.println("❌ لم يتم التحديث بشكل صحيح!");
                }
            }
        }
        
        // تحديث أي نوافذ أخرى تستخدم UIThemeSettingsWindow
        System.out.println("\n📋 البحث عن نوافذ أخرى تستخدم UIThemeSettingsWindow...");
        String searchSQL = "SELECT TREE_ID, NODE_NAME_AR, WINDOW_CLASS FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = 'UIThemeSettingsWindow'";
        
        try (PreparedStatement searchStmt = connection.prepareStatement(searchSQL);
             ResultSet rs = searchStmt.executeQuery()) {
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                int treeId = rs.getInt("TREE_ID");
                String nodeName = rs.getString("NODE_NAME_AR");
                
                System.out.println("  وجدت نافذة أخرى: " + nodeName + " (ID: " + treeId + ")");
                
                // تحديثها أيضاً
                String updateOtherSQL = "UPDATE ERP_SYSTEM_TREE SET WINDOW_CLASS = ? WHERE TREE_ID = ?";
                try (PreparedStatement updateOtherStmt = connection.prepareStatement(updateOtherSQL)) {
                    updateOtherStmt.setString(1, "ComprehensiveThemeSettingsWindow");
                    updateOtherStmt.setInt(2, treeId);
                    
                    int updatedOther = updateOtherStmt.executeUpdate();
                    if (updatedOther > 0) {
                        System.out.println("    ✅ تم تحديثها إلى: ComprehensiveThemeSettingsWindow");
                    }
                }
            }
            
            if (!found) {
                System.out.println("  لا توجد نوافذ أخرى تستخدم UIThemeSettingsWindow");
            }
        }
    }
}
