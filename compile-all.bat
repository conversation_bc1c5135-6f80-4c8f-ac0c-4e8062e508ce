@echo off
echo ========================================
echo   Comprehensive Compilation Script
echo   سكريپت التجميع الشامل
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Cleaning old class files...
echo تنظيف الملفات المجمعة القديمة...
del *.class 2>nul

echo.
echo [2] Compiling Core Classes...
echo تجميع الكلاسات الأساسية...

echo Compiling TNSConnectionManager...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TNSConnectionManager.java
if %errorlevel% neq 0 (
    echo Failed to compile TNSConnectionManager
    pause
    exit /b 1
)

echo Compiling SystemTreeManager...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\SystemTreeManager.java
if %errorlevel% neq 0 (
    echo Failed to compile SystemTreeManager
    pause
    exit /b 1
)

echo Compiling User...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\User.java
if %errorlevel% neq 0 (
    echo Failed to compile User
    pause
    exit /b 1
)

echo.
echo [3] Compiling Window Classes...
echo تجميع نوافذ النظام...

echo Compiling GeneralSettingsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\GeneralSettingsWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile GeneralSettingsWindow
    pause
    exit /b 1
)

echo Compiling UserManagementWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\UserManagementWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile UserManagementWindow
    pause
    exit /b 1
)

echo Compiling GlobalVariablesWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\GlobalVariablesWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile GlobalVariablesWindow
    pause
    exit /b 1
)

echo Compiling WorkingThemeWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\WorkingThemeWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile WorkingThemeWindow
    pause
    exit /b 1
)

echo Compiling AdvancedCompanySettingsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\AdvancedCompanySettingsWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile AdvancedCompanySettingsWindow
    pause
    exit /b 1
)

echo Compiling EmailAccountsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\EmailAccountsWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile EmailAccountsWindow
    pause
    exit /b 1
)

echo Compiling ItemGroupsManagementWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\ItemGroupsManagementWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile ItemGroupsManagementWindow
    pause
    exit /b 1
)

echo Compiling MeasurementUnitsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\MeasurementUnitsWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile MeasurementUnitsWindow
    pause
    exit /b 1
)

echo.
echo [4] Compiling Main Components...
echo تجميع المكونات الرئيسية...

echo Compiling TreeMenuPanel...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo Failed to compile TreeMenuPanel
    pause
    exit /b 1
)

echo Compiling EnhancedMainWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\EnhancedMainWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile EnhancedMainWindow
    pause
    exit /b 1
)

echo Compiling ShipERPMain...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\ShipERPMain.java
if %errorlevel% neq 0 (
    echo Failed to compile ShipERPMain
    pause
    exit /b 1
)

echo.
echo [5] Testing Application...
echo اختبار التطبيق...

echo Starting Ship ERP System...
java -cp "lib\*;." ShipERPMain

echo.
echo ========================================
echo   Compilation Complete!
echo   اكتمل التجميع!
echo ========================================
pause
