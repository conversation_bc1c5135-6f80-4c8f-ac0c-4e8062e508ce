import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicInteger;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * مراقب الأداء - Performance Monitor
 * يراقب أداء النظام والاستعلامات
 */
public class PerformanceMonitor {
    
    private static PerformanceMonitor instance;
    
    // إحصائيات الاتصالات
    private AtomicInteger totalConnections = new AtomicInteger(0);
    private AtomicInteger activeConnections = new AtomicInteger(0);
    private AtomicLong totalConnectionTime = new AtomicLong(0);
    
    // إحصائيات الاستعلامات
    private AtomicInteger totalQueries = new AtomicInteger(0);
    private AtomicLong totalQueryTime = new AtomicLong(0);
    private AtomicInteger slowQueries = new AtomicInteger(0);
    
    // إحصائيات الذاكرة
    private Runtime runtime = Runtime.getRuntime();
    
    // سجل الأداء
    private ConcurrentHashMap<String, QueryStats> queryStats = new ConcurrentHashMap<>();
    private List<PerformanceEvent> recentEvents = new ArrayList<>();
    private final int MAX_EVENTS = 100;
    
    // عتبات الإنذار
    private static final long SLOW_QUERY_THRESHOLD = 1000; // 1 ثانية
    private static final long MEMORY_WARNING_THRESHOLD = 80; // 80% من الذاكرة
    
    private PerformanceMonitor() {
        startPerformanceMonitoring();
    }
    
    public static synchronized PerformanceMonitor getInstance() {
        if (instance == null) {
            instance = new PerformanceMonitor();
        }
        return instance;
    }
    
    /**
     * بدء مراقبة الأداء
     */
    private void startPerformanceMonitoring() {
        // إنشاء thread لمراقبة الأداء كل 30 ثانية
        Thread monitorThread = new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(30000); // 30 ثانية
                    checkSystemHealth();
                } catch (InterruptedException e) {
                    break;
                }
            }
        });
        monitorThread.setDaemon(true);
        monitorThread.start();
        
        System.out.println("✅ تم بدء مراقبة الأداء");
    }
    
    /**
     * تسجيل بداية اتصال
     */
    public void recordConnectionStart() {
        totalConnections.incrementAndGet();
        activeConnections.incrementAndGet();
    }
    
    /**
     * تسجيل انتهاء اتصال
     */
    public void recordConnectionEnd(long connectionTime) {
        activeConnections.decrementAndGet();
        totalConnectionTime.addAndGet(connectionTime);
    }
    
    /**
     * تسجيل بداية استعلام
     */
    public QueryTimer startQuery(String queryType) {
        return new QueryTimer(queryType);
    }
    
    /**
     * تسجيل انتهاء استعلام
     */
    public void recordQueryEnd(String queryType, long executionTime) {
        totalQueries.incrementAndGet();
        totalQueryTime.addAndGet(executionTime);
        
        // تحديث إحصائيات الاستعلام
        queryStats.computeIfAbsent(queryType, k -> new QueryStats()).addExecution(executionTime);
        
        // فحص الاستعلامات البطيئة
        if (executionTime > SLOW_QUERY_THRESHOLD) {
            slowQueries.incrementAndGet();
            recordEvent("SLOW_QUERY", queryType + " took " + executionTime + "ms");
        }
    }
    
    /**
     * تسجيل حدث أداء
     */
    public void recordEvent(String type, String description) {
        synchronized (recentEvents) {
            if (recentEvents.size() >= MAX_EVENTS) {
                recentEvents.remove(0);
            }
            recentEvents.add(new PerformanceEvent(type, description));
        }
    }
    
    /**
     * فحص صحة النظام
     */
    private void checkSystemHealth() {
        // فحص استخدام الذاكرة
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long memoryUsagePercent = (usedMemory * 100) / totalMemory;
        
        if (memoryUsagePercent > MEMORY_WARNING_THRESHOLD) {
            recordEvent("MEMORY_WARNING", "Memory usage: " + memoryUsagePercent + "%");
            System.out.println("⚠️ تحذير: استخدام الذاكرة مرتفع: " + memoryUsagePercent + "%");
        }
        
        // فحص الاتصالات النشطة
        int active = activeConnections.get();
        if (active > 8) { // تحذير إذا كان هناك أكثر من 8 اتصالات نشطة
            recordEvent("CONNECTION_WARNING", "High active connections: " + active);
            System.out.println("⚠️ تحذير: عدد كبير من الاتصالات النشطة: " + active);
        }
    }
    
    /**
     * الحصول على تقرير الأداء
     */
    public PerformanceReport getPerformanceReport() {
        PerformanceReport report = new PerformanceReport();
        
        // إحصائيات الاتصالات
        report.totalConnections = totalConnections.get();
        report.activeConnections = activeConnections.get();
        report.avgConnectionTime = totalConnections.get() > 0 ? 
            totalConnectionTime.get() / totalConnections.get() : 0;
        
        // إحصائيات الاستعلامات
        report.totalQueries = totalQueries.get();
        report.slowQueries = slowQueries.get();
        report.avgQueryTime = totalQueries.get() > 0 ? 
            totalQueryTime.get() / totalQueries.get() : 0;
        
        // إحصائيات الذاكرة
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        report.memoryUsed = totalMemory - freeMemory;
        report.memoryTotal = totalMemory;
        report.memoryUsagePercent = (report.memoryUsed * 100) / report.memoryTotal;
        
        // أحداث حديثة
        synchronized (recentEvents) {
            report.recentEvents = new ArrayList<>(recentEvents);
        }
        
        return report;
    }
    
    /**
     * طباعة تقرير الأداء
     */
    public void printPerformanceReport() {
        PerformanceReport report = getPerformanceReport();
        
        System.out.println("\n📊 تقرير الأداء - " + 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("=" + "=".repeat(50));
        
        System.out.println("🔗 الاتصالات:");
        System.out.println("   إجمالي الاتصالات: " + report.totalConnections);
        System.out.println("   الاتصالات النشطة: " + report.activeConnections);
        System.out.println("   متوسط وقت الاتصال: " + report.avgConnectionTime + " ms");
        
        System.out.println("\n🔍 الاستعلامات:");
        System.out.println("   إجمالي الاستعلامات: " + report.totalQueries);
        System.out.println("   الاستعلامات البطيئة: " + report.slowQueries);
        System.out.println("   متوسط وقت الاستعلام: " + report.avgQueryTime + " ms");
        
        System.out.println("\n💾 الذاكرة:");
        System.out.println("   المستخدمة: " + (report.memoryUsed / 1024 / 1024) + " MB");
        System.out.println("   الإجمالية: " + (report.memoryTotal / 1024 / 1024) + " MB");
        System.out.println("   نسبة الاستخدام: " + report.memoryUsagePercent + "%");
        
        if (!report.recentEvents.isEmpty()) {
            System.out.println("\n⚠️ الأحداث الحديثة:");
            for (int i = Math.max(0, report.recentEvents.size() - 5); 
                 i < report.recentEvents.size(); i++) {
                PerformanceEvent event = report.recentEvents.get(i);
                System.out.println("   " + event.timestamp + " - " + 
                    event.type + ": " + event.description);
            }
        }
        
        System.out.println("=" + "=".repeat(50));
    }
    
    /**
     * إعادة تعيين الإحصائيات
     */
    public void resetStatistics() {
        totalConnections.set(0);
        totalConnectionTime.set(0);
        totalQueries.set(0);
        totalQueryTime.set(0);
        slowQueries.set(0);
        queryStats.clear();
        synchronized (recentEvents) {
            recentEvents.clear();
        }
        System.out.println("✅ تم إعادة تعيين إحصائيات الأداء");
    }
    
    /**
     * كلاس مؤقت الاستعلام
     */
    public class QueryTimer {
        private final String queryType;
        private final long startTime;
        
        public QueryTimer(String queryType) {
            this.queryType = queryType;
            this.startTime = System.currentTimeMillis();
        }
        
        public void end() {
            long executionTime = System.currentTimeMillis() - startTime;
            recordQueryEnd(queryType, executionTime);
        }
    }
    
    /**
     * كلاس إحصائيات الاستعلام
     */
    private static class QueryStats {
        private AtomicInteger count = new AtomicInteger(0);
        private AtomicLong totalTime = new AtomicLong(0);
        private long minTime = Long.MAX_VALUE;
        private long maxTime = 0;
        
        public void addExecution(long time) {
            count.incrementAndGet();
            totalTime.addAndGet(time);
            minTime = Math.min(minTime, time);
            maxTime = Math.max(maxTime, time);
        }
    }
    
    /**
     * كلاس حدث الأداء
     */
    private static class PerformanceEvent {
        public final String timestamp;
        public final String type;
        public final String description;
        
        public PerformanceEvent(String type, String description) {
            this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            this.type = type;
            this.description = description;
        }
    }
    
    /**
     * كلاس تقرير الأداء
     */
    public static class PerformanceReport {
        public int totalConnections;
        public int activeConnections;
        public long avgConnectionTime;
        public int totalQueries;
        public int slowQueries;
        public long avgQueryTime;
        public long memoryUsed;
        public long memoryTotal;
        public long memoryUsagePercent;
        public List<PerformanceEvent> recentEvents;
    }
}
