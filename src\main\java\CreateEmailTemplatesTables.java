import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء جداول نظام إدارة القوالب
 * Create Email Templates System Tables
 */
public class CreateEmailTemplatesTables {

    public static void main(String[] args) {
        System.out.println("=== إنشاء جداول نظام إدارة القوالب ===");
        
        try (Connection connection = DatabaseConfig.getConnection()) {
            createEmailTemplatesTables(connection);
            System.out.println("✅ تم إنشاء جميع جداول نظام إدارة القوالب بنجاح!");
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء جداول نظام إدارة القوالب: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void createEmailTemplatesTables(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جداول نظام إدارة القوالب...");

        // 1. جدول فئات القوالب
        createTemplateCategories(connection);
        
        // 2. جدول القوالب الرئيسي
        createEmailTemplates(connection);
        
        // 3. جدول متغيرات القوالب
        createTemplateVariables(connection);
        
        // 4. جدول مرفقات القوالب
        createTemplateAttachments(connection);
        
        // 5. جدول إحصائيات استخدام القوالب
        createTemplateUsageStats(connection);
        
        // 6. جدول مشاركة القوالب
        createTemplateSharing(connection);
        
        // 7. جدول تاريخ تعديل القوالب
        createTemplateHistory(connection);
        
        // 8. إدراج البيانات الافتراضية
        insertDefaultData(connection);
    }

    private static void createTemplateCategories(Connection connection) throws SQLException {
        System.out.println("📁 إنشاء جدول فئات القوالب...");
        
        String sql = """
            CREATE TABLE EMAIL_TEMPLATE_CATEGORIES (
                CATEGORY_ID NUMBER PRIMARY KEY,
                CATEGORY_NAME_AR NVARCHAR2(100) NOT NULL,
                CATEGORY_NAME_EN VARCHAR2(100) NOT NULL,
                DESCRIPTION NVARCHAR2(500),
                ICON_NAME VARCHAR2(50),
                COLOR_CODE VARCHAR2(7),
                SORT_ORDER NUMBER DEFAULT 0,
                IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                CREATED_BY NUMBER,
                CREATED_DATE DATE DEFAULT SYSDATE,
                MODIFIED_BY NUMBER,
                MODIFIED_DATE DATE,
                CONSTRAINT UK_TEMPLATE_CAT_NAME_AR UNIQUE (CATEGORY_NAME_AR),
                CONSTRAINT UK_TEMPLATE_CAT_NAME_EN UNIQUE (CATEGORY_NAME_EN)
            )
            """;

        executeSQL(connection, sql, "EMAIL_TEMPLATE_CATEGORIES");
        
        // إنشاء sequence
        String seqSql = """
            CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_CATEGORIES
            START WITH 1
            INCREMENT BY 1
            NOCACHE
            """;
        executeSQL(connection, seqSql, "SEQ_EMAIL_TEMPLATE_CATEGORIES");
    }

    private static void createEmailTemplates(Connection connection) throws SQLException {
        System.out.println("📧 إنشاء جدول القوالب الرئيسي...");
        
        String sql = """
            CREATE TABLE EMAIL_TEMPLATES (
                TEMPLATE_ID NUMBER PRIMARY KEY,
                TEMPLATE_NAME_AR NVARCHAR2(200) NOT NULL,
                TEMPLATE_NAME_EN VARCHAR2(200) NOT NULL,
                CATEGORY_ID NUMBER,
                SUBJECT_AR NVARCHAR2(500),
                SUBJECT_EN VARCHAR2(500),
                BODY_HTML CLOB,
                BODY_TEXT CLOB,
                TEMPLATE_TYPE VARCHAR2(20) DEFAULT 'STANDARD' CHECK (TEMPLATE_TYPE IN ('STANDARD', 'CAMPAIGN', 'NOTIFICATION', 'SIGNATURE')),
                LANGUAGE_CODE VARCHAR2(5) DEFAULT 'AR',
                IS_HTML CHAR(1) DEFAULT 'Y' CHECK (IS_HTML IN ('Y', 'N')),
                IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                IS_PUBLIC CHAR(1) DEFAULT 'N' CHECK (IS_PUBLIC IN ('Y', 'N')),
                USAGE_COUNT NUMBER DEFAULT 0,
                LAST_USED_DATE DATE,
                TAGS NVARCHAR2(1000),
                PREVIEW_IMAGE_PATH VARCHAR2(500),
                CREATED_BY NUMBER,
                CREATED_DATE DATE DEFAULT SYSDATE,
                MODIFIED_BY NUMBER,
                MODIFIED_DATE DATE,
                CONSTRAINT FK_TEMPLATE_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES EMAIL_TEMPLATE_CATEGORIES(CATEGORY_ID)
            )
            """;

        executeSQL(connection, sql, "EMAIL_TEMPLATES");
        
        // إنشاء sequence
        String seqSql = """
            CREATE SEQUENCE SEQ_EMAIL_TEMPLATES
            START WITH 1
            INCREMENT BY 1
            NOCACHE
            """;
        executeSQL(connection, seqSql, "SEQ_EMAIL_TEMPLATES");
    }

    private static void createTemplateVariables(Connection connection) throws SQLException {
        System.out.println("🔧 إنشاء جدول متغيرات القوالب...");
        
        String sql = """
            CREATE TABLE EMAIL_TEMPLATE_VARIABLES (
                VARIABLE_ID NUMBER PRIMARY KEY,
                TEMPLATE_ID NUMBER NOT NULL,
                VARIABLE_NAME VARCHAR2(100) NOT NULL,
                VARIABLE_LABEL_AR NVARCHAR2(200),
                VARIABLE_LABEL_EN VARCHAR2(200),
                VARIABLE_TYPE VARCHAR2(20) DEFAULT 'TEXT' CHECK (VARIABLE_TYPE IN ('TEXT', 'NUMBER', 'DATE', 'EMAIL', 'URL', 'PHONE')),
                DEFAULT_VALUE NVARCHAR2(1000),
                IS_REQUIRED CHAR(1) DEFAULT 'N' CHECK (IS_REQUIRED IN ('Y', 'N')),
                VALIDATION_PATTERN VARCHAR2(500),
                DESCRIPTION NVARCHAR2(500),
                SORT_ORDER NUMBER DEFAULT 0,
                CREATED_DATE DATE DEFAULT SYSDATE,
                CONSTRAINT FK_TEMPLATE_VARIABLES FOREIGN KEY (TEMPLATE_ID) REFERENCES EMAIL_TEMPLATES(TEMPLATE_ID) ON DELETE CASCADE,
                CONSTRAINT UK_TEMPLATE_VARIABLE UNIQUE (TEMPLATE_ID, VARIABLE_NAME)
            )
            """;

        executeSQL(connection, sql, "EMAIL_TEMPLATE_VARIABLES");
        
        String seqSql = """
            CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_VARIABLES
            START WITH 1
            INCREMENT BY 1
            NOCACHE
            """;
        executeSQL(connection, seqSql, "SEQ_EMAIL_TEMPLATE_VARIABLES");
    }

    private static void createTemplateAttachments(Connection connection) throws SQLException {
        System.out.println("📎 إنشاء جدول مرفقات القوالب...");
        
        String sql = """
            CREATE TABLE EMAIL_TEMPLATE_ATTACHMENTS (
                ATTACHMENT_ID NUMBER PRIMARY KEY,
                TEMPLATE_ID NUMBER NOT NULL,
                FILE_NAME NVARCHAR2(255) NOT NULL,
                FILE_PATH VARCHAR2(1000) NOT NULL,
                FILE_SIZE NUMBER,
                MIME_TYPE VARCHAR2(100),
                IS_INLINE CHAR(1) DEFAULT 'N' CHECK (IS_INLINE IN ('Y', 'N')),
                CID VARCHAR2(100),
                DESCRIPTION NVARCHAR2(500),
                UPLOAD_DATE DATE DEFAULT SYSDATE,
                CONSTRAINT FK_TEMPLATE_ATTACHMENTS FOREIGN KEY (TEMPLATE_ID) REFERENCES EMAIL_TEMPLATES(TEMPLATE_ID) ON DELETE CASCADE
            )
            """;

        executeSQL(connection, sql, "EMAIL_TEMPLATE_ATTACHMENTS");
        
        String seqSql = """
            CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_ATTACHMENTS
            START WITH 1
            INCREMENT BY 1
            NOCACHE
            """;
        executeSQL(connection, seqSql, "SEQ_EMAIL_TEMPLATE_ATTACHMENTS");
    }

    private static void createTemplateUsageStats(Connection connection) throws SQLException {
        System.out.println("📊 إنشاء جدول إحصائيات استخدام القوالب...");
        
        String sql = """
            CREATE TABLE EMAIL_TEMPLATE_USAGE_STATS (
                USAGE_ID NUMBER PRIMARY KEY,
                TEMPLATE_ID NUMBER NOT NULL,
                USED_BY NUMBER,
                USAGE_DATE DATE DEFAULT SYSDATE,
                USAGE_TYPE VARCHAR2(20) DEFAULT 'SEND' CHECK (USAGE_TYPE IN ('SEND', 'PREVIEW', 'EDIT', 'COPY')),
                RECIPIENT_COUNT NUMBER DEFAULT 1,
                SUCCESS_COUNT NUMBER DEFAULT 0,
                FAILURE_COUNT NUMBER DEFAULT 0,
                CAMPAIGN_ID NUMBER,
                NOTES NVARCHAR2(1000),
                CONSTRAINT FK_TEMPLATE_USAGE FOREIGN KEY (TEMPLATE_ID) REFERENCES EMAIL_TEMPLATES(TEMPLATE_ID) ON DELETE CASCADE
            )
            """;

        executeSQL(connection, sql, "EMAIL_TEMPLATE_USAGE_STATS");
        
        String seqSql = """
            CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_USAGE_STATS
            START WITH 1
            INCREMENT BY 1
            NOCACHE
            """;
        executeSQL(connection, seqSql, "SEQ_EMAIL_TEMPLATE_USAGE_STATS");
    }

    private static void createTemplateSharing(Connection connection) throws SQLException {
        System.out.println("👥 إنشاء جدول مشاركة القوالب...");
        
        String sql = """
            CREATE TABLE EMAIL_TEMPLATE_SHARING (
                SHARING_ID NUMBER PRIMARY KEY,
                TEMPLATE_ID NUMBER NOT NULL,
                SHARED_BY NUMBER NOT NULL,
                SHARED_WITH NUMBER,
                SHARED_WITH_ROLE NUMBER,
                PERMISSION_TYPE VARCHAR2(20) DEFAULT 'READ' CHECK (PERMISSION_TYPE IN ('READ', 'edit', 'copy', 'delete')),
                SHARED_DATE DATE DEFAULT SYSDATE,
                EXPIRY_DATE DATE,
                IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                CONSTRAINT FK_TEMPLATE_SHARING FOREIGN KEY (TEMPLATE_ID) REFERENCES EMAIL_TEMPLATES(TEMPLATE_ID) ON DELETE CASCADE
            )
            """;

        executeSQL(connection, sql, "EMAIL_TEMPLATE_SHARING");
        
        String seqSql = """
            CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_SHARING
            START WITH 1
            INCREMENT BY 1
            NOCACHE
            """;
        executeSQL(connection, seqSql, "SEQ_EMAIL_TEMPLATE_SHARING");
    }

    private static void createTemplateHistory(Connection connection) throws SQLException {
        System.out.println("📜 إنشاء جدول تاريخ تعديل القوالب...");
        
        String sql = """
            CREATE TABLE EMAIL_TEMPLATE_HISTORY (
                HISTORY_ID NUMBER PRIMARY KEY,
                TEMPLATE_ID NUMBER NOT NULL,
                VERSION_NUMBER NUMBER NOT NULL,
                CHANGE_TYPE VARCHAR2(20) DEFAULT 'UPDATE' CHECK (CHANGE_TYPE IN ('CREATE', 'UPDATE', 'DELETE', 'RESTORE')),
                CHANGED_FIELDS NVARCHAR2(1000),
                OLD_VALUES CLOB,
                NEW_VALUES CLOB,
                CHANGE_REASON NVARCHAR2(500),
                CHANGED_BY NUMBER,
                CHANGE_DATE DATE DEFAULT SYSDATE,
                CONSTRAINT FK_TEMPLATE_HISTORY FOREIGN KEY (TEMPLATE_ID) REFERENCES EMAIL_TEMPLATES(TEMPLATE_ID) ON DELETE CASCADE
            )
            """;

        executeSQL(connection, sql, "EMAIL_TEMPLATE_HISTORY");
        
        String seqSql = """
            CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_HISTORY
            START WITH 1
            INCREMENT BY 1
            NOCACHE
            """;
        executeSQL(connection, seqSql, "SEQ_EMAIL_TEMPLATE_HISTORY");
    }

    private static void insertDefaultData(Connection connection) throws SQLException {
        System.out.println("📝 إدراج البيانات الافتراضية...");
        
        // إدراج فئات افتراضية
        String categorySql = """
            INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, ICON_NAME, COLOR_CODE, SORT_ORDER)
            VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, ?, ?, ?, ?, ?, ?)
            """;
        
        String[][] categories = {
            {"عام", "General", "قوالب عامة للاستخدام اليومي", "📧", "#2196F3", "1"},
            {"تسويق", "Marketing", "قوالب الحملات التسويقية", "📈", "#4CAF50", "2"},
            {"إشعارات", "Notifications", "قوالب الإشعارات والتنبيهات", "🔔", "#FF9800", "3"},
            {"فواتير", "Invoices", "قوالب الفواتير والمعاملات المالية", "💰", "#9C27B0", "4"},
            {"ترحيب", "Welcome", "قوالب الترحيب بالعملاء الجدد", "👋", "#E91E63", "5"},
            {"تأكيد", "Confirmation", "قوالب تأكيد العمليات", "✅", "#00BCD4", "6"}
        };
        
        try (PreparedStatement stmt = connection.prepareStatement(categorySql)) {
            for (String[] category : categories) {
                stmt.setString(1, category[0]);
                stmt.setString(2, category[1]);
                stmt.setString(3, category[2]);
                stmt.setString(4, category[3]);
                stmt.setString(5, category[4]);
                stmt.setInt(6, Integer.parseInt(category[5]));
                stmt.executeUpdate();
            }
        }
        
        System.out.println("✅ تم إدراج " + categories.length + " فئة افتراضية");
    }

    private static void executeSQL(Connection connection, String sql, String tableName) {
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(sql);
            System.out.println("✅ تم إنشاء: " + tableName);
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) { // ORA-00955: name is already used by an existing object
                System.out.println("⚠️  موجود مسبقاً: " + tableName);
            } else {
                System.err.println("❌ خطأ في إنشاء " + tableName + ": " + e.getMessage());
                throw new RuntimeException(e);
            }
        }
    }
}
