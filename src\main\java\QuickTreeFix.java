import java.sql.*;
import java.util.Properties;

public class QuickTreeFix {
    public static void main(String[] args) {
        try {
            Connection conn = getConnection();
            
            // إصلاح فوري للهيكل
            System.out.println("🔧 إصلاح فوري للهيكل...");
            
            // حذف جميع البيانات وإعادة إنشائها
            conn.createStatement().execute("DELETE FROM ERP_SYSTEM_TREE WHERE TREE_ID > 1");
            
            // إدراج الفئات الرئيسية
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (2, 1, 'إدارة الأصناف', 'Items Management', 'CATEGORY', 1, 1)");
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (3, 1, 'إدارة المستخدمين', 'User Management', 'CATEGORY', 2, 1)");
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (4, 1, 'الإعدادات العامة', 'General Settings', 'CATEGORY', 3, 1)");
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (5, 1, 'التقارير', 'Reports', 'CATEGORY', 4, 1)");
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) VALUES (6, 1, 'أدوات النظام', 'System Tools', 'CATEGORY', 5, 1)");
            
            // إدراج نوافذ إدارة الأصناف
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (2, 'بيانات الأصناف الحقيقية', 'Real Item Data', 'WINDOW', 'RealItemDataWindow', 1, 2)");
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (2, 'بيانات الأصناف الشاملة', 'Comprehensive Item Data', 'WINDOW', 'ComprehensiveItemDataWindow', 2, 2)");
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (2, 'مجموعات الأصناف', 'Item Groups', 'WINDOW', 'ItemGroupsManagementWindow', 3, 2)");
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (2, 'وحدات القياس', 'Measurement Units', 'WINDOW', 'MeasurementUnitsWindow', 4, 2)");
            
            // إدراج نوافذ إدارة المستخدمين
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (3, 'إدارة المستخدمين', 'User Management', 'WINDOW', 'UserManagementWindow', 1, 2)");
            
            // إدراج نوافذ الإعدادات
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (4, 'الإعدادات العامة', 'General Settings', 'WINDOW', 'GeneralSettingsWindow', 1, 2)");
            
            // إدراج أدوات النظام
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (6, 'فحص النظام الشامل', 'System Audit', 'TOOL', 'CompleteOracleSystemTest', 1, 2)");
            conn.createStatement().execute("INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL) VALUES (6, 'إدارة الاتصالات', 'Connection Manager', 'TOOL', 'TNSConnectionManager', 2, 2)");
            
            conn.close();
            System.out.println("✅ تم الإصلاح الفوري!");
            
            // اختبار
            SystemTreeManager manager = SystemTreeManager.getInstance();
            manager.printSystemTree();
            manager.close();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
}
