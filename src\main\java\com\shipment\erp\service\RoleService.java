package com.shipment.erp.service;

import com.shipment.erp.model.Role;
import java.util.List;

/**
 * خدمة إدارة الأدوار
 * Role Management Service
 */
public interface RoleService extends BaseService<Role> {
    
    /**
     * البحث عن الأدوار النشطة
     * Find active roles
     */
    List<Role> findActiveRoles();
    
    /**
     * البحث عن دور بالاسم
     * Find role by name
     */
    Role findByName(String name);
    
    /**
     * البحث عن الأدوار بالاسم (يحتوي على)
     * Find roles by name containing
     */
    List<Role> findByNameContaining(String name);
    
    /**
     * التحقق من وجود اسم الدور
     * Check if role name exists
     */
    boolean existsByName(String name);
    
    /**
     * تفعيل/إلغاء تفعيل الدور
     * Activate/Deactivate role
     */
    void toggleActive(Long roleId);
    
    /**
     * الحصول على عدد المستخدمين للدور
     * Get users count for role
     */
    long getUsersCount(Long roleId);
    
    /**
     * التحقق من إمكانية حذف الدور
     * Check if role can be deleted
     */
    boolean canDelete(Long roleId);
    
    /**
     * الحصول على الأدوار الافتراضية
     * Get default roles
     */
    List<Role> getDefaultRoles();
}
