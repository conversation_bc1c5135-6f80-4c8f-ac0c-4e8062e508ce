import java.sql.*;

/**
 * إنشاء جدول المظاهر الشامل الجديد
 * Create New Comprehensive Theme Table
 */
public class CreateComprehensiveThemeTable {
    
    public static void main(String[] args) {
        System.out.println("🎨 إنشاء جدول المظاهر الشامل الجديد...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            createComprehensiveThemeTable(connection);
            insertDefaultThemeSettings(connection);
            
            connection.close();
            System.out.println("✅ تم إنشاء جدول المظاهر الشامل بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء جدول المظاهر: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * إنشاء جدول المظاهر الشامل الجديد
     */
    private static void createComprehensiveThemeTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول ERP_COMPREHENSIVE_THEMES...");
        
        // حذف الجدول إذا كان موجوداً
        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_COMPREHENSIVE_THEMES CASCADE CONSTRAINTS");
            System.out.println("🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // الجدول غير موجود - لا مشكلة
            System.out.println("ℹ️ الجدول غير موجود مسبقاً");
        }
        
        // إنشاء الجدول الجديد
        String createTableSQL = """
            CREATE TABLE ERP_COMPREHENSIVE_THEMES (
                THEME_ID NUMBER PRIMARY KEY,
                USER_ID VARCHAR2(50) DEFAULT 'SYSTEM',
                THEME_NAME VARCHAR2(100) NOT NULL,
                THEME_DISPLAY_NAME NVARCHAR2(200),
                THEME_CATEGORY VARCHAR2(50),
                THEME_CLASS_NAME VARCHAR2(500),
                IS_DARK_THEME CHAR(1) DEFAULT 'N',
                IS_AVAILABLE CHAR(1) DEFAULT 'Y',
                THEME_DESCRIPTION NCLOB,
                THEME_AUTHOR VARCHAR2(100),
                THEME_VERSION VARCHAR2(20),
                THEME_LICENSE VARCHAR2(100),
                IS_CURRENT_THEME CHAR(1) DEFAULT 'N',
                LAST_APPLIED_DATE DATE,
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50) DEFAULT USER,
                MODIFIED_DATE DATE DEFAULT SYSDATE,
                MODIFIED_BY VARCHAR2(50) DEFAULT USER,
                NOTES NCLOB
            )
            """;
        
        Statement stmt = connection.createStatement();
        stmt.executeUpdate(createTableSQL);
        System.out.println("✅ تم إنشاء جدول ERP_COMPREHENSIVE_THEMES");
        
        // إنشاء sequence للـ ID
        try {
            stmt.executeUpdate("DROP SEQUENCE ERP_COMPREHENSIVE_THEMES_SEQ");
        } catch (SQLException e) {
            // Sequence غير موجود
        }
        
        stmt.executeUpdate("CREATE SEQUENCE ERP_COMPREHENSIVE_THEMES_SEQ START WITH 1 INCREMENT BY 1");
        System.out.println("✅ تم إنشاء SEQUENCE للجدول");
        
        // إنشاء فهارس
        stmt.executeUpdate("CREATE INDEX IDX_COMP_THEMES_USER ON ERP_COMPREHENSIVE_THEMES(USER_ID)");
        stmt.executeUpdate("CREATE INDEX IDX_COMP_THEMES_NAME ON ERP_COMPREHENSIVE_THEMES(THEME_NAME)");
        stmt.executeUpdate("CREATE INDEX IDX_COMP_THEMES_CATEGORY ON ERP_COMPREHENSIVE_THEMES(THEME_CATEGORY)");
        stmt.executeUpdate("CREATE INDEX IDX_COMP_THEMES_CURRENT ON ERP_COMPREHENSIVE_THEMES(IS_CURRENT_THEME)");
        System.out.println("✅ تم إنشاء الفهارس");
        
        stmt.close();
    }
    
    /**
     * إدراج الإعدادات الافتراضية للمظاهر
     */
    private static void insertDefaultThemeSettings(Connection connection) throws SQLException {
        System.out.println("📝 إدراج المظاهر الافتراضية...");
        
        String insertSQL = """
            INSERT INTO ERP_COMPREHENSIVE_THEMES 
            (THEME_ID, THEME_NAME, THEME_DISPLAY_NAME, THEME_CATEGORY, THEME_CLASS_NAME, 
             IS_DARK_THEME, THEME_DESCRIPTION, THEME_AUTHOR, THEME_VERSION, THEME_LICENSE, IS_CURRENT_THEME)
            VALUES (ERP_COMPREHENSIVE_THEMES_SEQ.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
        
        PreparedStatement pstmt = connection.prepareStatement(insertSQL);
        
        // FlatLaf Core Themes
        insertTheme(pstmt, "FlatLaf Light", "FlatLaf فاتح", "FlatLaf Core", 
                   "com.formdev.flatlaf.FlatLightLaf", "N", 
                   "مظهر فاتح حديث وأنيق من FlatLaf", "FormDev", "3.2.5", "Apache 2.0", "Y");
        
        insertTheme(pstmt, "FlatLaf Dark", "FlatLaf مظلم", "FlatLaf Core", 
                   "com.formdev.flatlaf.FlatDarkLaf", "Y", 
                   "مظهر مظلم حديث وأنيق من FlatLaf", "FormDev", "3.2.5", "Apache 2.0", "N");
        
        insertTheme(pstmt, "FlatLaf IntelliJ", "FlatLaf IntelliJ", "FlatLaf Core", 
                   "com.formdev.flatlaf.FlatIntelliJLaf", "N", 
                   "مظهر IntelliJ IDEA الكلاسيكي", "FormDev", "3.2.5", "Apache 2.0", "N");
        
        insertTheme(pstmt, "FlatLaf Darcula", "FlatLaf Darcula", "FlatLaf Core", 
                   "com.formdev.flatlaf.FlatDarculaLaf", "Y", 
                   "مظهر Darcula المظلم الشهير", "FormDev", "3.2.5", "Apache 2.0", "N");
        
        // IntelliJ Themes - Light
        insertTheme(pstmt, "Arc Theme", "مظهر Arc", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatArcIJTheme", "N", 
                   "مظهر Arc الحديث والأنيق", "Arc Team", "1.0", "GPL 3.0", "N");
        
        insertTheme(pstmt, "Cyan Light Theme", "مظهر Cyan الفاتح", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatCyanLightIJTheme", "N", 
                   "مظهر Cyan الفاتح المنعش", "Cyan Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Gray Theme", "المظهر الرمادي", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatGrayIJTheme", "N", 
                   "مظهر رمادي هادئ ومريح", "Gray Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Nord Theme", "مظهر Nord", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatNordIJTheme", "N", 
                   "مظهر Nord الاسكندنافي الهادئ", "Nord Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Solarized Light Theme", "مظهر Solarized الفاتح", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatSolarizedLightIJTheme", "N", 
                   "مظهر Solarized الفاتح المتوازن", "Solarized Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Vuesion Theme", "مظهر Vuesion", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatVuesionIJTheme", "N", 
                   "مظهر Vuesion الحديث والمتطور", "Vuesion Team", "1.0", "MIT", "N");
        
        // IntelliJ Themes - Dark
        insertTheme(pstmt, "Arc Dark Theme", "مظهر Arc المظلم", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatArcDarkIJTheme", "Y", 
                   "مظهر Arc المظلم الجميل", "Arc Team", "1.0", "GPL 3.0", "N");
        
        insertTheme(pstmt, "Carbon Theme", "مظهر Carbon", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatCarbonIJTheme", "Y", 
                   "مظهر Carbon المظلم الأنيق", "Carbon Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Dracula Theme", "مظهر Dracula", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatDraculaIJTheme", "Y", 
                   "مظهر Dracula الشهير والمحبوب", "Dracula Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "One Dark Theme", "مظهر One Dark", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatOneDarkIJTheme", "Y", 
                   "مظهر One Dark الشهير من Atom", "One Dark Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Solarized Dark Theme", "مظهر Solarized المظلم", "IntelliJ Themes", 
                   "com.formdev.flatlaf.intellijthemes.FlatSolarizedDarkIJTheme", "Y", 
                   "مظهر Solarized المظلم المتوازن", "Solarized Team", "1.0", "MIT", "N");
        
        // JTattoo Themes - Light
        insertTheme(pstmt, "Acryl", "مظهر Acryl", "JTattoo", 
                   "com.jtattoo.plaf.acryl.AcrylLookAndFeel", "N", 
                   "مظهر Acryl الشفاف والأنيق", "JTattoo", "1.6.13", "GPL 2.0", "N");
        
        insertTheme(pstmt, "Aero", "مظهر Aero", "JTattoo", 
                   "com.jtattoo.plaf.aero.AeroLookAndFeel", "N", 
                   "مظهر Aero الحديث والمتطور", "JTattoo", "1.6.13", "GPL 2.0", "N");
        
        insertTheme(pstmt, "Aluminium", "مظهر الألمنيوم", "JTattoo", 
                   "com.jtattoo.plaf.aluminium.AluminiumLookAndFeel", "N", 
                   "مظهر معدني أنيق بلمسة ألمنيوم", "JTattoo", "1.6.13", "GPL 2.0", "N");
        
        insertTheme(pstmt, "Mint", "مظهر النعناع", "JTattoo", 
                   "com.jtattoo.plaf.mint.MintLookAndFeel", "N", 
                   "مظهر النعناع المنعش والهادئ", "JTattoo", "1.6.13", "GPL 2.0", "N");
        
        // JTattoo Themes - Dark
        insertTheme(pstmt, "Graphite", "مظهر الجرافيت", "JTattoo", 
                   "com.jtattoo.plaf.graphite.GraphiteLookAndFeel", "Y", 
                   "مظهر الجرافيت المظلم والأنيق", "JTattoo", "1.6.13", "GPL 2.0", "N");
        
        insertTheme(pstmt, "HiFi", "مظهر HiFi", "JTattoo", 
                   "com.jtattoo.plaf.hifi.HiFiLookAndFeel", "Y", 
                   "مظهر HiFi المتقدم والمميز", "JTattoo", "1.6.13", "GPL 2.0", "N");
        
        insertTheme(pstmt, "Noire", "مظهر Noire", "JTattoo", 
                   "com.jtattoo.plaf.noire.NoireLookAndFeel", "Y", 
                   "مظهر Noire الأسود الأنيق", "JTattoo", "1.6.13", "GPL 2.0", "N");
        
        // System Themes
        insertTheme(pstmt, "System Default", "مظهر النظام", "System", 
                   "", "N", 
                   "مظهر النظام الافتراضي", "System", "Default", "System", "N");
        
        insertTheme(pstmt, "Metal", "مظهر Metal", "System", 
                   "javax.swing.plaf.metal.MetalLookAndFeel", "N", 
                   "مظهر Java Metal الكلاسيكي", "Oracle", "Java", "GPL 2.0", "N");
        
        insertTheme(pstmt, "Nimbus", "مظهر Nimbus", "System", 
                   "javax.swing.plaf.nimbus.NimbusLookAndFeel", "N", 
                   "مظهر Nimbus الحديث والجميل", "Oracle", "Java", "GPL 2.0", "N");
        
        pstmt.close();
        
        System.out.println("✅ تم إدراج " + getThemeCount(connection) + " مظهر في قاعدة البيانات");
    }
    
    /**
     * إدراج مظهر واحد
     */
    private static void insertTheme(PreparedStatement pstmt, String name, String displayName, 
                                   String category, String className, String isDark, 
                                   String description, String author, String version, 
                                   String license, String isCurrent) throws SQLException {
        pstmt.setString(1, name);
        pstmt.setString(2, displayName);
        pstmt.setString(3, category);
        pstmt.setString(4, className);
        pstmt.setString(5, isDark);
        pstmt.setString(6, description);
        pstmt.setString(7, author);
        pstmt.setString(8, version);
        pstmt.setString(9, license);
        pstmt.setString(10, isCurrent);
        pstmt.executeUpdate();
    }
    
    /**
     * عد المظاهر في قاعدة البيانات
     */
    private static int getThemeCount(Connection connection) throws SQLException {
        Statement stmt = connection.createStatement();
        ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM ERP_COMPREHENSIVE_THEMES");
        rs.next();
        int count = rs.getInt(1);
        rs.close();
        stmt.close();
        return count;
    }
}
