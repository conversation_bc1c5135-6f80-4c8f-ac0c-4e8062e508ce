-- إن<PERSON><PERSON><PERSON> جداول نظام إدارة القوالب
-- Create Email Templates System Tables

-- 1. جدول فئات القوالب
CREATE TABLE EMAIL_TEMPLATE_CATEGORIES (
    CATEGORY_ID NUMBER PRIMARY KEY,
    CATEGORY_NAME_AR NVARCHAR2(100) NOT NULL,
    CATEGORY_NAME_EN VARCHAR2(100) NOT NULL,
    DESCRIPTION NVARCHAR2(500),
    ICON_NAME VARCHAR2(50),
    COLOR_CODE VARCHAR2(7),
    SORT_ORDER NUMBER DEFAULT 0,
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    CREATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT UK_TEMPLATE_CAT_NAME_AR UNIQUE (CATEGORY_NAME_AR),
    CONSTRAINT UK_TEMPLATE_CAT_NAME_EN UNIQUE (CATEGORY_NAME_EN)
);

-- 2. إنشاء sequence لفئات القوالب
CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_CATEGORIES
START WITH 1
INCREMENT BY 1
NOCACHE;

-- 3. جدول القوالب الرئيسي
CREATE TABLE EMAIL_TEMPLATES (
    TEMPLATE_ID NUMBER PRIMARY KEY,
    TEMPLATE_NAME_AR NVARCHAR2(200) NOT NULL,
    TEMPLATE_NAME_EN VARCHAR2(200) NOT NULL,
    CATEGORY_ID NUMBER,
    SUBJECT_AR NVARCHAR2(500),
    SUBJECT_EN VARCHAR2(500),
    BODY_HTML CLOB,
    BODY_TEXT CLOB,
    TEMPLATE_TYPE VARCHAR2(20) DEFAULT 'STANDARD' CHECK (TEMPLATE_TYPE IN ('STANDARD', 'CAMPAIGN', 'NOTIFICATION', 'SIGNATURE')),
    LANGUAGE_CODE VARCHAR2(5) DEFAULT 'AR',
    IS_HTML CHAR(1) DEFAULT 'Y' CHECK (IS_HTML IN ('Y', 'N')),
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    IS_PUBLIC CHAR(1) DEFAULT 'N' CHECK (IS_PUBLIC IN ('Y', 'N')),
    USAGE_COUNT NUMBER DEFAULT 0,
    LAST_USED_DATE DATE,
    TAGS NVARCHAR2(1000),
    CREATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_TEMPLATE_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES EMAIL_TEMPLATE_CATEGORIES(CATEGORY_ID)
);

-- 4. إنشاء sequence للقوالب
CREATE SEQUENCE SEQ_EMAIL_TEMPLATES
START WITH 1
INCREMENT BY 1
NOCACHE;

-- 5. جدول متغيرات القوالب
CREATE TABLE EMAIL_TEMPLATE_VARIABLES (
    VARIABLE_ID NUMBER PRIMARY KEY,
    TEMPLATE_ID NUMBER NOT NULL,
    VARIABLE_NAME VARCHAR2(100) NOT NULL,
    VARIABLE_LABEL_AR NVARCHAR2(200),
    VARIABLE_LABEL_EN VARCHAR2(200),
    VARIABLE_TYPE VARCHAR2(20) DEFAULT 'TEXT' CHECK (VARIABLE_TYPE IN ('TEXT', 'NUMBER', 'DATE', 'EMAIL', 'URL', 'PHONE')),
    DEFAULT_VALUE NVARCHAR2(1000),
    IS_REQUIRED CHAR(1) DEFAULT 'N' CHECK (IS_REQUIRED IN ('Y', 'N')),
    SORT_ORDER NUMBER DEFAULT 0,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_TEMPLATE_VARIABLES FOREIGN KEY (TEMPLATE_ID) REFERENCES EMAIL_TEMPLATES(TEMPLATE_ID) ON DELETE CASCADE,
    CONSTRAINT UK_TEMPLATE_VARIABLE UNIQUE (TEMPLATE_ID, VARIABLE_NAME)
);

-- 6. إنشاء sequence لمتغيرات القوالب
CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_VARIABLES
START WITH 1
INCREMENT BY 1
NOCACHE;

-- 7. إدراج فئات افتراضية
INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, ICON_NAME, COLOR_CODE, SORT_ORDER)
VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'عام', 'General', 'قوالب عامة للاستخدام اليومي', '📧', '#2196F3', 1);

INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, ICON_NAME, COLOR_CODE, SORT_ORDER)
VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'تسويق', 'Marketing', 'قوالب الحملات التسويقية', '📈', '#4CAF50', 2);

INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, ICON_NAME, COLOR_CODE, SORT_ORDER)
VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'إشعارات', 'Notifications', 'قوالب الإشعارات والتنبيهات', '🔔', '#FF9800', 3);

INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, ICON_NAME, COLOR_CODE, SORT_ORDER)
VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'فواتير', 'Invoices', 'قوالب الفواتير والمعاملات المالية', '💰', '#9C27B0', 4);

INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, ICON_NAME, COLOR_CODE, SORT_ORDER)
VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'ترحيب', 'Welcome', 'قوالب الترحيب بالعملاء الجدد', '👋', '#E91E63', 5);

INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, ICON_NAME, COLOR_CODE, SORT_ORDER)
VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'تأكيد', 'Confirmation', 'قوالب تأكيد العمليات', '✅', '#00BCD4', 6);

-- 8. إدراج قوالب تجريبية
INSERT INTO EMAIL_TEMPLATES (TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN, CATEGORY_ID, SUBJECT_AR, SUBJECT_EN, BODY_HTML, BODY_TEXT, TEMPLATE_TYPE, LANGUAGE_CODE)
VALUES (SEQ_EMAIL_TEMPLATES.NEXTVAL, 'قالب ترحيب أساسي', 'Basic Welcome Template', 5, 'مرحباً بك معنا!', 'Welcome to our service!', 
'<h2>مرحباً بك!</h2><p>نحن سعداء بانضمامك إلينا. نتطلع للعمل معك.</p>', 
'مرحباً بك! نحن سعداء بانضمامك إلينا. نتطلع للعمل معك.', 'STANDARD', 'AR');

INSERT INTO EMAIL_TEMPLATES (TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN, CATEGORY_ID, SUBJECT_AR, SUBJECT_EN, BODY_HTML, BODY_TEXT, TEMPLATE_TYPE, LANGUAGE_CODE)
VALUES (SEQ_EMAIL_TEMPLATES.NEXTVAL, 'قالب إشعار عام', 'General Notification Template', 3, 'إشعار مهم', 'Important Notification', 
'<h3>إشعار</h3><p>لديك إشعار جديد يتطلب انتباهك.</p>', 
'إشعار: لديك إشعار جديد يتطلب انتباهك.', 'NOTIFICATION', 'AR');

COMMIT;
