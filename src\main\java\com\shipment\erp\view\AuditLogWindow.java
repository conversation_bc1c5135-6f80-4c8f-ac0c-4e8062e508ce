package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.text.SimpleDateFormat;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFileChooser;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSeparator;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumnModel;
import com.shipment.erp.model.AuditLog;
import com.shipment.erp.service.AuditLogService;

/**
 * نافذة سجل التدقيق Audit Log Window
 */
public class AuditLogWindow extends JDialog {

    private Font arabicFont;
    private JTable auditLogTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> actionFilterCombo;
    private JComboBox<String> tableFilterCombo;
    private JComboBox<String> userFilterCombo;

    private AuditLogService auditLogService;
    private List<AuditLog> auditLogList;

    public AuditLogWindow(JFrame parent) {
        super(parent, "سجل التدقيق", true);

        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeServices();
        initializeComponents();
        setupLayout();
        loadAuditLogData();

        setSize(1400, 800);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(true);
        setMinimumSize(new Dimension(1200, 600));
    }

    private void initializeServices() {
        // TODO: Initialize AuditLogService through dependency injection
        // auditLogService = ApplicationContext.getBean(AuditLogService.class);
    }

    private void initializeComponents() {
        // شريط الأدوات العلوي
        JPanel toolbarPanel = createToolbarPanel();

        // جدول سجل التدقيق
        createAuditLogTable();
        JScrollPane tableScrollPane = new JScrollPane(auditLogTable);
        tableScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();

        // تخطيط النافذة
        setLayout(new BorderLayout());
        add(toolbarPanel, BorderLayout.NORTH);
        add(tableScrollPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }

    private JPanel createToolbarPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // أزرار العمليات
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton viewDetailsButton = new JButton("عرض التفاصيل");
        viewDetailsButton.setFont(arabicFont);
        viewDetailsButton.addActionListener(e -> viewDetails());

        JButton exportButton = new JButton("تصدير");
        exportButton.setFont(arabicFont);
        exportButton.addActionListener(e -> exportAuditLog());

        JButton cleanOldButton = new JButton("تنظيف السجلات القديمة");
        cleanOldButton.setFont(arabicFont);
        cleanOldButton.addActionListener(e -> cleanOldLogs());

        JButton refreshButton = new JButton("تحديث");
        refreshButton.setFont(arabicFont);
        refreshButton.addActionListener(e -> loadAuditLogData());

        buttonsPanel.add(viewDetailsButton);
        buttonsPanel.add(exportButton);
        buttonsPanel.add(cleanOldButton);
        buttonsPanel.add(new JSeparator(SwingConstants.VERTICAL));
        buttonsPanel.add(refreshButton);

        // شريط البحث والفلترة
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);

        searchField = new JTextField(15);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                filterAuditLog();
            }
        });

        JLabel actionLabel = new JLabel("العملية:");
        actionLabel.setFont(arabicFont);

        actionFilterCombo = new JComboBox<>(
                new String[] {"الكل", "إنشاء", "تعديل", "حذف", "عرض", "تسجيل دخول", "تسجيل خروج"});
        actionFilterCombo.setFont(arabicFont);
        actionFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        actionFilterCombo.addActionListener(e -> filterAuditLog());

        JLabel tableLabel = new JLabel("الجدول:");
        tableLabel.setFont(arabicFont);

        tableFilterCombo = new JComboBox<>(
                new String[] {"الكل", "COMPANIES", "BRANCHES", "USERS", "ROLES", "PERMISSIONS"});
        tableFilterCombo.setFont(arabicFont);
        tableFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tableFilterCombo.addActionListener(e -> filterAuditLog());

        JLabel userLabel = new JLabel("المستخدم:");
        userLabel.setFont(arabicFont);

        userFilterCombo = new JComboBox<>(new String[] {"الكل", "admin", "user1", "user2"});
        userFilterCombo.setFont(arabicFont);
        userFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        userFilterCombo.addActionListener(e -> filterAuditLog());

        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        searchPanel.add(Box.createHorizontalStrut(5));
        searchPanel.add(actionLabel);
        searchPanel.add(actionFilterCombo);
        searchPanel.add(Box.createHorizontalStrut(5));
        searchPanel.add(tableLabel);
        searchPanel.add(tableFilterCombo);
        searchPanel.add(Box.createHorizontalStrut(5));
        searchPanel.add(userLabel);
        searchPanel.add(userFilterCombo);

        panel.add(buttonsPanel, BorderLayout.EAST);
        panel.add(searchPanel, BorderLayout.WEST);

        return panel;
    }

    private void createAuditLogTable() {
        String[] columnNames = {"الرقم", "المستخدم", "العملية", "الجدول", "رقم السجل", "عنوان IP",
                "التاريخ والوقت", "تفاصيل"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        auditLogTable = new JTable(tableModel);
        auditLogTable.setFont(arabicFont);
        auditLogTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        auditLogTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        auditLogTable.setRowHeight(25);

        // تخصيص عرض الأعمدة
        TableColumnModel columnModel = auditLogTable.getColumnModel();
        columnModel.getColumn(0).setPreferredWidth(60); // الرقم
        columnModel.getColumn(1).setPreferredWidth(100); // المستخدم
        columnModel.getColumn(2).setPreferredWidth(80); // العملية
        columnModel.getColumn(3).setPreferredWidth(100); // الجدول
        columnModel.getColumn(4).setPreferredWidth(80); // رقم السجل
        columnModel.getColumn(5).setPreferredWidth(120); // عنوان IP
        columnModel.getColumn(6).setPreferredWidth(150); // التاريخ والوقت
        columnModel.getColumn(7).setPreferredWidth(200); // تفاصيل

        // تخصيص رأس الجدول
        JTableHeader header = auditLogTable.getTableHeader();
        header.setFont(arabicFont);
        header.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إضافة مستمع النقر المزدوج
        auditLogTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    viewDetails();
                }
            }
        });
    }

    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);

        panel.add(statusLabel);
        return panel;
    }

    private void loadAuditLogData() {
        // TODO: Load data from AuditLogService
        // auditLogList = auditLogService.findAll();

        // بيانات تجريبية
        auditLogList = createSampleData();
        updateTableData();
    }

    private List<AuditLog> createSampleData() {
        AuditLog log1 = new AuditLog();
        log1.setId(1L);
        log1.setUserId(1L);
        log1.setAction("تسجيل دخول");
        log1.setTableName("USERS");
        log1.setRecordId(1L);
        log1.setIpAddress("*************");
        log1.setUserAgent("Mozilla/5.0");
        log1.setActionDate(java.time.LocalDateTime.now());

        AuditLog log2 = new AuditLog();
        log2.setId(2L);
        log2.setUserId(1L);
        log2.setAction("إنشاء");
        log2.setTableName("COMPANIES");
        log2.setRecordId(1L);
        log2.setOldValues("");
        log2.setNewValues("{\"name\":\"شركة جديدة\",\"active\":true}");
        log2.setIpAddress("*************");
        log2.setUserAgent("Mozilla/5.0");
        log2.setActionDate(java.time.LocalDateTime.now());

        AuditLog log3 = new AuditLog();
        log3.setId(3L);
        log3.setUserId(1L);
        log3.setAction("تعديل");
        log3.setTableName("BRANCHES");
        log3.setRecordId(1L);
        log3.setOldValues("{\"name\":\"الفرع القديم\"}");
        log3.setNewValues("{\"name\":\"الفرع الجديد\"}");
        log3.setIpAddress("*************");
        log3.setUserAgent("Mozilla/5.0");
        log3.setActionDate(java.time.LocalDateTime.now());

        return java.util.Arrays.asList(log1, log2, log3);
    }

    private void updateTableData() {
        tableModel.setRowCount(0);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (AuditLog auditLog : auditLogList) {
            String details = "";
            if (auditLog.getOldValues() != null && !auditLog.getOldValues().isEmpty()) {
                details = "تم التعديل";
            } else if (auditLog.getNewValues() != null && !auditLog.getNewValues().isEmpty()) {
                details = "تم الإنشاء";
            } else {
                details = auditLog.getAction();
            }

            Object[] row = {auditLog.getId(), "المستخدم " + auditLog.getUserId(), // TODO: Get
                                                                                  // actual username
                    auditLog.getAction(), auditLog.getTableName(), auditLog.getRecordId(),
                    auditLog.getIpAddress(),
                    auditLog.getActionDate() != null ? dateFormat.format(auditLog.getActionDate())
                            : "",
                    details};
            tableModel.addRow(row);
        }
    }

    private void filterAuditLog() {
        // TODO: Implement filtering logic
        updateTableData();
    }

    private void viewDetails() {
        int selectedRow = auditLogTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار سجل لعرض تفاصيله", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        AuditLog selectedLog = auditLogList.get(selectedRow);
        AuditLogDetailsDialog dialog = new AuditLogDetailsDialog(this, selectedLog);
        dialog.setVisible(true);
    }

    private void exportAuditLog() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("تصدير سجل التدقيق");
        fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);

        int result = fileChooser.showSaveDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            // TODO: Export audit log to file
            JOptionPane.showMessageDialog(this, "تم تصدير سجل التدقيق بنجاح", "نجح التصدير",
                    JOptionPane.INFORMATION_MESSAGE);
        }
    }

    private void cleanOldLogs() {
        String[] options = {"30 يوم", "60 يوم", "90 يوم", "180 يوم", "سنة واحدة"};
        String selected = (String) JOptionPane.showInputDialog(this,
                "اختر المدة لحذف السجلات الأقدم منها:", "تنظيف السجلات القديمة",
                JOptionPane.QUESTION_MESSAGE, null, options, options[2]);

        if (selected != null) {
            int result = JOptionPane.showConfirmDialog(this,
                    "هل أنت متأكد من حذف جميع السجلات الأقدم من " + selected + "؟\n"
                            + "هذه العملية لا يمكن التراجع عنها",
                    "تأكيد الحذف", JOptionPane.YES_NO_OPTION);

            if (result == JOptionPane.YES_OPTION) {
                // TODO: Clean old logs using AuditLogService
                JOptionPane.showMessageDialog(this, "تم تنظيف السجلات القديمة بنجاح", "نجح التنظيف",
                        JOptionPane.INFORMATION_MESSAGE);
                loadAuditLogData();
            }
        }
    }
}
