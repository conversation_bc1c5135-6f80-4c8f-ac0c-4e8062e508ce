@echo off
echo ========================================
echo    ENHANCED LIBRARIES DOWNLOADER
echo    تحميل المكتبات المحسنة
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Starting enhanced libraries download...
echo [معلومات] بدء تحميل المكتبات المحسنة...
echo.

echo ========================================
echo    PHASE 1: BACKUP EXISTING LIBRARIES
echo    المرحلة 1: نسخ احتياطي للمكتبات الحالية
echo ========================================

if not exist "lib-backup" mkdir lib-backup
echo [1] Creating backup of existing libraries...

if exist "lib\*.jar" (
    copy "lib\*.jar" "lib-backup\" >nul 2>&1
    echo OK: Existing libraries backed up
) else (
    echo INFO: No existing libraries to backup
)

echo.
echo ========================================
echo    PHASE 2: DOWNLOAD SECURITY LIBRARIES
echo    المرحلة 2: تحميل مكتبات الأمان
echo ========================================

echo [2] Downloading security and encryption libraries...

echo Downloading Jasypt for password encryption...
curl -L -o "lib\jasypt-1.9.3.jar" "https://repo1.maven.org/maven2/org/jasypt/jasypt/1.9.3/jasypt-1.9.3.jar"

echo Downloading Apache Commons Codec for encoding...
curl -L -o "lib\commons-codec-1.15.jar" "https://repo1.maven.org/maven2/commons-codec/commons-codec/1.15/commons-codec-1.15.jar"

echo Downloading Bouncy Castle for advanced encryption...
curl -L -o "lib\bcprov-jdk15on-1.70.jar" "https://repo1.maven.org/maven2/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar"

echo.
echo ========================================
echo    PHASE 3: DOWNLOAD CONNECTION POOLING
echo    المرحلة 3: تحميل مكتبات تجميع الاتصالات
echo ========================================

echo [3] Downloading connection pooling libraries...

echo Downloading HikariCP for connection pooling...
curl -L -o "lib\HikariCP-5.0.1.jar" "https://repo1.maven.org/maven2/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar"

echo Downloading Apache Commons DBCP2...
curl -L -o "lib\commons-dbcp2-2.9.0.jar" "https://repo1.maven.org/maven2/org/apache/commons/commons-dbcp2/2.9.0/commons-dbcp2-2.9.0.jar"

echo Downloading Apache Commons Pool2...
curl -L -o "lib\commons-pool2-2.11.1.jar" "https://repo1.maven.org/maven2/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar"

echo.
echo ========================================
echo    PHASE 4: DOWNLOAD MONITORING TOOLS
echo    المرحلة 4: تحميل أدوات المراقبة
echo ========================================

echo [4] Downloading monitoring and logging libraries...

echo Downloading Logback for advanced logging...
curl -L -o "lib\logback-classic-1.4.8.jar" "https://repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.4.8/logback-classic-1.4.8.jar"
curl -L -o "lib\logback-core-1.4.8.jar" "https://repo1.maven.org/maven2/ch/qos/logback/logback-core/1.4.8/logback-core-1.4.8.jar"

echo Downloading Micrometer for metrics...
curl -L -o "lib\micrometer-core-1.11.2.jar" "https://repo1.maven.org/maven2/io/micrometer/micrometer-core/1.11.2/micrometer-core-1.11.2.jar"

echo.
echo ========================================
echo    PHASE 5: DOWNLOAD CONFIGURATION TOOLS
echo    المرحلة 5: تحميل أدوات التكوين
echo ========================================

echo [5] Downloading configuration management libraries...

echo Downloading Apache Commons Configuration...
curl -L -o "lib\commons-configuration2-2.9.0.jar" "https://repo1.maven.org/maven2/org/apache/commons/commons-configuration2/2.9.0/commons-configuration2-2.9.0.jar"

echo Downloading Apache Commons Lang3...
curl -L -o "lib\commons-lang3-3.12.0.jar" "https://repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar"

echo Downloading Apache Commons IO...
curl -L -o "lib\commons-io-2.11.0.jar" "https://repo1.maven.org/maven2/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar"

echo.
echo ========================================
echo    PHASE 6: DOWNLOAD VALIDATION TOOLS
echo    المرحلة 6: تحميل أدوات التحقق
echo ========================================

echo [6] Downloading validation and utility libraries...

echo Downloading Apache Commons Validator...
curl -L -o "lib\commons-validator-1.7.jar" "https://repo1.maven.org/maven2/commons-validator/commons-validator/1.7/commons-validator-1.7.jar"

echo Downloading Google Guava for utilities...
curl -L -o "lib\guava-32.1.1-jre.jar" "https://repo1.maven.org/maven2/com/google/guava/guava/32.1.1-jre/guava-32.1.1-jre.jar"

echo.
echo ========================================
echo    PHASE 7: DOWNLOAD TESTING TOOLS
echo    المرحلة 7: تحميل أدوات الاختبار
echo ========================================

echo [7] Downloading testing libraries...

echo Downloading JUnit 5 for testing...
curl -L -o "lib\junit-jupiter-engine-5.10.0.jar" "https://repo1.maven.org/maven2/org/junit/jupiter/junit-jupiter-engine/5.10.0/junit-jupiter-engine-5.10.0.jar"
curl -L -o "lib\junit-jupiter-api-5.10.0.jar" "https://repo1.maven.org/maven2/org/junit/jupiter/junit-jupiter-api/5.10.0/junit-jupiter-api-5.10.0.jar"

echo Downloading Mockito for mocking...
curl -L -o "lib\mockito-core-5.4.0.jar" "https://repo1.maven.org/maven2/org/mockito/mockito-core/5.4.0/mockito-core-5.4.0.jar"

echo.
echo ========================================
echo    PHASE 8: DOWNLOAD PERFORMANCE TOOLS
echo    المرحلة 8: تحميل أدوات الأداء
echo ========================================

echo [8] Downloading performance optimization libraries...

echo Downloading Caffeine for caching...
curl -L -o "lib\caffeine-3.1.7.jar" "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.1.7/caffeine-3.1.7.jar"

echo Downloading Apache Commons Collections...
curl -L -o "lib\commons-collections4-4.4.jar" "https://repo1.maven.org/maven2/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar"

echo.
echo ========================================
echo    VERIFICATION AND SUMMARY
echo    التحقق والملخص
echo ========================================

echo [9] Verifying downloaded libraries...
echo.

set /a count=0
for %%f in (lib\*.jar) do set /a count+=1

echo Total libraries downloaded: %count%
echo إجمالي المكتبات المحملة: %count%
echo.

echo Libraries summary:
echo ملخص المكتبات:
echo.
echo Security and Encryption:
echo - jasypt-1.9.3.jar (Password encryption)
echo - commons-codec-1.15.jar (Encoding utilities)
echo - bcprov-jdk15on-1.70.jar (Advanced encryption)
echo.
echo Connection Pooling:
echo - HikariCP-5.0.1.jar (High-performance connection pool)
echo - commons-dbcp2-2.9.0.jar (Database connection pool)
echo - commons-pool2-2.11.1.jar (Object pooling)
echo.
echo Monitoring and Logging:
echo - logback-classic-1.4.8.jar (Advanced logging)
echo - logback-core-1.4.8.jar (Logging core)
echo - micrometer-core-1.11.2.jar (Metrics collection)
echo.
echo Configuration Management:
echo - commons-configuration2-2.9.0.jar (Configuration management)
echo - commons-lang3-3.12.0.jar (Language utilities)
echo - commons-io-2.11.0.jar (IO utilities)
echo.
echo Validation and Utilities:
echo - commons-validator-1.7.jar (Data validation)
echo - guava-32.1.1-jre.jar (Google utilities)
echo.
echo Testing:
echo - junit-jupiter-engine-5.10.0.jar (JUnit 5 engine)
echo - junit-jupiter-api-5.10.0.jar (JUnit 5 API)
echo - mockito-core-5.4.0.jar (Mocking framework)
echo.
echo Performance:
echo - caffeine-3.1.7.jar (High-performance caching)
echo - commons-collections4-4.4.jar (Enhanced collections)
echo.

echo ========================================
echo    DOWNLOAD COMPLETED SUCCESSFULLY
echo    تم التحميل بنجاح
echo ========================================

echo.
echo [SUCCESS] Enhanced libraries download completed!
echo [نجح] تم تحميل المكتبات المحسنة بنجاح!
echo.
echo Next steps:
echo الخطوات التالية:
echo 1. Run: update-classpath.bat (to update classpath)
echo 2. Run: implement-security-features.bat (to implement security)
echo 3. Run: setup-connection-pooling.bat (to setup pooling)
echo 4. Run: configure-monitoring.bat (to setup monitoring)
echo.

pause
