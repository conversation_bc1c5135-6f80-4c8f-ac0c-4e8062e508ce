@echo off
echo ========================================
echo 💰 تشغيل نظام إدارة العملات الشامل
echo Running Complete Currency Management System
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/5] تجميع جميع الملفات...
echo Compiling All Files...
echo ========================================

echo تجميع CreateCurrencyTables...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CreateCurrencyTables.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CreateCurrencyTables بنجاح
) else (
    echo ❌ فشل في تجميع CreateCurrencyTables
    pause
    exit /b 1
)

echo تجميع CurrencyManagementWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CurrencyManagementWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CurrencyManagementWindow بنجاح
) else (
    echo ❌ فشل في تجميع CurrencyManagementWindow
    pause
    exit /b 1
)

echo تجميع UpdateSystemTreeForCurrency...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\UpdateSystemTreeForCurrency.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع UpdateSystemTreeForCurrency بنجاح
) else (
    echo ❌ فشل في تجميع UpdateSystemTreeForCurrency
    pause
    exit /b 1
)

echo تجميع TreeMenuPanel المحدث...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع TreeMenuPanel المحدث بنجاح
) else (
    echo ❌ فشل في تجميع TreeMenuPanel المحدث
    pause
    exit /b 1
)

echo.
echo [2/5] اختيار العملية...
echo Choose Operation...
echo ========================================

echo.
echo اختر العملية التي تريد تنفيذها:
echo 1. إنشاء جداول قاعدة البيانات فقط
echo 2. تشغيل نافذة إدارة العملات مباشرة
echo 3. تحديث شجرة الأنظمة فقط
echo 4. تشغيل الشجرة الأصلية مع النافذة الجديدة
echo 5. تشغيل النظام الكامل (جداول + شجرة + نافذة)
echo.

set /p choice="أدخل اختيارك (1-5): "

echo.
echo [3/5] تنفيذ العملية المختارة...
echo Executing Selected Operation...
echo ========================================

if "%choice%"=="1" (
    echo إنشاء جداول قاعدة البيانات...
    java -cp "lib\*;." CreateCurrencyTables
    if %errorlevel% equ 0 (
        echo ✅ تم إنشاء جداول قاعدة البيانات بنجاح
    ) else (
        echo ⚠️ فشل في إنشاء الجداول - تحقق من الاتصال بقاعدة البيانات
    )
) else if "%choice%"=="2" (
    echo تشغيل نافذة إدارة العملات...
    start "Currency Management Window" java -cp "lib\*;." CurrencyManagementWindow
    echo ✅ تم تشغيل نافذة إدارة العملات
) else if "%choice%"=="3" (
    echo تحديث شجرة الأنظمة...
    java -cp "lib\*;." UpdateSystemTreeForCurrency
    if %errorlevel% equ 0 (
        echo ✅ تم تحديث شجرة الأنظمة بنجاح
    ) else (
        echo ⚠️ فشل في تحديث شجرة الأنظمة
    )
) else if "%choice%"=="4" (
    echo تشغيل الشجرة الأصلية مع النافذة الجديدة...
    start "Enhanced Ship ERP - Currency Management" java -cp "lib\*;." EnhancedShipERP
    echo ✅ تم تشغيل الشجرة الأصلية المحدثة
) else if "%choice%"=="5" (
    echo تشغيل النظام الكامل...
    
    echo 1. إنشاء جداول قاعدة البيانات...
    java -cp "lib\*;." CreateCurrencyTables
    if %errorlevel% equ 0 (
        echo ✅ تم إنشاء الجداول بنجاح
    ) else (
        echo ⚠️ تحذير: فشل في إنشاء الجداول
    )
    
    timeout /t 2 /nobreak >nul
    
    echo 2. تحديث شجرة الأنظمة...
    java -cp "lib\*;." UpdateSystemTreeForCurrency
    if %errorlevel% equ 0 (
        echo ✅ تم تحديث الشجرة بنجاح
    ) else (
        echo ⚠️ تحذير: فشل في تحديث الشجرة
    )
    
    timeout /t 2 /nobreak >nul
    
    echo 3. تشغيل نافذة إدارة العملات...
    start "Currency Management Window" java -cp "lib\*;." CurrencyManagementWindow
    
    timeout /t 2 /nobreak >nul
    
    echo 4. تشغيل الشجرة الأصلية المحدثة...
    start "Enhanced Ship ERP - Currency Management" java -cp "lib\*;." EnhancedShipERP
    
    echo ✅ تم تشغيل النظام الكامل
) else (
    echo ❌ اختيار غير صحيح، سيتم تشغيل النافذة افتراضياً
    start "Currency Management Window" java -cp "lib\*;." CurrencyManagementWindow
)

echo.
echo [4/5] معلومات النظام المطور...
echo Developed System Information...
echo ========================================

echo.
echo 💰 نظام إدارة العملات الشامل:
echo ==============================
echo • الاسم: نظام إدارة العملات الشامل
echo • النافذة: CurrencyManagementWindow
echo • الموقع: الإعدادات العامة ^> إدارة العملات
echo • الحالة: مطورة بالكامل

echo.
echo 🎯 الميزات المطورة:
echo ==================

echo.
echo 💱 إدارة العملات:
echo ✅ إضافة عملات جديدة
echo ✅ تعديل بيانات العملات الموجودة
echo ✅ حذف العملات غير المستخدمة
echo ✅ تفعيل/إلغاء تفعيل العملات

echo.
echo 📊 أسعار الصرف:
echo ✅ تحديد أسعار الصرف بين العملات
echo 🚧 تحديث أسعار الصرف التلقائي (قيد التطوير)
echo 🚧 تاريخ أسعار الصرف (قيد التطوير)
echo 🚧 مصادر أسعار الصرف الخارجية (قيد التطوير)

echo.
echo ⚙️ الإعدادات العامة:
echo ✅ العملة الافتراضية للنظام
echo ✅ عدد الخانات العشرية
echo ✅ رمز العملة وموضعه
echo ✅ تنسيق عرض المبالغ

echo.
echo 📈 التقارير:
echo 🚧 تقرير العملات المستخدمة (قيد التطوير)
echo 🚧 تقرير تغيرات أسعار الصرف (قيد التطوير)
echo 🚧 تقرير المعاملات بالعملات المختلفة (قيد التطوير)

echo.
echo 🔄 التكامل:
echo 🚧 ربط مع أنظمة أسعار الصرف الخارجية (قيد التطوير)
echo 🚧 تحديث تلقائي لأسعار الصرف (قيد التطوير)
echo 🚧 إشعارات تغيير الأسعار (قيد التطوير)

echo.
echo 💾 قاعدة البيانات:
echo ==================
echo • ERP_CURRENCIES - جدول العملات الرئيسي
echo • ERP_EXCHANGE_RATES - جدول أسعار الصرف
echo • ERP_EXCHANGE_RATE_HISTORY - جدول تاريخ أسعار الصرف
echo • ERP_CURRENCY_SETTINGS - جدول إعدادات النظام
echo • ERP_EXCHANGE_RATE_SOURCES - جدول مصادر أسعار الصرف

echo.
echo 🌳 الهيكل في الشجرة:
echo ====================
echo نظام إدارة الشحنات
echo └── الإعدادات والإدارة
echo    └── الإعدادات العامة
echo        ├── المتغيرات العامة
echo        ├── إدارة العملات ⭐ جديد ومطور
echo        ├── إعدادات النظام العامة
echo        └── إعدادات الأمان

echo.
echo 🚀 للاستخدام:
echo =============
echo • تشغيل مباشر: java -cp "lib\*;." CurrencyManagementWindow
echo • عبر الشجرة: الإعدادات والإدارة ^> الإعدادات العامة ^> إدارة العملات
echo • إنشاء الجداول: java -cp "lib\*;." CreateCurrencyTables
echo • تحديث الشجرة: java -cp "lib\*;." UpdateSystemTreeForCurrency

echo.
echo 📊 الإحصائيات:
echo ==============
echo • عدد الملفات المطورة: 4
echo • عدد الجداول المنشأة: 5
echo • عدد التبويبات في النافذة: 5
echo • عدد العملات الافتراضية: 6
echo • مستوى التطوير: 70%% (الأساسيات مكتملة)

echo.
echo [5/5] ملاحظات مهمة...
echo Important Notes...
echo ========================================

echo.
echo 💡 ملاحظات للاستخدام:
echo ======================
echo • تأكد من تشغيل قاعدة البيانات Oracle قبل الاستخدام
echo • استخدم الخيار 5 لتشغيل النظام الكامل لأول مرة
echo • النافذة تحتوي على 5 تبويبات رئيسية
echo • بعض الميزات المتقدمة قيد التطوير

echo.
echo 🔧 للتطوير المستقبلي:
echo ======================
echo • إضافة ميزات التحديث التلقائي لأسعار الصرف
echo • تطوير التقارير والإحصائيات
echo • إضافة التكامل مع المصادر الخارجية
echo • تحسين واجهة المستخدم

echo.
echo 📞 للدعم:
echo ==========
echo • راجع ملف المساعدة في النافذة
echo • استخدم زر المساعدة في النافذة
echo • تحقق من سجلات النظام للأخطاء

echo.
echo ========================================
echo ✅ نظام إدارة العملات جاهز للاستخدام!
echo Currency Management System Ready!
echo ========================================

echo.
pause
