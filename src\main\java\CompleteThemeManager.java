import java.awt.Window;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.swing.LookAndFeel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
// FlatLaf imports
import com.formdev.flatlaf.FlatDarculaLaf;
import com.formdev.flatlaf.FlatDarkLaf;
import com.formdev.flatlaf.FlatIntelliJLaf;
import com.formdev.flatlaf.FlatLightLaf;

/**
 * مدير المظاهر الكامل والشامل Complete and Comprehensive Theme Manager
 */
public class CompleteThemeManager {

    private static CompleteThemeManager instance;
    private Map<String, ThemeInfo> allThemes;
    private Connection connection;
    private String currentTheme = "FlatLaf Light";

    /**
     * معلومات المظهر
     */
    public static class ThemeInfo {
        public final String name;
        public final String displayName;
        public final String category;
        public final String className;
        public final boolean isDark;
        public final String description;
        public final String author;
        public final String version;
        public final boolean isAvailable;

        public ThemeInfo(String name, String displayName, String category, String className,
                boolean isDark, String description, String author, String version) {
            this.name = name;
            this.displayName = displayName;
            this.category = category;
            this.className = className;
            this.isDark = isDark;
            this.description = description;
            this.author = author;
            this.version = version;
            this.isAvailable = checkAvailability();
        }

        private boolean checkAvailability() {
            if (className == null || className.isEmpty())
                return true;
            try {
                Class.forName(className);
                return true;
            } catch (ClassNotFoundException e) {
                System.err.println("⚠️ Theme not available: " + name + " - " + className);
                return false;
            }
        }
    }

    private CompleteThemeManager() {
        initializeAllThemes();
        connectToDatabase();
        loadSavedTheme();
    }

    public static CompleteThemeManager getInstance() {
        if (instance == null) {
            instance = new CompleteThemeManager();
        }
        return instance;
    }

    /**
     * تهيئة جميع المظاهر
     */
    private void initializeAllThemes() {
        allThemes = new LinkedHashMap<>();
        System.out.println("🎨 تهيئة جميع المظاهر المتاحة...");

        // FlatLaf Core Themes
        addTheme("FlatLaf Light", "FlatLaf فاتح", "FlatLaf Core",
                "com.formdev.flatlaf.FlatLightLaf", false, "مظهر فاتح حديث وأنيق من FlatLaf",
                "FormDev", "3.2.5");

        addTheme("FlatLaf Dark", "FlatLaf مظلم", "FlatLaf Core", "com.formdev.flatlaf.FlatDarkLaf",
                true, "مظهر مظلم حديث وأنيق من FlatLaf", "FormDev", "3.2.5");

        addTheme("FlatLaf IntelliJ", "FlatLaf IntelliJ", "FlatLaf Core",
                "com.formdev.flatlaf.FlatIntelliJLaf", false, "مظهر IntelliJ IDEA الكلاسيكي",
                "FormDev", "3.2.5");

        addTheme("FlatLaf Darcula", "FlatLaf Darcula", "FlatLaf Core",
                "com.formdev.flatlaf.FlatDarculaLaf", true, "مظهر Darcula المظلم الشهير", "FormDev",
                "3.2.5");

        // IntelliJ Light Themes
        addIntelliJLightThemes();

        // IntelliJ Dark Themes
        addIntelliJDarkThemes();

        // JTattoo Themes
        addJTattooThemes();

        // System Themes
        addSystemThemes();

        int availableCount = (int) allThemes.values().stream().filter(t -> t.isAvailable).count();
        System.out.println("✅ تم تهيئة " + allThemes.size() + " مظهر، منها " + availableCount
                + " متاح للاستخدام");
    }

    private void addIntelliJLightThemes() {
        addTheme("Arc Theme", "مظهر Arc", "IntelliJ Light",
                "com.formdev.flatlaf.intellijthemes.FlatArcIJTheme", false,
                "مظهر Arc الحديث والأنيق", "Arc Team", "1.0");

        addTheme("Cyan Light", "مظهر Cyan الفاتح", "IntelliJ Light",
                "com.formdev.flatlaf.intellijthemes.FlatCyanLightIJTheme", false,
                "مظهر Cyan الفاتح المنعش", "Cyan Team", "1.0");

        addTheme("Gray Theme", "المظهر الرمادي", "IntelliJ Light",
                "com.formdev.flatlaf.intellijthemes.FlatGrayIJTheme", false,
                "مظهر رمادي هادئ ومريح", "Gray Team", "1.0");

        addTheme("Light Flat", "المظهر الفاتح المسطح", "IntelliJ Light",
                "com.formdev.flatlaf.intellijthemes.FlatLightFlatIJTheme", false,
                "مظهر فاتح مسطح وبسيط", "Light Team", "1.0");

        addTheme("Nord Theme", "مظهر Nord", "IntelliJ Light",
                "com.formdev.flatlaf.intellijthemes.FlatNordIJTheme", false,
                "مظهر Nord الاسكندنافي الهادئ", "Nord Team", "1.0");

        addTheme("Solarized Light", "مظهر Solarized الفاتح", "IntelliJ Light",
                "com.formdev.flatlaf.intellijthemes.FlatSolarizedLightIJTheme", false,
                "مظهر Solarized الفاتح المتوازن", "Solarized Team", "1.0");

        addTheme("Vuesion Theme", "مظهر Vuesion", "IntelliJ Light",
                "com.formdev.flatlaf.intellijthemes.FlatVuesionIJTheme", false,
                "مظهر Vuesion الحديث والمتطور", "Vuesion Team", "1.0");
    }

    private void addIntelliJDarkThemes() {
        addTheme("Arc Dark", "مظهر Arc المظلم", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatArcDarkIJTheme", true,
                "مظهر Arc المظلم الجميل", "Arc Team", "1.0");

        addTheme("Carbon Theme", "مظهر Carbon", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatCarbonIJTheme", true,
                "مظهر Carbon المظلم الأنيق", "Carbon Team", "1.0");

        addTheme("Cobalt 2", "مظهر Cobalt 2", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatCobalt2IJTheme", true,
                "مظهر Cobalt 2 الأزرق المميز", "Cobalt Team", "2.0");

        addTheme("Dark Purple", "مظهر البنفسجي المظلم", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatDarkPurpleIJTheme", true,
                "مظهر بنفسجي مظلم رائع", "Purple Team", "1.0");

        addTheme("Dracula", "مظهر Dracula", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatDraculaIJTheme", true,
                "مظهر Dracula الشهير والمحبوب", "Dracula Team", "1.0");

        addTheme("Gradianto Dark Fuchsia", "مظهر Gradianto", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatGradiantoDarkFuchsiaIJTheme", true,
                "مظهر Gradianto بتدرجات جميلة", "Gradianto Team", "1.0");

        addTheme("Gruvbox Dark Hard", "مظهر Gruvbox", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatGruvboxDarkHardIJTheme", true,
                "مظهر Gruvbox المظلم المتباين", "Gruvbox Team", "1.0");

        addTheme("Hiberbee Dark", "مظهر Hiberbee", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatHiberbeeDarkIJTheme", true,
                "مظهر Hiberbee المظلم الجميل", "Hiberbee Team", "1.0");

        addTheme("High Contrast", "مظهر التباين العالي", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatHighContrastIJTheme", true,
                "مظهر عالي التباين للوضوح", "Contrast Team", "1.0");

        addTheme("Material Design Dark", "Material Design المظلم", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatMaterialDesignDarkIJTheme", true,
                "مظهر Material Design المظلم", "Material Team", "1.0");

        addTheme("Monocai", "مظهر Monocai", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatMonocaiIJTheme", true,
                "مظهر Monocai الأحادي اللون", "Monocai Team", "1.0");

        addTheme("One Dark", "مظهر One Dark", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatOneDarkIJTheme", true,
                "مظهر One Dark الشهير من Atom", "One Dark Team", "1.0");

        addTheme("Solarized Dark", "مظهر Solarized المظلم", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatSolarizedDarkIJTheme", true,
                "مظهر Solarized المظلم المتوازن", "Solarized Team", "1.0");

        addTheme("Spacegray", "مظهر Spacegray", "IntelliJ Dark",
                "com.formdev.flatlaf.intellijthemes.FlatSpacegrayIJTheme", true,
                "مظهر Spacegray الفضائي", "Spacegray Team", "1.0");
    }

    private void addJTattooThemes() {
        // JTattoo Light Themes
        addTheme("Acryl", "مظهر Acryl", "JTattoo Light", "com.jtattoo.plaf.acryl.AcrylLookAndFeel",
                false, "مظهر Acryl الشفاف والأنيق", "JTattoo", "1.6.13");

        addTheme("Aero", "مظهر Aero", "JTattoo Light", "com.jtattoo.plaf.aero.AeroLookAndFeel",
                false, "مظهر Aero الحديث والمتطور", "JTattoo", "1.6.13");

        addTheme("Aluminium", "مظهر الألمنيوم", "JTattoo Light",
                "com.jtattoo.plaf.aluminium.AluminiumLookAndFeel", false,
                "مظهر معدني أنيق بلمسة ألمنيوم", "JTattoo", "1.6.13");

        addTheme("Bernstein", "مظهر Bernstein", "JTattoo Light",
                "com.jtattoo.plaf.bernstein.BernsteinLookAndFeel", false,
                "مظهر Bernstein الدافئ والجميل", "JTattoo", "1.6.13");

        addTheme("Fast", "مظهر Fast", "JTattoo Light", "com.jtattoo.plaf.fast.FastLookAndFeel",
                false, "مظهر سريع وبسيط للأداء العالي", "JTattoo", "1.6.13");

        addTheme("Luna", "مظهر Luna", "JTattoo Light", "com.jtattoo.plaf.luna.LunaLookAndFeel",
                false, "مظهر Luna الكلاسيكي والجميل", "JTattoo", "1.6.13");

        addTheme("McWin", "مظهر McWin", "JTattoo Light", "com.jtattoo.plaf.mcwin.McWinLookAndFeel",
                false, "مظهر McWin الحديث والمتطور", "JTattoo", "1.6.13");

        addTheme("Mint", "مظهر النعناع", "JTattoo Light", "com.jtattoo.plaf.mint.MintLookAndFeel",
                false, "مظهر النعناع المنعش والهادئ", "JTattoo", "1.6.13");

        addTheme("Smart", "مظهر Smart", "JTattoo Light", "com.jtattoo.plaf.smart.SmartLookAndFeel",
                false, "مظهر Smart الذكي والعملي", "JTattoo", "1.6.13");

        addTheme("Texture", "مظهر Texture", "JTattoo Light",
                "com.jtattoo.plaf.texture.TextureLookAndFeel", false, "مظهر Texture المحكم والمميز",
                "JTattoo", "1.6.13");

        // JTattoo Dark Themes
        addTheme("Graphite", "مظهر الجرافيت", "JTattoo Dark",
                "com.jtattoo.plaf.graphite.GraphiteLookAndFeel", true,
                "مظهر الجرافيت المظلم والأنيق", "JTattoo", "1.6.13");

        addTheme("HiFi", "مظهر HiFi", "JTattoo Dark", "com.jtattoo.plaf.hifi.HiFiLookAndFeel", true,
                "مظهر HiFi المتقدم والمميز", "JTattoo", "1.6.13");

        addTheme("Noire", "مظهر Noire", "JTattoo Dark", "com.jtattoo.plaf.noire.NoireLookAndFeel",
                true, "مظهر Noire الأسود الأنيق", "JTattoo", "1.6.13");
    }

    private void addSystemThemes() {
        addTheme("System Default", "مظهر النظام", "System", "", false, "مظهر النظام الافتراضي",
                "System", "Default");

        addTheme("Metal", "مظهر Metal", "System", "javax.swing.plaf.metal.MetalLookAndFeel", false,
                "مظهر Java Metal الكلاسيكي", "Oracle", "Java");

        addTheme("Nimbus", "مظهر Nimbus", "System", "javax.swing.plaf.nimbus.NimbusLookAndFeel",
                false, "مظهر Nimbus الحديث والجميل", "Oracle", "Java");

        addTheme("Windows", "مظهر Windows", "System",
                "com.sun.java.swing.plaf.windows.WindowsLookAndFeel", false, "مظهر Windows الأصلي",
                "Microsoft", "Windows");
    }

    private void addTheme(String name, String displayName, String category, String className,
            boolean isDark, String description, String author, String version) {
        ThemeInfo theme = new ThemeInfo(name, displayName, category, className, isDark, description,
                author, version);
        allThemes.put(name, theme);
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private void connectToDatabase() {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            connection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات لمدير المظاهر الكامل");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
        }
    }

    /**
     * تطبيق مظهر
     */
    public boolean applyTheme(String themeName) {
        ThemeInfo theme = allThemes.get(themeName);
        if (theme == null || !theme.isAvailable) {
            System.err.println("❌ المظهر غير متاح: " + themeName);
            return false;
        }

        try {
            System.out.println("🎨 تطبيق المظهر: " + theme.displayName);

            boolean success = false;

            // تطبيق المظهر حسب الفئة
            if (theme.category.startsWith("FlatLaf")) {
                success = applyFlatLafTheme(theme);
            } else if (theme.category.startsWith("IntelliJ")) {
                success = applyIntelliJTheme(theme);
            } else if (theme.category.startsWith("JTattoo")) {
                success = applyJTattooTheme(theme);
            } else if (theme.category.equals("System")) {
                success = applySystemTheme(theme);
            }

            if (success) {
                currentTheme = themeName;
                updateAllWindows();
                saveCurrentTheme(themeName);
                System.out.println("✅ تم تطبيق المظهر بنجاح: " + theme.displayName);
                return true;
            } else {
                System.err.println("❌ فشل في تطبيق المظهر: " + theme.displayName);
                return false;
            }

        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر: " + e.getMessage());
            return false;
        }
    }

    private boolean applyFlatLafTheme(ThemeInfo theme) {
        try {
            switch (theme.name) {
                case "FlatLaf Light":
                    return FlatLightLaf.setup();
                case "FlatLaf Dark":
                    return FlatDarkLaf.setup();
                case "FlatLaf IntelliJ":
                    return FlatIntelliJLaf.setup();
                case "FlatLaf Darcula":
                    return FlatDarculaLaf.setup();
                default:
                    return false;
            }
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق FlatLaf: " + e.getMessage());
            return false;
        }
    }

    private boolean applyIntelliJTheme(ThemeInfo theme) {
        try {
            Class<?> themeClass = Class.forName(theme.className);
            LookAndFeel laf = (LookAndFeel) themeClass.getDeclaredConstructor().newInstance();
            UIManager.setLookAndFeel(laf);
            return true;
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق IntelliJ Theme: " + e.getMessage());
            return false;
        }
    }

    private boolean applyJTattooTheme(ThemeInfo theme) {
        try {
            Class<?> themeClass = Class.forName(theme.className);
            LookAndFeel laf = (LookAndFeel) themeClass.getDeclaredConstructor().newInstance();
            UIManager.setLookAndFeel(laf);
            return true;
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق JTattoo Theme: " + e.getMessage());
            return false;
        }
    }

    private boolean applySystemTheme(ThemeInfo theme) {
        try {
            if (theme.name.equals("System Default")) {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel().getClassName());
                return true;
            } else {
                Class<?> themeClass = Class.forName(theme.className);
                LookAndFeel laf = (LookAndFeel) themeClass.getDeclaredConstructor().newInstance();
                UIManager.setLookAndFeel(laf);
                return true;
            }
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق System Theme: " + e.getMessage());
            return false;
        }
    }

    /**
     * تحديث جميع النوافذ
     */
    private void updateAllWindows() {
        SwingUtilities.invokeLater(() -> {
            for (Window window : Window.getWindows()) {
                if (window.isDisplayable()) {
                    SwingUtilities.updateComponentTreeUI(window);
                    window.repaint();
                }
            }
        });
    }

    /**
     * حفظ المظهر الحالي
     */
    private void saveCurrentTheme(String themeName) {
        if (connection == null)
            return;

        try {
            // إزالة المظهر الحالي
            String resetSQL = "UPDATE ERP_COMPREHENSIVE_THEMES SET IS_CURRENT_THEME = 'N'";
            try (PreparedStatement resetStmt = connection.prepareStatement(resetSQL)) {
                resetStmt.executeUpdate();
            }

            // تعيين المظهر الجديد كحالي
            String updateSQL =
                    "UPDATE ERP_COMPREHENSIVE_THEMES SET IS_CURRENT_THEME = 'Y', LAST_APPLIED_DATE = SYSDATE WHERE THEME_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(updateSQL)) {
                stmt.setString(1, themeName);
                int updated = stmt.executeUpdate();
                if (updated > 0) {
                    System.out.println("💾 تم حفظ المظهر في قاعدة البيانات: " + themeName);
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في حفظ المظهر الحالي: " + e.getMessage());
        }
    }

    /**
     * تحميل المظهر المحفوظ
     */
    private void loadSavedTheme() {
        if (connection == null)
            return;

        try {
            String sql =
                    "SELECT THEME_NAME FROM ERP_COMPREHENSIVE_THEMES WHERE IS_CURRENT_THEME = 'Y'";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    String savedTheme = rs.getString("THEME_NAME");
                    if (savedTheme != null && allThemes.containsKey(savedTheme)) {
                        System.out.println("📂 تحميل المظهر المحفوظ: " + savedTheme);
                        currentTheme = savedTheme;
                        applyTheme(savedTheme);
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في تحميل المظهر المحفوظ: " + e.getMessage());
        }
    }

    /**
     * الحصول على جميع المظاهر حسب الفئة
     */
    public Map<String, List<ThemeInfo>> getThemesByCategory() {
        Map<String, List<ThemeInfo>> themesByCategory = new LinkedHashMap<>();

        for (ThemeInfo theme : allThemes.values()) {
            if (theme.isAvailable) {
                themesByCategory.computeIfAbsent(theme.category, k -> new ArrayList<>()).add(theme);
            }
        }

        return themesByCategory;
    }

    /**
     * الحصول على معلومات المظهر
     */
    public ThemeInfo getThemeInfo(String themeName) {
        return allThemes.get(themeName);
    }

    /**
     * الحصول على المظهر الحالي
     */
    public String getCurrentTheme() {
        return currentTheme;
    }

    /**
     * الحصول على جميع أسماء المظاهر المتاحة
     */
    public String[] getAvailableThemeNames() {
        return allThemes.values().stream().filter(theme -> theme.isAvailable)
                .map(theme -> theme.name).toArray(String[]::new);
    }

    /**
     * طباعة تقرير المظاهر
     */
    public void printThemeReport() {
        System.out.println("\n📊 تقرير المظاهر المتاحة:");
        System.out.println("========================");

        Map<String, List<ThemeInfo>> themesByCategory = getThemesByCategory();

        for (Map.Entry<String, List<ThemeInfo>> entry : themesByCategory.entrySet()) {
            System.out
                    .println("\n🎨 " + entry.getKey() + " (" + entry.getValue().size() + " مظهر):");
            for (ThemeInfo theme : entry.getValue()) {
                System.out.println("  ✅ " + theme.displayName + " - " + theme.description);
            }
        }

        int totalAvailable = (int) allThemes.values().stream().filter(t -> t.isAvailable).count();
        System.out.println("\n📈 إجمالي المظاهر المتاحة: " + totalAvailable);
    }
}
