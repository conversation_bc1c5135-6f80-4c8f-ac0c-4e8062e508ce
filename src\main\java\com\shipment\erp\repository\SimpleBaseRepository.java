package com.shipment.erp.repository;

import com.shipment.erp.model.BaseEntity;
import java.util.List;
import java.util.Optional;

/**
 * مستودع أساسي مبسط لجميع الكيانات
 * Simple Base Repository for all entities
 */
public interface SimpleBaseRepository<T extends BaseEntity> {
    
    /**
     * حفظ كيان جديد أو تحديث موجود
     */
    T save(T entity);
    
    /**
     * البحث عن كيان بالمعرف
     */
    Optional<T> findById(Long id);
    
    /**
     * التحقق من وجود كيان بالمعرف
     */
    boolean existsById(Long id);
    
    /**
     * الحصول على جميع الكيانات
     */
    List<T> findAll();
    
    /**
     * الحصول على عدد الكيانات
     */
    long count();
    
    /**
     * حذف كيان بالمعرف
     */
    void deleteById(Long id);
    
    /**
     * حذف كيان
     */
    void delete(T entity);
    
    /**
     * حذف جميع الكيانات
     */
    void deleteAll();
    
    /**
     * البحث عن كيانات متعددة بالمعرفات
     */
    List<T> findAllById(Iterable<Long> ids);
    
    /**
     * حفظ قائمة من الكيانات
     */
    List<T> saveAll(Iterable<T> entities);
    
    /**
     * حذف قائمة من الكيانات
     */
    void deleteAll(Iterable<T> entities);
    
    /**
     * حذف كيانات بالمعرفات
     */
    void deleteAllById(Iterable<Long> ids);
}
