@echo off
echo ========================================
echo    SHIP ERP - DEVELOPMENT MODE
echo ========================================

cd /d "e:\ship_erp\java"

set CP=.;lib\ojdbc11.jar;lib\orai18n.jar;lib\commons-logging-1.2.jar

echo [DEV] Development mode starting...
echo.

echo [1] Clean previous builds...
if exist "*.class" del /q *.class

echo [2] Compile all components...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\*.java

if %errorlevel% neq 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo [3] Starting system in development mode...
echo.

java -cp "%CP%" -Dfile.encoding=UTF-8 -Doracle.jdbc.defaultNChar=true CompleteOracleSystemTest

echo.
echo Development session ended.
pause
