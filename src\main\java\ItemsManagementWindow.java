import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.*;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة الأصناف الحقيقية
 * Real Items Management Window
 */
public class ItemsManagementWindow extends JFrame {
    
    private JTable itemsTable;
    private DefaultTableModel tableModel;
    private JTextField itemCodeField;
    private JTextField itemNameArField;
    private JTextField itemNameEnField;
    private JTextField itemDescriptionField;
    private JComboBox<String> categoryCombo;
    private JTextField unitPriceField;
    private JTextField quantityField;
    private JButton saveButton;
    private JButton deleteButton;
    private JButton newButton;
    private Connection connection;
    private int currentItemId = -1;
    
    public ItemsManagementWindow() {
        initializeConnection();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadItems();
        
        setTitle("📦 إدارة الأصناف - Items Management");
        setSize(1000, 600);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);
        
        // تطبيق اتجاه اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        applyComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    private void initializeConnection() {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            connection = tnsManager.getShipErpConnection();
            createItemsTableIfNotExists();
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
        }
    }
    
    private void createItemsTableIfNotExists() {
        try {
            String createTableSQL = """
                CREATE TABLE ERP_ITEMS (
                    ITEM_ID NUMBER(10) NOT NULL,
                    ITEM_CODE VARCHAR2(50) NOT NULL UNIQUE,
                    ITEM_NAME_AR NVARCHAR2(200) NOT NULL,
                    ITEM_NAME_EN VARCHAR2(200),
                    ITEM_DESCRIPTION NVARCHAR2(500),
                    CATEGORY VARCHAR2(100),
                    UNIT_PRICE NUMBER(10,2) DEFAULT 0,
                    QUANTITY NUMBER(10,2) DEFAULT 0,
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    CONSTRAINT PK_ERP_ITEMS PRIMARY KEY (ITEM_ID)
                )
                """;
            
            try (Statement stmt = connection.createStatement()) {
                stmt.executeUpdate(createTableSQL);
                System.out.println("✅ تم إنشاء جدول الأصناف");
            }
            
            // إنشاء المتسلسل
            try (Statement stmt = connection.createStatement()) {
                stmt.executeUpdate("CREATE SEQUENCE ERP_ITEMS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
                System.out.println("✅ تم إنشاء متسلسل الأصناف");
            }
            
            // إدراج بيانات تجريبية
            insertSampleItems();
            
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                System.err.println("❌ خطأ في إنشاء جدول الأصناف: " + e.getMessage());
            }
        }
    }
    
    private void insertSampleItems() {
        String[] sampleItems = {
            "ITM001|صندوق شحن كبير|Large Shipping Box|صندوق شحن كبير الحجم|صناديق|25.50|100",
            "ITM002|صندوق شحن متوسط|Medium Shipping Box|صندوق شحن متوسط الحجم|صناديق|18.75|150",
            "ITM003|شريط لاصق|Packing Tape|شريط لاصق للتغليف|مواد التغليف|5.25|500",
            "ITM004|فقاعات الحماية|Bubble Wrap|فقاعات الحماية للشحن|مواد الحماية|12.00|200"
        };
        
        for (String item : sampleItems) {
            String[] parts = item.split("\\|");
            try {
                String insertSQL = """
                    INSERT INTO ERP_ITEMS (ITEM_ID, ITEM_CODE, ITEM_NAME_AR, ITEM_NAME_EN, 
                                         ITEM_DESCRIPTION, CATEGORY, UNIT_PRICE, QUANTITY)
                    VALUES (ERP_ITEMS_SEQ.NEXTVAL, ?, ?, ?, ?, ?, ?, ?)
                    """;
                
                try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
                    stmt.setString(1, parts[0]);
                    stmt.setString(2, parts[1]);
                    stmt.setString(3, parts[2]);
                    stmt.setString(4, parts[3]);
                    stmt.setString(5, parts[4]);
                    stmt.setDouble(6, Double.parseDouble(parts[5]));
                    stmt.setDouble(7, Double.parseDouble(parts[6]));
                    stmt.executeUpdate();
                }
            } catch (SQLException e) {
                // تجاهل إذا كان الصنف موجود
            }
        }
    }
    
    private void initializeComponents() {
        // إنشاء الجدول
        String[] columnNames = {"المعرف", "رمز الصنف", "الاسم بالعربية", "الاسم بالإنجليزية", "الفئة", "السعر", "الكمية"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        itemsTable = new JTable(tableModel);
        itemsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        itemsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        // إنشاء حقول الإدخال
        itemCodeField = new JTextField(20);
        itemCodeField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        itemNameArField = new JTextField(30);
        itemNameArField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        itemNameEnField = new JTextField(30);
        itemNameEnField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        itemDescriptionField = new JTextField(40);
        itemDescriptionField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        categoryCombo = new JComboBox<>(new String[]{"صناديق", "مواد التغليف", "مواد الحماية", "أدوات الشحن", "أخرى"});
        categoryCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        unitPriceField = new JTextField(15);
        unitPriceField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        quantityField = new JTextField(15);
        quantityField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إنشاء الأزرار
        saveButton = new JButton("💾 حفظ");
        deleteButton = new JButton("🗑️ حذف");
        newButton = new JButton("➕ جديد");
        
        saveButton.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        deleteButton.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        newButton.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة العلوية - الجدول
        JScrollPane scrollPane = new JScrollPane(itemsTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // اللوحة السفلية - النموذج
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        formPanel.setBorder(BorderFactory.createTitledBorder("تفاصيل الصنف"));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // الصف الأول
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(new JLabel("رمز الصنف:"), gbc);
        gbc.gridx = 1;
        formPanel.add(itemCodeField, gbc);
        
        gbc.gridx = 2;
        formPanel.add(new JLabel("الفئة:"), gbc);
        gbc.gridx = 3;
        formPanel.add(categoryCombo, gbc);
        
        // الصف الثاني
        gbc.gridx = 0; gbc.gridy = 1;
        formPanel.add(new JLabel("الاسم بالعربية:"), gbc);
        gbc.gridx = 1;
        formPanel.add(itemNameArField, gbc);
        
        gbc.gridx = 2;
        formPanel.add(new JLabel("السعر:"), gbc);
        gbc.gridx = 3;
        formPanel.add(unitPriceField, gbc);
        
        // الصف الثالث
        gbc.gridx = 0; gbc.gridy = 2;
        formPanel.add(new JLabel("الاسم بالإنجليزية:"), gbc);
        gbc.gridx = 1;
        formPanel.add(itemNameEnField, gbc);
        
        gbc.gridx = 2;
        formPanel.add(new JLabel("الكمية:"), gbc);
        gbc.gridx = 3;
        formPanel.add(quantityField, gbc);
        
        // الصف الرابع
        gbc.gridx = 0; gbc.gridy = 3;
        formPanel.add(new JLabel("الوصف:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 3;
        formPanel.add(itemDescriptionField, gbc);
        
        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonPanel.add(newButton);
        buttonPanel.add(saveButton);
        buttonPanel.add(deleteButton);
        
        add(scrollPane, BorderLayout.CENTER);
        add(formPanel, BorderLayout.SOUTH);
        add(buttonPanel, BorderLayout.PAGE_END);
    }
    
    private void setupEventHandlers() {
        itemsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedItem();
            }
        });
        
        saveButton.addActionListener(e -> saveItem());
        deleteButton.addActionListener(e -> deleteItem());
        newButton.addActionListener(e -> newItem());
    }
    
    private void loadItems() {
        if (connection == null) return;
        
        try {
            String sql = "SELECT * FROM ERP_ITEMS WHERE IS_ACTIVE = 'Y' ORDER BY ITEM_CODE";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                tableModel.setRowCount(0);
                
                while (rs.next()) {
                    Object[] row = {
                        rs.getInt("ITEM_ID"),
                        rs.getString("ITEM_CODE"),
                        rs.getString("ITEM_NAME_AR"),
                        rs.getString("ITEM_NAME_EN"),
                        rs.getString("CATEGORY"),
                        rs.getDouble("UNIT_PRICE"),
                        rs.getDouble("QUANTITY")
                    };
                    tableModel.addRow(row);
                }
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل الأصناف: " + e.getMessage());
        }
    }
    
    private void loadSelectedItem() {
        int selectedRow = itemsTable.getSelectedRow();
        if (selectedRow >= 0) {
            currentItemId = (Integer) tableModel.getValueAt(selectedRow, 0);
            itemCodeField.setText((String) tableModel.getValueAt(selectedRow, 1));
            itemNameArField.setText((String) tableModel.getValueAt(selectedRow, 2));
            itemNameEnField.setText((String) tableModel.getValueAt(selectedRow, 3));
            categoryCombo.setSelectedItem(tableModel.getValueAt(selectedRow, 4));
            unitPriceField.setText(String.valueOf(tableModel.getValueAt(selectedRow, 5)));
            quantityField.setText(String.valueOf(tableModel.getValueAt(selectedRow, 6)));
        }
    }
    
    private void saveItem() {
        try {
            String sql;
            if (currentItemId == -1) {
                sql = """
                    INSERT INTO ERP_ITEMS (ITEM_ID, ITEM_CODE, ITEM_NAME_AR, ITEM_NAME_EN, 
                                         ITEM_DESCRIPTION, CATEGORY, UNIT_PRICE, QUANTITY)
                    VALUES (ERP_ITEMS_SEQ.NEXTVAL, ?, ?, ?, ?, ?, ?, ?)
                    """;
            } else {
                sql = """
                    UPDATE ERP_ITEMS SET ITEM_CODE = ?, ITEM_NAME_AR = ?, ITEM_NAME_EN = ?, 
                                       ITEM_DESCRIPTION = ?, CATEGORY = ?, UNIT_PRICE = ?, QUANTITY = ?
                    WHERE ITEM_ID = ?
                    """;
            }
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, itemCodeField.getText());
                stmt.setString(2, itemNameArField.getText());
                stmt.setString(3, itemNameEnField.getText());
                stmt.setString(4, itemDescriptionField.getText());
                stmt.setString(5, (String) categoryCombo.getSelectedItem());
                stmt.setDouble(6, Double.parseDouble(unitPriceField.getText()));
                stmt.setDouble(7, Double.parseDouble(quantityField.getText()));
                
                if (currentItemId != -1) {
                    stmt.setInt(8, currentItemId);
                }
                
                stmt.executeUpdate();
                JOptionPane.showMessageDialog(this, "تم حفظ الصنف بنجاح");
                loadItems();
                newItem();
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "خطأ في حفظ الصنف: " + e.getMessage());
        }
    }
    
    private void deleteItem() {
        if (currentItemId == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار صنف للحذف");
            return;
        }
        
        int confirm = JOptionPane.showConfirmDialog(this, "هل أنت متأكد من حذف هذا الصنف؟");
        if (confirm == JOptionPane.YES_OPTION) {
            try {
                String sql = "UPDATE ERP_ITEMS SET IS_ACTIVE = 'N' WHERE ITEM_ID = ?";
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setInt(1, currentItemId);
                    stmt.executeUpdate();
                    JOptionPane.showMessageDialog(this, "تم حذف الصنف بنجاح");
                    loadItems();
                    newItem();
                }
            } catch (SQLException e) {
                JOptionPane.showMessageDialog(this, "خطأ في حذف الصنف: " + e.getMessage());
            }
        }
    }
    
    private void newItem() {
        currentItemId = -1;
        itemCodeField.setText("");
        itemNameArField.setText("");
        itemNameEnField.setText("");
        itemDescriptionField.setText("");
        categoryCombo.setSelectedIndex(0);
        unitPriceField.setText("0.00");
        quantityField.setText("0.00");
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new ItemsManagementWindow().setVisible(true);
        });
    }
}
