# 📊 تقرير تحليل جدول شجرة الأنظمة
## System Tree Analysis Report

---

**تاريخ التقرير:** 2025-01-20  
**قاعدة البيانات:** Oracle ORCL  
**الجدول:** ERP_SYSTEM_TREE  
**النظام:** Ship ERP System  

---

## 🎯 الهدف من التحليل

تم إنشاء هذا التقرير لفحص وتحليل جدول شجرة الأنظمة `ERP_SYSTEM_TREE` في نظام إدارة الشحنات، والذي يحتوي على البنية الهرمية لقوائم النظام والنوافذ.

---

## 📋 هيكل الجدول

### الأعمدة الرئيسية:

| العمود | النوع | الوصف |
|--------|-------|--------|
| `TREE_ID` | NUMBER(10) | المعرف الفريد للعقدة |
| `PARENT_ID` | NUMBER(10) | معرف العقدة الأب |
| `NODE_NAME_AR` | NVARCHAR2(100) | اسم العقدة بالعربية |
| `NODE_NAME_EN` | VARCHAR2(100) | اسم العقدة بالإنجليزية |
| `NODE_DESCRIPTION` | NVARCHAR2(500) | وصف العقدة |
| `NODE_TYPE` | VARCHAR2(20) | نوع العقدة |
| `WINDOW_CLASS` | VARCHAR2(200) | اسم كلاس النافذة |
| `ICON_PATH` | VARCHAR2(500) | مسار الأيقونة |
| `DISPLAY_ORDER` | NUMBER(5) | ترتيب العرض |
| `TREE_LEVEL` | NUMBER(3) | مستوى العمق |
| `IS_ACTIVE` | CHAR(1) | هل العقدة نشطة |
| `IS_VISIBLE` | CHAR(1) | هل العقدة مرئية |

### الأعمدة الإضافية:
- `ACCESS_PERMISSIONS` - صلاحيات الوصول
- `ADDITIONAL_INFO` - معلومات إضافية (CLOB)
- `CREATED_DATE` - تاريخ الإنشاء
- `CREATED_BY` - المستخدم المنشئ
- `LAST_UPDATED` - تاريخ آخر تحديث
- `UPDATED_BY` - المستخدم المحدث
- `VERSION_NUMBER` - رقم الإصدار

---

## 📊 الإحصائيات المتوقعة

### أنواع العقد:
- **CATEGORY** - الفئات الرئيسية
- **WINDOW** - النوافذ التطبيقية
- **TOOL** - الأدوات المساعدة
- **REPORT** - التقارير

### المستويات المتوقعة:
- **المستوى 0** - العقدة الجذرية (نظام إدارة الشحنات)
- **المستوى 1** - الفئات الرئيسية (إدارة الأصناف، المستخدمين، إلخ)
- **المستوى 2** - النوافذ والأدوات الفرعية
- **المستوى 3+** - عقد فرعية إضافية (إن وجدت)

---

## 🔍 المشاكل المحتملة

### 1. العقد اليتيمة (Orphaned Nodes)
- عقد تشير إلى `PARENT_ID` غير موجود
- **التأثير:** كسر في الهيكل الهرمي
- **الحل:** ربط العقد بالجذر أو حذفها

### 2. الأسماء المكررة
- عقد متعددة بنفس `NODE_NAME_AR`
- **التأثير:** التباس في التنقل
- **الحل:** إعادة تسمية أو دمج العقد

### 3. النوافذ بدون كلاس
- عقد من نوع `WINDOW` بدون `WINDOW_CLASS`
- **التأثير:** عدم إمكانية فتح النافذة
- **الحل:** إضافة اسم الكلاس الصحيح

### 4. العقد غير النشطة
- عقد بحالة `IS_ACTIVE = 'N'`
- **التأثير:** عدم ظهور في القائمة
- **الحل:** مراجعة الحاجة وتفعيل أو حذف

---

## 🛠️ الأدوات المتاحة للفحص

### 1. محلل شجرة الأنظمة (SystemTreeAnalyzer)
```bash
run-system-tree-analyzer.bat
```

**الميزات:**
- ✅ واجهة رسومية متقدمة
- ✅ تحليل شامل للمشاكل
- ✅ إحصائيات تفصيلية
- ✅ إصلاح تلقائي للمشاكل
- ✅ تصدير التقارير

### 2. فحص قاعدة البيانات المباشر
```bash
run-database-tree-check.bat
```

**الميزات:**
- ✅ فحص مباشر عبر SQL
- ✅ تقرير نصي شامل
- ✅ لا يحتاج واجهة رسومية
- ✅ سريع ومباشر

### 3. ملف SQL للفحص اليدوي
```sql
@check-system-tree-database.sql
```

**الميزات:**
- ✅ تحكم كامل في الاستعلامات
- ✅ قابل للتخصيص
- ✅ مناسب للمطورين

---

## 💡 توصيات الصيانة

### 1. الفحص الدوري
- **التكرار:** شهرياً
- **الهدف:** اكتشاف المشاكل مبكراً
- **الأداة:** `SystemTreeAnalyzer`

### 2. النسخ الاحتياطي
```sql
-- إنشاء نسخة احتياطية
CREATE TABLE ERP_SYSTEM_TREE_BACKUP AS 
SELECT * FROM ERP_SYSTEM_TREE;
```

### 3. تحسين الأداء
- إنشاء فهارس على الأعمدة المستخدمة كثيراً
- تنظيف العقد غير المستخدمة
- ضغط البيانات القديمة

### 4. التوثيق
- توثيق كل تغيير في الهيكل
- الاحتفاظ بسجل للتعديلات
- مراجعة دورية للصلاحيات

---

## 🚀 خطوات التشغيل السريع

### للفحص السريع:
1. افتح Command Prompt
2. انتقل إلى مجلد المشروع: `cd d:\java\java`
3. شغل: `run-database-tree-check.bat`
4. أدخل كلمة مرور SHIP_ERP

### للتحليل المتقدم:
1. افتح Command Prompt
2. انتقل إلى مجلد المشروع: `cd d:\java\java`
3. شغل: `run-system-tree-analyzer.bat`
4. استخدم الواجهة الرسومية للتحليل والإصلاح

---

## 📞 الدعم الفني

### في حالة المشاكل:
1. **تحقق من الاتصال بقاعدة البيانات**
2. **تأكد من صحة كلمات المرور**
3. **راجع رسائل الخطأ في وحدة التحكم**
4. **استخدم الأدوات المتاحة للتشخيص**

### ملفات السجلات:
- رسائل وحدة التحكم
- ملفات التقارير المصدرة
- سجلات قاعدة البيانات

---

## ✅ قائمة المراجعة

- [ ] فحص وجود الجدول
- [ ] تحليل الهيكل والأعمدة
- [ ] فحص الإحصائيات العامة
- [ ] اكتشاف المشاكل المحتملة
- [ ] مراجعة العقد اليتيمة
- [ ] فحص الأسماء المكررة
- [ ] التحقق من كلاسات النوافذ
- [ ] مراجعة العقد غير النشطة
- [ ] فحص الفهارس والقيود
- [ ] تطبيق التوصيات
- [ ] إنشاء نسخة احتياطية
- [ ] توثيق النتائج

---

**📊 هذا التقرير يوفر إطار عمل شامل لفحص وصيانة جدول شجرة الأنظمة في نظام Ship ERP**
