@echo off
echo ========================================
echo    TEST MEASUREMENT UNITS - FIXED
echo    اختبار وحدات القياس - مُصحح
echo ========================================

cd /d "e:\ship_erp\java"

set CP=.;lib\ojdbc11.jar;lib\orai18n.jar;lib\commons-logging-1.2.jar

echo [INFO] Testing Fixed Measurement Units Window...
echo.
echo Database Table: ERP_MEASUREMENT
echo Columns: MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, etc.
echo.

echo [1] Cleaning and compiling...
if exist "*.class" del /q *.class

echo [2] Compiling table structure checker...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\CheckERPMeasurementTable.java

echo [3] Compiling fixed Measurement Units Window...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\MeasurementUnitsWindow.java

echo [4] Compiling core components...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\DatabaseConfig.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ItemData.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UIUtils.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\SettingsManager.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\GeneralSettingsWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\User.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserFormDialog.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserPermissionsDialog.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserManagementWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\RealItemDataWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ComprehensiveItemDataWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ItemGroupsManagementWindow.java

echo [5] Compiling Tree Menu with fixed support...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\TreeMenuPanel.java

echo [6] Compiling Enhanced Main Window...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\EnhancedMainWindow.java

echo [7] Compiling Complete Oracle System...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\CompleteOracleSystemTest.java

echo.
echo [SUCCESS] All components compiled with fixed Measurement Units!
echo.

echo ========================================
echo    TESTING FIXED MEASUREMENT UNITS
echo ========================================
echo.

echo [TEST 1] Checking ERP_MEASUREMENT table structure...
java -cp "%CP%" CheckERPMeasurementTable

echo.
echo [TEST 2] Testing standalone Measurement Units Window...
java -cp "%CP%" MeasurementUnitsWindow

echo.
echo [TEST 3] Testing complete system with fixed window...
java -cp "%CP%" -Dfile.encoding=UTF-8 -Doracle.jdbc.defaultNChar=true CompleteOracleSystemTest

echo.
echo ========================================
echo    FIXED MEASUREMENT UNITS TEST COMPLETED
echo ========================================
pause
