import java.sql.*;

/**
 * تحديث جدول النظام لاستخدام نظام المظاهر الكامل
 * Update System Tree for Complete Theme System
 */
public class UpdateSystemTreeForCompleteThemes {
    
    public static void main(String[] args) {
        System.out.println("🔧 تحديث جدول النظام لنظام المظاهر الكامل...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            updateSystemTree(connection);
            
            connection.close();
            System.out.println("✅ تم تحديث جدول النظام بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحديث جدول النظام: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void updateSystemTree(Connection connection) throws SQLException {
        System.out.println("📋 تحديث إعدادات نافذة المظاهر في جدول النظام...");
        
        // البحث عن النافذة الحالية
        String searchSQL = "SELECT TREE_ID, NODE_NAME_AR, WINDOW_CLASS FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR LIKE '%مظهر%' OR NODE_NAME_AR LIKE '%واجهة%'";
        
        try (PreparedStatement searchStmt = connection.prepareStatement(searchSQL);
             ResultSet rs = searchStmt.executeQuery()) {
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                int treeId = rs.getInt("TREE_ID");
                String nodeName = rs.getString("NODE_NAME_AR");
                String currentClassName = rs.getString("WINDOW_CLASS");
                
                System.out.println("  وجدت نافذة: " + nodeName + " (ID: " + treeId + ")");
                System.out.println("  الكلاس الحالي: " + currentClassName);
                
                // تحديث اسم الكلاس إلى النافذة الجديدة
                String updateSQL = "UPDATE ERP_SYSTEM_TREE SET WINDOW_CLASS = ?, NODE_DESCRIPTION = ?, LAST_UPDATED = SYSDATE WHERE TREE_ID = ?";
                
                try (PreparedStatement updateStmt = connection.prepareStatement(updateSQL)) {
                    updateStmt.setString(1, "CompleteThemeWindow");
                    updateStmt.setString(2, "نظام المظاهر الشامل الكامل مع أكثر من 40 مظهر مختلف");
                    updateStmt.setInt(3, treeId);
                    
                    int updated = updateStmt.executeUpdate();
                    
                    if (updated > 0) {
                        System.out.println("  ✅ تم تحديث الكلاس إلى: CompleteThemeWindow");
                    } else {
                        System.err.println("  ❌ فشل في تحديث الكلاس");
                    }
                }
            }
            
            if (!found) {
                System.out.println("  لم يتم العثور على نافذة المظاهر، سيتم إنشاؤها...");
                createThemeWindowEntry(connection);
            }
        }
        
        // التحقق من النتيجة
        System.out.println("\n📋 التحقق من النتيجة:");
        try (PreparedStatement checkStmt = connection.prepareStatement(searchSQL);
             ResultSet rs = checkStmt.executeQuery()) {
            
            while (rs.next()) {
                String nodeName = rs.getString("NODE_NAME_AR");
                String className = rs.getString("WINDOW_CLASS");
                System.out.println("  " + nodeName + " -> " + className);
            }
        }
    }
    
    private static void createThemeWindowEntry(Connection connection) throws SQLException {
        System.out.println("📝 إنشاء إدخال جديد لنافذة المظاهر...");
        
        // الحصول على معرف فئة الإعدادات العامة
        int settingsId = getCategoryId(connection, "الإعدادات العامة");
        
        if (settingsId == 0) {
            System.err.println("❌ لم يتم العثور على فئة الإعدادات العامة");
            return;
        }
        
        // الحصول على الترتيب التالي
        int nextOrder = getNextOrder(connection, settingsId);
        
        String insertSQL = """
            INSERT INTO ERP_SYSTEM_TREE 
            (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, 
             WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
             CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
            VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, ?, ?, ?, ?, 'WINDOW', ?, ?, 2, 'Y', 'Y', 
                    SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
            stmt.setInt(1, settingsId);
            stmt.setString(2, "نظام المظاهر الشامل");
            stmt.setString(3, "Complete Theme System");
            stmt.setString(4, "نظام المظاهر الشامل الكامل مع أكثر من 40 مظهر مختلف من مختلف المكتبات");
            stmt.setString(5, "CompleteThemeWindow");
            stmt.setInt(6, nextOrder);
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("  ✅ تم إنشاء إدخال نافذة المظاهر الشاملة بنجاح");
            } else {
                System.err.println("  ❌ فشل في إنشاء إدخال نافذة المظاهر");
            }
        }
    }
    
    private static int getCategoryId(Connection conn, String categoryName) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("TREE_ID");
                }
            }
        }
        
        return 0;
    }
    
    private static int getNextOrder(Connection conn, int parentId) throws SQLException {
        String sql = "SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        
        return 1;
    }
}
