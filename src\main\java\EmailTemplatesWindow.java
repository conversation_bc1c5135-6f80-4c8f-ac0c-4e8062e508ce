import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.DefaultListModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JEditorPane;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JList;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.JToolBar;
import javax.swing.JTree;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;

/**
 * نافذة إدارة القوالب الشاملة والمتقدمة Advanced Email Templates Management Window
 */
public class EmailTemplatesWindow extends JFrame {

    // المكونات الرئيسية
    private Font arabicFont;
    private Font boldArabicFont;
    private Connection connection;

    // مكونات الواجهة
    private JSplitPane mainSplitPane;
    private JSplitPane rightSplitPane;

    // لوحة الفئات
    private JTree categoriesTree;
    private DefaultTreeModel categoriesTreeModel;
    private DefaultMutableTreeNode rootNode;

    // لوحة القوالب
    private JTable templatesTable;
    private DefaultTableModel templatesTableModel;
    private JTextField searchField;
    private JComboBox<String> filterCombo;

    // لوحة المعاينة والتحرير
    private JTabbedPane previewTabs;
    private JEditorPane htmlPreview;
    private JTextArea textPreview;
    private JPanel variablesPanel;

    // شريط الأدوات
    private JToolBar toolBar;
    private JButton newTemplateBtn;
    private JButton editTemplateBtn;
    private JButton deleteTemplateBtn;
    private JButton copyTemplateBtn;
    private JButton importTemplateBtn;
    private JButton exportTemplateBtn;
    private JButton onlineTemplatesBtn;

    // شريط الحالة
    private JLabel statusLabel;
    private JProgressBar progressBar;

    // بيانات القوالب
    private List<EmailTemplate> loadedTemplates;
    private EmailTemplate selectedTemplate;
    private Map<Integer, String> categoriesMap;

    public EmailTemplatesWindow() {
        initializeComponents();
        setupDatabase();
        setupLayout();
        setupEventHandlers();
        loadData();

        setTitle("إدارة القوالب - Email Templates Management");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1400, 900);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void initializeComponents() {
        // الخطوط
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        boldArabicFont = new Font("Tahoma", Font.BOLD, 12);

        // البيانات
        loadedTemplates = new ArrayList<>();
        categoriesMap = new HashMap<>();

        // شجرة الفئات
        rootNode = new DefaultMutableTreeNode("فئات القوالب");
        categoriesTreeModel = new DefaultTreeModel(rootNode);
        categoriesTree = new JTree(categoriesTreeModel);
        categoriesTree.setFont(arabicFont);
        categoriesTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        categoriesTree.setRootVisible(true);
        categoriesTree.setShowsRootHandles(true);

        // جدول القوالب
        String[] columnNames =
                {"الاسم", "الفئة", "النوع", "اللغة", "آخر استخدام", "عدد الاستخدامات", "الحالة"};
        templatesTableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        templatesTable = new JTable(templatesTableModel);
        templatesTable.setFont(arabicFont);
        templatesTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        templatesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        templatesTable.setRowHeight(25);

        // حقل البحث
        searchField = new JTextField();
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // مرشح الفئات
        filterCombo = new JComboBox<>();
        filterCombo.setFont(arabicFont);
        filterCombo.addItem("جميع الفئات");

        // تبويبات المعاينة
        previewTabs = new JTabbedPane();
        previewTabs.setFont(arabicFont);
        previewTabs.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // معاينة HTML
        htmlPreview = new JEditorPane();
        htmlPreview.setContentType("text/html");
        htmlPreview.setEditable(false);
        htmlPreview.setFont(arabicFont);

        // معاينة النص
        textPreview = new JTextArea();
        textPreview.setFont(arabicFont);
        textPreview.setEditable(false);
        textPreview.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        textPreview.setLineWrap(true);
        textPreview.setWrapStyleWord(true);

        // لوحة المتغيرات
        variablesPanel = new JPanel(new GridBagLayout());
        variablesPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إضافة التبويبات
        previewTabs.addTab("معاينة HTML", new JScrollPane(htmlPreview));
        previewTabs.addTab("معاينة النص", new JScrollPane(textPreview));
        previewTabs.addTab("المتغيرات", new JScrollPane(variablesPanel));

        // شريط الأدوات
        createToolBar();

        // شريط الحالة
        statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);
        progressBar = new JProgressBar();
        progressBar.setVisible(false);
    }

    private void createToolBar() {
        toolBar = new JToolBar();
        toolBar.setFloatable(false);
        toolBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // أزرار شريط الأدوات
        newTemplateBtn = createToolBarButton("جديد", "قالب جديد", this::createNewTemplate);
        editTemplateBtn = createToolBarButton("تحرير", "تحرير القالب", this::editTemplate);
        deleteTemplateBtn = createToolBarButton("حذف", "حذف القالب", this::deleteTemplate);
        copyTemplateBtn = createToolBarButton("نسخ", "نسخ القالب", this::copyTemplate);

        toolBar.add(newTemplateBtn);
        toolBar.add(editTemplateBtn);
        toolBar.add(deleteTemplateBtn);
        toolBar.add(copyTemplateBtn);
        toolBar.addSeparator();

        importTemplateBtn = createToolBarButton("استيراد", "استيراد قالب", this::importTemplate);
        exportTemplateBtn = createToolBarButton("تصدير", "تصدير القالب", this::exportTemplate);
        toolBar.add(importTemplateBtn);
        toolBar.add(exportTemplateBtn);
        toolBar.addSeparator();

        onlineTemplatesBtn =
                createToolBarButton("إنترنت", "قوالب من الإنترنت", this::browseOnlineTemplates);
        toolBar.add(onlineTemplatesBtn);

        // إضافة مساحة مرنة
        toolBar.add(Box.createHorizontalGlue());

        // إضافة البحث
        JLabel searchLabel = new JLabel("بحث:");
        searchLabel.setFont(boldArabicFont);
        searchLabel.setForeground(new Color(60, 60, 60));
        toolBar.add(searchLabel);
        toolBar.add(Box.createHorizontalStrut(8));

        // تحسين حقل البحث
        searchField.setMaximumSize(new Dimension(250, 30));
        searchField.setPreferredSize(new Dimension(250, 30));
        searchField.setFont(new Font("Tahoma", Font.PLAIN, 14));
        searchField.setBorder(
                BorderFactory.createCompoundBorder(BorderFactory.createLoweredBevelBorder(),
                        BorderFactory.createEmptyBorder(5, 8, 5, 8)));
        searchField.setBackground(Color.WHITE);
        searchField.setToolTipText("اكتب هنا للبحث في القوالب...");
        toolBar.add(searchField);

        // إضافة زر البحث
        JButton searchButton = new JButton("بحث");
        searchButton.setFont(boldArabicFont);
        searchButton.setPreferredSize(new Dimension(60, 30));
        searchButton.setBackground(new Color(70, 130, 180));
        searchButton.setForeground(Color.WHITE);
        searchButton.setBorder(BorderFactory.createRaisedBevelBorder());
        searchButton.setToolTipText("البحث في القوالب");
        searchButton.addActionListener(e -> searchTemplates());
        toolBar.add(Box.createHorizontalStrut(5));
        toolBar.add(searchButton);
        toolBar.add(Box.createHorizontalStrut(15));

        JLabel filterLabel = new JLabel("الفئة:");
        filterLabel.setFont(boldArabicFont);
        filterLabel.setForeground(new Color(60, 60, 60));
        toolBar.add(filterLabel);
        toolBar.add(Box.createHorizontalStrut(8));

        // تحسين مربع الفئات
        filterCombo.setMaximumSize(new Dimension(180, 30));
        filterCombo.setPreferredSize(new Dimension(180, 30));
        filterCombo.setFont(new Font("Tahoma", Font.PLAIN, 13));
        filterCombo.setBorder(BorderFactory.createLoweredBevelBorder());
        toolBar.add(filterCombo);
    }

    private JButton createToolBarButton(String text, String tooltip, Runnable action) {
        JButton button = new JButton(text);
        button.setToolTipText(tooltip);
        button.setFont(boldArabicFont);
        button.setFocusPainted(false);
        button.setPreferredSize(new Dimension(80, 35));
        button.setBackground(new Color(240, 240, 240));
        button.setBorder(BorderFactory.createRaisedBevelBorder());
        button.addActionListener(e -> action.run());

        // تأثير hover
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(220, 220, 220));
            }

            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(240, 240, 240));
            }
        });

        return button;
    }

    private void setupDatabase() {
        try {
            // الحصول على اتصال قاعدة البيانات
            connection = getConnection();

            // التأكد من وجود الجداول
            ensureTablesExist();

        } catch (SQLException e) {
            showError("خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
        }
    }

    private Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "SHIP_ERP";
            String password = "ship_erp_password";
            return java.sql.DriverManager.getConnection(url, username, password);
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }

    private void ensureTablesExist() {
        try {
            // فحص وجود الجداول
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet rs = metaData.getTables(null, null, "EMAIL_TEMPLATES", null);

            if (!rs.next()) {
                // إنشاء الجداول إذا لم تكن موجودة
                createEmailTemplatesTables();
                showMessage("تم إنشاء جداول نظام إدارة القوالب بنجاح!");
            }

        } catch (SQLException e) {
            showError("خطأ في فحص الجداول: " + e.getMessage());
        }
    }

    private void createEmailTemplatesTables() throws SQLException {
        // إنشاء جدول فئات القوالب
        String categoriesTable = """
                CREATE TABLE EMAIL_TEMPLATE_CATEGORIES (
                    CATEGORY_ID NUMBER PRIMARY KEY,
                    CATEGORY_NAME_AR NVARCHAR2(100) NOT NULL,
                    CATEGORY_NAME_EN VARCHAR2(100) NOT NULL,
                    DESCRIPTION NVARCHAR2(500),
                    ICON_NAME VARCHAR2(50),
                    COLOR_CODE VARCHAR2(7),
                    SORT_ORDER NUMBER DEFAULT 0,
                    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                    CREATED_DATE DATE DEFAULT SYSDATE
                )
                """;

        // إنشاء جدول القوالب
        String templatesTable =
                """
                        CREATE TABLE EMAIL_TEMPLATES (
                            TEMPLATE_ID NUMBER PRIMARY KEY,
                            TEMPLATE_NAME_AR NVARCHAR2(200) NOT NULL,
                            TEMPLATE_NAME_EN VARCHAR2(200) NOT NULL,
                            CATEGORY_ID NUMBER,
                            SUBJECT_AR NVARCHAR2(500),
                            SUBJECT_EN VARCHAR2(500),
                            BODY_HTML CLOB,
                            BODY_TEXT CLOB,
                            TEMPLATE_TYPE VARCHAR2(20) DEFAULT 'STANDARD',
                            LANGUAGE_CODE VARCHAR2(5) DEFAULT 'AR',
                            IS_HTML CHAR(1) DEFAULT 'Y',
                            IS_ACTIVE CHAR(1) DEFAULT 'Y',
                            USAGE_COUNT NUMBER DEFAULT 0,
                            LAST_USED_DATE DATE,
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CONSTRAINT FK_TEMPLATE_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES EMAIL_TEMPLATE_CATEGORIES(CATEGORY_ID)
                        )
                        """;

        try (java.sql.Statement stmt = connection.createStatement()) {
            // إنشاء الجداول
            try {
                stmt.execute(categoriesTable);
                System.out.println("✅ تم إنشاء جدول فئات القوالب");
            } catch (SQLException e) {
                if (e.getErrorCode() != 955) { // ORA-00955: name is already used
                    throw e;
                }
            }

            try {
                stmt.execute(templatesTable);
                System.out.println("✅ تم إنشاء جدول القوالب");
            } catch (SQLException e) {
                if (e.getErrorCode() != 955) {
                    throw e;
                }
            }

            // إنشاء sequences
            try {
                stmt.execute(
                        "CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_CATEGORIES START WITH 1 INCREMENT BY 1");
                stmt.execute("CREATE SEQUENCE SEQ_EMAIL_TEMPLATES START WITH 1 INCREMENT BY 1");
            } catch (SQLException e) {
                if (e.getErrorCode() != 955) {
                    throw e;
                }
            }

            // إدراج بيانات افتراضية
            insertDefaultCategories();
        }
    }

    private void insertDefaultCategories() throws SQLException {
        String sql =
                """
                        INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, SORT_ORDER)
                        VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, ?, ?, ?, ?)
                        """;

        String[][] categories = {{"عام", "General", "قوالب عامة للاستخدام اليومي", "1"},
                {"تسويق", "Marketing", "قوالب الحملات التسويقية", "2"},
                {"إشعارات", "Notifications", "قوالب الإشعارات والتنبيهات", "3"},
                {"فواتير", "Invoices", "قوالب الفواتير والمعاملات المالية", "4"}};

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            for (String[] category : categories) {
                stmt.setString(1, category[0]);
                stmt.setString(2, category[1]);
                stmt.setString(3, category[2]);
                stmt.setInt(4, Integer.parseInt(category[3]));
                try {
                    stmt.executeUpdate();
                } catch (SQLException e) {
                    // تجاهل الأخطاء إذا كانت البيانات موجودة مسبقاً
                    if (e.getErrorCode() != 1) { // ORA-00001: unique constraint violated
                        throw e;
                    }
                }
            }
        }
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // إضافة شريط الأدوات
        add(toolBar, BorderLayout.NORTH);

        // اللوحة الرئيسية
        mainSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        mainSplitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // اللوحة اليمنى (الفئات والقوالب)
        rightSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        rightSplitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // لوحة الفئات
        JPanel categoriesPanel = new JPanel(new BorderLayout());
        categoriesPanel.setBorder(BorderFactory.createTitledBorder("فئات القوالب"));
        categoriesPanel.add(new JScrollPane(categoriesTree), BorderLayout.CENTER);
        categoriesPanel.setPreferredSize(new Dimension(250, 200));

        // لوحة القوالب
        JPanel templatesPanel = new JPanel(new BorderLayout());
        templatesPanel.setBorder(BorderFactory.createTitledBorder("القوالب"));
        templatesPanel.add(new JScrollPane(templatesTable), BorderLayout.CENTER);

        rightSplitPane.setTopComponent(categoriesPanel);
        rightSplitPane.setBottomComponent(templatesPanel);
        rightSplitPane.setDividerLocation(250);

        // لوحة المعاينة
        JPanel previewPanel = new JPanel(new BorderLayout());
        previewPanel.setBorder(BorderFactory.createTitledBorder("معاينة القالب"));
        previewPanel.add(previewTabs, BorderLayout.CENTER);
        previewPanel.setPreferredSize(new Dimension(500, 0));

        mainSplitPane.setLeftComponent(rightSplitPane);
        mainSplitPane.setRightComponent(previewPanel);
        mainSplitPane.setDividerLocation(800);

        add(mainSplitPane, BorderLayout.CENTER);

        // شريط الحالة
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(new EmptyBorder(5, 10, 5, 10));
        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.add(progressBar, BorderLayout.EAST);
        add(statusPanel, BorderLayout.SOUTH);
    }

    private void setupEventHandlers() {
        // اختيار فئة
        categoriesTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode =
                    (DefaultMutableTreeNode) categoriesTree.getLastSelectedPathComponent();
            if (selectedNode != null && !selectedNode.isRoot()) {
                String categoryName = selectedNode.toString();
                filterTemplatesByCategory(categoryName);
            } else {
                loadTemplates();
            }
        });

        // اختيار قالب
        templatesTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = templatesTable.getSelectedRow();
                if (selectedRow >= 0) {
                    selectedTemplate = loadedTemplates.get(selectedRow);
                    showTemplatePreview(selectedTemplate);
                    updateButtonStates();
                }
            }
        });

        // البحث
        searchField.addActionListener(e -> searchTemplates());

        // البحث التلقائي عند الكتابة
        searchField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            public void changedUpdate(javax.swing.event.DocumentEvent e) {
                searchTemplates();
            }

            public void removeUpdate(javax.swing.event.DocumentEvent e) {
                searchTemplates();
            }

            public void insertUpdate(javax.swing.event.DocumentEvent e) {
                searchTemplates();
            }
        });

        // مرشح الفئات
        filterCombo.addActionListener(e -> {
            String selectedCategory = (String) filterCombo.getSelectedItem();
            if ("جميع الفئات".equals(selectedCategory)) {
                loadTemplates();
            } else {
                filterTemplatesByCategory(selectedCategory);
            }
        });
    }

    private void loadData() {
        SwingUtilities.invokeLater(() -> {
            statusLabel.setText("جاري تحميل البيانات...");
            progressBar.setVisible(true);
            progressBar.setIndeterminate(true);

            try {
                loadCategories();
                loadTemplates();
                statusLabel.setText("تم تحميل البيانات بنجاح");
            } catch (Exception e) {
                statusLabel.setText("خطأ في تحميل البيانات");
                showError("خطأ في تحميل البيانات: " + e.getMessage());
            } finally {
                progressBar.setVisible(false);
            }
        });
    }

    private void loadCategories() {
        try {
            String sql =
                    "SELECT CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN FROM EMAIL_TEMPLATE_CATEGORIES WHERE IS_ACTIVE = 'Y' ORDER BY SORT_ORDER";

            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                // مسح الفئات الحالية
                rootNode.removeAllChildren();
                categoriesMap.clear();
                filterCombo.removeAllItems();
                filterCombo.addItem("جميع الفئات");

                while (rs.next()) {
                    int categoryId = rs.getInt("CATEGORY_ID");
                    String nameAr = rs.getString("CATEGORY_NAME_AR");
                    String nameEn = rs.getString("CATEGORY_NAME_EN");

                    categoriesMap.put(categoryId, nameAr);

                    DefaultMutableTreeNode categoryNode = new DefaultMutableTreeNode(nameAr);
                    rootNode.add(categoryNode);

                    filterCombo.addItem(nameAr);
                }

                categoriesTreeModel.reload();
                categoriesTree.expandRow(0);
            }

        } catch (SQLException e) {
            showError("خطأ في تحميل الفئات: " + e.getMessage());
        }
    }

    private void loadTemplates() {
        try {
            String sql = """
                    SELECT t.TEMPLATE_ID, t.TEMPLATE_NAME_AR, t.CATEGORY_ID, t.TEMPLATE_TYPE,
                           t.LANGUAGE_CODE, t.LAST_USED_DATE, t.USAGE_COUNT, t.IS_ACTIVE,
                           c.CATEGORY_NAME_AR
                    FROM EMAIL_TEMPLATES t
                    LEFT JOIN EMAIL_TEMPLATE_CATEGORIES c ON t.CATEGORY_ID = c.CATEGORY_ID
                    WHERE t.IS_ACTIVE = 'Y'
                    ORDER BY t.TEMPLATE_NAME_AR
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                loadedTemplates.clear();
                templatesTableModel.setRowCount(0);

                while (rs.next()) {
                    EmailTemplate template = new EmailTemplate();
                    template.templateId = rs.getInt("TEMPLATE_ID");
                    template.nameAr = rs.getString("TEMPLATE_NAME_AR");
                    template.categoryId = rs.getInt("CATEGORY_ID");
                    template.templateType = rs.getString("TEMPLATE_TYPE");
                    template.languageCode = rs.getString("LANGUAGE_CODE");
                    template.lastUsedDate = rs.getDate("LAST_USED_DATE");
                    template.usageCount = rs.getInt("USAGE_COUNT");
                    template.isActive = "Y".equals(rs.getString("IS_ACTIVE"));
                    template.categoryName = rs.getString("CATEGORY_NAME_AR");

                    loadedTemplates.add(template);

                    Object[] row = {template.nameAr,
                            template.categoryName != null ? template.categoryName : "غير محدد",
                            getTemplateTypeDisplay(template.templateType), template.languageCode,
                            template.lastUsedDate != null ? template.lastUsedDate.toString()
                                    : "لم يستخدم",
                            template.usageCount, template.isActive ? "نشط" : "غير نشط"};

                    templatesTableModel.addRow(row);
                }
            }

        } catch (SQLException e) {
            showError("خطأ في تحميل القوالب: " + e.getMessage());
        }
    }

    private String getTemplateTypeDisplay(String type) {
        switch (type) {
            case "STANDARD":
                return "عادي";
            case "CAMPAIGN":
                return "حملة";
            case "NOTIFICATION":
                return "إشعار";
            case "SIGNATURE":
                return "توقيع";
            default:
                return type;
        }
    }

    private void filterTemplatesByCategory(String categoryName) {
        // تطبيق مرشح الفئة
        // سيتم تنفيذه في الجزء التالي
    }

    private void searchTemplates() {
        String searchTerm = searchField.getText().trim();

        if (searchTerm.isEmpty()) {
            // إذا كان البحث فارغاً، أعرض جميع القوالب
            loadTemplates();
            return;
        }

        // تطبيق البحث على القوالب المحملة
        templatesTableModel.setRowCount(0);

        for (EmailTemplate template : loadedTemplates) {
            boolean matches = false;

            // البحث في اسم القالب
            if (template.nameAr != null && template.nameAr.contains(searchTerm)) {
                matches = true;
            }

            // البحث في اسم الفئة
            if (template.categoryName != null && template.categoryName.contains(searchTerm)) {
                matches = true;
            }

            // البحث في نوع القالب
            String typeDisplay = getTemplateTypeDisplay(template.templateType);
            if (typeDisplay.contains(searchTerm)) {
                matches = true;
            }

            // إذا وُجد تطابق، أضف القالب للجدول
            if (matches) {
                Object[] row = {template.nameAr,
                        template.categoryName != null ? template.categoryName : "غير محدد",
                        typeDisplay, template.languageCode,
                        template.lastUsedDate != null ? template.lastUsedDate.toString()
                                : "لم يستخدم",
                        template.usageCount, template.isActive ? "نشط" : "غير نشط"};
                templatesTableModel.addRow(row);
            }
        }

        // تحديث شريط الحالة
        int resultCount = templatesTableModel.getRowCount();
        statusLabel
                .setText("تم العثور على " + resultCount + " قالب من أصل " + loadedTemplates.size());
    }

    private void showTemplatePreview(EmailTemplate template) {
        if (template == null) {
            htmlPreview.setText(
                    "<html><body><h3>لا يوجد قالب محدد</h3><p>يرجى اختيار قالب من القائمة لعرض المعاينة</p></body></html>");
            textPreview.setText("لا يوجد قالب محدد\n\nيرجى اختيار قالب من القائمة لعرض المعاينة");
            return;
        }

        // تحميل تفاصيل القالب من قاعدة البيانات
        try {
            String sql =
                    "SELECT SUBJECT_AR, BODY_HTML, BODY_TEXT FROM EMAIL_TEMPLATES WHERE TEMPLATE_ID = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setInt(1, template.templateId);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        String subject = rs.getString("SUBJECT_AR");
                        String bodyHtml = rs.getString("BODY_HTML");
                        String bodyText = rs.getString("BODY_TEXT");

                        // عرض معاينة HTML
                        if (bodyHtml != null && !bodyHtml.trim().isEmpty()) {
                            String htmlContent = "<html><head><meta charset='UTF-8'></head><body>"
                                    + "<h2 style='color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;'>"
                                    + (subject != null ? subject : "بدون موضوع") + "</h2>"
                                    + "<div style='margin-top: 20px;'>" + bodyHtml + "</div>"
                                    + "</body></html>";
                            htmlPreview.setText(htmlContent);
                        } else {
                            htmlPreview.setText(
                                    "<html><body><h3>لا يوجد محتوى HTML</h3></body></html>");
                        }

                        // عرض معاينة النص
                        if (bodyText != null && !bodyText.trim().isEmpty()) {
                            String textContent =
                                    "الموضوع: " + (subject != null ? subject : "بدون موضوع") + "\n"
                                            + "=".repeat(50) + "\n\n" + bodyText;
                            textPreview.setText(textContent);
                        } else {
                            textPreview.setText("لا يوجد محتوى نصي");
                        }

                        // عرض المتغيرات (إذا وجدت)
                        showTemplateVariables(template.templateId);

                    } else {
                        htmlPreview.setText(
                                "<html><body><h3>خطأ</h3><p>لم يتم العثور على القالب</p></body></html>");
                        textPreview.setText("خطأ: لم يتم العثور على القالب");
                    }
                }
            }
        } catch (SQLException e) {
            htmlPreview.setText("<html><body><h3>خطأ</h3><p>خطأ في تحميل القالب: " + e.getMessage()
                    + "</p></body></html>");
            textPreview.setText("خطأ في تحميل القالب: " + e.getMessage());
        }
    }

    private void showTemplateVariables(int templateId) {
        variablesPanel.removeAll();
        variablesPanel.setLayout(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        try {
            String sql =
                    "SELECT VARIABLE_NAME, VARIABLE_LABEL_AR, VARIABLE_TYPE, DEFAULT_VALUE FROM EMAIL_TEMPLATE_VARIABLES WHERE TEMPLATE_ID = ? ORDER BY SORT_ORDER";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setInt(1, templateId);
                try (ResultSet rs = stmt.executeQuery()) {
                    int row = 0;
                    boolean hasVariables = false;

                    while (rs.next()) {
                        hasVariables = true;
                        String varName = rs.getString("VARIABLE_NAME");
                        String varLabel = rs.getString("VARIABLE_LABEL_AR");
                        String varType = rs.getString("VARIABLE_TYPE");
                        String defaultValue = rs.getString("DEFAULT_VALUE");

                        gbc.gridx = 0;
                        gbc.gridy = row;
                        JLabel label = new JLabel((varLabel != null ? varLabel : varName) + ":");
                        label.setFont(arabicFont);
                        variablesPanel.add(label, gbc);

                        gbc.gridx = 1;
                        gbc.fill = GridBagConstraints.HORIZONTAL;
                        gbc.weightx = 1.0;
                        JTextField field = new JTextField(defaultValue != null ? defaultValue : "");
                        field.setFont(arabicFont);
                        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                        field.setToolTipText("نوع المتغير: " + varType);
                        variablesPanel.add(field, gbc);

                        row++;
                    }

                    if (!hasVariables) {
                        gbc.gridx = 0;
                        gbc.gridy = 0;
                        gbc.gridwidth = 2;
                        JLabel noVarsLabel = new JLabel("لا توجد متغيرات لهذا القالب");
                        noVarsLabel.setFont(arabicFont);
                        noVarsLabel.setForeground(Color.GRAY);
                        variablesPanel.add(noVarsLabel, gbc);
                    }
                }
            }
        } catch (SQLException e) {
            gbc.gridx = 0;
            gbc.gridy = 0;
            gbc.gridwidth = 2;
            JLabel errorLabel = new JLabel("خطأ في تحميل المتغيرات: " + e.getMessage());
            errorLabel.setFont(arabicFont);
            errorLabel.setForeground(Color.RED);
            variablesPanel.add(errorLabel, gbc);
        }

        variablesPanel.revalidate();
        variablesPanel.repaint();
    }

    private void updateButtonStates() {
        boolean hasSelection = selectedTemplate != null;
        editTemplateBtn.setEnabled(hasSelection);
        deleteTemplateBtn.setEnabled(hasSelection);
        copyTemplateBtn.setEnabled(hasSelection);
        exportTemplateBtn.setEnabled(hasSelection);
    }

    // إجراءات الأزرار
    private void createNewTemplate() {
        SwingUtilities.invokeLater(() -> {
            JDialog createDialog = new JDialog(this, "إنشاء قالب جديد", true);
            createDialog.setSize(700, 600);
            createDialog.setLocationRelativeTo(this);
            createDialog.setLayout(new BorderLayout());

            // لوحة المعلومات الأساسية
            JPanel infoPanel = new JPanel(new GridBagLayout());
            infoPanel.setBorder(BorderFactory.createTitledBorder("معلومات القالب"));
            infoPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            GridBagConstraints gbc = new GridBagConstraints();
            gbc.insets = new Insets(5, 5, 5, 5);
            gbc.anchor = GridBagConstraints.EAST;

            // اسم القالب
            gbc.gridx = 0;
            gbc.gridy = 0;
            infoPanel.add(new JLabel("اسم القالب:"), gbc);
            gbc.gridx = 1;
            gbc.fill = GridBagConstraints.HORIZONTAL;
            gbc.weightx = 1.0;
            JTextField nameField = new JTextField();
            nameField.setFont(arabicFont);
            nameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            infoPanel.add(nameField, gbc);

            // الفئة
            gbc.gridx = 0;
            gbc.gridy = 1;
            gbc.fill = GridBagConstraints.NONE;
            gbc.weightx = 0;
            infoPanel.add(new JLabel("الفئة:"), gbc);
            gbc.gridx = 1;
            gbc.fill = GridBagConstraints.HORIZONTAL;
            gbc.weightx = 1.0;
            JComboBox<String> categoryCombo = new JComboBox<>();
            categoryCombo.setFont(arabicFont);
            // تحميل الفئات
            for (String category : categoriesMap.values()) {
                categoryCombo.addItem(category);
            }
            infoPanel.add(categoryCombo, gbc);

            // موضوع الرسالة
            gbc.gridx = 0;
            gbc.gridy = 2;
            gbc.fill = GridBagConstraints.NONE;
            gbc.weightx = 0;
            infoPanel.add(new JLabel("الموضوع:"), gbc);
            gbc.gridx = 1;
            gbc.fill = GridBagConstraints.HORIZONTAL;
            gbc.weightx = 1.0;
            JTextField subjectField = new JTextField();
            subjectField.setFont(arabicFont);
            subjectField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            infoPanel.add(subjectField, gbc);

            // نوع القالب
            gbc.gridx = 0;
            gbc.gridy = 3;
            gbc.fill = GridBagConstraints.NONE;
            gbc.weightx = 0;
            infoPanel.add(new JLabel("نوع القالب:"), gbc);
            gbc.gridx = 1;
            gbc.fill = GridBagConstraints.HORIZONTAL;
            gbc.weightx = 1.0;
            JComboBox<String> typeCombo =
                    new JComboBox<>(new String[] {"عادي", "حملة تسويقية", "إشعار", "توقيع"});
            typeCombo.setFont(arabicFont);
            infoPanel.add(typeCombo, gbc);

            // محتوى القالب
            JTabbedPane contentTabs = new JTabbedPane();
            contentTabs.setFont(arabicFont);
            contentTabs.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            // محتوى HTML
            JTextArea htmlArea = new JTextArea();
            htmlArea.setFont(new Font("Courier New", Font.PLAIN, 12));
            htmlArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            htmlArea.setLineWrap(true);
            htmlArea.setWrapStyleWord(true);
            htmlArea.setText(
                    "<html>\n<head>\n<meta charset='UTF-8'>\n</head>\n<body>\n<h2>عنوان الرسالة</h2>\n<p>محتوى الرسالة هنا...</p>\n</body>\n</html>");
            JScrollPane htmlScroll = new JScrollPane(htmlArea);
            contentTabs.addTab("محتوى HTML", htmlScroll);

            // محتوى نصي
            JTextArea textArea = new JTextArea();
            textArea.setFont(arabicFont);
            textArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            textArea.setLineWrap(true);
            textArea.setWrapStyleWord(true);
            textArea.setText("عنوان الرسالة\n\nمحتوى الرسالة هنا...");
            JScrollPane textScroll = new JScrollPane(textArea);
            contentTabs.addTab("محتوى نصي", textScroll);

            // لوحة الأزرار
            JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
            buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            JButton saveButton = new JButton("حفظ القالب");
            saveButton.setFont(boldArabicFont);
            saveButton.addActionListener(e -> {
                if (nameField.getText().trim().isEmpty()) {
                    JOptionPane.showMessageDialog(createDialog, "يرجى إدخال اسم القالب", "خطأ",
                            JOptionPane.ERROR_MESSAGE);
                    return;
                }

                // حفظ القالب في قاعدة البيانات
                try {
                    saveNewTemplate(nameField.getText().trim(),
                            (String) categoryCombo.getSelectedItem(), subjectField.getText().trim(),
                            htmlArea.getText(), textArea.getText(),
                            getTemplateTypeCode((String) typeCombo.getSelectedItem()));

                    JOptionPane.showMessageDialog(createDialog, "تم حفظ القالب بنجاح!", "نجح الحفظ",
                            JOptionPane.INFORMATION_MESSAGE);
                    loadTemplates(); // إعادة تحميل القوالب
                    createDialog.dispose();
                } catch (Exception ex) {
                    JOptionPane.showMessageDialog(createDialog,
                            "خطأ في حفظ القالب: " + ex.getMessage(), "خطأ",
                            JOptionPane.ERROR_MESSAGE);
                }
            });

            JButton previewButton = new JButton("معاينة");
            previewButton.setFont(arabicFont);
            previewButton.addActionListener(e -> {
                // معاينة القالب
                showTemplatePreview(htmlArea.getText(), textArea.getText());
            });

            JButton cancelButton = new JButton("إلغاء");
            cancelButton.setFont(arabicFont);
            cancelButton.addActionListener(e -> createDialog.dispose());

            buttonPanel.add(saveButton);
            buttonPanel.add(previewButton);
            buttonPanel.add(cancelButton);

            createDialog.add(infoPanel, BorderLayout.NORTH);
            createDialog.add(contentTabs, BorderLayout.CENTER);
            createDialog.add(buttonPanel, BorderLayout.SOUTH);

            createDialog.setVisible(true);
        });
    }

    private String getTemplateTypeCode(String displayName) {
        switch (displayName) {
            case "عادي":
                return "STANDARD";
            case "حملة تسويقية":
                return "CAMPAIGN";
            case "إشعار":
                return "NOTIFICATION";
            case "توقيع":
                return "SIGNATURE";
            default:
                return "STANDARD";
        }
    }

    private void saveNewTemplate(String name, String categoryName, String subject,
            String htmlContent, String textContent, String type) throws SQLException {
        // البحث عن ID الفئة
        int categoryId = 0;
        for (Map.Entry<Integer, String> entry : categoriesMap.entrySet()) {
            if (entry.getValue().equals(categoryName)) {
                categoryId = entry.getKey();
                break;
            }
        }

        String sql = """
                INSERT INTO EMAIL_TEMPLATES (
                    TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN, CATEGORY_ID,
                    SUBJECT_AR, BODY_HTML, BODY_TEXT, TEMPLATE_TYPE, LANGUAGE_CODE
                ) VALUES (
                    SEQ_EMAIL_TEMPLATES.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, 'AR'
                )
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, name);
            stmt.setString(2, name); // نفس الاسم للإنجليزية مؤقتاً
            stmt.setInt(3, categoryId);
            stmt.setString(4, subject);
            stmt.setString(5, htmlContent);
            stmt.setString(6, textContent);
            stmt.setString(7, type);
            stmt.executeUpdate();
        }
    }

    private void showTemplatePreview(String htmlContent, String textContent) {
        JDialog previewDialog = new JDialog(this, "معاينة القالب", true);
        previewDialog.setSize(600, 500);
        previewDialog.setLocationRelativeTo(this);
        previewDialog.setLayout(new BorderLayout());

        JTabbedPane previewTabs = new JTabbedPane();
        previewTabs.setFont(arabicFont);
        previewTabs.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // معاينة HTML
        JEditorPane htmlPane = new JEditorPane();
        htmlPane.setContentType("text/html");
        htmlPane.setText(htmlContent);
        htmlPane.setEditable(false);
        previewTabs.addTab("معاينة HTML", new JScrollPane(htmlPane));

        // معاينة النص
        JTextArea textPane = new JTextArea(textContent);
        textPane.setFont(arabicFont);
        textPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        textPane.setEditable(false);
        textPane.setLineWrap(true);
        textPane.setWrapStyleWord(true);
        previewTabs.addTab("معاينة النص", new JScrollPane(textPane));

        JButton closeButton = new JButton("إغلاق");
        closeButton.setFont(arabicFont);
        closeButton.addActionListener(e -> previewDialog.dispose());

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(closeButton);

        previewDialog.add(previewTabs, BorderLayout.CENTER);
        previewDialog.add(buttonPanel, BorderLayout.SOUTH);
        previewDialog.setVisible(true);
    }

    private void editTemplate() {
        if (selectedTemplate != null) {
            showMessage("نافذة تحرير القالب قيد التطوير");
        }
    }

    private void deleteTemplate() {
        if (selectedTemplate != null) {
            int result = JOptionPane.showConfirmDialog(this,
                    "هل تريد حذف القالب: " + selectedTemplate.nameAr + "؟", "تأكيد الحذف",
                    JOptionPane.YES_NO_OPTION);

            if (result == JOptionPane.YES_OPTION) {
                try {
                    // حذف القالب من قاعدة البيانات
                    String sql = "DELETE FROM EMAIL_TEMPLATES WHERE TEMPLATE_ID = ?";
                    try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                        stmt.setInt(1, selectedTemplate.templateId);
                        int deletedRows = stmt.executeUpdate();

                        if (deletedRows > 0) {
                            JOptionPane.showMessageDialog(this, "تم حذف القالب بنجاح!", "تم الحذف",
                                    JOptionPane.INFORMATION_MESSAGE);

                            // إعادة تحميل القوالب
                            loadTemplates();
                            selectedTemplate = null;
                            updateButtonStates();

                            // مسح المعاينة
                            htmlPreview.setText("");
                            textPreview.setText("");

                        } else {
                            JOptionPane.showMessageDialog(this, "فشل في حذف القالب", "خطأ",
                                    JOptionPane.ERROR_MESSAGE);
                        }
                    }
                } catch (SQLException e) {
                    JOptionPane.showMessageDialog(this, "خطأ في حذف القالب: " + e.getMessage(),
                            "خطأ", JOptionPane.ERROR_MESSAGE);
                }
            }
        }
    }

    private void copyTemplate() {
        if (selectedTemplate != null) {
            showMessage("نافذة نسخ القالب قيد التطوير");
        }
    }

    private void importTemplate() {
        showMessage("نافذة استيراد القالب قيد التطوير");
    }

    private void exportTemplate() {
        if (selectedTemplate != null) {
            showMessage("نافذة تصدير القالب قيد التطوير");
        }
    }

    private void browseOnlineTemplates() {
        SwingUtilities.invokeLater(() -> {
            OnlineTemplatesBrowser browser = new OnlineTemplatesBrowser(this);
            browser.setVisible(true);
        });
    }

    private void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "إشعار", JOptionPane.INFORMATION_MESSAGE);
    }

    private void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "خطأ", JOptionPane.ERROR_MESSAGE);
    }

    // كلاس بيانات القالب
    private static class EmailTemplate {
        int templateId;
        String nameAr;
        String nameEn;
        int categoryId;
        String categoryName;
        String subjectAr;
        String subjectEn;
        String bodyHtml;
        String bodyText;
        String templateType;
        String languageCode;
        boolean isHtml;
        boolean isActive;
        boolean isPublic;
        int usageCount;
        java.util.Date lastUsedDate;
        String tags;
        String previewImagePath;
    }

    // كلاس تصفح القوالب من الإنترنت
    private class OnlineTemplatesBrowser extends JDialog {
        private JList<OnlineTemplate> templatesList;
        private DefaultListModel<OnlineTemplate> templatesListModel;
        private JEditorPane previewPane;
        private JButton downloadButton;
        private JTextField searchOnlineField;

        public OnlineTemplatesBrowser(JFrame parent) {
            super(parent, "تصفح القوالب من الإنترنت", true);
            initializeOnlineComponents();
            setupOnlineLayout();
            loadOnlineTemplates();

            setSize(800, 600);
            setLocationRelativeTo(parent);
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        }

        private void initializeOnlineComponents() {
            templatesListModel = new DefaultListModel<>();
            templatesList = new JList<>(templatesListModel);
            templatesList.setFont(arabicFont);
            templatesList.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            templatesList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

            previewPane = new JEditorPane();
            previewPane.setContentType("text/html");
            previewPane.setEditable(false);

            downloadButton = new JButton("تحميل القالب");
            downloadButton.setFont(boldArabicFont);
            downloadButton.setEnabled(false);
            downloadButton.addActionListener(e -> downloadSelectedTemplate());

            searchOnlineField = new JTextField();
            searchOnlineField.setFont(new Font("Tahoma", Font.PLAIN, 14));
            searchOnlineField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            searchOnlineField.setPreferredSize(new Dimension(200, 30));
            searchOnlineField.setBorder(
                    BorderFactory.createCompoundBorder(BorderFactory.createLoweredBevelBorder(),
                            BorderFactory.createEmptyBorder(5, 8, 5, 8)));
            searchOnlineField.setBackground(Color.WHITE);
            searchOnlineField.setToolTipText("البحث في القوالب الأونلاين...");
        }

        private void setupOnlineLayout() {
            setLayout(new BorderLayout());

            // شريط البحث
            JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
            searchPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
            searchPanel.setBackground(new Color(245, 245, 245));

            JLabel searchLabel = new JLabel("البحث في القوالب:");
            searchLabel.setFont(boldArabicFont);
            searchLabel.setForeground(new Color(60, 60, 60));
            searchPanel.add(searchLabel);
            searchPanel.add(Box.createHorizontalStrut(10));

            searchPanel.add(searchOnlineField);
            searchPanel.add(Box.createHorizontalStrut(5));

            JButton searchBtn = new JButton("بحث");
            searchBtn.setFont(boldArabicFont);
            searchBtn.setPreferredSize(new Dimension(60, 30));
            searchBtn.setBackground(new Color(70, 130, 180));
            searchBtn.setForeground(Color.WHITE);
            searchBtn.setBorder(BorderFactory.createRaisedBevelBorder());
            searchBtn.addActionListener(e -> searchOnlineTemplates());
            searchPanel.add(searchBtn);
            add(searchPanel, BorderLayout.NORTH);

            // اللوحة الرئيسية
            JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
            splitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            // قائمة القوالب
            JPanel listPanel = new JPanel(new BorderLayout());
            listPanel.setBorder(BorderFactory.createTitledBorder("القوالب المتاحة"));
            listPanel.add(new JScrollPane(templatesList), BorderLayout.CENTER);
            listPanel.setPreferredSize(new Dimension(300, 0));

            // معاينة القالب
            JPanel previewPanel = new JPanel(new BorderLayout());
            previewPanel.setBorder(BorderFactory.createTitledBorder("معاينة القالب"));
            previewPanel.add(new JScrollPane(previewPane), BorderLayout.CENTER);

            JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
            buttonPanel.add(downloadButton);
            JButton closeBtn = new JButton("إغلاق");
            closeBtn.setFont(arabicFont);
            closeBtn.addActionListener(e -> dispose());
            buttonPanel.add(closeBtn);
            previewPanel.add(buttonPanel, BorderLayout.SOUTH);

            splitPane.setLeftComponent(listPanel);
            splitPane.setRightComponent(previewPanel);
            splitPane.setDividerLocation(300);

            add(splitPane, BorderLayout.CENTER);

            // معالج اختيار القالب
            templatesList.addListSelectionListener(e -> {
                if (!e.getValueIsAdjusting()) {
                    OnlineTemplate selected = templatesList.getSelectedValue();
                    if (selected != null) {
                        showOnlineTemplatePreview(selected);
                        downloadButton.setEnabled(true);
                    }
                }
            });
        }

        private void loadOnlineTemplates() {
            SwingUtilities.invokeLater(() -> {
                try {
                    // محاكاة تحميل القوالب من الإنترنت
                    templatesListModel.clear();

                    // قوالب تجريبية (في التطبيق الحقيقي ستأتي من API)
                    OnlineTemplate[] sampleTemplates = {new OnlineTemplate("قالب ترحيب احترافي",
                            "Professional Welcome Template", "قالب ترحيب أنيق للعملاء الجدد",
                            "welcome", "<h2>مرحباً بك!</h2><p>نحن سعداء بانضمامك إلينا...</p>"),
                            new OnlineTemplate("قالب فاتورة حديث", "Modern Invoice Template",
                                    "قالب فاتورة عصري وأنيق", "invoice",
                                    "<h2>فاتورة رقم: #12345</h2><table>...</table>"),
                            new OnlineTemplate("قالب نشرة إخبارية", "Newsletter Template",
                                    "قالب نشرة إخبارية جذاب", "newsletter",
                                    "<h1>النشرة الإخبارية</h1><div>أحدث الأخبار...</div>"),
                            new OnlineTemplate("قالب تأكيد طلب", "Order Confirmation Template",
                                    "قالب تأكيد الطلبات", "confirmation",
                                    "<h2>تم تأكيد طلبك</h2><p>شكراً لك على طلبك...</p>"),
                            new OnlineTemplate("قالب دعوة فعالية", "Event Invitation Template",
                                    "قالب دعوة للفعاليات", "invitation",
                                    "<h2>دعوة خاصة</h2><p>يسرنا دعوتك لحضور...</p>")};

                    for (OnlineTemplate template : sampleTemplates) {
                        templatesListModel.addElement(template);
                    }

                } catch (Exception e) {
                    showError("خطأ في تحميل القوالب من الإنترنت: " + e.getMessage());
                }
            });
        }

        private void searchOnlineTemplates() {
            String searchTerm = searchOnlineField.getText().trim();
            if (searchTerm.isEmpty()) {
                loadOnlineTemplates();
                return;
            }

            // تطبيق البحث (محاكاة)
            DefaultListModel<OnlineTemplate> filteredModel = new DefaultListModel<>();
            for (int i = 0; i < templatesListModel.size(); i++) {
                OnlineTemplate template = templatesListModel.getElementAt(i);
                if (template.nameAr.contains(searchTerm)
                        || template.nameEn.toLowerCase().contains(searchTerm.toLowerCase())
                        || template.description.contains(searchTerm)) {
                    filteredModel.addElement(template);
                }
            }
            templatesList.setModel(filteredModel);
        }

        private void showOnlineTemplatePreview(OnlineTemplate template) {
            String html = "<html><head><meta charset='UTF-8'></head><body>" + "<h3>"
                    + template.nameAr + "</h3>" + "<p><strong>الوصف:</strong> "
                    + template.description + "</p>" + "<p><strong>الفئة:</strong> "
                    + template.category + "</p>" + "<hr>" + "<h4>معاينة المحتوى:</h4>"
                    + template.htmlContent + "</body></html>";
            previewPane.setText(html);
        }

        private void downloadSelectedTemplate() {
            OnlineTemplate selected = templatesList.getSelectedValue();
            if (selected != null) {
                // محاكاة تحميل القالب
                int result = JOptionPane.showConfirmDialog(this,
                        "هل تريد تحميل القالب: " + selected.nameAr + "؟", "تأكيد التحميل",
                        JOptionPane.YES_NO_OPTION);

                if (result == JOptionPane.YES_OPTION) {
                    // هنا سيتم حفظ القالب في قاعدة البيانات
                    JOptionPane.showMessageDialog(this,
                            "تم تحميل القالب بنجاح!\nسيظهر في قائمة القوالب المحلية.", "تم التحميل",
                            JOptionPane.INFORMATION_MESSAGE);

                    // إعادة تحميل القوالب في النافذة الرئيسية
                    loadTemplates();
                    dispose();
                }
            }
        }
    }

    // كلاس بيانات القالب من الإنترنت
    private static class OnlineTemplate {
        String nameAr;
        String nameEn;
        String description;
        String category;
        String htmlContent;

        public OnlineTemplate(String nameAr, String nameEn, String description, String category,
                String htmlContent) {
            this.nameAr = nameAr;
            this.nameEn = nameEn;
            this.description = description;
            this.category = category;
            this.htmlContent = htmlContent;
        }

        @Override
        public String toString() {
            return nameAr;
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق مظهر النظام
                for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                    if ("Nimbus".equals(info.getName())) {
                        UIManager.setLookAndFeel(info.getClassName());
                        break;
                    }
                }
            } catch (Exception e) {
                // استخدام المظهر الافتراضي في حالة الخطأ
                e.printStackTrace();
            }

            new EmailTemplatesWindow().setVisible(true);
        });
    }
}
