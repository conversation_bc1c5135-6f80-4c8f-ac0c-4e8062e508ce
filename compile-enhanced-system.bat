@echo off
echo ========================================
echo    ENHANCED SYSTEM COMPILER
echo    مجمع النظام المحسن
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Compiling enhanced system with new libraries...
echo [معلومات] تجميع النظام المحسن مع المكتبات الجديدة...
echo.

echo ========================================
echo    PHASE 1: VERIFY LIBRARIES
echo    المرحلة 1: التحقق من المكتبات
echo ========================================

echo [1] Checking required libraries...

set CLASSPATH=.
for %%f in (lib\*.jar) do (
    set CLASSPATH=!CLASSPATH!;%%f
)

echo Libraries found:
dir lib\*.jar /b

echo.
echo ========================================
echo    PHASE 2: COMPILE CORE CLASSES
echo    المرحلة 2: تجميع الكلاسات الأساسية
echo ========================================

echo [2] Compiling core enhanced classes...

echo Compiling SecurityManager...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\SecurityManager.java
if errorlevel 1 (
    echo ERROR: Failed to compile SecurityManager
    pause
    exit /b 1
)

echo Compiling ConnectionPoolManager...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\ConnectionPoolManager.java
if errorlevel 1 (
    echo ERROR: Failed to compile ConnectionPoolManager
    pause
    exit /b 1
)

echo Compiling PerformanceMonitor...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\PerformanceMonitor.java
if errorlevel 1 (
    echo ERROR: Failed to compile PerformanceMonitor
    pause
    exit /b 1
)

echo Compiling EnhancedConfigManager...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\EnhancedConfigManager.java
if errorlevel 1 (
    echo ERROR: Failed to compile EnhancedConfigManager
    pause
    exit /b 1
)

echo.
echo ========================================
echo    PHASE 3: COMPILE EXISTING CLASSES
echo    المرحلة 3: تجميع الكلاسات الموجودة
echo ========================================

echo [3] Compiling existing system classes...

echo Compiling DatabaseConfig...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\DatabaseConfig.java

echo Compiling SettingsManager...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\SettingsManager.java

echo Compiling UIUtils...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\UIUtils.java

echo Compiling User...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\User.java

echo Compiling ItemData...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\ItemData.java

echo.
echo ========================================
echo    PHASE 4: COMPILE WINDOW CLASSES
echo    المرحلة 4: تجميع كلاسات النوافذ
echo ========================================

echo [4] Compiling window classes...

echo Compiling TreeMenuPanel...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\TreeMenuPanel.java

echo Compiling MeasurementUnitsWindow...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\MeasurementUnitsWindow.java

echo Compiling RealItemDataWindow...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\RealItemDataWindow.java

echo Compiling ComprehensiveItemDataWindow...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\ComprehensiveItemDataWindow.java

echo Compiling ItemGroupsManagementWindow...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\ItemGroupsManagementWindow.java

echo Compiling UserManagementWindow...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\UserManagementWindow.java

echo Compiling GeneralSettingsWindow...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\GeneralSettingsWindow.java

echo Compiling EnhancedMainWindow...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\EnhancedMainWindow.java

echo.
echo ========================================
echo    PHASE 5: COMPILE MAIN SYSTEM
echo    المرحلة 5: تجميع النظام الرئيسي
echo ========================================

echo [5] Compiling main system class...

echo Compiling CompleteOracleSystemTest...
javac -encoding UTF-8 -cp "%CLASSPATH%" -d . src\main\java\CompleteOracleSystemTest.java
if errorlevel 1 (
    echo ERROR: Failed to compile CompleteOracleSystemTest
    pause
    exit /b 1
)

echo.
echo ========================================
echo    VERIFICATION
echo    التحقق
echo ========================================

echo [6] Verifying compilation...

set /a count=0
for %%f in (*.class) do set /a count+=1

echo Total compiled classes: %count%
echo إجمالي الكلاسات المجمعة: %count%

if %count% LSS 10 (
    echo WARNING: Low number of compiled classes
    echo تحذير: عدد قليل من الكلاسات المجمعة
)

echo.
echo ========================================
echo    COMPILATION COMPLETED
echo    تم التجميع بنجاح
echo ========================================

echo.
echo [SUCCESS] Enhanced system compilation completed!
echo [نجح] تم تجميع النظام المحسن بنجاح!
echo.
echo Enhanced features available:
echo الميزات المحسنة المتاحة:
echo - ✅ Connection Pooling (تجميع الاتصالات)
echo - ✅ Password Encryption (تشفير كلمات المرور)
echo - ✅ Performance Monitoring (مراقبة الأداء)
echo - ✅ Enhanced Configuration (التكوين المحسن)
echo - ✅ Security Management (إدارة الأمان)
echo.
echo Next step: Run start-enhanced-system.bat
echo الخطوة التالية: تشغيل start-enhanced-system.bat
echo.

pause
