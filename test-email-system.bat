@echo off
echo ========================================
echo   Test Email Management System
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Compiling Email System...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\EmailAccountsWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile EmailAccountsWindow
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo Failed to compile TreeMenuPanel
    pause
    exit /b 1
)

echo.
echo [2] Testing Email Accounts Window...
start /min java -cp "lib\*;." EmailAccountsWindow

echo.
echo [3] Starting Main System...
start .\start-ship-erp.bat

echo.
echo ========================================
echo   Email System Test Complete!
echo ========================================
echo.
echo INSTRUCTIONS:
echo 1. Email Accounts Window opened directly
echo 2. Main system started - check Email Management category
echo 3. All windows support Arabic orientation
echo.
pause
