@echo off
echo ========================================
echo 💰 اختبار حفظ العملة الافتراضية
echo Testing Default Currency Save/Load
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/4] تجميع النافذة المصححة...
echo Compiling Fixed Window...
echo ========================================

echo تجميع CurrencyManagementWindow مع إصلاح العملة الافتراضية...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CurrencyManagementWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع النافذة بنجاح
) else (
    echo ❌ فشل في تجميع النافذة
    pause
    exit /b 1
)

echo.
echo [2/4] تشغيل النافذة للاختبار...
echo Running Window for Testing...
echo ========================================

echo.
echo 🧪 خطوات الاختبار:
echo ==================
echo 1. ستفتح نافذة إدارة العملات
echo 2. انتقل إلى تبويب "الإعدادات العامة"
echo 3. اختر عملة افتراضية مختلفة
echo 4. انقر "حفظ الإعدادات"
echo 5. أغلق النافذة
echo 6. افتح النافذة مرة أخرى
echo 7. تحقق من العملة الافتراضية

echo.
echo 📊 مراقبة الرسائل التشخيصية:
echo ==============================
echo • ستظهر رسائل في وحدة التحكم
echo • راقب رسائل الحفظ والتحميل
echo • تأكد من عدم وجود أخطاء

echo.
echo تشغيل النافذة الآن...
start "Currency Management - Default Currency Test" java -cp "lib\*;." CurrencyManagementWindow

echo.
echo [3/4] معلومات الإصلاحات المطبقة...
echo Applied Fixes Information...
echo ========================================

echo.
echo 🔧 الإصلاحات المطبقة (الإصدار المحدث):
echo ==========================================
echo ✅ إضافة case "DEFAULT_CURRENCY" في loadGeneralSettings()
echo ✅ إنشاء دالة setDefaultCurrencyInCombo() محسنة
echo ✅ تحسين دالة saveGeneralSettings() مع التحقق
echo ✅ إضافة رسائل تشخيصية مفصلة جداً
echo ✅ تحسين معالجة الأخطاء
echo ✅ إصلاح ترتيب تحميل البيانات
echo ✅ إضافة SwingUtilities.invokeLater للتوقيت
echo ✅ إضافة دالة verifyDefaultCurrencySaved()
echo ✅ تحسين آلية البحث في القائمة المنسدلة

echo.
echo 📋 الدوال المضافة/المحدثة:
echo ===========================
echo • setDefaultCurrencyInCombo(String currencyCode)
echo   - البحث عن العملة في القائمة المنسدلة
echo   - تحديد العملة الصحيحة
echo   - رسائل تشخيصية واضحة

echo • loadGeneralSettings() - محدثة
echo   - إضافة حالة DEFAULT_CURRENCY
echo   - رسائل تحميل مفصلة
echo   - عداد الإعدادات المحملة

echo • saveGeneralSettings() - محدثة
echo   - رسائل حفظ واضحة
echo   - تحقق من وجود العملة المختارة
echo   - معالجة أفضل للأخطاء

echo • updateSetting() - محدثة
echo   - رسائل تحديث مفصلة
echo   - عداد الصفوف المتأثرة
echo   - تتبع أفضل للعمليات

echo.
echo 🎯 المشكلة الأصلية:
echo ==================
echo ❌ العملة الافتراضية لا تُحفظ
echo ❌ عند إعادة فتح النافذة تعود للقيمة السابقة
echo ❌ لا توجد رسائل تشخيصية

echo.
echo ✅ الحل المطبق:
echo ================
echo ✅ إضافة تحميل العملة الافتراضية
echo ✅ تحسين آلية الحفظ
echo ✅ رسائل تشخيصية شاملة
echo ✅ معالجة أخطاء محسنة

echo.
echo [4/4] تعليمات الاختبار التفصيلية...
echo Detailed Testing Instructions...
echo ========================================

echo.
echo 🧪 اختبار شامل للعملة الافتراضية:
echo ==================================

echo.
echo 📝 الخطوة 1: الاختبار الأولي
echo -----------------------------
echo 1. افتح النافذة (تم تشغيلها)
echo 2. انتقل إلى "الإعدادات العامة"
echo 3. لاحظ العملة الافتراضية الحالية
echo 4. راقب رسائل التحميل في وحدة التحكم

echo.
echo 📝 الخطوة 2: تغيير العملة
echo ---------------------------
echo 1. اختر عملة مختلفة من القائمة
echo 2. انقر "حفظ الإعدادات"
echo 3. راقب رسائل الحفظ في وحدة التحكم
echo 4. تأكد من ظهور "تم حفظ الإعدادات بنجاح"

echo.
echo 📝 الخطوة 3: اختبار الثبات
echo ----------------------------
echo 1. أغلق النافذة
echo 2. افتح النافذة مرة أخرى
echo 3. انتقل إلى "الإعدادات العامة"
echo 4. تحقق من العملة الافتراضية
echo 5. يجب أن تكون العملة التي اخترتها

echo.
echo 📝 الخطوة 4: اختبار متقدم
echo ---------------------------
echo 1. جرب عملات مختلفة
echo 2. احفظ واختبر كل مرة
echo 3. تأكد من ثبات الاختيار
echo 4. راقب الرسائل التشخيصية

echo.
echo 🔍 الرسائل المتوقعة في وحدة التحكم:
echo ====================================
echo 📥 تحميل الإعدادات العامة...
echo 📋 تحميل إعداد: DEFAULT_CURRENCY = USD
echo ✅ تم تحديد العملة الافتراضية: USD
echo ✅ تم تحميل X إعداد

echo.
echo عند الحفظ:
echo 💾 حفظ العملة الافتراضية: EUR
echo 🔧 تحديث الإعداد: DEFAULT_CURRENCY = EUR
echo ✅ تم تحديث 1 صف للإعداد: DEFAULT_CURRENCY
echo ✅ تم حفظ العملة الافتراضية بنجاح

echo.
echo 🚨 إذا ظهرت مشاكل:
echo ===================
echo ⚠️ لم يتم العثور على العملة الافتراضية في القائمة
echo ⚠️ لم يتم اختيار عملة افتراضية
echo ⚠️ إعداد غير معروف: DEFAULT_CURRENCY

echo.
echo 💡 نصائح لحل المشاكل:
echo ======================
echo • تأكد من وجود العملات في قاعدة البيانات
echo • تحقق من تنسيق القائمة المنسدلة
echo • راجع رسائل وحدة التحكم للتفاصيل
echo • تأكد من صحة اتصال قاعدة البيانات

echo.
echo 🎉 النتائج المتوقعة بعد الإصلاح:
echo =================================
echo ✅ العملة الافتراضية تُحفظ بنجاح
echo ✅ العملة تبقى محفوظة بعد إعادة فتح النافذة
echo ✅ رسائل واضحة لجميع العمليات
echo ✅ معالجة صحيحة للأخطاء
echo ✅ تجربة مستخدم محسنة

echo.
echo ========================================
echo ✅ تم إصلاح مشكلة العملة الافتراضية!
echo Default Currency Issue Fixed!
echo ========================================

echo.
echo 📞 للدعم الفني:
echo ===============
echo • راجع رسائل وحدة التحكم
echo • تحقق من اتصال قاعدة البيانات
echo • تأكد من وجود الجدول ERP_CURRENCY_SETTINGS
echo • تحقق من صحة بيانات العملات

echo.
pause
