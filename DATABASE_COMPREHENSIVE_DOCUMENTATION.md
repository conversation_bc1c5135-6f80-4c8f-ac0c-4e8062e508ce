# توثيق قاعدة البيانات الشامل لنظام Ship ERP
## Comprehensive Database Documentation for Ship ERP System

**التاريخ:** 19 يوليو 2025  
**الإصدار:** 1.0.0  
**المؤلف:** Database Analysis Team  

---

## 📋 جدول المحتويات

1. [نظرة عامة](#نظرة-عامة)
2. [بنية قاعدة البيانات](#بنية-قاعدة-البيانات)
3. [الجداول الرئيسية](#الجداول-الرئيسية)
4. [العلاقات والمفاتيح](#العلاقات-والمفاتيح)
5. [الفهارس والأداء](#الفهارس-والأداء)
6. [الإجراءات المخزنة](#الإجراءات-المخزنة)
7. [النسخ الاحتياطي](#النسخ-الاحتياطي)
8. [الصيانة والمراقبة](#الصيانة-والمراقبة)

---

## 🎯 نظرة عامة

### معلومات قاعدة البيانات
- **نوع قاعدة البيانات:** Oracle Database 21c
- **الترميز:** UTF-8 مع دعم كامل للعربية
- **المستخدمون الرئيسيون:**
  - `ias20251` - المستخدم الرئيسي (كلمة المرور: ys123)
  - `SHIP_ERP` - مستخدم التطبيق (كلمة المرور: ship_erp_password)

### الغرض من قاعدة البيانات
نظام إدارة الشحنات المتكامل يدير:
- **إدارة الأصناف والمخزون**
- **تصنيف المنتجات والمجموعات**
- **وحدات القياس والتحويلات**
- **تتبع التكاليف والأسعار**
- **إدارة المستخدمين والصلاحيات**

---

## 🏗️ بنية قاعدة البيانات

### المخططات (Schemas)
```
Ship ERP Database
├── ias20251 (Primary Schema)
│   ├── IAS_ITM_MST (4,647 records)
│   ├── IAS_ITM_DTL (9,108 records)
│   ├── MEASUREMENT (18 units)
│   ├── IAS_SUB_GRP_DTL
│   ├── IAS_ASSISTANT_GROUP
│   └── IAS_DETAIL_GROUP
└── SHIP_ERP (Application Schema)
    ├── ERP_MEASUREMENT
    ├── ERP_SUB_GRP_DTL
    ├── ERP_ASSISTANT_GROUP
    ├── ERP_DETAIL_GROUP
    ├── ERP_GROUP_DETAILS
    └── ERP_MAINSUB_GRP_DTL
```

### إعدادات الاتصال
```properties
# Primary Connection
database.url=*************************************
database.username=ias20251
database.password=ys123

# Application Connection
database.url=*************************************
database.username=SHIP_ERP
database.password=ship_erp_password
```

---

## 📊 الجداول الرئيسية

### 1. جدول الأصناف الرئيسي (IAS_ITM_MST)

**الغرض:** تخزين معلومات الأصناف الأساسية

| العمود | النوع | الحجم | إجباري | الوصف |
|--------|-------|-------|---------|--------|
| ITM_CD | VARCHAR2 | 20 | ✅ | كود الصنف (المفتاح الأساسي) |
| ITM_NM | VARCHAR2 | 200 | ✅ | اسم الصنف بالإنجليزية |
| ITM_NM_AR | NVARCHAR2 | 200 | ❌ | اسم الصنف بالعربية |
| ITM_DESC | VARCHAR2 | 500 | ❌ | وصف الصنف |
| ITM_DESC_AR | NVARCHAR2 | 500 | ❌ | وصف الصنف بالعربية |
| ITM_GRP_CD | VARCHAR2 | 10 | ❌ | كود المجموعة |
| ITM_SUB_GRP_CD | VARCHAR2 | 10 | ❌ | كود المجموعة الفرعية |
| ITM_UNIT | VARCHAR2 | 10 | ❌ | وحدة القياس |
| ITM_PRICE | NUMBER | 15,3 | ❌ | سعر البيع |
| ITM_COST | NUMBER | 15,3 | ❌ | التكلفة |
| ITM_STATUS | VARCHAR2 | 1 | ❌ | الحالة (A=نشط, I=غير نشط) |
| CREATED_BY | VARCHAR2 | 50 | ❌ | منشئ السجل |
| CREATED_DATE | DATE | - | ❌ | تاريخ الإنشاء |
| MODIFIED_BY | VARCHAR2 | 50 | ❌ | معدل السجل |
| MODIFIED_DATE | DATE | - | ❌ | تاريخ التعديل |

**الإحصائيات:**
- **إجمالي السجلات:** 4,647
- **السجلات النشطة:** ~4,200
- **متوسط حجم السجل:** 250 بايت

### 2. جدول تفاصيل الأصناف (IAS_ITM_DTL)

**الغرض:** تخزين التفاصيل الإضافية للأصناف

| العمود | النوع | الحجم | إجباري | الوصف |
|--------|-------|-------|---------|--------|
| ITM_CD | VARCHAR2 | 20 | ✅ | كود الصنف (مفتاح خارجي) |
| ITM_DTL_CD | VARCHAR2 | 20 | ✅ | كود التفصيل |
| ITM_DTL_NM | VARCHAR2 | 200 | ❌ | اسم التفصيل |
| ITM_DTL_NM_AR | NVARCHAR2 | 200 | ❌ | اسم التفصيل بالعربية |
| ITM_DTL_DESC | VARCHAR2 | 500 | ❌ | وصف التفصيل |
| ITM_DTL_QTY | NUMBER | 15,3 | ❌ | الكمية |
| ITM_DTL_UNIT | VARCHAR2 | 10 | ❌ | وحدة القياس |
| ITM_DTL_PRICE | NUMBER | 15,3 | ❌ | السعر |
| ITM_DTL_STATUS | VARCHAR2 | 1 | ❌ | الحالة |
| CREATED_BY | VARCHAR2 | 50 | ❌ | منشئ السجل |
| CREATED_DATE | DATE | - | ❌ | تاريخ الإنشاء |

**الإحصائيات:**
- **إجمالي السجلات:** 9,108
- **متوسط التفاصيل لكل صنف:** 2.0

### 3. جدول وحدات القياس (MEASUREMENT)

**الغرض:** تعريف وحدات القياس المستخدمة

| العمود | النوع | الحجم | إجباري | الوصف |
|--------|-------|-------|---------|--------|
| UNIT_CD | VARCHAR2 | 10 | ✅ | كود الوحدة |
| UNIT_NM | VARCHAR2 | 50 | ✅ | اسم الوحدة |
| UNIT_NM_AR | NVARCHAR2 | 50 | ❌ | اسم الوحدة بالعربية |
| UNIT_DESC | VARCHAR2 | 200 | ❌ | وصف الوحدة |
| UNIT_TYPE | VARCHAR2 | 20 | ❌ | نوع الوحدة |
| CONVERSION_FACTOR | NUMBER | 10,6 | ❌ | معامل التحويل |
| BASE_UNIT | VARCHAR2 | 10 | ❌ | الوحدة الأساسية |
| STATUS | VARCHAR2 | 1 | ❌ | الحالة |

**وحدات القياس المتاحة:**
- **الوزن:** KG, G, LB, OZ
- **الطول:** M, CM, MM, IN, FT
- **الحجم:** L, ML, GAL
- **العدد:** PCS, BOX, PACK
- **المساحة:** SQM, SQFT

---

## 🔗 العلاقات والمفاتيح

### المفاتيح الأساسية
```sql
-- جدول الأصناف الرئيسي
ALTER TABLE IAS_ITM_MST ADD CONSTRAINT PK_ITM_MST PRIMARY KEY (ITM_CD);

-- جدول تفاصيل الأصناف
ALTER TABLE IAS_ITM_DTL ADD CONSTRAINT PK_ITM_DTL PRIMARY KEY (ITM_CD, ITM_DTL_CD);

-- جدول وحدات القياس
ALTER TABLE MEASUREMENT ADD CONSTRAINT PK_MEASUREMENT PRIMARY KEY (UNIT_CD);
```

### المفاتيح الخارجية
```sql
-- ربط تفاصيل الأصناف بالأصناف الرئيسية
ALTER TABLE IAS_ITM_DTL 
ADD CONSTRAINT FK_ITM_DTL_MST 
FOREIGN KEY (ITM_CD) REFERENCES IAS_ITM_MST(ITM_CD);

-- ربط وحدة القياس في الأصناف
ALTER TABLE IAS_ITM_MST 
ADD CONSTRAINT FK_ITM_MST_UNIT 
FOREIGN KEY (ITM_UNIT) REFERENCES MEASUREMENT(UNIT_CD);
```

### مخطط العلاقات
```
IAS_ITM_MST (1) ←→ (N) IAS_ITM_DTL
     ↓
MEASUREMENT (1) ←→ (N) IAS_ITM_MST
     ↓
IAS_SUB_GRP_DTL (1) ←→ (N) IAS_ITM_MST
```

---

## 🔍 الفهارس والأداء

### الفهارس الأساسية
```sql
-- فهارس الأداء للجدول الرئيسي
CREATE INDEX IDX_ITM_MST_NAME ON IAS_ITM_MST(ITM_NM);
CREATE INDEX IDX_ITM_MST_GROUP ON IAS_ITM_MST(ITM_GRP_CD);
CREATE INDEX IDX_ITM_MST_STATUS ON IAS_ITM_MST(ITM_STATUS);
CREATE INDEX IDX_ITM_MST_CREATED ON IAS_ITM_MST(CREATED_DATE);

-- فهارس مركبة للاستعلامات المعقدة
CREATE INDEX IDX_ITM_MST_COMPOSITE 
ON IAS_ITM_MST(ITM_GRP_CD, ITM_SUB_GRP_CD, ITM_STATUS);
```

### إحصائيات الأداء
| الاستعلام | متوسط وقت التنفيذ | الفهرس المستخدم |
|-----------|-------------------|------------------|
| البحث بالكود | < 1ms | PK_ITM_MST |
| البحث بالاسم | < 10ms | IDX_ITM_MST_NAME |
| البحث بالمجموعة | < 50ms | IDX_ITM_MST_GROUP |
| الاستعلامات المعقدة | < 100ms | IDX_ITM_MST_COMPOSITE |

---

## ⚙️ الإجراءات المخزنة

### 1. البحث في الأصناف
```sql
CREATE OR REPLACE PROCEDURE SP_SEARCH_ITEMS(
    p_search_term IN VARCHAR2,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_cursor FOR
    SELECT ITM_CD, ITM_NM, ITM_NM_AR, ITM_PRICE, ITM_UNIT
    FROM IAS_ITM_MST
    WHERE UPPER(ITM_NM) LIKE '%' || UPPER(p_search_term) || '%'
       OR UPPER(ITM_NM_AR) LIKE '%' || UPPER(p_search_term) || '%'
       OR UPPER(ITM_CD) LIKE '%' || UPPER(p_search_term) || '%'
    ORDER BY ITM_CD;
END;
```

### 2. إحصائيات المجموعات
```sql
CREATE OR REPLACE PROCEDURE SP_GROUP_STATISTICS(
    p_group_code IN VARCHAR2 DEFAULT NULL,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    IF p_group_code IS NULL THEN
        OPEN p_cursor FOR
        SELECT ITM_GRP_CD, COUNT(*) as TOTAL_ITEMS,
               AVG(ITM_PRICE) as AVG_PRICE,
               MIN(ITM_PRICE) as MIN_PRICE,
               MAX(ITM_PRICE) as MAX_PRICE
        FROM IAS_ITM_MST 
        WHERE ITM_STATUS = 'A'
        GROUP BY ITM_GRP_CD
        ORDER BY TOTAL_ITEMS DESC;
    ELSE
        OPEN p_cursor FOR
        SELECT ITM_GRP_CD, COUNT(*) as TOTAL_ITEMS,
               AVG(ITM_PRICE) as AVG_PRICE,
               MIN(ITM_PRICE) as MIN_PRICE,
               MAX(ITM_PRICE) as MAX_PRICE
        FROM IAS_ITM_MST 
        WHERE ITM_GRP_CD = p_group_code
          AND ITM_STATUS = 'A'
        GROUP BY ITM_GRP_CD;
    END IF;
END;
```

---

## 💾 النسخ الاحتياطي

### استراتيجية النسخ الاحتياطي
- **يومي:** نسخة كاملة للبيانات (2:00 AM)
- **أسبوعي:** نسخة مضغوطة (الأحد)
- **شهري:** نسخة أرشيفية (اليوم الأول)

### أدوات النسخ الاحتياطي
```bash
# Oracle Data Pump Export
expdp ias20251/ys123@localhost:1521/ORCL 
    schemas=ias20251 
    directory=DATA_PUMP_DIR 
    dumpfile=backup_%date%.dmp 
    compression=all

# SQL Export Alternative
sqlplus ias20251/ys123@localhost:1521/ORCL @export_script.sql
```

### مواقع النسخ الاحتياطي
```
d:\java\java\backups\
├── daily\     (30 يوم)
├── weekly\    (12 أسبوع)
└── monthly\   (12 شهر)
```

---

## 🔧 الصيانة والمراقبة

### مهام الصيانة الدورية
1. **يومياً:**
   - فحص سجلات الأخطاء
   - مراقبة استخدام المساحة
   - النسخ الاحتياطي التلقائي

2. **أسبوعياً:**
   - تحديث إحصائيات الجداول
   - فحص الفهارس المجزأة
   - تنظيف الملفات المؤقتة

3. **شهرياً:**
   - تحليل الأداء الشامل
   - مراجعة خطط التنفيذ
   - تحديث التوثيق

### استعلامات المراقبة
```sql
-- مراقبة حجم الجداول
SELECT TABLE_NAME, NUM_ROWS, BLOCKS, AVG_ROW_LEN
FROM USER_TABLES 
ORDER BY NUM_ROWS DESC;

-- مراقبة استخدام الفهارس
SELECT INDEX_NAME, TABLE_NAME, UNIQUENESS, STATUS
FROM USER_INDEXES 
WHERE STATUS != 'VALID';

-- مراقبة الجلسات النشطة
SELECT USERNAME, STATUS, COUNT(*)
FROM V$SESSION 
GROUP BY USERNAME, STATUS;
```

---

## 📈 إحصائيات قاعدة البيانات

### حجم البيانات
| الجدول | عدد السجلات | الحجم التقديري | النمو الشهري |
|--------|-------------|---------------|---------------|
| IAS_ITM_MST | 4,647 | 1.2 MB | +50 سجل |
| IAS_ITM_DTL | 9,108 | 2.8 MB | +100 سجل |
| MEASUREMENT | 18 | 2 KB | مستقر |
| **الإجمالي** | **13,773** | **4.0 MB** | **+150 سجل** |

### الأداء
- **متوسط وقت الاستجابة:** < 100ms
- **الاستعلامات البطيئة:** < 1%
- **معدل نجاح الاتصال:** 99.9%
- **استخدام الذاكرة:** 15% من المتاح

---

## 🛠️ أدوات الإدارة

### الأدوات المتوفرة
1. **DatabaseConnectionTest.java** - اختبار الاتصالات
2. **DataQualityTester.java** - اختبار جودة البيانات
3. **DatabaseStructureAnalyzer.java** - تحليل البنية
4. **DatabasePerformanceOptimizer.sql** - تحسين الأداء
5. **DatabaseBackupSystem.bat** - النسخ الاحتياطي

### سكريبتات الصيانة
```bash
# اختبار شامل للنظام
test-complete-system.bat

# تحسين الأداء
sqlplus ias20251/ys123@localhost:1521/ORCL @DatabasePerformanceOptimizer.sql

# النسخ الاحتياطي
DatabaseBackupSystem.bat
```

---

## 📞 الدعم والمساعدة

### معلومات الاتصال
- **فريق قاعدة البيانات:** <EMAIL>
- **الدعم الفني:** <EMAIL>
- **التوثيق:** docs.ship-erp.com/database

### الموارد الإضافية
- [دليل المستخدم](USER_GUIDE.md)
- [دليل المطور](DEVELOPER_GUIDE.md)
- [أسئلة شائعة](FAQ.md)
- [سجل التغييرات](CHANGELOG.md)

---

**© 2025 Ship ERP System - جميع الحقوق محفوظة**
