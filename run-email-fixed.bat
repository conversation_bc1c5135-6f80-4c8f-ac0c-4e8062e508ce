@echo off
echo 📧 تشغيل نافذة إدارة حسابات البريد الإلكتروني...
cd /d "e:\ship_erp\java"

echo 🔄 تجميع النافذة مع JavaMail...
javac -encoding UTF-8 -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\ojdbc11.jar;lib\*;." -d . src\main\java\CompleteEmailAccountsWindow.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في التجميع
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح

echo 🚀 تشغيل النافذة...
java -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\ojdbc11.jar;lib\*;." CompleteEmailAccountsWindow

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في التشغيل
)

pause
