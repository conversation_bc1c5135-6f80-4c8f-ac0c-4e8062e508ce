@echo off
echo ========================================
echo 🌳 تشغيل الشجرة الأصلية المحدثة
echo Running Updated Original Tree
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/3] تجميع الملفات المحدثة...
echo Compiling Updated Files...
echo ========================================

echo تجميع TreeMenuPanel المحدث...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع TreeMenuPanel المحدث بنجاح
) else (
    echo ❌ فشل في تجميع TreeMenuPanel المحدث
    pause
    exit /b 1
)

echo تجميع CurrencySettingsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CurrencySettingsWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CurrencySettingsWindow بنجاح
) else (
    echo ❌ فشل في تجميع CurrencySettingsWindow
    pause
    exit /b 1
)

echo تجميع AddCurrencySettingsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\AddCurrencySettingsWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع AddCurrencySettingsWindow بنجاح
) else (
    echo ❌ فشل في تجميع AddCurrencySettingsWindow
    pause
    exit /b 1
)

echo.
echo [2/3] اختيار التطبيق للتشغيل...
echo Choose Application to Run...
echo ========================================

echo.
echo اختر التطبيق الذي تريد تشغيله:
echo 1. تشغيل الشجرة الأصلية المحدثة (EnhancedShipERP)
echo 2. تشغيل نافذة إعدادات العملة مباشرة
echo 3. إضافة النافذة إلى قاعدة البيانات
echo 4. تشغيل جميع العمليات
echo.

set /p choice="أدخل اختيارك (1-4): "

echo.
echo [3/3] تنفيذ العملية المختارة...
echo Executing Selected Operation...
echo ========================================

if "%choice%"=="1" (
    echo تشغيل الشجرة الأصلية المحدثة...
    start "Enhanced Ship ERP - Updated Tree" java -cp "lib\*;." EnhancedShipERP
    echo ✅ تم تشغيل الشجرة الأصلية المحدثة
) else if "%choice%"=="2" (
    echo تشغيل نافذة إعدادات العملة...
    start "Currency Settings Window" java -cp "lib\*;." CurrencySettingsWindow
    echo ✅ تم تشغيل نافذة إعدادات العملة
) else if "%choice%"=="3" (
    echo محاولة إضافة النافذة إلى قاعدة البيانات...
    java -cp "lib\*;." AddCurrencySettingsWindow
    if %errorlevel% equ 0 (
        echo ✅ تم إضافة النافذة إلى قاعدة البيانات بنجاح
    ) else (
        echo ⚠️ فشل في الاتصال بقاعدة البيانات
        echo يمكنك استخدام ملف SQL المرفق: scripts\add_currency_settings_window.sql
    )
) else if "%choice%"=="4" (
    echo تشغيل جميع العمليات...
    
    echo 1. تشغيل نافذة إعدادات العملة...
    start "Currency Settings Window" java -cp "lib\*;." CurrencySettingsWindow
    timeout /t 2 /nobreak >nul
    
    echo 2. تشغيل الشجرة الأصلية المحدثة...
    start "Enhanced Ship ERP - Updated Tree" java -cp "lib\*;." EnhancedShipERP
    timeout /t 2 /nobreak >nul
    
    echo 3. محاولة إضافة النافذة إلى قاعدة البيانات...
    java -cp "lib\*;." AddCurrencySettingsWindow
    
    echo ✅ تم تشغيل جميع العمليات
) else (
    echo ❌ اختيار غير صحيح، سيتم تشغيل الشجرة الأصلية افتراضياً
    start "Enhanced Ship ERP - Updated Tree" java -cp "lib\*;." EnhancedShipERP
)

echo.
echo ========================================
echo ✅ انتهت العملية بنجاح!
echo Operation Completed Successfully!
echo ========================================

echo.
echo 📋 ملخص التحديثات في الشجرة الأصلية:
echo =====================================
echo • تم إضافة العقد الفرعية تحت "الإعدادات العامة"
echo • تم إضافة نافذة "إعدادات العملة (قيد التطوير)"
echo • تم إضافة "المتغيرات العامة"
echo • تم إضافة "إعدادات النظام العامة"
echo • تم إضافة "إعدادات الأمان"

echo.
echo 🌳 هيكل الشجرة المحدث:
echo ====================
echo الإعدادات والإدارة
echo ├── الإعدادات العامة
echo │   ├── المتغيرات العامة
echo │   ├── إعدادات العملة (قيد التطوير) ⭐ جديد
echo │   ├── إعدادات النظام العامة
echo │   └── إعدادات الأمان
echo ├── إدارة المستخدمين
echo ├── إدارة الربط والاستيراد
echo ├── إعدادات النظام
echo └── النسخ الاحتياطية

echo.
echo 💰 نافذة إعدادات العملة:
echo =========================
echo • الاسم: إعدادات العملة
echo • الحالة: قيد التطوير
echo • الموقع: الإعدادات والإدارة ^> الإعدادات العامة ^> إعدادات العملة
echo • الكلاس: CurrencySettingsWindow
echo • الوصف: إعدادات وإدارة العملات المستخدمة في النظام

echo.
echo 🎯 الميزات المخططة:
echo ==================
echo • إدارة العملات (إضافة، تعديل، حذف)
echo • أسعار الصرف والتحديث التلقائي
echo • الإعدادات العامة للعملة
echo • التقارير والإحصائيات
echo • التكامل مع الأنظمة الخارجية

echo.
echo 💾 قاعدة البيانات:
echo ==================
echo • الجدول: ERP_SYSTEM_TREE
echo • ملف SQL: scripts\add_currency_settings_window.sql
echo • يمكن تشغيل الملف يدوياً إذا لم يتم الاتصال تلقائياً

echo.
echo 📁 الملفات المطورة/المحدثة:
echo ============================
echo • TreeMenuPanel.java - محدث ليتضمن العقد الفرعية والنافذة الجديدة
echo • CurrencySettingsWindow.java - النافذة الجديدة
echo • AddCurrencySettingsWindow.java - أداة إضافة إلى قاعدة البيانات
echo • scripts\add_currency_settings_window.sql - ملف SQL

echo.
echo 🚀 للاستخدام:
echo =============
echo • تشغيل الشجرة المحدثة: java -cp "lib\*;." EnhancedShipERP
echo • تشغيل النافذة مباشرة: java -cp "lib\*;." CurrencySettingsWindow
echo • إضافة إلى قاعدة البيانات: java -cp "lib\*;." AddCurrencySettingsWindow

echo.
echo 💡 ملاحظات:
echo ===========
echo • النافذة تظهر تحت "الإعدادات العامة" في الشجرة الأصلية
echo • تم إضافة العقد الفرعية للإعدادات العامة كما طُلب
echo • النافذة تعمل بشكل مستقل ولا تحتاج اتصال بقاعدة البيانات
echo • يمكن الوصول للنافذة عبر النقر المزدوج في الشجرة

echo.
pause
