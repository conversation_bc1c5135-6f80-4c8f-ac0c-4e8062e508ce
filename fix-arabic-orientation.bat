@echo off
echo ========================================
echo   Fix Arabic Orientation Issues
echo   اصلاح مشاكل اتجاه اللغة العربية
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Compiling AdvancedCompanySettingsWindow with Arabic fixes...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\AdvancedCompanySettingsWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile AdvancedCompanySettingsWindow
    pause
    exit /b 1
)

echo.
echo [2] Compiling TreeMenuPanel...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo Failed to compile TreeMenuPanel
    pause
    exit /b 1
)

echo.
echo [3] Testing Arabic orientation fixes...
echo Starting AdvancedCompanySettingsWindow...
start /min java -cp "lib\*;." AdvancedCompanySettingsWindow

echo.
echo [4] Starting main system to test from tree menu...
echo Starting Ship ERP system...
start .\start-ship-erp.bat

echo.
echo ========================================
echo   Arabic Orientation Fixes Applied!
echo   تم تطبيق اصلاحات اتجاه اللغة العربية!
echo ========================================
echo.
echo FIXES APPLIED:
echo - Window orientation set to RIGHT_TO_LEFT
echo - All tabs now start from right
echo - All labels positioned on the right
echo - All text fields positioned on the left
echo - All panels use Arabic orientation
echo - ComboBoxes use Arabic orientation
echo.
echo TEST INSTRUCTIONS:
echo 1. Check AdvancedCompanySettingsWindow opened directly
echo 2. Check main system - go to Settings ^> Company Settings
echo 3. Verify all tabs start from right
echo 4. Verify all labels are on right, fields on left
echo.
pause
