import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

/**
 * إنشاء جداول نظام البريد الإلكتروني في قاعدة بيانات ship_erp
 */
public class CreateShipErpEmailTables {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  CREATING SHIP_ERP EMAIL TABLES");
        System.out.println("  إنشاء جداول البريد الإلكتروني في ship_erp");
        System.out.println("========================================");

        try {
            // استخدام بيانات الاعتماد الصحيحة
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";

            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة بيانات ship_erp بنجاح");
            
            // إنشاء الجداول
            createEmailAccountsTable(connection);
            createEmailMessagesTable(connection);
            createEmailTemplatesTable(connection);
            createEmailAttachmentsTable(connection);
            createEmailAddressBookTable(connection);
            createEmailFoldersTable(connection);
            
            // إنشاء المتسلسلات
            createSequences(connection);
            
            // إنشاء الفهارس
            createIndexes(connection);
            
            // إدراج بيانات تجريبية
            insertSampleData(connection);
            
            connection.close();
            System.out.println("\n🎉 تم إنشاء جميع جداول نظام البريد الإلكتروني بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء الجداول: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void createEmailAccountsTable(Connection connection) {
        try {
            System.out.println("\n📧 إنشاء جدول EMAIL_ACCOUNTS...");
            
            String sql = """
                CREATE TABLE EMAIL_ACCOUNTS (
                    ACCOUNT_ID NUMBER(10) NOT NULL,
                    ACCOUNT_NAME NVARCHAR2(200) NOT NULL,
                    EMAIL_ADDRESS VARCHAR2(255) NOT NULL UNIQUE,
                    DISPLAY_NAME NVARCHAR2(200),
                    ACCOUNT_TYPE VARCHAR2(20) DEFAULT 'IMAP',
                    
                    -- إعدادات الخادم الوارد
                    INCOMING_SERVER VARCHAR2(255) NOT NULL,
                    INCOMING_PORT NUMBER(5) DEFAULT 993,
                    INCOMING_SECURITY VARCHAR2(10) DEFAULT 'SSL',
                    INCOMING_USERNAME VARCHAR2(255),
                    INCOMING_PASSWORD VARCHAR2(500),
                    
                    -- إعدادات الخادم الصادر
                    OUTGOING_SERVER VARCHAR2(255) NOT NULL,
                    OUTGOING_PORT NUMBER(5) DEFAULT 587,
                    OUTGOING_SECURITY VARCHAR2(10) DEFAULT 'TLS',
                    OUTGOING_USERNAME VARCHAR2(255),
                    OUTGOING_PASSWORD VARCHAR2(500),
                    OUTGOING_AUTH CHAR(1) DEFAULT 'Y',
                    
                    -- إعدادات متقدمة
                    DEFAULT_ACCOUNT CHAR(1) DEFAULT 'N',
                    AUTO_CHECK_INTERVAL NUMBER(5) DEFAULT 15,
                    KEEP_MESSAGES_ON_SERVER CHAR(1) DEFAULT 'Y',
                    DELETE_AFTER_DAYS NUMBER(3) DEFAULT 30,
                    
                    -- معلومات الحالة
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    IS_CONNECTED CHAR(1) DEFAULT 'N',
                    LAST_CHECK_DATE DATE,
                    LAST_ERROR NVARCHAR2(1000),
                    
                    -- معلومات التدقيق
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    LAST_UPDATED DATE DEFAULT SYSDATE,
                    UPDATED_BY VARCHAR2(50) DEFAULT USER,
                    VERSION_NUMBER NUMBER(10) DEFAULT 1,
                    
                    CONSTRAINT PK_EMAIL_ACCOUNTS PRIMARY KEY (ACCOUNT_ID)
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول EMAIL_ACCOUNTS");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) {
                System.out.println("⚠️ جدول EMAIL_ACCOUNTS موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء جدول EMAIL_ACCOUNTS: " + e.getMessage());
            }
        }
    }
    
    private static void createEmailMessagesTable(Connection connection) {
        try {
            System.out.println("\n📨 إنشاء جدول EMAIL_MESSAGES...");
            
            String sql = """
                CREATE TABLE EMAIL_MESSAGES (
                    MESSAGE_ID NUMBER(10) NOT NULL,
                    ACCOUNT_ID NUMBER(10) NOT NULL,
                    MESSAGE_UID VARCHAR2(100),
                    SUBJECT NVARCHAR2(500),
                    FROM_ADDRESS VARCHAR2(255),
                    TO_ADDRESS CLOB,
                    CC_ADDRESS CLOB,
                    BCC_ADDRESS CLOB,
                    MESSAGE_BODY CLOB,
                    MESSAGE_DATE DATE,
                    RECEIVED_DATE DATE DEFAULT SYSDATE,
                    IS_READ CHAR(1) DEFAULT 'N',
                    IS_REPLIED CHAR(1) DEFAULT 'N',
                    IS_FORWARDED CHAR(1) DEFAULT 'N',
                    HAS_ATTACHMENTS CHAR(1) DEFAULT 'N',
                    MESSAGE_SIZE NUMBER(10),
                    FOLDER_NAME VARCHAR2(100) DEFAULT 'INBOX',
                    
                    CONSTRAINT PK_EMAIL_MESSAGES PRIMARY KEY (MESSAGE_ID),
                    CONSTRAINT FK_EMAIL_MESSAGES_ACCOUNT FOREIGN KEY (ACCOUNT_ID) REFERENCES EMAIL_ACCOUNTS(ACCOUNT_ID)
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول EMAIL_MESSAGES");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) {
                System.out.println("⚠️ جدول EMAIL_MESSAGES موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء جدول EMAIL_MESSAGES: " + e.getMessage());
            }
        }
    }
    
    private static void createEmailTemplatesTable(Connection connection) {
        try {
            System.out.println("\n📄 إنشاء جدول EMAIL_TEMPLATES...");
            
            String sql = """
                CREATE TABLE EMAIL_TEMPLATES (
                    TEMPLATE_ID NUMBER(10) NOT NULL,
                    TEMPLATE_NAME NVARCHAR2(200) NOT NULL,
                    TEMPLATE_SUBJECT NVARCHAR2(500),
                    TEMPLATE_BODY CLOB,
                    TEMPLATE_TYPE VARCHAR2(50) DEFAULT 'GENERAL',
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    
                    CONSTRAINT PK_EMAIL_TEMPLATES PRIMARY KEY (TEMPLATE_ID)
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول EMAIL_TEMPLATES");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) {
                System.out.println("⚠️ جدول EMAIL_TEMPLATES موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء جدول EMAIL_TEMPLATES: " + e.getMessage());
            }
        }
    }
    
    private static void createEmailAttachmentsTable(Connection connection) {
        try {
            System.out.println("\n📎 إنشاء جدول EMAIL_ATTACHMENTS...");
            
            String sql = """
                CREATE TABLE EMAIL_ATTACHMENTS (
                    ATTACHMENT_ID NUMBER(10) NOT NULL,
                    MESSAGE_ID NUMBER(10) NOT NULL,
                    FILE_NAME NVARCHAR2(255) NOT NULL,
                    FILE_SIZE NUMBER(10),
                    CONTENT_TYPE VARCHAR2(100),
                    FILE_DATA BLOB,
                    
                    CONSTRAINT PK_EMAIL_ATTACHMENTS PRIMARY KEY (ATTACHMENT_ID),
                    CONSTRAINT FK_EMAIL_ATTACHMENTS_MESSAGE FOREIGN KEY (MESSAGE_ID) REFERENCES EMAIL_MESSAGES(MESSAGE_ID)
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول EMAIL_ATTACHMENTS");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) {
                System.out.println("⚠️ جدول EMAIL_ATTACHMENTS موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء جدول EMAIL_ATTACHMENTS: " + e.getMessage());
            }
        }
    }
    
    private static void createEmailAddressBookTable(Connection connection) {
        try {
            System.out.println("\n📇 إنشاء جدول EMAIL_ADDRESS_BOOK...");
            
            String sql = """
                CREATE TABLE EMAIL_ADDRESS_BOOK (
                    CONTACT_ID NUMBER(10) NOT NULL,
                    CONTACT_NAME NVARCHAR2(200) NOT NULL,
                    EMAIL_ADDRESS VARCHAR2(255) NOT NULL,
                    PHONE_NUMBER VARCHAR2(50),
                    COMPANY_NAME NVARCHAR2(200),
                    NOTES NVARCHAR2(1000),
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    
                    CONSTRAINT PK_EMAIL_ADDRESS_BOOK PRIMARY KEY (CONTACT_ID)
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول EMAIL_ADDRESS_BOOK");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) {
                System.out.println("⚠️ جدول EMAIL_ADDRESS_BOOK موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء جدول EMAIL_ADDRESS_BOOK: " + e.getMessage());
            }
        }
    }
    
    private static void createEmailFoldersTable(Connection connection) {
        try {
            System.out.println("\n📁 إنشاء جدول EMAIL_FOLDERS...");
            
            String sql = """
                CREATE TABLE EMAIL_FOLDERS (
                    FOLDER_ID NUMBER(10) NOT NULL,
                    ACCOUNT_ID NUMBER(10) NOT NULL,
                    FOLDER_NAME VARCHAR2(100) NOT NULL,
                    FOLDER_TYPE VARCHAR2(20) DEFAULT 'CUSTOM',
                    MESSAGE_COUNT NUMBER(10) DEFAULT 0,
                    UNREAD_COUNT NUMBER(10) DEFAULT 0,
                    
                    CONSTRAINT PK_EMAIL_FOLDERS PRIMARY KEY (FOLDER_ID),
                    CONSTRAINT FK_EMAIL_FOLDERS_ACCOUNT FOREIGN KEY (ACCOUNT_ID) REFERENCES EMAIL_ACCOUNTS(ACCOUNT_ID)
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول EMAIL_FOLDERS");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) {
                System.out.println("⚠️ جدول EMAIL_FOLDERS موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء جدول EMAIL_FOLDERS: " + e.getMessage());
            }
        }
    }
    
    private static void createSequences(Connection connection) {
        System.out.println("\n🔢 إنشاء المتسلسلات...");
        
        String[] sequences = {
            "EMAIL_ACCOUNTS_SEQ",
            "EMAIL_MESSAGES_SEQ", 
            "EMAIL_TEMPLATES_SEQ",
            "EMAIL_ATTACHMENTS_SEQ",
            "EMAIL_ADDRESS_BOOK_SEQ",
            "EMAIL_FOLDERS_SEQ"
        };
        
        for (String seqName : sequences) {
            try {
                String sql = "CREATE SEQUENCE " + seqName + " START WITH 1 INCREMENT BY 1";
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.execute();
                    System.out.println("✅ تم إنشاء متسلسل " + seqName);
                }
            } catch (SQLException e) {
                if (e.getErrorCode() == 955) {
                    System.out.println("⚠️ متسلسل " + seqName + " موجود مسبقاً");
                } else {
                    System.err.println("❌ خطأ في إنشاء متسلسل " + seqName + ": " + e.getMessage());
                }
            }
        }
    }
    
    private static void createIndexes(Connection connection) {
        System.out.println("\n📊 إنشاء الفهارس...");
        
        String[] indexes = {
            "CREATE INDEX IDX_EMAIL_ADDRESS ON EMAIL_ACCOUNTS(EMAIL_ADDRESS)",
            "CREATE INDEX IDX_ACCOUNT_STATUS ON EMAIL_ACCOUNTS(IS_ACTIVE)",
            "CREATE INDEX IDX_MESSAGE_ACCOUNT ON EMAIL_MESSAGES(ACCOUNT_ID)",
            "CREATE INDEX IDX_MESSAGE_DATE ON EMAIL_MESSAGES(MESSAGE_DATE)",
            "CREATE INDEX IDX_MESSAGE_READ ON EMAIL_MESSAGES(IS_READ)"
        };
        
        for (String indexSql : indexes) {
            try {
                try (PreparedStatement stmt = connection.prepareStatement(indexSql)) {
                    stmt.execute();
                    System.out.println("✅ تم إنشاء فهرس");
                }
            } catch (SQLException e) {
                if (e.getErrorCode() == 955) {
                    System.out.println("⚠️ فهرس موجود مسبقاً");
                } else {
                    System.err.println("❌ خطأ في إنشاء فهرس: " + e.getMessage());
                }
            }
        }
    }
    
    private static void insertSampleData(Connection connection) {
        try {
            System.out.println("\n📝 إدراج بيانات تجريبية...");
            
            // إدراج حساب بريد إلكتروني تجريبي
            String sql = """
                INSERT INTO EMAIL_ACCOUNTS (
                    ACCOUNT_ID, ACCOUNT_NAME, EMAIL_ADDRESS, DISPLAY_NAME, ACCOUNT_TYPE,
                    INCOMING_SERVER, INCOMING_PORT, INCOMING_SECURITY,
                    OUTGOING_SERVER, OUTGOING_PORT, OUTGOING_SECURITY,
                    IS_ACTIVE, DEFAULT_ACCOUNT
                ) VALUES (
                    EMAIL_ACCOUNTS_SEQ.NEXTVAL, 'حساب تجريبي', '<EMAIL>', 'حساب تجريبي', 'IMAP',
                    'imap.example.com', 993, 'SSL',
                    'smtp.example.com', 587, 'TLS',
                    'Y', 'Y'
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.executeUpdate();
                System.out.println("✅ تم إدراج حساب بريد إلكتروني تجريبي");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 1) {
                System.out.println("⚠️ البيانات التجريبية موجودة مسبقاً");
            } else {
                System.err.println("❌ خطأ في إدراج البيانات التجريبية: " + e.getMessage());
            }
        }
    }
}
