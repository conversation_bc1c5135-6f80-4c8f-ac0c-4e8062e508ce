@echo off
echo Testing JavaMail availability...
cd /d "e:\ship_erp\java"

echo Checking if javax.mail-1.6.2.jar exists...
if exist "lib\javax.mail-1.6.2.jar" (
    echo ✅ javax.mail-1.6.2.jar found
) else (
    echo ❌ javax.mail-1.6.2.jar NOT found
    pause
    exit /b 1
)

echo Testing JavaMail class loading...
java -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\*;." -c "try { Class.forName(\"javax.mail.Session\"); System.out.println(\"✅ JavaMail loaded successfully\"); } catch (Exception e) { System.out.println(\"❌ JavaMail failed: \" + e.getMessage()); }"

echo Testing complete email window...
java -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\*;." CompleteEmailAccountsWindow

pause
