import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * أداة إصلاح شاملة لجميع مشاكل المظهر في التطبيق
 * Complete Theme Fixer for All Application Theme Issues
 */
public class CompleteThemeFixer {
    
    private List<String> fixedFiles = new ArrayList<>();
    private List<String> issues = new ArrayList<>();
    
    public static void main(String[] args) {
        System.out.println("🔧 أداة إصلاح شاملة لجميع مشاكل المظهر");
        System.out.println("Complete Theme Fixer for All Theme Issues");
        System.out.println("==========================================");
        
        CompleteThemeFixer fixer = new CompleteThemeFixer();
        fixer.fixAllThemeIssues();
    }
    
    public void fixAllThemeIssues() {
        try {
            System.out.println("🚀 بدء الإصلاح الشامل للمظاهر...");
            
            // 1. تشخيص المشاكل
            diagnoseProblem();
            
            // 2. إنشاء نسخ احتياطية
            createBackups();
            
            // 3. إصلاح الملفات الرئيسية
            fixMainApplications();
            
            // 4. إصلاح النوافذ المساعدة
            fixUtilityWindows();
            
            // 5. إنشاء ملف إعدادات موحد
            createUnifiedSettings();
            
            // 6. اختبار الإصلاحات
            testFixes();
            
            // 7. عرض التقرير النهائي
            showFinalReport();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في الإصلاح الشامل: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void diagnoseProblem() {
        System.out.println("\n🔍 تشخيص مشاكل المظهر...");
        
        // فحص المكتبات المتاحة
        checkLibraries();
        
        // فحص الملفات الموجودة
        checkExistingFiles();
        
        // فحص تضارب المظاهر
        checkThemeConflicts();
    }
    
    private void checkLibraries() {
        String[] libraries = {
            "com.formdev.flatlaf.FlatLightLaf",
            "com.formdev.flatlaf.FlatDarkLaf",
            "com.formdev.flatlaf.FlatIntelliJLaf",
            "com.formdev.flatlaf.FlatDarculaLaf"
        };
        
        System.out.println("📚 فحص المكتبات:");
        for (String lib : libraries) {
            try {
                Class.forName(lib);
                System.out.println("✅ " + lib.substring(lib.lastIndexOf('.') + 1));
            } catch (ClassNotFoundException e) {
                System.out.println("❌ " + lib.substring(lib.lastIndexOf('.') + 1));
                issues.add("مكتبة مفقودة: " + lib);
            }
        }
    }
    
    private void checkExistingFiles() {
        String[] files = {
            "src/main/java/EnhancedShipERP.java",
            "src/main/java/SimpleShipERP.java",
            "src/main/java/AdvancedSystemTreeFixer.java",
            "src/main/java/SystemTreeWithIcons.java",
            "src/main/java/ItemGroupsDisplayFixer.java"
        };
        
        System.out.println("\n📄 فحص الملفات:");
        for (String filePath : files) {
            File file = new File(filePath);
            if (file.exists()) {
                System.out.println("✅ " + file.getName());
            } else {
                System.out.println("❌ " + file.getName());
                issues.add("ملف مفقود: " + filePath);
            }
        }
    }
    
    private void checkThemeConflicts() {
        System.out.println("\n⚠️ فحص تضارب المظاهر:");
        
        // فحص عدد مدراء المظاهر
        String[] managers = {
            "FinalThemeManager", "WorkingThemeManager", "SimpleUnifiedThemeManager",
            "UnifiedThemeManager", "ThemeApplier", "SettingsManager"
        };
        
        int managerCount = 0;
        for (String manager : managers) {
            try {
                Class.forName(manager);
                System.out.println("🔍 تم العثور على: " + manager);
                managerCount++;
            } catch (ClassNotFoundException e) {
                // لا يوجد
            }
        }
        
        if (managerCount > 1) {
            issues.add("تضارب مدراء المظاهر: " + managerCount + " مدير");
            System.out.println("⚠️ تضارب: " + managerCount + " مدير مظاهر");
        }
    }
    
    private void createBackups() {
        System.out.println("\n📦 إنشاء نسخ احتياطية...");
        
        String[] filesToBackup = {
            "src/main/java/EnhancedShipERP.java",
            "src/main/java/SimpleShipERP.java"
        };
        
        for (String filePath : filesToBackup) {
            try {
                File originalFile = new File(filePath);
                if (originalFile.exists()) {
                    File backupFile = new File(filePath + ".theme-backup");
                    Files.copy(originalFile.toPath(), backupFile.toPath(), 
                        java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                    System.out.println("✅ نسخة احتياطية: " + originalFile.getName());
                }
            } catch (IOException e) {
                System.err.println("❌ فشل في إنشاء نسخة احتياطية لـ " + filePath);
            }
        }
    }
    
    private void fixMainApplications() {
        System.out.println("\n🔄 إصلاح التطبيقات الرئيسية...");
        
        // إصلاح EnhancedShipERP
        fixEnhancedShipERP();
        
        // إصلاح SimpleShipERP
        fixSimpleShipERP();
    }
    
    private void fixEnhancedShipERP() {
        try {
            File file = new File("src/main/java/EnhancedShipERP.java");
            if (!file.exists()) return;
            
            String content = Files.readString(file.toPath());
            
            // البحث عن إعدادات المظهر القديمة واستبدالها
            if (content.contains("FlatLightLaf.setup()") || 
                content.contains("UIManager.setLookAndFeel")) {
                
                // استبدال جميع إعدادات المظهر القديمة
                content = content.replaceAll(
                    "FlatLightLaf\\.setup\\(\\);?",
                    "FinalThemeManager.initializeDefaultTheme();"
                );
                
                content = content.replaceAll(
                    "UIManager\\.setLookAndFeel\\([^)]+\\);?",
                    "FinalThemeManager.initializeDefaultTheme();"
                );
                
                // إضافة import إذا لم يكن موجوداً
                if (!content.contains("import FinalThemeManager")) {
                    content = content.replaceFirst(
                        "(import [^;]+;)",
                        "$1\n// import FinalThemeManager; // سيتم إضافته تلقائياً"
                    );
                }
                
                Files.writeString(file.toPath(), content);
                fixedFiles.add("EnhancedShipERP.java");
                System.out.println("✅ تم إصلاح EnhancedShipERP.java");
            } else {
                System.out.println("ℹ️ EnhancedShipERP.java لا يحتاج إصلاح");
            }
            
        } catch (IOException e) {
            System.err.println("❌ فشل في إصلاح EnhancedShipERP.java: " + e.getMessage());
        }
    }
    
    private void fixSimpleShipERP() {
        try {
            File file = new File("src/main/java/SimpleShipERP.java");
            if (!file.exists()) return;
            
            String content = Files.readString(file.toPath());
            
            // البحث عن إعدادات المظهر القديمة واستبدالها
            if (content.contains("UIManager.setLookAndFeel") || 
                content.contains("FlatLightLaf")) {
                
                content = content.replaceAll(
                    "UIManager\\.setLookAndFeel\\([^)]+\\);?",
                    "FinalThemeManager.initializeDefaultTheme();"
                );
                
                Files.writeString(file.toPath(), content);
                fixedFiles.add("SimpleShipERP.java");
                System.out.println("✅ تم إصلاح SimpleShipERP.java");
            } else {
                System.out.println("ℹ️ SimpleShipERP.java لا يحتاج إصلاح");
            }
            
        } catch (IOException e) {
            System.err.println("❌ فشل في إصلاح SimpleShipERP.java: " + e.getMessage());
        }
    }
    
    private void fixUtilityWindows() {
        System.out.println("\n🔄 إصلاح النوافذ المساعدة...");
        
        String[] utilityFiles = {
            "src/main/java/AdvancedSystemTreeFixer.java",
            "src/main/java/SystemTreeWithIcons.java",
            "src/main/java/ItemGroupsDisplayFixer.java",
            "src/main/java/ItemGroupsComparison.java",
            "src/main/java/IconTestViewer.java"
        };
        
        for (String filePath : utilityFiles) {
            fixUtilityWindow(filePath);
        }
    }
    
    private void fixUtilityWindow(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) return;
            
            String content = Files.readString(file.toPath());
            boolean modified = false;
            
            // استبدال إعدادات المظهر في main method
            if (content.contains("UIManager.setLookAndFeel") || 
                content.contains("FlatDarkLaf.setup") ||
                content.contains("FlatLightLaf.setup")) {
                
                content = content.replaceAll(
                    "UIManager\\.setLookAndFeel\\([^)]+\\);?",
                    "FinalThemeManager.initializeDefaultTheme();"
                );
                
                content = content.replaceAll(
                    "FlatDarkLaf\\.setup\\(\\);?",
                    "FinalThemeManager.initializeDefaultTheme();"
                );
                
                content = content.replaceAll(
                    "FlatLightLaf\\.setup\\(\\);?",
                    "FinalThemeManager.initializeDefaultTheme();"
                );
                
                modified = true;
            }
            
            if (modified) {
                Files.writeString(file.toPath(), content);
                fixedFiles.add(file.getName());
                System.out.println("✅ تم إصلاح " + file.getName());
            } else {
                System.out.println("ℹ️ " + file.getName() + " لا يحتاج إصلاح");
            }
            
        } catch (IOException e) {
            System.err.println("❌ فشل في إصلاح " + filePath + ": " + e.getMessage());
        }
    }
    
    private void createUnifiedSettings() {
        System.out.println("\n⚙️ إنشاء إعدادات موحدة...");
        
        try {
            // إنشاء مدير المظاهر النهائي وحفظ الإعدادات
            FinalThemeManager manager = FinalThemeManager.getInstance();
            manager.applyTheme("FlatLaf Light");
            
            System.out.println("✅ تم إنشاء ملف الإعدادات الموحد");
            
        } catch (Exception e) {
            System.err.println("❌ فشل في إنشاء الإعدادات: " + e.getMessage());
        }
    }
    
    private void testFixes() {
        System.out.println("\n🧪 اختبار الإصلاحات...");
        
        try {
            // اختبار المدير النهائي
            FinalThemeManager manager = FinalThemeManager.getInstance();
            System.out.println("✅ FinalThemeManager يعمل بشكل صحيح");
            
            // اختبار تطبيق المظاهر
            boolean testResult = manager.applyTheme("FlatLaf Light");
            if (testResult) {
                System.out.println("✅ تطبيق المظاهر يعمل بشكل صحيح");
            } else {
                System.out.println("⚠️ مشكلة في تطبيق المظاهر");
            }
            
        } catch (Exception e) {
            System.err.println("❌ فشل في اختبار الإصلاحات: " + e.getMessage());
        }
    }
    
    private void showFinalReport() {
        System.out.println("\n📊 التقرير النهائي:");
        System.out.println("==================");
        
        System.out.println("🔧 الملفات المُصلحة (" + fixedFiles.size() + "):");
        for (String file : fixedFiles) {
            System.out.println("  ✅ " + file);
        }
        
        System.out.println("\n⚠️ المشاكل المكتشفة (" + issues.size() + "):");
        for (String issue : issues) {
            System.out.println("  • " + issue);
        }
        
        System.out.println("\n🎯 النتائج:");
        System.out.println("• تم توحيد جميع المظاهر تحت FinalThemeManager");
        System.out.println("• تم إنشاء نسخ احتياطية من الملفات المهمة");
        System.out.println("• تم إنشاء ملف إعدادات موحد");
        System.out.println("• جميع النوافذ ستستخدم نفس المظهر");
        
        System.out.println("\n💡 للاستخدام:");
        System.out.println("• استخدم FinalThemeManager.initializeDefaultTheme() في بداية التطبيق");
        System.out.println("• استخدم FinalThemeManager.getInstance().applyTheme() لتغيير المظهر");
        System.out.println("• جميع الإعدادات محفوظة في unified-theme.properties");
        
        System.out.println("\n✅ تم إكمال الإصلاح الشامل للمظاهر!");
    }
}
