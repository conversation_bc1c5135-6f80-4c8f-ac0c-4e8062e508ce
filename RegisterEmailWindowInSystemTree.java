import java.sql.*;

/**
 * تسجيل نافذة إدارة حسابات البريد الإلكتروني في شجرة النظام
 */
public class RegisterEmailWindowInSystemTree {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  REGISTERING EMAIL WINDOW IN SYSTEM TREE");
        System.out.println("  تسجيل نافذة البريد الإلكتروني في شجرة النظام");
        System.out.println("========================================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";
            
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة بيانات ship_erp بنجاح");
            
            // التحقق من وجود جدول شجرة النظام
            if (!checkSystemTreeTable(connection)) {
                createSystemTreeTable(connection);
            }
            
            // تسجيل نظام البريد الإلكتروني
            registerEmailSystem(connection);
            
            connection.close();
            System.out.println("\n🎉 تم تسجيل نافذة البريد الإلكتروني في شجرة النظام بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تسجيل النافذة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static boolean checkSystemTreeTable(Connection connection) {
        try {
            String sql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'SYSTEM_TREE'";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    boolean exists = rs.getInt(1) > 0;
                    if (exists) {
                        System.out.println("✅ جدول SYSTEM_TREE موجود");
                    } else {
                        System.out.println("❌ جدول SYSTEM_TREE غير موجود");
                    }
                    return exists;
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في فحص جدول شجرة النظام: " + e.getMessage());
        }
        
        return false;
    }
    
    private static void createSystemTreeTable(Connection connection) {
        try {
            System.out.println("📋 إنشاء جدول SYSTEM_TREE...");
            
            String createTableSQL = """
                CREATE TABLE SYSTEM_TREE (
                    NODE_ID NUMBER PRIMARY KEY,
                    NODE_NAME_AR NVARCHAR2(100) NOT NULL,
                    NODE_NAME_EN VARCHAR2(100) NOT NULL,
                    PARENT_ID NUMBER,
                    NODE_LEVEL NUMBER DEFAULT 0,
                    NODE_ORDER NUMBER DEFAULT 0,
                    WINDOW_CLASS VARCHAR2(200),
                    DESCRIPTION NVARCHAR2(500),
                    ICON_PATH VARCHAR2(200),
                    IS_ACTIVE NUMBER(1) DEFAULT 1,
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    CONSTRAINT FK_SYSTEM_TREE_PARENT FOREIGN KEY (PARENT_ID) REFERENCES SYSTEM_TREE(NODE_ID)
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(createTableSQL)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول SYSTEM_TREE");
            }
            
            // إنشاء sequence للمفاتيح الأساسية
            String createSequenceSQL = "CREATE SEQUENCE SYSTEM_TREE_SEQ START WITH 1 INCREMENT BY 1";
            try (PreparedStatement stmt = connection.prepareStatement(createSequenceSQL)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء SYSTEM_TREE_SEQ");
            }
            
            // إدراج العقدة الجذر
            insertRootNode(connection);
            
        } catch (SQLException e) {
            if (e.getErrorCode() != 955) { // Table already exists
                System.err.println("خطأ في إنشاء جدول شجرة النظام: " + e.getMessage());
            }
        }
    }
    
    private static void insertRootNode(Connection connection) {
        try {
            String sql = """
                INSERT INTO SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, NODE_ORDER, DESCRIPTION, IS_ACTIVE)
                VALUES (SYSTEM_TREE_SEQ.NEXTVAL, 'نظام إدارة الشحنات', 'Ship ERP System', NULL, 0, 1, 'النظام الرئيسي لإدارة الشحنات', 1)
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.executeUpdate();
                System.out.println("✅ تم إدراج العقدة الجذر");
            }
        } catch (SQLException e) {
            if (e.getErrorCode() == 1) {
                System.out.println("⚠️ العقدة الجذر موجودة مسبقاً");
            } else {
                System.err.println("خطأ في إدراج العقدة الجذر: " + e.getMessage());
            }
        }
    }
    
    private static void registerEmailSystem(Connection connection) {
        try {
            System.out.println("\n📧 تسجيل نظام البريد الإلكتروني...");
            
            // البحث عن العقدة الجذر
            int rootId = getRootNodeId(connection);
            if (rootId == -1) {
                System.err.println("❌ لم يتم العثور على العقدة الجذر");
                return;
            }
            
            // إدراج عقدة نظام البريد الإلكتروني
            int emailSystemId = insertEmailSystemNode(connection, rootId);
            if (emailSystemId == -1) {
                return;
            }
            
            // إدراج نافذة إدارة حسابات البريد
            insertEmailAccountsWindow(connection, emailSystemId);
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تسجيل نظام البريد الإلكتروني: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static int getRootNodeId(Connection connection) throws SQLException {
        String sql = "SELECT NODE_ID FROM SYSTEM_TREE WHERE NODE_NAME_EN = 'Ship ERP System' AND IS_ACTIVE = 1";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt("NODE_ID");
            }
        }
        
        return -1;
    }
    
    private static int insertEmailSystemNode(Connection connection, int parentId) throws SQLException {
        // التحقق من عدم وجود العقدة مسبقاً
        String checkSql = "SELECT NODE_ID FROM SYSTEM_TREE WHERE NODE_NAME_EN = 'Email Management System' AND IS_ACTIVE = 1";
        try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next()) {
                System.out.println("⚠️ عقدة نظام البريد الإلكتروني موجودة مسبقاً");
                return rs.getInt("NODE_ID");
            }
        }
        
        // إدراج عقدة نظام البريد الإلكتروني
        String sql = """
            INSERT INTO SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, NODE_ORDER, DESCRIPTION, IS_ACTIVE)
            VALUES (SYSTEM_TREE_SEQ.NEXTVAL, 'نظام إدارة البريد الإلكتروني', 'Email Management System', ?, 1, 5, 'نظام شامل لإدارة البريد الإلكتروني والرسائل', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql, new String[]{"NODE_ID"})) {
            stmt.setInt(1, parentId);
            stmt.executeUpdate();
            
            try (ResultSet rs = stmt.getGeneratedKeys()) {
                if (rs.next()) {
                    int nodeId = rs.getInt(1);
                    System.out.println("✅ تم إدراج عقدة نظام البريد الإلكتروني");
                    return nodeId;
                }
            }
        }
        
        return -1;
    }
    
    private static void insertEmailAccountsWindow(Connection connection, int parentId) throws SQLException {
        // التحقق من عدم وجود النافذة مسبقاً
        String checkSql = "SELECT NODE_ID FROM SYSTEM_TREE WHERE WINDOW_CLASS = 'CompleteEmailAccountsWindow' AND IS_ACTIVE = 1";
        try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next()) {
                System.out.println("⚠️ نافذة إدارة حسابات البريد موجودة مسبقاً");
                return;
            }
        }
        
        // إدراج نافذة إدارة حسابات البريد
        String sql = """
            INSERT INTO SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, NODE_ORDER, WINDOW_CLASS, DESCRIPTION, IS_ACTIVE)
            VALUES (SYSTEM_TREE_SEQ.NEXTVAL, 'إدارة حسابات البريد', 'Email Accounts Management', ?, 2, 1, 'CompleteEmailAccountsWindow', 'إدارة شاملة لحسابات البريد الإلكتروني مع دعم IMAP وPOP3 وSMTP', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            stmt.executeUpdate();
            System.out.println("✅ تم إدراج نافذة إدارة حسابات البريد");
        }
    }
}
