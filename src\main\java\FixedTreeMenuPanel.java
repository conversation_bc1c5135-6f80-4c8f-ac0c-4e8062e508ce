import java.awt.*;
import java.awt.event.*;
import java.sql.*;
import java.util.*;
import javax.swing.*;
import javax.swing.tree.*;

/**
 * TreeMenuPanel مصحح ومبسط يدعم نوافذ البريد الإلكتروني
 */
public class FixedTreeMenuPanel extends JPanel {
    
    private JTree menuTree;
    private DefaultMutableTreeNode rootNode;
    private DefaultTreeModel treeModel;
    private Font arabicFont;
    private JFrame parentFrame;
    private Map<String, Runnable> menuActions;
    private Connection connection;
    
    public FixedTreeMenuPanel(JFrame parentFrame) {
        this.parentFrame = parentFrame;
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        this.menuActions = new HashMap<>();
        
        initializeConnection();
        initializeMenuActions();
        createTreeStructure();
        setupLayout();
        setupEventHandlers();
    }
    
    private void initializeConnection() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";
            
            connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
        }
    }
    
    private void initializeMenuActions() {
        // إجراءات البريد الإلكتروني
        menuActions.put("إدارة حسابات البريد", this::openEmailAccountsWindow);
        menuActions.put("Email Accounts Management", this::openEmailAccountsWindow);
        menuActions.put("CompleteEmailAccountsWindow", this::openEmailAccountsWindow);
        
        menuActions.put("صندوق البريد الوارد الشامل", this::openEmailInboxWindow);
        menuActions.put("Comprehensive Email Inbox", this::openEmailInboxWindow);
        menuActions.put("EmailInboxWindow", this::openEmailInboxWindow);
        menuActions.put("ComprehensiveEmailInboxWindow", this::openEmailInboxWindow);
        
        // إجراءات أخرى
        menuActions.put("الإعدادات العامة", this::openGeneralSettings);
        menuActions.put("إدارة المستخدمين", this::openUserManagement);
        menuActions.put("إدارة الأصناف", this::openItemsManagement);
        menuActions.put("مجموعات الأصناف", this::openItemGroupsManagement);
        menuActions.put("وحدات القياس", this::openMeasurementUnits);
        menuActions.put("إعدادات الواجهة والمظهر", this::openThemeSettings);
        menuActions.put("المتغيرات العامة", this::openGlobalVariables);
        menuActions.put("ربط النظام واستيراد البيانات", this::openSystemIntegration);
    }
    
    private void createTreeStructure() {
        rootNode = new DefaultMutableTreeNode("نظام إدارة الشحنات");
        treeModel = new DefaultTreeModel(rootNode);
        
        // إنشاء الشجرة من قاعدة البيانات
        if (connection != null) {
            createTreeFromDatabase();
        } else {
            createStaticTree();
        }
        
        menuTree = new JTree(treeModel);
        menuTree.setFont(arabicFont);
        menuTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        menuTree.setRootVisible(true);
        menuTree.setShowsRootHandles(true);
        
        // توسيع العقد الرئيسية
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }
    }
    
    private void createTreeFromDatabase() {
        try {
            String sql = """
                SELECT TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, WINDOW_CLASS, TREE_LEVEL
                FROM ERP_SYSTEM_TREE 
                WHERE IS_ACTIVE = 'Y' 
                ORDER BY PARENT_ID NULLS FIRST, DISPLAY_ORDER, TREE_ID
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                Map<Integer, DefaultMutableTreeNode> nodeMap = new HashMap<>();
                nodeMap.put(1, rootNode); // العقدة الجذر
                
                while (rs.next()) {
                    int treeId = rs.getInt("TREE_ID");
                    Integer parentId = rs.getObject("PARENT_ID") != null ? rs.getInt("PARENT_ID") : null;
                    String nameAr = rs.getString("NODE_NAME_AR");
                    String nameEn = rs.getString("NODE_NAME_EN");
                    String windowClass = rs.getString("WINDOW_CLASS");
                    
                    if (treeId == 1) continue; // تخطي العقدة الجذر
                    
                    DefaultMutableTreeNode node = new DefaultMutableTreeNode(nameAr);
                    nodeMap.put(treeId, node);
                    
                    // إضافة العقدة للوالد
                    DefaultMutableTreeNode parentNode = nodeMap.get(parentId);
                    if (parentNode != null) {
                        parentNode.add(node);
                    } else {
                        rootNode.add(node);
                    }
                    
                    // إضافة إجراء إذا كان هناك window class
                    if (windowClass != null && !windowClass.trim().isEmpty()) {
                        menuActions.put(nameAr, () -> openWindowByClassName(windowClass));
                        menuActions.put(nameEn, () -> openWindowByClassName(windowClass));
                        menuActions.put(windowClass, () -> openWindowByClassName(windowClass));
                    }
                }
                
                System.out.println("✅ تم تحميل شجرة النظام من قاعدة البيانات");
                
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل شجرة النظام: " + e.getMessage());
            createStaticTree();
        }
    }
    
    private void createStaticTree() {
        // إنشاء شجرة ثابتة كبديل
        DefaultMutableTreeNode emailNode = new DefaultMutableTreeNode("نظام إدارة البريد الإلكتروني");
        emailNode.add(new DefaultMutableTreeNode("إدارة حسابات البريد"));
        emailNode.add(new DefaultMutableTreeNode("صندوق البريد الوارد الشامل"));
        rootNode.add(emailNode);
        
        DefaultMutableTreeNode settingsNode = new DefaultMutableTreeNode("الإعدادات");
        settingsNode.add(new DefaultMutableTreeNode("الإعدادات العامة"));
        settingsNode.add(new DefaultMutableTreeNode("إعدادات الواجهة والمظهر"));
        settingsNode.add(new DefaultMutableTreeNode("المتغيرات العامة"));
        rootNode.add(settingsNode);
        
        DefaultMutableTreeNode itemsNode = new DefaultMutableTreeNode("إدارة الأصناف");
        itemsNode.add(new DefaultMutableTreeNode("مجموعات الأصناف"));
        itemsNode.add(new DefaultMutableTreeNode("وحدات القياس"));
        rootNode.add(itemsNode);
        
        DefaultMutableTreeNode usersNode = new DefaultMutableTreeNode("إدارة المستخدمين");
        rootNode.add(usersNode);
        
        DefaultMutableTreeNode integrationNode = new DefaultMutableTreeNode("ربط النظام واستيراد البيانات");
        rootNode.add(integrationNode);
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JScrollPane scrollPane = new JScrollPane(menuTree);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setPreferredSize(new Dimension(300, 600));
        
        add(scrollPane, BorderLayout.CENTER);
        
        // شريط الحالة
        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        add(statusLabel, BorderLayout.SOUTH);
    }
    
    private void setupEventHandlers() {
        menuTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode = 
                (DefaultMutableTreeNode) menuTree.getLastSelectedPathComponent();
            
            if (selectedNode != null && selectedNode.isLeaf() && !selectedNode.isRoot()) {
                String menuItem = selectedNode.toString();
                handleMenuItemClick(menuItem);
            }
        });
    }
    
    private void handleMenuItemClick(String menuItem) {
        System.out.println("🔄 تم النقر على: " + menuItem);
        
        Runnable action = menuActions.get(menuItem);
        if (action != null) {
            try {
                action.run();
            } catch (Exception e) {
                System.err.println("❌ خطأ في تنفيذ الإجراء: " + e.getMessage());
                e.printStackTrace();
                showMessage("خطأ في تنفيذ الإجراء: " + e.getMessage());
            }
        } else {
            showMessage("لم يتم تنفيذ هذه الوظيفة بعد: " + menuItem);
        }
    }
    
    // ===== إجراءات النوافذ =====
    
    private void openEmailAccountsWindow() {
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("📧 فتح نافذة إدارة حسابات البريد الإلكتروني...");
                CompleteEmailAccountsWindow emailWindow = new CompleteEmailAccountsWindow();
                emailWindow.setVisible(true);
                System.out.println("✅ تم فتح نافذة إدارة حسابات البريد الإلكتروني بنجاح");
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة إدارة حسابات البريد: " + e.getMessage());
                e.printStackTrace();
                showMessage("خطأ في فتح نافذة إدارة حسابات البريد:\n" + e.getMessage());
            }
        });
    }
    
    private void openEmailInboxWindow() {
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("📥 فتح نافذة صندوق البريد الوارد...");
                EmailInboxWindow inboxWindow = new EmailInboxWindow();
                inboxWindow.setVisible(true);
                System.out.println("✅ تم فتح نافذة صندوق البريد الوارد بنجاح");
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة صندوق البريد الوارد: " + e.getMessage());
                e.printStackTrace();
                showMessage("خطأ في فتح نافذة صندوق البريد الوارد:\n" + e.getMessage());
            }
        });
    }
    
    private void openWindowByClassName(String className) {
        System.out.println("🔄 محاولة فتح النافذة: " + className);
        
        switch (className) {
            case "CompleteEmailAccountsWindow":
                openEmailAccountsWindow();
                break;
            case "EmailInboxWindow":
            case "ComprehensiveEmailInboxWindow":
                openEmailInboxWindow();
                break;
            default:
                showMessage("النافذة " + className + " غير مدعومة حالياً");
        }
    }
    
    private void openGeneralSettings() {
        showMessage("نافذة الإعدادات العامة قيد التطوير");
    }
    
    private void openUserManagement() {
        showMessage("نافذة إدارة المستخدمين قيد التطوير");
    }
    
    private void openItemsManagement() {
        showMessage("نافذة إدارة الأصناف قيد التطوير");
    }
    
    private void openItemGroupsManagement() {
        showMessage("نافذة مجموعات الأصناف قيد التطوير");
    }
    
    private void openMeasurementUnits() {
        showMessage("نافذة وحدات القياس قيد التطوير");
    }
    
    private void openThemeSettings() {
        showMessage("نافذة إعدادات المظهر قيد التطوير");
    }
    
    private void openGlobalVariables() {
        showMessage("نافذة المتغيرات العامة قيد التطوير");
    }
    
    private void openSystemIntegration() {
        showMessage("نافذة ربط النظام قيد التطوير");
    }
    
    private void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "رسالة النظام", JOptionPane.INFORMATION_MESSAGE);
    }
}
