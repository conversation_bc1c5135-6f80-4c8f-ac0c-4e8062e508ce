import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.Window;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;
import javax.swing.LookAndFeel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * مدير المظاهر النهائي الموحد للتطبيق
 * Final Unified Theme Manager for the Application
 */
public class FinalThemeManager {
    
    private static FinalThemeManager instance;
    private static final String SETTINGS_FILE = "unified-theme.properties";
    private static final String THEME_KEY = "theme";
    
    private String currentTheme = "FlatLaf Light";
    
    private FinalThemeManager() {
        loadSettings();
    }
    
    public static FinalThemeManager getInstance() {
        if (instance == null) {
            instance = new FinalThemeManager();
        }
        return instance;
    }
    
    /**
     * تطبيق المظهر الافتراضي عند بدء التطبيق
     */
    public static void initializeDefaultTheme() {
        try {
            FinalThemeManager manager = getInstance();
            manager.applyStoredTheme();
            System.out.println("✅ تم تطبيق المظهر الافتراضي: " + manager.currentTheme);
        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر الافتراضي: " + e.getMessage());
            // تطبيق مظهر احتياطي
            try {
                applyFlatLightTheme();
                System.out.println("✅ تم تطبيق المظهر الاحتياطي: FlatLaf Light");
            } catch (Exception ex) {
                System.err.println("❌ فشل في تطبيق المظهر الاحتياطي");
            }
        }
    }
    
    /**
     * تطبيق مظهر محدد
     */
    public boolean applyTheme(String themeName) {
        try {
            System.out.println("🎨 تطبيق المظهر: " + themeName);
            
            boolean success = false;
            
            switch (themeName) {
                case "FlatLaf Light":
                    success = applyFlatLightTheme();
                    break;
                case "FlatLaf Dark":
                    success = applyFlatDarkTheme();
                    break;
                case "FlatLaf IntelliJ":
                    success = applyFlatIntelliJTheme();
                    break;
                case "FlatLaf Darcula":
                    success = applyFlatDarculaTheme();
                    break;
                case "System Default":
                    success = applySystemTheme();
                    break;
                case "Windows":
                    success = applyWindowsTheme();
                    break;
                case "Metal":
                    success = applyMetalTheme();
                    break;
                case "Nimbus":
                    success = applyNimbusTheme();
                    break;
                default:
                    success = applyFlatLightTheme();
                    themeName = "FlatLaf Light";
                    break;
            }
            
            if (success) {
                currentTheme = themeName;
                applyCustomizations();
                updateAllWindows();
                saveSettings();
                System.out.println("✅ تم تطبيق المظهر بنجاح: " + themeName);
                return true;
            } else {
                System.err.println("❌ فشل في تطبيق المظهر: " + themeName);
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyFlatLightTheme() {
        try {
            Class<?> flatLightClass = Class.forName("com.formdev.flatlaf.FlatLightLaf");
            java.lang.reflect.Method setupMethod = flatLightClass.getMethod("setup");
            return (Boolean) setupMethod.invoke(null);
        } catch (Exception e) {
            System.err.println("FlatLaf Light غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyFlatDarkTheme() {
        try {
            Class<?> flatDarkClass = Class.forName("com.formdev.flatlaf.FlatDarkLaf");
            java.lang.reflect.Method setupMethod = flatDarkClass.getMethod("setup");
            return (Boolean) setupMethod.invoke(null);
        } catch (Exception e) {
            System.err.println("FlatLaf Dark غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyFlatIntelliJTheme() {
        try {
            Class<?> flatIntelliJClass = Class.forName("com.formdev.flatlaf.FlatIntelliJLaf");
            java.lang.reflect.Method setupMethod = flatIntelliJClass.getMethod("setup");
            return (Boolean) setupMethod.invoke(null);
        } catch (Exception e) {
            System.err.println("FlatLaf IntelliJ غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyFlatDarculaTheme() {
        try {
            Class<?> flatDarculaClass = Class.forName("com.formdev.flatlaf.FlatDarculaLaf");
            java.lang.reflect.Method setupMethod = flatDarculaClass.getMethod("setup");
            return (Boolean) setupMethod.invoke(null);
        } catch (Exception e) {
            System.err.println("FlatLaf Darcula غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applySystemTheme() {
        try {
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("windows")) {
                UIManager.setLookAndFeel("com.sun.java.swing.plaf.windows.WindowsLookAndFeel");
            } else if (osName.contains("mac")) {
                UIManager.setLookAndFeel("com.apple.laf.AquaLookAndFeel");
            } else {
                UIManager.setLookAndFeel("javax.swing.plaf.metal.MetalLookAndFeel");
            }
            return true;
        } catch (Exception e) {
            System.err.println("System Theme غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyWindowsTheme() {
        try {
            UIManager.setLookAndFeel("com.sun.java.swing.plaf.windows.WindowsLookAndFeel");
            return true;
        } catch (Exception e) {
            System.err.println("Windows Theme غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyMetalTheme() {
        try {
            UIManager.setLookAndFeel("javax.swing.plaf.metal.MetalLookAndFeel");
            return true;
        } catch (Exception e) {
            System.err.println("Metal Theme غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyNimbusTheme() {
        try {
            UIManager.setLookAndFeel("javax.swing.plaf.nimbus.NimbusLookAndFeel");
            return true;
        } catch (Exception e) {
            System.err.println("Nimbus Theme غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * تطبيق التخصيصات الإضافية
     */
    private void applyCustomizations() {
        // إعدادات الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        UIManager.put("defaultFont", arabicFont);
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("TextArea.font", arabicFont);
        UIManager.put("ComboBox.font", arabicFont);
        UIManager.put("Table.font", arabicFont);
        UIManager.put("Tree.font", arabicFont);
        UIManager.put("Menu.font", arabicFont);
        UIManager.put("MenuItem.font", arabicFont);
        
        // إعدادات FlatLaf المتقدمة
        if (currentTheme.startsWith("FlatLaf")) {
            UIManager.put("Button.arc", 8);
            UIManager.put("Component.arc", 8);
            UIManager.put("ProgressBar.arc", 8);
            UIManager.put("TextComponent.arc", 8);
            UIManager.put("ScrollBar.width", 12);
            UIManager.put("Table.showHorizontalLines", true);
            UIManager.put("Table.showVerticalLines", true);
        }
        
        System.out.println("✅ تم تطبيق التخصيصات الإضافية");
    }
    
    /**
     * تحديث جميع النوافذ المفتوحة
     */
    private void updateAllWindows() {
        SwingUtilities.invokeLater(() -> {
            for (Window window : Window.getWindows()) {
                if (window.isDisplayable()) {
                    SwingUtilities.updateComponentTreeUI(window);
                    window.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                    window.repaint();
                }
            }
            System.out.println("✅ تم تحديث جميع النوافذ المفتوحة");
        });
    }
    
    /**
     * تطبيق المظهر المحفوظ
     */
    public void applyStoredTheme() {
        applyTheme(currentTheme);
    }
    
    /**
     * تحميل الإعدادات
     */
    private void loadSettings() {
        try {
            Properties props = new Properties();
            props.load(new FileInputStream(SETTINGS_FILE));
            
            currentTheme = props.getProperty(THEME_KEY, "FlatLaf Light");
            System.out.println("✅ تم تحميل الإعدادات: " + currentTheme);
            
        } catch (IOException e) {
            System.out.println("ℹ️ لم يتم العثور على ملف الإعدادات، سيتم استخدام الإعدادات الافتراضية");
            currentTheme = "FlatLaf Light";
        }
    }
    
    /**
     * حفظ الإعدادات
     */
    private void saveSettings() {
        try {
            Properties props = new Properties();
            props.setProperty(THEME_KEY, currentTheme);
            
            props.store(new FileOutputStream(SETTINGS_FILE), "Unified Theme Settings");
            System.out.println("✅ تم حفظ الإعدادات");
            
        } catch (IOException e) {
            System.err.println("❌ خطأ في حفظ الإعدادات: " + e.getMessage());
        }
    }
    
    // Getters
    public String getCurrentTheme() { return currentTheme; }
    
    /**
     * الحصول على معلومات المظهر الحالي
     */
    public String getCurrentThemeInfo() {
        LookAndFeel laf = UIManager.getLookAndFeel();
        if (laf != null) {
            return String.format("المظهر: %s | الكلاس: %s", currentTheme, laf.getClass().getSimpleName());
        }
        return "لا يوجد مظهر محدد";
    }
    
    /**
     * إصلاح جميع النوافذ لتستخدم المدير الموحد
     */
    public static void fixAllWindowsThemes() {
        System.out.println("🔧 إصلاح مظاهر جميع النوافذ...");
        
        FinalThemeManager manager = getInstance();
        manager.applyStoredTheme();
        
        System.out.println("✅ تم إصلاح مظاهر جميع النوافذ");
    }
    
    /**
     * الحصول على قائمة المظاهر المتاحة
     */
    public static String[] getAvailableThemes() {
        return new String[] {
            "FlatLaf Light", "FlatLaf Dark", "FlatLaf IntelliJ", "FlatLaf Darcula",
            "System Default", "Windows", "Metal", "Nimbus"
        };
    }
}
