import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.io.File;
import javax.swing.Icon;
import javax.swing.ImageIcon;
import javax.swing.JFrame;
import javax.swing.JScrollPane;
import javax.swing.JTree;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeCellRenderer;
import javax.swing.tree.DefaultTreeModel;

/**
 * شجرة الأنظمة مع الأيقونات الكاملة
 * System Tree with Complete Icons
 */
public class SystemTreeWithIcons extends J<PERSON>rame {
    
    private JTree systemTree;
    private Font arabicFont;
    private SystemTreeManager systemTreeManager;
    
    public SystemTreeWithIcons() {
        setTitle("🌳 شجرة الأنظمة مع الأيقونات - System Tree with Icons");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(900, 700);
        setLocationRelativeTo(null);
        
        // إعداد الخط العربي
        try {
            arabicFont = new Font("Tahoma", Font.PLAIN, 14);
        } catch (Exception e) {
            arabicFont = new Font("SansSerif", Font.PLAIN, 14);
        }
        
        initializeComponents();
        setupUI();
        loadSystemTree();
    }
    
    private void initializeComponents() {
        try {
            systemTreeManager = SystemTreeManager.getInstance();
            System.out.println("✅ تم إنشاء مدير شجرة النظام");
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء مدير شجرة النظام: " + e.getMessage());
        }
    }
    
    private void setupUI() {
        setLayout(new BorderLayout());
        
        // إنشاء الشجرة
        systemTree = new JTree();
        systemTree.setFont(arabicFont);
        systemTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        systemTree.setRowHeight(32); // ارتفاع مناسب للأيقونات
        systemTree.setRootVisible(true);
        systemTree.setShowsRootHandles(true);
        
        // إعداد عارض الخلايا المحسن
        systemTree.setCellRenderer(new EnhancedTreeCellRenderer());
        
        // إضافة الشجرة إلى scroll pane
        JScrollPane scrollPane = new JScrollPane(systemTree);
        scrollPane.setPreferredSize(new Dimension(880, 680));
        
        add(scrollPane, BorderLayout.CENTER);
    }
    
    private void loadSystemTree() {
        try {
            if (systemTreeManager != null) {
                DefaultTreeModel treeModel = systemTreeManager.getSystemTreeModel();
                systemTree.setModel(treeModel);
                
                // توسيع العقد الرئيسية
                expandMainNodes();
                
                System.out.println("✅ تم تحميل شجرة النظام مع الأيقونات");
                printIconStatus();
            } else {
                System.err.println("❌ مدير شجرة النظام غير متاح");
            }
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحميل شجرة النظام: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void expandMainNodes() {
        // توسيع العقد الرئيسية فقط
        for (int i = 0; i < Math.min(systemTree.getRowCount(), 10); i++) {
            systemTree.expandRow(i);
        }
    }
    
    private void printIconStatus() {
        System.out.println("\n📊 حالة الأيقونات:");
        System.out.println("==================");
        
        String[] iconFiles = {
            "resources/icons/folder.png",
            "resources/icons/window.png", 
            "resources/icons/tool.png",
            "resources/icons/report.png",
            "resources/icons/user.png",
            "resources/icons/settings.png",
            "resources/icons/email.png",
            "resources/icons/database.png",
            "resources/icons/default.png"
        };
        
        for (String iconPath : iconFiles) {
            File iconFile = new File(iconPath);
            if (iconFile.exists()) {
                System.out.println("✅ " + iconPath + " (" + iconFile.length() + " bytes)");
            } else {
                System.out.println("❌ " + iconPath + " (مفقود)");
            }
        }
    }
    
    /**
     * عارض خلايا الشجرة المحسن مع دعم كامل للأيقونات
     */
    private class EnhancedTreeCellRenderer extends DefaultTreeCellRenderer {
        
        public EnhancedTreeCellRenderer() {
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        }
        
        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value, boolean selected,
                boolean expanded, boolean leaf, int row, boolean hasFocus) {
            
            super.getTreeCellRendererComponent(tree, value, selected, expanded, leaf, row, hasFocus);
            
            // إعداد الخط العربي
            setFont(arabicFont);
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            
            // تحميل الأيقونة من قاعدة البيانات
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
            Icon nodeIcon = loadNodeIcon(node);
            
            if (nodeIcon != null) {
                setIcon(nodeIcon);
            } else {
                // استخدام أيقونة افتراضية حسب نوع العقدة
                setFallbackIcon(node, leaf);
            }
            
            return this;
        }
        
        /**
         * تحميل أيقونة العقدة من قاعدة البيانات
         */
        private Icon loadNodeIcon(DefaultMutableTreeNode node) {
            try {
                Object userObject = node.getUserObject();
                if (userObject instanceof SystemTreeManager.SystemTreeNode) {
                    SystemTreeManager.SystemTreeNode treeNode = (SystemTreeManager.SystemTreeNode) userObject;
                    if (treeNode.iconPath != null && !treeNode.iconPath.trim().isEmpty()) {
                        return createIconFromPath(treeNode.iconPath);
                    }
                }
            } catch (Exception e) {
                System.err.println("خطأ في تحميل أيقونة العقدة: " + e.getMessage());
            }
            return null;
        }
        
        /**
         * إنشاء أيقونة من المسار
         */
        private Icon createIconFromPath(String iconPath) {
            try {
                File iconFile = new File(iconPath);
                if (iconFile.exists()) {
                    ImageIcon originalIcon = new ImageIcon(iconPath);
                    if (originalIcon.getIconWidth() > 0) {
                        // تغيير حجم الأيقونة لتناسب الشجرة
                        java.awt.Image scaledImage = originalIcon.getImage()
                            .getScaledInstance(24, 24, java.awt.Image.SCALE_SMOOTH);
                        return new ImageIcon(scaledImage);
                    }
                }
            } catch (Exception e) {
                System.err.println("خطأ في إنشاء أيقونة من " + iconPath + ": " + e.getMessage());
            }
            return null;
        }
        
        /**
         * تعيين أيقونة احتياطية
         */
        private void setFallbackIcon(DefaultMutableTreeNode node, boolean leaf) {
            String nodeText = node.toString();
            
            if (node.isRoot()) {
                setIcon(createIconFromPath("resources/icons/folder.png"));
            } else if (!leaf) {
                // فئة أو مجلد
                setIcon(createIconFromPath("resources/icons/folder.png"));
            } else {
                // نافذة أو عنصر
                if (nodeText.contains("إعدادات")) {
                    setIcon(createIconFromPath("resources/icons/settings.png"));
                } else if (nodeText.contains("مستخدم")) {
                    setIcon(createIconFromPath("resources/icons/user.png"));
                } else if (nodeText.contains("بريد")) {
                    setIcon(createIconFromPath("resources/icons/email.png"));
                } else if (nodeText.contains("قاعدة") || nodeText.contains("سجل")) {
                    setIcon(createIconFromPath("resources/icons/database.png"));
                } else {
                    setIcon(createIconFromPath("resources/icons/window.png"));
                }
            }
        }
        
        @Override
        protected void paintComponent(Graphics g) {
            if (g instanceof Graphics2D) {
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
                        RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING,
                        RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                        RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            }
            super.paintComponent(g);
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                FinalThemeManager.initializeDefaultTheme();
            } catch (Exception e) {
                System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
            }
            
            System.out.println("🌳 تشغيل شجرة الأنظمة مع الأيقونات");
            System.out.println("System Tree with Icons Starting...");
            System.out.println("==========================================");
            
            SystemTreeWithIcons treeViewer = new SystemTreeWithIcons();
            treeViewer.setVisible(true);
            
            System.out.println("✅ تم تشغيل شجرة الأنظمة مع الأيقونات");
            System.out.println("📋 تحقق من ظهور الأيقونات بجانب أسماء العقد");
        });
    }
}
