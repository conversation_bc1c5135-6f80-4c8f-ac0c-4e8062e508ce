# سكريبت تنزيل المكتبات المفقودة لنظام Ship ERP
# Download Missing Libraries Script for Ship ERP System

Write-Host "========================================" -ForegroundColor Green
Write-Host "  تنزيل المكتبات المفقودة" -ForegroundColor Green
Write-Host "  Download Missing Libraries" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# إنشاء مجلد lib إذا لم يكن موجوداً
if (!(Test-Path "lib")) {
    New-Item -ItemType Directory -Path "lib"
    Write-Host "✅ تم إنشاء مجلد lib" -ForegroundColor Green
}

# قائمة المكتبات المطلوبة
$libraries = @(
    @{
        Name = "Apache POI OOXML"
        Url = "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml/5.2.4/poi-ooxml-5.2.4.jar"
        File = "lib\poi-ooxml-5.2.4.jar"
    },
    @{
        Name = "Apache POI"
        Url = "https://repo1.maven.org/maven2/org/apache/poi/poi/5.2.4/poi-5.2.4.jar"
        File = "lib\poi-5.2.4.jar"
    },
    @{
        Name = "iText Kernel"
        Url = "https://repo1.maven.org/maven2/com/itextpdf/kernel/7.2.5/kernel-7.2.5.jar"
        File = "lib\itext-kernel-7.2.5.jar"
    },
    @{
        Name = "iText Layout"
        Url = "https://repo1.maven.org/maven2/com/itextpdf/layout/7.2.5/layout-7.2.5.jar"
        File = "lib\itext-layout-7.2.5.jar"
    },
    @{
        Name = "iText IO"
        Url = "https://repo1.maven.org/maven2/com/itextpdf/io/7.2.5/io-7.2.5.jar"
        File = "lib\itext-io-7.2.5.jar"
    },
    @{
        Name = "Jackson Databind"
        Url = "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar"
        File = "lib\jackson-databind-2.15.2.jar"
    },
    @{
        Name = "Jackson Core"
        Url = "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar"
        File = "lib\jackson-core-2.15.2.jar"
    },
    @{
        Name = "Jackson Annotations"
        Url = "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar"
        File = "lib\jackson-annotations-2.15.2.jar"
    },
    @{
        Name = "BCrypt"
        Url = "https://repo1.maven.org/maven2/org/mindrot/jbcrypt/0.4/jbcrypt-0.4.jar"
        File = "lib\jbcrypt-0.4.jar"
    },
    @{
        Name = "Typesafe Config"
        Url = "https://repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2.jar"
        File = "lib\config-1.4.2.jar"
    }
)

# تنزيل المكتبات
$successCount = 0
$totalCount = $libraries.Count

foreach ($lib in $libraries) {
    Write-Host "🔄 تنزيل $($lib.Name)..." -ForegroundColor Yellow
    
    try {
        # التحقق من وجود الملف
        if (Test-Path $lib.File) {
            Write-Host "  ✅ $($lib.Name) موجود بالفعل" -ForegroundColor Green
            $successCount++
            continue
        }
        
        # تنزيل الملف
        Invoke-WebRequest -Uri $lib.Url -OutFile $lib.File -UseBasicParsing
        
        # التحقق من نجاح التنزيل
        if (Test-Path $lib.File) {
            $fileSize = (Get-Item $lib.File).Length
            Write-Host "  ✅ تم تنزيل $($lib.Name) بنجاح ($([math]::Round($fileSize/1KB, 2)) KB)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "  ❌ فشل في تنزيل $($lib.Name)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "  ❌ خطأ في تنزيل $($lib.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 500
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  تقرير التنزيل" -ForegroundColor Green
Write-Host "  Download Report" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "✅ تم تنزيل: $successCount من $totalCount مكتبة" -ForegroundColor Green
Write-Host "📁 المجلد: lib\" -ForegroundColor Cyan

# عرض قائمة المكتبات المنزلة
Write-Host ""
Write-Host "المكتبات الموجودة في lib:" -ForegroundColor Cyan
Get-ChildItem "lib\*.jar" | ForEach-Object {
    $size = [math]::Round($_.Length/1KB, 2)
    Write-Host "  📦 $($_.Name) ($size KB)" -ForegroundColor White
}

Write-Host ""
Write-Host "🎉 تم الانتهاء من تنزيل المكتبات!" -ForegroundColor Green
