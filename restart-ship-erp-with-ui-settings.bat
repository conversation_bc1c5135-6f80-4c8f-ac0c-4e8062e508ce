@echo off
echo ========================================
echo   RESTARTING SHIP ERP WITH UI SETTINGS
echo   إعادة تشغيل Ship ERP مع إعدادات الواجهة
echo ========================================

cd /d "d:\java\java"

echo.
echo [INFO] Stopping current Ship ERP processes...
echo [INFO] إيقاف عمليات Ship ERP الحالية...

REM إيقاف عمليات Java الحالية
taskkill /f /im java.exe >nul 2>&1

echo ✅ Current processes stopped
echo ✅ تم إيقاف العمليات الحالية

echo.
echo [INFO] Compiling updated files...
echo [INFO] تجميع الملفات المحدثة...

REM تجميع نافذة إعدادات الواجهة
javac -encoding UTF-8 -cp "lib\*" AdvancedUISettingsWindow.java EnhancedSettingsManager.java

if %errorlevel% neq 0 (
    echo ❌ Failed to compile UI Settings Window
    pause
    exit /b 1
)

echo ✅ UI Settings Window compiled successfully
echo ✅ تم تجميع نافذة إعدادات الواجهة بنجاح

echo.
echo [INFO] Starting Ship ERP System...
echo [INFO] تشغيل نظام Ship ERP...

REM تشغيل النظام الرئيسي
start "Ship ERP System" java -cp "lib\*;." -Djava.awt.headless=false -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA ShipERPMain

echo.
echo ✅ Ship ERP System started successfully!
echo ✅ تم تشغيل نظام Ship ERP بنجاح!

echo.
echo [INFO] Waiting for system to load...
echo [INFO] انتظار تحميل النظام...

timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo   HOW TO ACCESS UI SETTINGS
echo   كيفية الوصول لإعدادات الواجهة
echo ========================================

echo.
echo The Advanced UI Settings Window is now available in the system tree:
echo نافذة إعدادات الواجهة المتطورة متاحة الآن في شجرة النظام:
echo.
echo 📍 LOCATION IN TREE:
echo    موقع النافذة في الشجرة:
echo    نظام إدارة الشحنات
echo    └── الإعدادات العامة
echo        └── إعدادات الواجهة والمظهر
echo.
echo 🎯 HOW TO ACCESS:
echo    كيفية الوصول:
echo    1. Open Ship ERP main window
echo       افتح النافذة الرئيسية لـ Ship ERP
echo.
echo    2. Look for the system tree on the left side
echo       ابحث عن شجرة النظام في الجانب الأيسر
echo.
echo    3. Expand "الإعدادات العامة" (General Settings)
echo       وسع "الإعدادات العامة"
echo.
echo    4. Click on "إعدادات الواجهة والمظهر"
echo       انقر على "إعدادات الواجهة والمظهر"
echo.
echo 🎨 FEATURES AVAILABLE:
echo    الميزات المتاحة:
echo    • 19+ Themes (FlatLaf, JTattoo, SeaGlass, etc.)
echo    • Font customization with Arabic support
echo    • Interface options (animations, sounds, tooltips)
echo    • Color customization with live preview
echo    • Database integration for settings persistence
echo    • Real-time theme application
echo.

echo.
echo ========================================
echo   ALTERNATIVE ACCESS METHODS
echo   طرق الوصول البديلة
echo ========================================

echo.
echo If you cannot find it in the tree, you can also:
echo إذا لم تجدها في الشجرة، يمكنك أيضاً:
echo.
echo 1. Use the Tools menu in the main window:
echo    استخدم قائمة الأدوات في النافذة الرئيسية:
echo    Tools → Advanced UI Settings
echo    أدوات ← إعدادات الواجهة المتطورة
echo.
echo 2. Run directly from command line:
echo    تشغيل مباشر من سطر الأوامر:
echo    java -cp "lib\*;." AdvancedUISettingsWindow
echo.

pause
