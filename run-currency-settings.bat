@echo off
echo ========================================
echo 💰 تشغيل نافذة إعدادات العملة
echo Running Currency Settings Window
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/4] تجميع الملفات...
echo Compiling Files...
echo ========================================

echo تجميع CurrencySettingsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CurrencySettingsWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CurrencySettingsWindow بنجاح
) else (
    echo ❌ فشل في تجميع CurrencySettingsWindow
    pause
    exit /b 1
)

echo تجميع AddCurrencySettingsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\AddCurrencySettingsWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع AddCurrencySettingsWindow بنجاح
) else (
    echo ❌ فشل في تجميع AddCurrencySettingsWindow
    pause
    exit /b 1
)

echo تجميع CollapsedTreeMenuPanel المحدث...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CollapsedTreeMenuPanel.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CollapsedTreeMenuPanel المحدث بنجاح
) else (
    echo ❌ فشل في تجميع CollapsedTreeMenuPanel المحدث
    pause
    exit /b 1
)

echo.
echo [2/4] اختيار العملية...
echo Choose Operation...
echo ========================================

echo.
echo اختر العملية التي تريد تنفيذها:
echo 1. تشغيل نافذة إعدادات العملة مباشرة
echo 2. إضافة النافذة إلى قاعدة البيانات (يتطلب اتصال بقاعدة البيانات)
echo 3. تشغيل الشجرة المحدثة مع النافذة الجديدة
echo 4. تشغيل جميع العمليات
echo.

set /p choice="أدخل اختيارك (1-4): "

echo.
echo [3/4] تنفيذ العملية المختارة...
echo Executing Selected Operation...
echo ========================================

if "%choice%"=="1" (
    echo تشغيل نافذة إعدادات العملة...
    start "Currency Settings Window" java -cp "lib\*;." CurrencySettingsWindow
    echo ✅ تم تشغيل نافذة إعدادات العملة
) else if "%choice%"=="2" (
    echo محاولة إضافة النافذة إلى قاعدة البيانات...
    java -cp "lib\*;." AddCurrencySettingsWindow
    if %errorlevel% equ 0 (
        echo ✅ تم إضافة النافذة إلى قاعدة البيانات بنجاح
    ) else (
        echo ⚠️ فشل في الاتصال بقاعدة البيانات
        echo يمكنك تشغيل النافذة مباشرة أو استخدام ملف SQL المرفق
    )
) else if "%choice%"=="3" (
    echo تشغيل الشجرة المحدثة...
    start "Updated Tree with Currency Settings" java -cp "lib\*;." CollapsedTreeTest
    echo ✅ تم تشغيل الشجرة المحدثة
) else if "%choice%"=="4" (
    echo تشغيل جميع العمليات...
    
    echo 1. تشغيل نافذة إعدادات العملة...
    start "Currency Settings Window" java -cp "lib\*;." CurrencySettingsWindow
    timeout /t 2 /nobreak >nul
    
    echo 2. تشغيل الشجرة المحدثة...
    start "Updated Tree" java -cp "lib\*;." CollapsedTreeTest
    timeout /t 2 /nobreak >nul
    
    echo 3. محاولة إضافة النافذة إلى قاعدة البيانات...
    java -cp "lib\*;." AddCurrencySettingsWindow
    
    echo ✅ تم تشغيل جميع العمليات
) else (
    echo ❌ اختيار غير صحيح، سيتم تشغيل النافذة افتراضياً
    start "Currency Settings Window" java -cp "lib\*;." CurrencySettingsWindow
)

echo.
echo [4/4] معلومات إضافية...
echo Additional Information...
echo ========================================

echo.
echo 📋 ملخص النافذة الجديدة:
echo ========================
echo • الاسم: إعدادات العملة
echo • الحالة: قيد التطوير
echo • الموقع: الإعدادات العامة ^> إعدادات العملة
echo • الكلاس: CurrencySettingsWindow
echo • الوصف: إعدادات وإدارة العملات المستخدمة في النظام

echo.
echo 🌳 تحديث شجرة الأنظمة:
echo =====================
echo • تم إضافة النافذة إلى CollapsedTreeMenuPanel
echo • تظهر تحت فئة "الإعدادات العامة"
echo • بعد "المتغيرات العامة"
echo • مع حالة "قيد التطوير"

echo.
echo 💾 قاعدة البيانات:
echo ==================
echo • الجدول: ERP_SYSTEM_TREE
echo • ملف SQL: scripts\add_currency_settings_window.sql
echo • يمكن تشغيل الملف يدوياً إذا لم يتم الاتصال تلقائياً

echo.
echo 🎯 الميزات المخططة:
echo ==================
echo • إدارة العملات (إضافة، تعديل، حذف)
echo • أسعار الصرف والتحديث التلقائي
echo • الإعدادات العامة للعملة
echo • التقارير والإحصائيات
echo • التكامل مع الأنظمة الخارجية

echo.
echo 📁 الملفات المطورة:
echo ==================
echo • CurrencySettingsWindow.java - النافذة الرئيسية
echo • AddCurrencySettingsWindow.java - أداة إضافة إلى قاعدة البيانات
echo • scripts\add_currency_settings_window.sql - ملف SQL
echo • CollapsedTreeMenuPanel.java - محدث ليتضمن النافذة

echo.
echo 🚀 للاستخدام المستقبلي:
echo =======================
echo • تشغيل النافذة: java -cp "lib\*;." CurrencySettingsWindow
echo • تشغيل الشجرة المحدثة: java -cp "lib\*;." CollapsedTreeTest
echo • إضافة إلى قاعدة البيانات: java -cp "lib\*;." AddCurrencySettingsWindow

echo.
echo ========================================
echo ✅ انتهت العملية بنجاح!
echo Operation Completed Successfully!
echo ========================================

echo.
pause
