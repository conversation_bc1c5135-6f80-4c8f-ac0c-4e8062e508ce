package com.shipment.erp.service;

import com.shipment.erp.model.Branch;
import java.util.List;

/**
 * خدمة إدارة الفروع المبسطة
 * Simple Branch Management Service
 */
public interface SimpleBranchService extends SimpleBaseService<Branch> {
    
    /**
     * البحث عن الفروع حسب الشركة
     * Find branches by company
     */
    List<Branch> findByCompanyId(Long companyId);
    
    /**
     * البحث عن الفروع النشطة حسب الشركة
     * Find active branches by company
     */
    List<Branch> findActiveByCompanyId(Long companyId);
    
    /**
     * البحث عن فرع بالكود
     * Find branch by code
     */
    Branch findByCode(String code);
    
    /**
     * البحث عن الفروع بالاسم
     * Find branches by name
     */
    List<Branch> findByNameContaining(String name);
    
    /**
     * التحقق من وجود كود الفرع
     * Check if branch code exists
     */
    boolean existsByCode(String code);
    
    /**
     * تفعيل/إلغاء تفعيل الفرع
     * Activate/Deactivate branch
     */
    void toggleActive(Long branchId);
    
    /**
     * الحصول على عدد الفروع النشطة للشركة
     * Get active branches count for company
     */
    long getActiveBranchesCount(Long companyId);
}
