import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.IvParameterSpec;

/**
 * مدير الأمان - Security Manager
 * يدير تشفير كلمات المرور والبيانات الحساسة
 */
public class SecurityManager {
    
    private static SecurityManager instance;
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final String MASTER_KEY = "ShipERP2025SecureKey!@#$%^&*()";
    
    private SecretKey secretKey;
    
    private SecurityManager() {
        initializeEncryption();
    }
    
    public static synchronized SecurityManager getInstance() {
        if (instance == null) {
            instance = new SecurityManager();
        }
        return instance;
    }
    
    /**
     * تهيئة نظام التشفير
     */
    private void initializeEncryption() {
        try {
            // إنشاء مفتاح التشفير من المفتاح الرئيسي
            MessageDigest sha = MessageDigest.getInstance("SHA-256");
            byte[] key = sha.digest(MASTER_KEY.getBytes(StandardCharsets.UTF_8));
            byte[] keyBytes = new byte[16]; // AES-128
            System.arraycopy(key, 0, keyBytes, 0, 16);
            
            secretKey = new SecretKeySpec(keyBytes, ALGORITHM);
            
            System.out.println("✅ تم تهيئة نظام التشفير بنجاح");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة نظام التشفير: " + e.getMessage());
        }
    }
    
    /**
     * تشفير كلمة مرور
     */
    public String encryptPassword(String password) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            
            // إنشاء IV عشوائي
            byte[] iv = new byte[16];
            new SecureRandom().nextBytes(iv);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
            byte[] encryptedData = cipher.doFinal(password.getBytes(StandardCharsets.UTF_8));
            
            // دمج IV مع البيانات المشفرة
            byte[] encryptedWithIv = new byte[iv.length + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, iv.length);
            System.arraycopy(encryptedData, 0, encryptedWithIv, iv.length, encryptedData.length);
            
            return Base64.getEncoder().encodeToString(encryptedWithIv);
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تشفير كلمة المرور: " + e.getMessage());
            return password; // إرجاع كلمة المرور الأصلية في حالة الخطأ
        }
    }
    
    /**
     * فك تشفير كلمة مرور
     */
    public String decryptPassword(String encryptedPassword) {
        try {
            byte[] encryptedWithIv = Base64.getDecoder().decode(encryptedPassword);
            
            // استخراج IV
            byte[] iv = new byte[16];
            System.arraycopy(encryptedWithIv, 0, iv, 0, 16);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            // استخراج البيانات المشفرة
            byte[] encryptedData = new byte[encryptedWithIv.length - 16];
            System.arraycopy(encryptedWithIv, 16, encryptedData, 0, encryptedData.length);
            
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
            byte[] decryptedData = cipher.doFinal(encryptedData);
            
            return new String(decryptedData, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فك تشفير كلمة المرور: " + e.getMessage());
            return encryptedPassword; // إرجاع النص المشفر في حالة الخطأ
        }
    }
    
    /**
     * إنشاء hash لكلمة مرور (للتحقق)
     */
    public String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashedBytes = md.digest(password.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashedBytes);
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء hash لكلمة المرور: " + e.getMessage());
            return password;
        }
    }
    
    /**
     * التحقق من كلمة مرور
     */
    public boolean verifyPassword(String password, String hashedPassword) {
        String newHash = hashPassword(password);
        return newHash.equals(hashedPassword);
    }
    
    /**
     * إنشاء كلمة مرور عشوائية آمنة
     */
    public String generateSecurePassword(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return password.toString();
    }
    
    /**
     * تشفير نص عام
     */
    public String encryptText(String text) {
        return encryptPassword(text);
    }
    
    /**
     * فك تشفير نص عام
     */
    public String decryptText(String encryptedText) {
        return decryptPassword(encryptedText);
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public PasswordStrength checkPasswordStrength(String password) {
        if (password == null || password.length() < 6) {
            return PasswordStrength.WEAK;
        }
        
        boolean hasUpper = password.chars().anyMatch(Character::isUpperCase);
        boolean hasLower = password.chars().anyMatch(Character::isLowerCase);
        boolean hasDigit = password.chars().anyMatch(Character::isDigit);
        boolean hasSpecial = password.chars().anyMatch(ch -> "!@#$%^&*()_+-=[]{}|;:,.<>?".indexOf(ch) >= 0);
        
        int score = 0;
        if (hasUpper) score++;
        if (hasLower) score++;
        if (hasDigit) score++;
        if (hasSpecial) score++;
        if (password.length() >= 12) score++;
        
        if (score >= 4) return PasswordStrength.STRONG;
        if (score >= 2) return PasswordStrength.MEDIUM;
        return PasswordStrength.WEAK;
    }
    
    /**
     * تنظيف البيانات الحساسة من الذاكرة
     */
    public void clearSensitiveData() {
        // تنظيف المفتاح السري
        if (secretKey != null) {
            try {
                // محاولة تنظيف البيانات الحساسة
                System.gc(); // اقتراح garbage collection
                System.out.println("✅ تم تنظيف البيانات الحساسة من الذاكرة");
            } catch (Exception e) {
                System.err.println("⚠️ تحذير: لم يتم تنظيف البيانات الحساسة بالكامل");
            }
        }
    }
    
    /**
     * تسجيل محاولة دخول
     */
    public void logSecurityEvent(String event, String user, boolean success) {
        String timestamp = java.time.LocalDateTime.now().toString();
        String status = success ? "SUCCESS" : "FAILED";
        String logEntry = String.format("[%s] %s - User: %s - %s", timestamp, event, user, status);
        
        System.out.println("🔐 Security Log: " + logEntry);
        
        // يمكن إضافة كتابة إلى ملف سجل هنا
    }
    
    /**
     * enum لقوة كلمة المرور
     */
    public enum PasswordStrength {
        WEAK("ضعيفة"),
        MEDIUM("متوسطة"),
        STRONG("قوية");
        
        private final String arabicName;
        
        PasswordStrength(String arabicName) {
            this.arabicName = arabicName;
        }
        
        public String getArabicName() {
            return arabicName;
        }
    }
    
    /**
     * اختبار نظام التشفير
     */
    public static void testEncryption() {
        System.out.println("🔍 اختبار نظام التشفير...");
        
        SecurityManager sm = SecurityManager.getInstance();
        
        // اختبار تشفير كلمات المرور
        String originalPassword = "ship_erp_password";
        String encrypted = sm.encryptPassword(originalPassword);
        String decrypted = sm.decryptPassword(encrypted);
        
        System.out.println("Original: " + originalPassword);
        System.out.println("Encrypted: " + encrypted);
        System.out.println("Decrypted: " + decrypted);
        System.out.println("Match: " + originalPassword.equals(decrypted));
        
        // اختبار قوة كلمة المرور
        PasswordStrength strength = sm.checkPasswordStrength(originalPassword);
        System.out.println("Password Strength: " + strength.getArabicName());
        
        // اختبار إنشاء كلمة مرور آمنة
        String securePassword = sm.generateSecurePassword(16);
        System.out.println("Generated Secure Password: " + securePassword);
        
        System.out.println("✅ اختبار نظام التشفير مكتمل");
    }
}
