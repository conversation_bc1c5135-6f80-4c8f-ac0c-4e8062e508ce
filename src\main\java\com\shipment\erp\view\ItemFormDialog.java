package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.math.BigDecimal;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSpinner;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SpinnerNumberModel;
import com.shipment.erp.model.Item;

/**
 * نافذة نموذج الصنف
 * Item Form Dialog
 */
public class ItemFormDialog extends JDialog {

    private Font arabicFont;
    private boolean confirmed = false;
    private Item item;

    // Form fields
    private JTextField codeField;
    private JTextField nameField;
    private JTextField nameEnField;
    private JTextArea descriptionArea;
    private JTextArea descriptionEnArea;
    private JComboBox<String> categoryCombo;
    private JComboBox<String> unitCombo;
    private JTextField weightField;
    private JTextField dimensionsField;
    private JTextField priceField;
    private JTextField costField;
    private JTextField barcodeField;
    private JTextField skuField;
    private JSpinner minStockSpinner;
    private JSpinner maxStockSpinner;
    private JSpinner currentStockSpinner;
    private JCheckBox trackableCheckBox;
    private JCheckBox perishableCheckBox;
    private JSpinner shelfLifeSpinner;
    private JTextField supplierNameField;
    private JTextField supplierCodeField;
    private JTextArea notesArea;

    public ItemFormDialog(JFrame parent, String title, Item item) {
        super(parent, title, true);
        this.item = item;
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeComponents();
        setupLayout();
        loadItemData();

        setSize(800, 700);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(true);
        setMinimumSize(new Dimension(700, 600));
    }

    private void initializeComponents() {
        // معلومات أساسية
        codeField = new JTextField(20);
        codeField.setFont(arabicFont);
        codeField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        nameField = new JTextField(30);
        nameField.setFont(arabicFont);
        nameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        nameEnField = new JTextField(30);
        nameEnField.setFont(arabicFont);

        descriptionArea = new JTextArea(3, 30);
        descriptionArea.setFont(arabicFont);
        descriptionArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);

        descriptionEnArea = new JTextArea(3, 30);
        descriptionEnArea.setFont(arabicFont);
        descriptionEnArea.setLineWrap(true);
        descriptionEnArea.setWrapStyleWord(true);

        // الفئة والوحدة
        String[] categories = {"إلكترونيات", "ملابس", "أغذية", "مواد بناء", "أدوات", "أخرى"};
        categoryCombo = new JComboBox<>(categories);
        categoryCombo.setFont(arabicFont);
        categoryCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        categoryCombo.setEditable(true);

        String[] units = {"قطعة", "كيلو", "متر", "لتر", "صندوق", "كرتون", "طن"};
        unitCombo = new JComboBox<>(units);
        unitCombo.setFont(arabicFont);
        unitCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        unitCombo.setEditable(true);

        // المواصفات الفيزيائية
        weightField = new JTextField(15);
        weightField.setFont(arabicFont);
        weightField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        dimensionsField = new JTextField(20);
        dimensionsField.setFont(arabicFont);
        dimensionsField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الأسعار
        priceField = new JTextField(15);
        priceField.setFont(arabicFont);
        priceField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        costField = new JTextField(15);
        costField.setFont(arabicFont);
        costField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // معرفات المنتج
        barcodeField = new JTextField(20);
        barcodeField.setFont(arabicFont);

        skuField = new JTextField(20);
        skuField.setFont(arabicFont);

        // المخزون
        minStockSpinner = new JSpinner(new SpinnerNumberModel(0, 0, Integer.MAX_VALUE, 1));
        minStockSpinner.setFont(arabicFont);

        maxStockSpinner = new JSpinner(new SpinnerNumberModel(100, 0, Integer.MAX_VALUE, 1));
        maxStockSpinner.setFont(arabicFont);

        currentStockSpinner = new JSpinner(new SpinnerNumberModel(0, 0, Integer.MAX_VALUE, 1));
        currentStockSpinner.setFont(arabicFont);

        // خصائص المنتج
        trackableCheckBox = new JCheckBox("قابل للتتبع");
        trackableCheckBox.setFont(arabicFont);
        trackableCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        trackableCheckBox.setSelected(true);

        perishableCheckBox = new JCheckBox("قابل للتلف");
        perishableCheckBox.setFont(arabicFont);
        perishableCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        shelfLifeSpinner = new JSpinner(new SpinnerNumberModel(0, 0, 9999, 1));
        shelfLifeSpinner.setFont(arabicFont);

        // معلومات المورد
        supplierNameField = new JTextField(25);
        supplierNameField.setFont(arabicFont);
        supplierNameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        supplierCodeField = new JTextField(15);
        supplierCodeField.setFont(arabicFont);
        supplierCodeField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // ملاحظات
        notesArea = new JTextArea(4, 30);
        notesArea.setFont(arabicFont);
        notesArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        notesArea.setLineWrap(true);
        notesArea.setWrapStyleWord(true);
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة الرئيسية مع التمرير
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // معلومات أساسية
        addFormField(mainPanel, gbc, row++, "كود الصنف *:", codeField);
        addFormField(mainPanel, gbc, row++, "اسم الصنف *:", nameField);
        addFormField(mainPanel, gbc, row++, "الاسم الإنجليزي:", nameEnField);
        addFormField(mainPanel, gbc, row++, "الوصف:", new JScrollPane(descriptionArea));
        addFormField(mainPanel, gbc, row++, "الوصف الإنجليزي:", new JScrollPane(descriptionEnArea));

        // الفئة والوحدة
        addFormField(mainPanel, gbc, row++, "الفئة:", categoryCombo);
        addFormField(mainPanel, gbc, row++, "الوحدة:", unitCombo);

        // المواصفات الفيزيائية
        addFormField(mainPanel, gbc, row++, "الوزن (كجم):", weightField);
        addFormField(mainPanel, gbc, row++, "الأبعاد:", dimensionsField);

        // الأسعار
        addFormField(mainPanel, gbc, row++, "السعر:", priceField);
        addFormField(mainPanel, gbc, row++, "التكلفة:", costField);

        // معرفات المنتج
        addFormField(mainPanel, gbc, row++, "الباركود:", barcodeField);
        addFormField(mainPanel, gbc, row++, "SKU:", skuField);

        // المخزون
        addFormField(mainPanel, gbc, row++, "الحد الأدنى للمخزون:", minStockSpinner);
        addFormField(mainPanel, gbc, row++, "الحد الأقصى للمخزون:", maxStockSpinner);
        addFormField(mainPanel, gbc, row++, "المخزون الحالي:", currentStockSpinner);

        // خصائص المنتج
        JPanel propertiesPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        propertiesPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        propertiesPanel.add(trackableCheckBox);
        propertiesPanel.add(perishableCheckBox);
        addFormField(mainPanel, gbc, row++, "خصائص المنتج:", propertiesPanel);

        addFormField(mainPanel, gbc, row++, "مدة الصلاحية (أيام):", shelfLifeSpinner);

        // معلومات المورد
        addFormField(mainPanel, gbc, row++, "اسم المورد:", supplierNameField);
        addFormField(mainPanel, gbc, row++, "كود المورد:", supplierCodeField);

        // ملاحظات
        addFormField(mainPanel, gbc, row++, "ملاحظات:", new JScrollPane(notesArea));

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        add(scrollPane, BorderLayout.CENTER);

        // أزرار التحكم
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private void addFormField(JPanel panel, GridBagConstraints gbc, int row, String labelText, 
                             javax.swing.JComponent field) {
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        gbc.fill = GridBagConstraints.NONE;

        JLabel label = new JLabel(labelText);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(label, gbc);

        gbc.gridx = 0;
        gbc.weightx = 1.0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(field, gbc);
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton saveButton = new JButton("حفظ");
        saveButton.setFont(arabicFont);
        saveButton.setPreferredSize(new Dimension(100, 30));
        saveButton.addActionListener(e -> saveItem());

        JButton cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.setPreferredSize(new Dimension(100, 30));
        cancelButton.addActionListener(e -> dispose());

        panel.add(saveButton);
        panel.add(cancelButton);

        return panel;
    }

    private void loadItemData() {
        if (item != null) {
            codeField.setText(item.getCode());
            nameField.setText(item.getName());
            nameEnField.setText(item.getNameEn());
            descriptionArea.setText(item.getDescription());
            descriptionEnArea.setText(item.getDescriptionEn());
            
            if (item.getCategory() != null) {
                categoryCombo.setSelectedItem(item.getCategory());
            }
            if (item.getUnit() != null) {
                unitCombo.setSelectedItem(item.getUnit());
            }
            
            if (item.getWeight() != null) {
                weightField.setText(item.getWeight().toString());
            }
            dimensionsField.setText(item.getDimensions());
            
            if (item.getPrice() != null) {
                priceField.setText(item.getPrice().toString());
            }
            if (item.getCost() != null) {
                costField.setText(item.getCost().toString());
            }
            
            barcodeField.setText(item.getBarcode());
            skuField.setText(item.getSku());
            
            if (item.getMinStockLevel() != null) {
                minStockSpinner.setValue(item.getMinStockLevel());
            }
            if (item.getMaxStockLevel() != null) {
                maxStockSpinner.setValue(item.getMaxStockLevel());
            }
            if (item.getCurrentStock() != null) {
                currentStockSpinner.setValue(item.getCurrentStock());
            }
            
            trackableCheckBox.setSelected(item.isTrackable());
            perishableCheckBox.setSelected(item.isPerishable());
            
            if (item.getShelfLifeDays() != null) {
                shelfLifeSpinner.setValue(item.getShelfLifeDays());
            }
            
            supplierNameField.setText(item.getSupplierName());
            supplierCodeField.setText(item.getSupplierCode());
            notesArea.setText(item.getNotes());
        }
    }

    private void saveItem() {
        if (!validateInput()) {
            return;
        }

        if (item == null) {
            item = new Item();
        }

        // حفظ البيانات
        item.setCode(codeField.getText().trim());
        item.setName(nameField.getText().trim());
        item.setNameEn(nameEnField.getText().trim());
        item.setDescription(descriptionArea.getText().trim());
        item.setDescriptionEn(descriptionEnArea.getText().trim());
        item.setCategory((String) categoryCombo.getSelectedItem());
        item.setUnit((String) unitCombo.getSelectedItem());

        // الوزن
        try {
            if (!weightField.getText().trim().isEmpty()) {
                item.setWeight(new BigDecimal(weightField.getText().trim()));
            }
        } catch (NumberFormatException e) {
            // تم التحقق من صحة البيانات مسبقاً
        }

        item.setDimensions(dimensionsField.getText().trim());

        // الأسعار
        try {
            if (!priceField.getText().trim().isEmpty()) {
                item.setPrice(new BigDecimal(priceField.getText().trim()));
            }
            if (!costField.getText().trim().isEmpty()) {
                item.setCost(new BigDecimal(costField.getText().trim()));
            }
        } catch (NumberFormatException e) {
            // تم التحقق من صحة البيانات مسبقاً
        }

        item.setBarcode(barcodeField.getText().trim());
        item.setSku(skuField.getText().trim());
        item.setMinStockLevel((Integer) minStockSpinner.getValue());
        item.setMaxStockLevel((Integer) maxStockSpinner.getValue());
        item.setCurrentStock((Integer) currentStockSpinner.getValue());
        item.setIsTrackable(trackableCheckBox.isSelected());
        item.setIsPerishable(perishableCheckBox.isSelected());
        item.setShelfLifeDays((Integer) shelfLifeSpinner.getValue());
        item.setSupplierName(supplierNameField.getText().trim());
        item.setSupplierCode(supplierCodeField.getText().trim());
        item.setNotes(notesArea.getText().trim());

        confirmed = true;
        dispose();
    }

    private boolean validateInput() {
        // التحقق من الحقول المطلوبة
        if (codeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كود الصنف", "خطأ", JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }

        if (nameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم الصنف", "خطأ", JOptionPane.ERROR_MESSAGE);
            nameField.requestFocus();
            return false;
        }

        // التحقق من صحة الأرقام
        try {
            if (!weightField.getText().trim().isEmpty()) {
                new BigDecimal(weightField.getText().trim());
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال وزن صحيح", "خطأ", JOptionPane.ERROR_MESSAGE);
            weightField.requestFocus();
            return false;
        }

        try {
            if (!priceField.getText().trim().isEmpty()) {
                new BigDecimal(priceField.getText().trim());
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال سعر صحيح", "خطأ", JOptionPane.ERROR_MESSAGE);
            priceField.requestFocus();
            return false;
        }

        try {
            if (!costField.getText().trim().isEmpty()) {
                new BigDecimal(costField.getText().trim());
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال تكلفة صحيحة", "خطأ", JOptionPane.ERROR_MESSAGE);
            costField.requestFocus();
            return false;
        }

        return true;
    }

    public boolean isConfirmed() {
        return confirmed;
    }

    public Item getItem() {
        return item;
    }
}
