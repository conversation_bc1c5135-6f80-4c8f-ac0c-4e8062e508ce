@echo off
echo ========================================
echo    DOWNLOAD UI LIBRARIES
echo ========================================

cd /d "e:\ship_erp\java\lib"

echo [INFO] Downloading UI and Theme libraries...
echo.

echo [1] Downloading FlatLaf...
curl -L -o flatlaf-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar"
if exist "flatlaf-3.2.5.jar" (
    echo    OK: FlatLaf downloaded
) else (
    echo    ERROR: Failed to download FlatLaf
)

echo [2] Downloading FlatLaf Extras...
curl -L -o flatlaf-extras-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar"
if exist "flatlaf-extras-3.2.5.jar" (
    echo    OK: FlatLaf Extras downloaded
) else (
    echo    ERROR: Failed to download FlatLaf Extras
)

echo [3] Downloading MigLayout Core...
curl -L -o miglayout-core-11.0.jar "https://repo1.maven.org/maven2/com/miglayout/miglayout-core/11.0/miglayout-core-11.0.jar"
if exist "miglayout-core-11.0.jar" (
    echo    OK: MigLayout Core downloaded
) else (
    echo    WARNING: MigLayout Core not available
)

echo [4] Downloading MigLayout Swing...
curl -L -o miglayout-swing-11.0.jar "https://repo1.maven.org/maven2/com/miglayout/miglayout-swing/11.0/miglayout-swing-11.0.jar"
if exist "miglayout-swing-11.0.jar" (
    echo    OK: MigLayout Swing downloaded
) else (
    echo    WARNING: MigLayout Swing not available
)

echo.
echo ========================================
echo    DOWNLOAD COMPLETED
echo ========================================

echo.
echo Downloaded libraries:
dir /b *.jar | findstr -i "flat\|mig"

echo.
echo Libraries ready for UI Theme development!

pause
