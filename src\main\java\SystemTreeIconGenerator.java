import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import javax.imageio.ImageIO;

/**
 * مولد أيقونات شجرة الأنظمة
 * System Tree Icon Generator
 */
public class SystemTreeIconGenerator {
    
    private static final int ICON_SIZE = 32;
    
    public static void main(String[] args) {
        System.out.println("🎨 مولد أيقونات شجرة الأنظمة");
        System.out.println("System Tree Icon Generator");
        System.out.println("==========================================");
        
        SystemTreeIconGenerator generator = new SystemTreeIconGenerator();
        generator.generateAllIcons();
    }
    
    public void generateAllIcons() {
        try {
            // إنشاء مجلد الأيقونات
            createIconsDirectory();
            
            // إنشاء الأيقونات الأساسية
            generateFolderIcon();
            generateWindowIcon();
            generateToolIcon();
            generateReportIcon();
            generateDefaultIcon();
            
            // إنشاء أيقونات إضافية
            generateEmailIcon();
            generateDatabaseIcon();
            generateUserIcon();
            generateSettingsIcon();
            
            System.out.println("\n✅ تم إنشاء جميع الأيقونات بنجاح!");
            System.out.println("📁 الموقع: resources/icons/");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء الأيقونات: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void createIconsDirectory() {
        File iconsDir = new File("resources/icons");
        if (!iconsDir.exists()) {
            iconsDir.mkdirs();
            System.out.println("📁 تم إنشاء مجلد الأيقونات: " + iconsDir.getAbsolutePath());
        }
    }
    
    /**
     * إنشاء أيقونة المجلد
     */
    private void generateFolderIcon() throws IOException {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createGraphics(image);
        
        // رسم المجلد
        g2d.setColor(new Color(255, 215, 0)); // ذهبي
        g2d.fillRoundRect(4, 10, 24, 18, 4, 4);
        
        // رسم علامة التبويب
        g2d.fillRoundRect(4, 6, 12, 8, 4, 4);
        
        // رسم الحدود
        g2d.setColor(new Color(200, 170, 0));
        g2d.setStroke(new BasicStroke(1.5f));
        g2d.drawRoundRect(4, 10, 24, 18, 4, 4);
        g2d.drawRoundRect(4, 6, 12, 8, 4, 4);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("resources/icons/folder.png"));
        System.out.println("✅ تم إنشاء أيقونة المجلد: folder.png");
    }
    
    /**
     * إنشاء أيقونة النافذة
     */
    private void generateWindowIcon() throws IOException {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createGraphics(image);
        
        // رسم النافذة
        g2d.setColor(new Color(135, 206, 250)); // أزرق فاتح
        g2d.fillRoundRect(4, 4, 24, 24, 4, 4);
        
        // رسم شريط العنوان
        g2d.setColor(new Color(70, 130, 180));
        g2d.fillRoundRect(4, 4, 24, 6, 4, 4);
        g2d.fillRect(4, 7, 24, 3);
        
        // رسم أزرار النافذة
        g2d.setColor(new Color(255, 100, 100));
        g2d.fillOval(22, 5, 4, 4);
        
        // رسم محتوى النافذة
        g2d.setColor(Color.WHITE);
        g2d.fillRect(6, 12, 20, 14);
        
        // رسم خطوط المحتوى
        g2d.setColor(new Color(200, 200, 200));
        for (int i = 0; i < 3; i++) {
            g2d.drawLine(8, 15 + i * 3, 24, 15 + i * 3);
        }
        
        // رسم الحدود
        g2d.setColor(new Color(100, 100, 100));
        g2d.setStroke(new BasicStroke(1.5f));
        g2d.drawRoundRect(4, 4, 24, 24, 4, 4);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("resources/icons/window.png"));
        System.out.println("✅ تم إنشاء أيقونة النافذة: window.png");
    }
    
    /**
     * إنشاء أيقونة الأداة
     */
    private void generateToolIcon() throws IOException {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createGraphics(image);
        
        // رسم دائرة خلفية
        g2d.setColor(new Color(255, 255, 255, 150));
        g2d.fillOval(2, 2, 28, 28);
        
        // رسم مفك البراغي
        g2d.setColor(new Color(169, 169, 169)); // رمادي
        g2d.setStroke(new BasicStroke(3f));
        g2d.drawLine(8, 8, 24, 24);
        
        // رسم مقبض المفك
        g2d.setColor(new Color(139, 69, 19)); // بني
        g2d.setStroke(new BasicStroke(4f));
        g2d.drawLine(6, 6, 12, 12);
        
        // رسم المطرقة
        g2d.setColor(new Color(160, 82, 45)); // بني محمر
        g2d.setStroke(new BasicStroke(2f));
        g2d.drawLine(16, 8, 16, 20);
        
        // رسم رأس المطرقة
        g2d.setColor(new Color(105, 105, 105));
        g2d.fillRect(12, 6, 8, 4);
        
        // رسم الحدود
        g2d.setColor(new Color(100, 100, 100));
        g2d.setStroke(new BasicStroke(1.5f));
        g2d.drawOval(2, 2, 28, 28);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("resources/icons/tool.png"));
        System.out.println("✅ تم إنشاء أيقونة الأداة: tool.png");
    }
    
    /**
     * إنشاء أيقونة التقرير
     */
    private void generateReportIcon() throws IOException {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createGraphics(image);
        
        // رسم الورقة
        g2d.setColor(Color.WHITE);
        g2d.fillRoundRect(6, 4, 20, 26, 4, 4);
        
        // رسم الزاوية المطوية
        g2d.setColor(new Color(240, 240, 240));
        int[] xPoints = {22, 26, 22};
        int[] yPoints = {4, 8, 8};
        g2d.fillPolygon(xPoints, yPoints, 3);
        
        // رسم خطوط النص
        g2d.setColor(new Color(100, 100, 100));
        for (int i = 0; i < 6; i++) {
            int y = 10 + i * 3;
            int width = (i % 2 == 0) ? 16 : 12;
            g2d.drawLine(8, y, 8 + width, y);
        }
        
        // رسم رسم بياني صغير
        g2d.setColor(new Color(0, 150, 0));
        g2d.setStroke(new BasicStroke(2f));
        g2d.drawLine(8, 24, 12, 20);
        g2d.drawLine(12, 20, 16, 22);
        g2d.drawLine(16, 22, 20, 18);
        
        // رسم الحدود
        g2d.setColor(new Color(100, 100, 100));
        g2d.setStroke(new BasicStroke(1.5f));
        g2d.drawRoundRect(6, 4, 20, 26, 4, 4);
        g2d.drawLine(22, 4, 26, 8);
        g2d.drawLine(22, 4, 22, 8);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("resources/icons/report.png"));
        System.out.println("✅ تم إنشاء أيقونة التقرير: report.png");
    }
    
    /**
     * إنشاء الأيقونة الافتراضية
     */
    private void generateDefaultIcon() throws IOException {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createGraphics(image);
        
        // رسم دائرة خلفية
        g2d.setColor(new Color(200, 200, 200));
        g2d.fillOval(4, 4, 24, 24);
        
        // رسم علامة استفهام
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("Arial", Font.BOLD, 18));
        FontMetrics fm = g2d.getFontMetrics();
        String text = "?";
        int x = (ICON_SIZE - fm.stringWidth(text)) / 2;
        int y = (ICON_SIZE + fm.getAscent()) / 2 - 2;
        g2d.drawString(text, x, y);
        
        // رسم الحدود
        g2d.setColor(new Color(100, 100, 100));
        g2d.setStroke(new BasicStroke(1.5f));
        g2d.drawOval(4, 4, 24, 24);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("resources/icons/default.png"));
        System.out.println("✅ تم إنشاء الأيقونة الافتراضية: default.png");
    }
    
    /**
     * إنشاء أيقونة البريد الإلكتروني
     */
    private void generateEmailIcon() throws IOException {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createGraphics(image);
        
        // رسم المغلف
        g2d.setColor(new Color(255, 255, 255));
        g2d.fillRoundRect(4, 8, 24, 16, 4, 4);
        
        // رسم الغطاء
        g2d.setColor(new Color(70, 130, 180));
        int[] xPoints = {4, 16, 28};
        int[] yPoints = {8, 16, 8};
        g2d.fillPolygon(xPoints, yPoints, 3);
        
        // رسم الحدود
        g2d.setColor(new Color(100, 100, 100));
        g2d.setStroke(new BasicStroke(1.5f));
        g2d.drawRoundRect(4, 8, 24, 16, 4, 4);
        g2d.drawLine(4, 8, 16, 16);
        g2d.drawLine(28, 8, 16, 16);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("resources/icons/email.png"));
        System.out.println("✅ تم إنشاء أيقونة البريد: email.png");
    }

    /**
     * إنشاء أيقونة قاعدة البيانات
     */
    private void generateDatabaseIcon() throws IOException {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createGraphics(image);

        // رسم قاعدة البيانات
        g2d.setColor(new Color(70, 130, 180));

        // الجزء العلوي
        g2d.fillOval(6, 4, 20, 8);

        // الجسم
        g2d.fillRect(6, 8, 20, 16);

        // الجزء السفلي
        g2d.fillOval(6, 20, 20, 8);

        // خطوط الفصل
        g2d.setColor(new Color(50, 100, 150));
        g2d.setStroke(new BasicStroke(1f));
        g2d.drawOval(6, 10, 20, 6);
        g2d.drawOval(6, 16, 20, 6);

        // الحدود الخارجية
        g2d.setColor(new Color(30, 80, 120));
        g2d.setStroke(new BasicStroke(1.5f));
        g2d.drawOval(6, 4, 20, 8);
        g2d.drawOval(6, 20, 20, 8);
        g2d.drawLine(6, 8, 6, 24);
        g2d.drawLine(26, 8, 26, 24);

        g2d.dispose();
        ImageIO.write(image, "PNG", new File("resources/icons/database.png"));
        System.out.println("✅ تم إنشاء أيقونة قاعدة البيانات: database.png");
    }

    /**
     * إنشاء أيقونة المستخدم
     */
    private void generateUserIcon() throws IOException {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createGraphics(image);

        // رسم الرأس
        g2d.setColor(new Color(255, 220, 177)); // لون البشرة
        g2d.fillOval(10, 6, 12, 12);

        // رسم الجسم
        g2d.setColor(new Color(70, 130, 180)); // أزرق
        g2d.fillRoundRect(8, 18, 16, 12, 8, 8);

        // رسم الحدود
        g2d.setColor(new Color(100, 100, 100));
        g2d.setStroke(new BasicStroke(1.5f));
        g2d.drawOval(10, 6, 12, 12);
        g2d.drawRoundRect(8, 18, 16, 12, 8, 8);

        g2d.dispose();
        ImageIO.write(image, "PNG", new File("resources/icons/user.png"));
        System.out.println("✅ تم إنشاء أيقونة المستخدم: user.png");
    }

    /**
     * إنشاء أيقونة الإعدادات
     */
    private void generateSettingsIcon() throws IOException {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createGraphics(image);

        // رسم الترس
        g2d.setColor(new Color(128, 128, 128));

        // رسم الدائرة الخارجية مع الأسنان
        for (int i = 0; i < 8; i++) {
            double angle = i * Math.PI / 4;
            int x1 = (int) (16 + 12 * Math.cos(angle));
            int y1 = (int) (16 + 12 * Math.sin(angle));
            int x2 = (int) (16 + 14 * Math.cos(angle));
            int y2 = (int) (16 + 14 * Math.sin(angle));

            g2d.setStroke(new BasicStroke(3f));
            g2d.drawLine(x1, y1, x2, y2);
        }

        // رسم الدائرة الرئيسية
        g2d.fillOval(8, 8, 16, 16);

        // رسم الثقب الوسطي
        g2d.setColor(Color.WHITE);
        g2d.fillOval(12, 12, 8, 8);

        // رسم الحدود
        g2d.setColor(new Color(80, 80, 80));
        g2d.setStroke(new BasicStroke(1.5f));
        g2d.drawOval(8, 8, 16, 16);
        g2d.drawOval(12, 12, 8, 8);

        g2d.dispose();
        ImageIO.write(image, "PNG", new File("resources/icons/settings.png"));
        System.out.println("✅ تم إنشاء أيقونة الإعدادات: settings.png");
    }

    /**
     * إنشاء كائن Graphics2D مع إعدادات التحسين
     */
    private Graphics2D createGraphics(BufferedImage image) {
        Graphics2D g2d = image.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
        return g2d;
    }
}
