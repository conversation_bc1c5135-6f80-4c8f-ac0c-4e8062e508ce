@echo off
echo Testing Email Window with JavaMail...
cd /d "e:\ship_erp\java"

echo Compiling with JavaMail...
javac -encoding UTF-8 -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\*;." -d . src\main\java\CompleteEmailAccountsWindow.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Running Email Window...
java -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\*;." CompleteEmailAccountsWindow

pause
