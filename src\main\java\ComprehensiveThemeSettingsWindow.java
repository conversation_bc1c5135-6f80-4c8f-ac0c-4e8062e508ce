import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Map;
import java.util.List;

/**
 * نافذة إعدادات المظاهر الشاملة
 * Comprehensive Theme Settings Window
 */
public class ComprehensiveThemeSettingsWindow extends JFrame {
    
    private AdvancedThemeManager themeManager;
    private JComboBox<String> categoryComboBox;
    private JList<String> themeList;
    private DefaultListModel<String> themeListModel;
    private JTextArea themeInfoArea;
    private JButton applyButton;
    private JButton previewButton;
    private JButton resetButton;
    private JLabel currentThemeLabel;
    private JProgressBar progressBar;
    private JCheckBox darkModeFilterCheckBox;
    private JCheckBox lightModeFilterCheckBox;
    
    public ComprehensiveThemeSettingsWindow() {
        themeManager = AdvancedThemeManager.getInstance();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadThemeCategories();
        updateCurrentThemeDisplay();
        
        setTitle("إعدادات المظاهر الشاملة - Comprehensive Theme Settings");
        setSize(900, 700);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);
        
        // تطبيق الخط العربي
        applyArabicFont();
        
        // تطبيق المظهر الحالي على النافذة
        SwingUtilities.updateComponentTreeUI(this);
    }
    
    private void initializeComponents() {
        // Category selection
        categoryComboBox = new JComboBox<>();
        categoryComboBox.addItem("جميع الفئات - All Categories");
        
        // Theme list
        themeListModel = new DefaultListModel<>();
        themeList = new JList<>(themeListModel);
        themeList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        themeList.setCellRenderer(new ThemeListCellRenderer());
        
        // Theme info area
        themeInfoArea = new JTextArea(8, 30);
        themeInfoArea.setEditable(false);
        themeInfoArea.setWrapStyleWord(true);
        themeInfoArea.setLineWrap(true);
        themeInfoArea.setBorder(BorderFactory.createTitledBorder("معلومات المظهر - Theme Information"));
        
        // Buttons
        applyButton = new JButton("تطبيق المظهر - Apply Theme");
        previewButton = new JButton("معاينة - Preview");
        resetButton = new JButton("إعادة تعيين - Reset");
        
        // Current theme label
        currentThemeLabel = new JLabel("المظهر الحالي: " + themeManager.getCurrentTheme());
        currentThemeLabel.setFont(currentThemeLabel.getFont().deriveFont(Font.BOLD, 14f));
        
        // Progress bar
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setVisible(false);
        
        // Filter checkboxes
        darkModeFilterCheckBox = new JCheckBox("المظاهر المظلمة فقط - Dark Themes Only");
        lightModeFilterCheckBox = new JCheckBox("المظاهر الفاتحة فقط - Light Themes Only");
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Top panel - Current theme and filters
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBorder(BorderFactory.createTitledBorder("الحالة الحالية والمرشحات - Current Status & Filters"));
        
        JPanel currentThemePanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        currentThemePanel.add(currentThemeLabel);
        
        JPanel filtersPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        filtersPanel.add(darkModeFilterCheckBox);
        filtersPanel.add(lightModeFilterCheckBox);
        
        topPanel.add(currentThemePanel, BorderLayout.NORTH);
        topPanel.add(filtersPanel, BorderLayout.SOUTH);
        
        // Center panel - Theme selection
        JPanel centerPanel = new JPanel(new BorderLayout());
        
        // Category selection panel
        JPanel categoryPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        categoryPanel.setBorder(BorderFactory.createTitledBorder("فئة المظاهر - Theme Category"));
        categoryPanel.add(new JLabel("الفئة - Category:"));
        categoryPanel.add(categoryComboBox);
        
        // Theme list panel
        JPanel themeListPanel = new JPanel(new BorderLayout());
        themeListPanel.setBorder(BorderFactory.createTitledBorder("قائمة المظاهر المتاحة - Available Themes"));
        
        JScrollPane themeListScrollPane = new JScrollPane(themeList);
        themeListScrollPane.setPreferredSize(new Dimension(400, 300));
        themeListPanel.add(themeListScrollPane, BorderLayout.CENTER);
        
        // Theme info panel
        JScrollPane themeInfoScrollPane = new JScrollPane(themeInfoArea);
        themeInfoScrollPane.setPreferredSize(new Dimension(400, 200));
        
        // Split pane for theme list and info
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, themeListPanel, themeInfoScrollPane);
        splitPane.setDividerLocation(450);
        
        centerPanel.add(categoryPanel, BorderLayout.NORTH);
        centerPanel.add(splitPane, BorderLayout.CENTER);
        
        // Bottom panel - Buttons and progress
        JPanel bottomPanel = new JPanel(new BorderLayout());
        
        JPanel buttonsPanel = new JPanel(new FlowLayout());
        buttonsPanel.add(previewButton);
        buttonsPanel.add(applyButton);
        buttonsPanel.add(resetButton);
        
        bottomPanel.add(buttonsPanel, BorderLayout.CENTER);
        bottomPanel.add(progressBar, BorderLayout.SOUTH);
        
        add(topPanel, BorderLayout.NORTH);
        add(centerPanel, BorderLayout.CENTER);
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    private void setupEventHandlers() {
        // Category selection handler
        categoryComboBox.addActionListener(e -> loadThemesForCategory());
        
        // Theme list selection handler
        themeList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                updateThemeInfo();
                updateButtonStates();
            }
        });
        
        // Filter checkboxes handlers
        darkModeFilterCheckBox.addActionListener(e -> {
            if (darkModeFilterCheckBox.isSelected()) {
                lightModeFilterCheckBox.setSelected(false);
            }
            loadThemesForCategory();
        });
        
        lightModeFilterCheckBox.addActionListener(e -> {
            if (lightModeFilterCheckBox.isSelected()) {
                darkModeFilterCheckBox.setSelected(false);
            }
            loadThemesForCategory();
        });
        
        // Apply button handler
        applyButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                applySelectedTheme();
            }
        });
        
        // Preview button handler
        previewButton.addActionListener(e -> previewSelectedTheme());
        
        // Reset button handler
        resetButton.addActionListener(e -> resetToDefaultTheme());
    }
    
    private void loadThemeCategories() {
        categoryComboBox.removeAllItems();
        categoryComboBox.addItem("جميع الفئات - All Categories");
        
        Map<String, List<AdvancedThemeManager.ThemeInfo>> themesByCategory = themeManager.getThemesByCategory();
        for (String category : themesByCategory.keySet()) {
            categoryComboBox.addItem(category);
        }
    }
    
    private void loadThemesForCategory() {
        themeListModel.clear();
        
        String selectedCategory = (String) categoryComboBox.getSelectedItem();
        Map<String, List<AdvancedThemeManager.ThemeInfo>> themesByCategory = themeManager.getThemesByCategory();
        
        if ("جميع الفئات - All Categories".equals(selectedCategory)) {
            // Load all themes
            for (List<AdvancedThemeManager.ThemeInfo> themes : themesByCategory.values()) {
                for (AdvancedThemeManager.ThemeInfo theme : themes) {
                    if (shouldIncludeTheme(theme)) {
                        themeListModel.addElement(theme.name);
                    }
                }
            }
        } else {
            // Load themes for specific category
            List<AdvancedThemeManager.ThemeInfo> themes = themesByCategory.get(selectedCategory);
            if (themes != null) {
                for (AdvancedThemeManager.ThemeInfo theme : themes) {
                    if (shouldIncludeTheme(theme)) {
                        themeListModel.addElement(theme.name);
                    }
                }
            }
        }
        
        // Update theme count
        updateThemeCount();
    }
    
    private boolean shouldIncludeTheme(AdvancedThemeManager.ThemeInfo theme) {
        if (darkModeFilterCheckBox.isSelected() && !theme.isDark) {
            return false;
        }
        if (lightModeFilterCheckBox.isSelected() && theme.isDark) {
            return false;
        }
        return true;
    }
    
    private void updateThemeInfo() {
        String selectedTheme = themeList.getSelectedValue();
        if (selectedTheme != null) {
            AdvancedThemeManager.ThemeInfo themeInfo = themeManager.getThemeInfo(selectedTheme);
            if (themeInfo != null) {
                StringBuilder info = new StringBuilder();
                info.append("الاسم - Name: ").append(themeInfo.displayName).append("\n");
                info.append("الفئة - Category: ").append(themeInfo.category).append("\n");
                info.append("المطور - Author: ").append(themeInfo.author).append("\n");
                info.append("الإصدار - Version: ").append(themeInfo.version).append("\n");
                info.append("النوع - Type: ").append(themeInfo.isDark ? "مظلم - Dark" : "فاتح - Light").append("\n");
                info.append("الحالة - Status: ").append(themeInfo.isAvailable ? "متاح - Available" : "غير متاح - Not Available").append("\n");
                info.append("الوصف - Description:\n").append(themeInfo.description).append("\n");
                
                if (!themeInfo.isAvailable) {
                    info.append("\n⚠️ تحذير - Warning:\n");
                    info.append("هذا المظهر غير متاح حالياً. قد تكون المكتبة المطلوبة مفقودة.\n");
                    info.append("This theme is currently unavailable. The required library may be missing.\n");
                    info.append("المكتبة المطلوبة - Required Class: ").append(themeInfo.className);
                }
                
                themeInfoArea.setText(info.toString());
            }
        } else {
            themeInfoArea.setText("اختر مظهراً لعرض معلوماته\nSelect a theme to view its information");
        }
    }
    
    private void updateButtonStates() {
        String selectedTheme = themeList.getSelectedValue();
        boolean hasSelection = selectedTheme != null;
        boolean isAvailable = false;
        
        if (hasSelection) {
            AdvancedThemeManager.ThemeInfo themeInfo = themeManager.getThemeInfo(selectedTheme);
            isAvailable = themeInfo != null && themeInfo.isAvailable;
        }
        
        applyButton.setEnabled(hasSelection && isAvailable);
        previewButton.setEnabled(hasSelection && isAvailable);
    }
    
    private void updateCurrentThemeDisplay() {
        currentThemeLabel.setText("المظهر الحالي - Current Theme: " + themeManager.getCurrentTheme());
    }
    
    private void updateThemeCount() {
        int totalCount = themeListModel.getSize();
        String title = String.format("قائمة المظاهر المتاحة - Available Themes (%d)", totalCount);
        
        // Find the theme list panel and update its border
        Container parent = themeList.getParent().getParent();
        if (parent instanceof JPanel) {
            ((JPanel) parent).setBorder(BorderFactory.createTitledBorder(title));
        }
    }
    
    private void applySelectedTheme() {
        String selectedTheme = themeList.getSelectedValue();
        if (selectedTheme != null) {
            showProgress("تطبيق المظهر - Applying Theme...");
            
            SwingWorker<Boolean, Void> worker = new SwingWorker<Boolean, Void>() {
                @Override
                protected Boolean doInBackground() throws Exception {
                    return themeManager.applyTheme(selectedTheme, ComprehensiveThemeSettingsWindow.this);
                }
                
                @Override
                protected void done() {
                    try {
                        boolean success = get();
                        hideProgress();
                        
                        if (success) {
                            updateCurrentThemeDisplay();
                            SwingUtilities.updateComponentTreeUI(ComprehensiveThemeSettingsWindow.this);
                            
                            JOptionPane.showMessageDialog(ComprehensiveThemeSettingsWindow.this,
                                "تم تطبيق المظهر بنجاح!\nTheme applied successfully!",
                                "نجح التطبيق - Success", JOptionPane.INFORMATION_MESSAGE);
                        } else {
                            JOptionPane.showMessageDialog(ComprehensiveThemeSettingsWindow.this,
                                "فشل في تطبيق المظهر!\nFailed to apply theme!",
                                "خطأ - Error", JOptionPane.ERROR_MESSAGE);
                        }
                    } catch (Exception e) {
                        hideProgress();
                        JOptionPane.showMessageDialog(ComprehensiveThemeSettingsWindow.this,
                            "خطأ في تطبيق المظهر:\n" + e.getMessage(),
                            "خطأ - Error", JOptionPane.ERROR_MESSAGE);
                    }
                }
            };
            
            worker.execute();
        }
    }
    
    private void previewSelectedTheme() {
        String selectedTheme = themeList.getSelectedValue();
        if (selectedTheme != null) {
            // Create a preview dialog
            JDialog previewDialog = new JDialog(this, "معاينة المظهر - Theme Preview", true);
            previewDialog.setSize(400, 300);
            previewDialog.setLocationRelativeTo(this);
            
            // Apply theme temporarily to preview dialog
            String currentTheme = themeManager.getCurrentTheme();
            themeManager.applyTheme(selectedTheme, previewDialog);
            
            // Add some sample components
            JPanel previewPanel = new JPanel(new GridLayout(4, 2, 10, 10));
            previewPanel.setBorder(BorderFactory.createTitledBorder("معاينة المكونات - Component Preview"));
            
            previewPanel.add(new JLabel("تسمية - Label:"));
            previewPanel.add(new JTextField("نص تجريبي - Sample Text"));
            
            previewPanel.add(new JLabel("زر - Button:"));
            previewPanel.add(new JButton("زر تجريبي - Sample Button"));
            
            previewPanel.add(new JLabel("قائمة - ComboBox:"));
            previewPanel.add(new JComboBox<>(new String[]{"خيار 1 - Option 1", "خيار 2 - Option 2"}));
            
            previewPanel.add(new JLabel("مربع اختيار - CheckBox:"));
            previewPanel.add(new JCheckBox("خيار - Option"));
            
            JButton closeButton = new JButton("إغلاق - Close");
            closeButton.addActionListener(e -> {
                // Restore original theme
                themeManager.applyTheme(currentTheme, this);
                previewDialog.dispose();
            });
            
            previewDialog.add(previewPanel, BorderLayout.CENTER);
            previewDialog.add(closeButton, BorderLayout.SOUTH);
            
            SwingUtilities.updateComponentTreeUI(previewDialog);
            previewDialog.setVisible(true);
        }
    }
    
    private void resetToDefaultTheme() {
        int result = JOptionPane.showConfirmDialog(this,
            "هل تريد إعادة تعيين المظهر إلى الافتراضي؟\nDo you want to reset to the default theme?",
            "إعادة تعيين - Reset", JOptionPane.YES_NO_OPTION);
        
        if (result == JOptionPane.YES_OPTION) {
            showProgress("إعادة تعيين المظهر - Resetting Theme...");
            
            SwingWorker<Boolean, Void> worker = new SwingWorker<Boolean, Void>() {
                @Override
                protected Boolean doInBackground() throws Exception {
                    return themeManager.applyTheme("FlatLaf Light", ComprehensiveThemeSettingsWindow.this);
                }
                
                @Override
                protected void done() {
                    try {
                        get();
                        hideProgress();
                        updateCurrentThemeDisplay();
                        SwingUtilities.updateComponentTreeUI(ComprehensiveThemeSettingsWindow.this);
                    } catch (Exception e) {
                        hideProgress();
                        e.printStackTrace();
                    }
                }
            };
            
            worker.execute();
        }
    }
    
    private void showProgress(String message) {
        progressBar.setString(message);
        progressBar.setIndeterminate(true);
        progressBar.setVisible(true);
        applyButton.setEnabled(false);
        previewButton.setEnabled(false);
        resetButton.setEnabled(false);
    }
    
    private void hideProgress() {
        progressBar.setVisible(false);
        progressBar.setIndeterminate(false);
        updateButtonStates();
        resetButton.setEnabled(true);
    }
    
    private void applyArabicFont() {
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        setFontRecursively(this, arabicFont);
        
        // Set component orientation for Arabic text
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    private void setFontRecursively(Container container, Font font) {
        for (Component component : container.getComponents()) {
            component.setFont(font);
            if (component instanceof Container) {
                setFontRecursively((Container) component, font);
            }
        }
    }
    
    // Custom cell renderer for theme list
    private class ThemeListCellRenderer extends DefaultListCellRenderer {
        @Override
        public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                boolean isSelected, boolean cellHasFocus) {
            
            super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
            
            if (value instanceof String) {
                String themeName = (String) value;
                AdvancedThemeManager.ThemeInfo themeInfo = themeManager.getThemeInfo(themeName);
                
                if (themeInfo != null) {
                    setText(themeInfo.displayName + " (" + themeInfo.category + ")");
                    
                    if (!themeInfo.isAvailable) {
                        setForeground(Color.GRAY);
                        setText(getText() + " [غير متاح - Not Available]");
                    } else if (themeInfo.isDark) {
                        if (!isSelected) {
                            setForeground(new Color(100, 100, 255)); // Blue for dark themes
                        }
                    }
                    
                    // Highlight current theme
                    if (themeName.equals(themeManager.getCurrentTheme())) {
                        setFont(getFont().deriveFont(Font.BOLD));
                        if (!isSelected) {
                            setBackground(new Color(255, 255, 200)); // Light yellow background
                        }
                    }
                }
            }
            
            return this;
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // Apply a default theme
                com.formdev.flatlaf.FlatLightLaf.setup();
            } catch (Exception e) {
                e.printStackTrace();
            }
            
            new ComprehensiveThemeSettingsWindow().setVisible(true);
        });
    }
}
