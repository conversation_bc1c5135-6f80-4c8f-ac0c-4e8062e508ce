package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.Icon;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSeparator;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumnModel;
import com.shipment.erp.model.Company;

/**
 * نافذة إدارة الشركات Company Management Window
 */
public class CompanyManagementWindow extends JDialog {

    private Font arabicFont;
    private JTable companiesTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> statusFilterCombo;

    // private CompanyService companyService; // TODO: Implement CompanyService
    private List<Company> companiesList;

    public CompanyManagementWindow(JFrame parent) {
        super(parent, "إدارة الشركات", true);

        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeServices();
        initializeComponents();
        setupLayout();
        loadCompaniesData();

        setSize(1200, 800);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(true);
        setMinimumSize(new Dimension(1000, 600));
    }

    private void initializeServices() {
        // TODO: Initialize CompanyService through dependency injection
        // companyService = ApplicationContext.getBean(CompanyService.class);
    }

    private void initializeComponents() {
        // شريط الأدوات العلوي
        JPanel toolbarPanel = createToolbarPanel();

        // جدول الشركات
        createCompaniesTable();
        JScrollPane tableScrollPane = new JScrollPane(companiesTable);
        tableScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();

        // تخطيط النافذة
        setLayout(new BorderLayout());
        add(toolbarPanel, BorderLayout.NORTH);
        add(tableScrollPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }

    private JPanel createToolbarPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // أزرار العمليات
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addButton = new JButton("إضافة شركة جديدة");
        addButton.setFont(arabicFont);
        addButton.setIcon(createIcon("add"));
        addButton.addActionListener(e -> addCompany());

        JButton editButton = new JButton("تعديل");
        editButton.setFont(arabicFont);
        editButton.setIcon(createIcon("edit"));
        editButton.addActionListener(e -> editCompany());

        JButton deleteButton = new JButton("حذف");
        deleteButton.setFont(arabicFont);
        deleteButton.setIcon(createIcon("delete"));
        deleteButton.addActionListener(e -> deleteCompany());

        JButton refreshButton = new JButton("تحديث");
        refreshButton.setFont(arabicFont);
        refreshButton.setIcon(createIcon("refresh"));
        refreshButton.addActionListener(e -> loadCompaniesData());

        buttonsPanel.add(addButton);
        buttonsPanel.add(editButton);
        buttonsPanel.add(deleteButton);
        buttonsPanel.add(new JSeparator(SwingConstants.VERTICAL));
        buttonsPanel.add(refreshButton);

        // شريط البحث والفلترة
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);

        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                filterCompanies();
            }
        });

        JLabel statusLabel = new JLabel("الحالة:");
        statusLabel.setFont(arabicFont);

        statusFilterCombo = new JComboBox<>(new String[] {"الكل", "نشط", "غير نشط"});
        statusFilterCombo.setFont(arabicFont);
        statusFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusFilterCombo.addActionListener(e -> filterCompanies());

        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        searchPanel.add(Box.createHorizontalStrut(10));
        searchPanel.add(statusLabel);
        searchPanel.add(statusFilterCombo);

        panel.add(buttonsPanel, BorderLayout.EAST);
        panel.add(searchPanel, BorderLayout.WEST);

        return panel;
    }

    private void createCompaniesTable() {
        String[] columnNames = {"الرقم", "اسم الشركة", "اسم الشركة (إنجليزي)", "المدينة", "البلد",
                "الهاتف", "البريد الإلكتروني", "الحالة", "تاريخ الإنشاء"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        companiesTable = new JTable(tableModel);
        companiesTable.setFont(arabicFont);
        companiesTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        companiesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        companiesTable.setRowHeight(25);

        // تخصيص عرض الأعمدة
        TableColumnModel columnModel = companiesTable.getColumnModel();
        columnModel.getColumn(0).setPreferredWidth(60); // الرقم
        columnModel.getColumn(1).setPreferredWidth(150); // اسم الشركة
        columnModel.getColumn(2).setPreferredWidth(150); // اسم الشركة (إنجليزي)
        columnModel.getColumn(3).setPreferredWidth(100); // المدينة
        columnModel.getColumn(4).setPreferredWidth(100); // البلد
        columnModel.getColumn(5).setPreferredWidth(120); // الهاتف
        columnModel.getColumn(6).setPreferredWidth(150); // البريد الإلكتروني
        columnModel.getColumn(7).setPreferredWidth(80); // الحالة
        columnModel.getColumn(8).setPreferredWidth(120); // تاريخ الإنشاء

        // تخصيص رأس الجدول
        JTableHeader header = companiesTable.getTableHeader();
        header.setFont(arabicFont);
        header.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إضافة مستمع النقر المزدوج
        companiesTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    editCompany();
                }
            }
        });
    }

    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);

        panel.add(statusLabel);
        return panel;
    }

    private void setupLayout() {
        // تم تنفيذه في initializeComponents
    }

    private void loadCompaniesData() {
        // TODO: Load data from CompanyService
        // companiesList = companyService.findAll();

        // بيانات تجريبية
        companiesList = createSampleData();
        updateTableData();
    }

    private List<Company> createSampleData() {
        // TODO: Replace with actual data from service
        return java.util.Arrays.asList(
                createSampleCompany(1L, "شركة الشحن المتحدة", "United Shipping Company", "الرياض",
                        "السعودية"),
                createSampleCompany(2L, "شركة النقل السريع", "Fast Transport Company", "جدة",
                        "السعودية"),
                createSampleCompany(3L, "شركة الخليج للشحن", "Gulf Shipping Company", "الدمام",
                        "السعودية"));
    }

    private Company createSampleCompany(Long id, String name, String nameEn, String city,
            String country) {
        Company company = new Company();
        company.setId(id);
        company.setName(name);
        company.setNameEn(nameEn);
        company.setCity(city);
        company.setCountry(country);
        company.setPhone("966-11-1234567");
        company.setEmail("<EMAIL>");
        company.setIsActive(true);
        company.setCreatedDate(java.time.LocalDateTime.now());
        return company;
    }

    private void updateTableData() {
        tableModel.setRowCount(0);
        java.time.format.DateTimeFormatter dateFormatter =
                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        for (Company company : companiesList) {
            Object[] row = {company.getId(), company.getName(), company.getNameEn(),
                    company.getCity(), company.getCountry(), company.getPhone(), company.getEmail(),
                    company.getIsActive() ? "نشط" : "غير نشط",
                    company.getCreatedDate() != null
                            ? company.getCreatedDate().format(dateFormatter)
                            : ""};
            tableModel.addRow(row);
        }
    }

    private void filterCompanies() {
        // TODO: Implement filtering logic
        String searchText = searchField.getText().toLowerCase();
        String statusFilter = (String) statusFilterCombo.getSelectedItem();

        // تطبيق الفلترة وتحديث الجدول
        updateTableData();
    }

    private void addCompany() {
        com.shipment.erp.view.CompanyFormDialog dialog =
                new com.shipment.erp.view.CompanyFormDialog(this, "إضافة شركة جديدة", null);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            Company newCompany = dialog.getCompany();
            // TODO: Save using CompanyService
            // companyService.save(newCompany);
            loadCompaniesData();
        }
    }

    private void editCompany() {
        int selectedRow = companiesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار شركة للتعديل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Company selectedCompany = companiesList.get(selectedRow);
        com.shipment.erp.view.CompanyFormDialog dialog =
                new com.shipment.erp.view.CompanyFormDialog(this, "تعديل الشركة", selectedCompany);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            Company updatedCompany = dialog.getCompany();
            // TODO: Update using CompanyService
            // companyService.save(updatedCompany);
            loadCompaniesData();
        }
    }

    private void deleteCompany() {
        int selectedRow = companiesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار شركة للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Company selectedCompany = companiesList.get(selectedRow);
        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف الشركة: " + selectedCompany.getName() + "؟", "تأكيد الحذف",
                JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Delete using CompanyService
            // companyService.delete(selectedCompany.getId());
            loadCompaniesData();
        }
    }

    private Icon createIcon(String name) {
        // TODO: Load actual icons
        return null;
    }

    /**
     * طريقة اختبار لنافذة إدارة الشركات Test method for Company Management Window
     */
    public static void main(String[] args) {
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("sun.jnu.encoding", "UTF-8");

        javax.swing.SwingUtilities.invokeLater(() -> {
            try {
                JFrame testFrame = new JFrame("Test Frame");
                testFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
                testFrame.setSize(300, 200);
                testFrame.setLocationRelativeTo(null);

                CompanyManagementWindow window = new CompanyManagementWindow(testFrame);
                window.setVisible(true);

                System.out.println("✅ نافذة إدارة الشركات تعمل بنجاح!");

            } catch (Exception e) {
                System.err.println("❌ خطأ في اختبار نافذة إدارة الشركات: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
}
