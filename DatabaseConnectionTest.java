import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;

/**
 * اختبار شامل لاتصالات قاعدة البيانات
 * Comprehensive Database Connection Test
 */
public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  DATABASE CONNECTION TEST");
        System.out.println("  اختبار اتصالات قاعدة البيانات");
        System.out.println("========================================");
        
        // تعيين خصائص Oracle
        System.setProperty("oracle.jdbc.defaultNChar", "true");
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("oracle.net.tns_admin", "d:\\java\\java\\network\\admin");
        
        // اختبار الاتصالات
        testConnection("SHIP_ERP", "ship_erp_password", "localhost", "1521", "ORCL");
        testConnection("ias20251", "ys123", "localhost", "1521", "ORCL");
        
        System.out.println("\n========================================");
        System.out.println("  CONNECTION TEST COMPLETED");
        System.out.println("  تم الانتهاء من اختبار الاتصالات");
        System.out.println("========================================");
    }
    
    /**
     * اختبار الاتصال بقاعدة بيانات محددة
     */
    private static void testConnection(String username, String password, String host, String port, String sid) {
        System.out.println("\n🔄 Testing connection to: " + username);
        System.out.println("🔄 اختبار الاتصال بـ: " + username);
        
        try {
            // تحميل Oracle JDBC Driver
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            // إعداد خصائص الاتصال
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            // بناء URL الاتصال
            String url = "jdbc:oracle:thin:@" + host + ":" + port + ":" + sid;
            System.out.println("📡 Connection URL: " + url);
            
            // محاولة الاتصال
            try (Connection connection = DriverManager.getConnection(url, props)) {
                System.out.println("✅ Connection successful!");
                System.out.println("✅ تم الاتصال بنجاح!");
                
                // اختبار استعلام بسيط
                testBasicQuery(connection, username);
                
                // فحص الجداول المهمة
                checkImportantTables(connection, username);
                
            } catch (SQLException e) {
                System.out.println("❌ Connection failed: " + e.getMessage());
                System.out.println("❌ فشل الاتصال: " + e.getMessage());
                
                // تشخيص الخطأ
                diagnoseError(e);
            }
            
        } catch (ClassNotFoundException e) {
            System.out.println("❌ Oracle JDBC Driver not found!");
            System.out.println("❌ لم يتم العثور على Oracle JDBC Driver!");
        }
    }
    
    /**
     * اختبار استعلام بسيط
     */
    private static void testBasicQuery(Connection connection, String schema) {
        try {
            String query = "SELECT SYSDATE FROM DUAL";
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    System.out.println("📅 Current Date: " + rs.getTimestamp(1));
                    System.out.println("📅 التاريخ الحالي: " + rs.getTimestamp(1));
                }
            }
        } catch (SQLException e) {
            System.out.println("⚠️ Basic query failed: " + e.getMessage());
        }
    }
    
    /**
     * فحص الجداول المهمة
     */
    private static void checkImportantTables(Connection connection, String schema) {
        System.out.println("\n📋 Checking important tables in " + schema + ":");
        System.out.println("📋 فحص الجداول المهمة في " + schema + ":");
        
        String[] tables;
        if ("SHIP_ERP".equalsIgnoreCase(schema)) {
            tables = new String[]{
                "IAS_ITM_MST", "IAS_ITM_DTL", "ERP_MEASUREMENT",
                "ERP_SUB_GRP_DTL", "ERP_ASSISTANT_GROUP", "ERP_DETAIL_GROUP",
                "ERP_GROUP_DETAILS", "ERP_MAINSUB_GRP_DTL"
            };
        } else {
            tables = new String[]{
                "IAS_ITM_MST", "IAS_ITM_DTL", "MEASUREMENT",
                "IAS_SUB_GRP_DTL", "IAS_ASSISTANT_GROUP", "IAS_DETAIL_GROUP"
            };
        }
        
        for (String tableName : tables) {
            checkTable(connection, tableName);
        }
    }
    
    /**
     * فحص جدول محدد
     */
    private static void checkTable(Connection connection, String tableName) {
        try {
            // فحص وجود الجدول
            String checkQuery = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(checkQuery)) {
                stmt.setString(1, tableName);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        // عد الصفوف
                        String countQuery = "SELECT COUNT(*) FROM " + tableName;
                        try (PreparedStatement countStmt = connection.prepareStatement(countQuery);
                             ResultSet countRs = countStmt.executeQuery()) {
                            
                            if (countRs.next()) {
                                int rowCount = countRs.getInt(1);
                                System.out.println("  ✅ " + tableName + " - " + rowCount + " rows");
                                System.out.println("  ✅ " + tableName + " - " + rowCount + " صف");
                            }
                        }
                    } else {
                        System.out.println("  ❌ " + tableName + " - Table not found");
                        System.out.println("  ❌ " + tableName + " - الجدول غير موجود");
                    }
                }
            }
        } catch (SQLException e) {
            System.out.println("  ⚠️ " + tableName + " - Error: " + e.getMessage());
        }
    }
    
    /**
     * تشخيص أخطاء الاتصال
     */
    private static void diagnoseError(SQLException e) {
        String errorCode = String.valueOf(e.getErrorCode());
        String message = e.getMessage();
        
        System.out.println("\n🔍 Error Diagnosis:");
        System.out.println("🔍 تشخيص الخطأ:");
        System.out.println("Error Code: " + errorCode);
        
        if (message.contains("ORA-01017")) {
            System.out.println("💡 Invalid username/password");
            System.out.println("💡 اسم المستخدم أو كلمة المرور غير صحيحة");
        } else if (message.contains("ORA-12505")) {
            System.out.println("💡 TNS:listener does not currently know of SID");
            System.out.println("💡 المستمع لا يعرف SID المحدد");
        } else if (message.contains("ORA-12541")) {
            System.out.println("💡 TNS:no listener");
            System.out.println("💡 لا يوجد مستمع");
        } else if (message.contains("Connection refused")) {
            System.out.println("💡 Database server is not running");
            System.out.println("💡 خادم قاعدة البيانات غير يعمل");
        }
    }
}
