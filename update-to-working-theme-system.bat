@echo off
echo ========================================
echo   Update to Working Theme System
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Updating database to use WorkingThemeWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\UpdateSystemTreeToWorkingTheme.java
if %errorlevel% neq 0 (
    echo Failed to compile database updater
    pause
    exit /b 1
)

java -cp "lib\*;." UpdateSystemTreeToWorkingTheme
if %errorlevel% neq 0 (
    echo Failed to update database - continuing anyway
)

echo.
echo [2] Compiling WorkingThemeManager...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\WorkingThemeManager.java
if %errorlevel% neq 0 (
    echo Failed to compile WorkingThemeManager
    pause
    exit /b 1
)

echo.
echo [3] Compiling WorkingThemeWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\WorkingThemeWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile WorkingThemeWindow
    pause
    exit /b 1
)

echo.
echo [4] Updating TreeMenuPanel...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo Failed to compile TreeMenuPanel
    pause
    exit /b 1
)

echo.
echo [5] Updating UIThemeSettingsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\UIThemeSettingsWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile UIThemeSettingsWindow
    pause
    exit /b 1
)

echo.
echo [6] Testing WorkingThemeWindow...
start /min java -cp "lib\*;." WorkingThemeWindow
timeout /t 3 /nobreak > nul

echo.
echo ========================================
echo   Update Complete!
echo ========================================
echo.
echo SUMMARY:
echo - Database updated to use WorkingThemeWindow
echo - All components compiled successfully
echo - WorkingThemeWindow is now the default theme system
echo.
echo USAGE:
echo 1. From main system: Settings ^> Interface and Theme Settings
echo 2. Direct: .\start-working-theme-system.bat
echo 3. Manual: java -cp "lib\*;." WorkingThemeWindow
echo.
echo The system now uses WorkingThemeWindow which:
echo - Works with any available libraries
echo - Includes custom themes
echo - Has Arabic/English interface
echo - Saves settings to database
echo.
pause
