@echo off
echo ========================================
echo    TNS CONNECTIONS TESTER
echo    اختبار اتصالات TNS
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Testing all TNS connections...
echo [معلومات] اختبار جميع اتصالات TNS...
echo.

REM إعداد البيئة
call set-tns-env.bat

echo ========================================
echo    PHASE 1: VERIFY TNS ENVIRONMENT
echo    المرحلة 1: التحقق من بيئة TNS
echo ========================================

echo [1] Checking TNS environment...

echo TNS_ADMIN: %TNS_ADMIN%
echo ORACLE_NET_TNS_ADMIN: %ORACLE_NET_TNS_ADMIN%

if exist "%TNS_ADMIN%\tnsnames.ora" (
    echo ✅ tnsnames.ora found
) else (
    echo ❌ tnsnames.ora not found
    goto :error
)

if exist "%TNS_ADMIN%\sqlnet.ora" (
    echo ✅ sqlnet.ora found
) else (
    echo ❌ sqlnet.ora not found
    goto :error
)

echo.
echo ========================================
echo    PHASE 2: COMPILE TNS MANAGER
echo    المرحلة 2: تجميع مدير TNS
echo ========================================

echo [2] Compiling TNSConnectionManager...

javac -encoding UTF-8 -cp . -d . src\main\java\TNSConnectionManager.java
if errorlevel 1 (
    echo ❌ Failed to compile TNSConnectionManager
    goto :error
) else (
    echo ✅ TNSConnectionManager compiled successfully
)

echo.
echo ========================================
echo    PHASE 3: TEST TNS CONNECTIONS
echo    المرحلة 3: اختبار اتصالات TNS
echo ========================================

echo [3] Testing TNS connections through Java...

java %JAVA_TNS_OPTS% -cp . TNSConnectionManager
if errorlevel 1 (
    echo ❌ TNS connection test failed
) else (
    echo ✅ TNS connection test completed
)

echo.
echo ========================================
echo    PHASE 4: INDIVIDUAL CONNECTION TESTS
echo    المرحلة 4: اختبار الاتصالات الفردية
echo ========================================

echo [4] Testing individual TNS names...

REM اختبار tnsping إذا كان متاحاً
where tnsping >nul 2>&1
if %errorlevel% equ 0 (
    echo Testing with tnsping...
    
    echo Testing ORCL...
    tnsping ORCL
    
    echo Testing SHIP_ERP...
    tnsping SHIP_ERP
    
    echo Testing IAS20251...
    tnsping IAS20251
    
    echo Testing SHIP_ERP_HA...
    tnsping SHIP_ERP_HA
    
) else (
    echo ⚠️ tnsping not available (Oracle client not in PATH)
    echo Using Java connection tests only
)

echo.
echo ========================================
echo    PHASE 5: CONNECTION STRING VALIDATION
echo    المرحلة 5: التحقق من سلاسل الاتصال
echo ========================================

echo [5] Validating connection strings...

echo Available TNS connection strings:
echo سلاسل الاتصال المتاحة:
echo.
echo Standard connections:
echo - jdbc:oracle:thin:@ORCL
echo - **************************
echo - **************************
echo.
echo Development connections:
echo - **************************_DEV
echo - **************************_TEST
echo.
echo Advanced connections:
echo - **************************_HA (High Availability)
echo - **************************_LB (Load Balancing)
echo - **************************_SSL (SSL Secure)
echo.

echo ========================================
echo    PHASE 6: PERFORMANCE TEST
echo    المرحلة 6: اختبار الأداء
echo ========================================

echo [6] Testing connection performance...

echo Measuring connection times...
echo قياس أوقات الاتصال...

REM يمكن إضافة اختبارات أداء مفصلة هنا

echo.
echo ========================================
echo    PHASE 7: CONFIGURATION SUMMARY
echo    المرحلة 7: ملخص التكوين
echo ========================================

echo [7] Configuration summary...

echo.
echo 📊 TNS Configuration Status:
echo حالة تكوين TNS:
echo.
echo 📁 Files:
echo الملفات:
if exist "%TNS_ADMIN%\tnsnames.ora" (
    echo   ✅ tnsnames.ora
) else (
    echo   ❌ tnsnames.ora
)

if exist "%TNS_ADMIN%\sqlnet.ora" (
    echo   ✅ sqlnet.ora
) else (
    echo   ❌ sqlnet.ora
)

if exist "%TNS_ADMIN%\listener.ora" (
    echo   ✅ listener.ora
) else (
    echo   ⚠️ listener.ora (optional)
)

echo.
echo 🔗 TNS Names:
echo أسماء TNS:
echo   - ORCL (Main database)
echo   - SHIP_ERP (Ship ERP database)
echo   - IAS20251 (Reference database)
echo   - SHIP_ERP_DEV (Development)
echo   - SHIP_ERP_TEST (Testing)
echo   - SHIP_ERP_HA (High Availability)
echo   - SHIP_ERP_LB (Load Balancing)
echo   - SHIP_ERP_SSL (SSL Secure)
echo.
echo ⚙️ Environment:
echo البيئة:
echo   - TNS_ADMIN: %TNS_ADMIN%
echo   - Java TNS Options: %JAVA_TNS_OPTS%
echo.

goto :success

:error
echo.
echo ========================================
echo    ERROR OCCURRED
echo    حدث خطأ
echo ========================================
echo.
echo [ERROR] TNS connection test failed!
echo [خطأ] فشل اختبار اتصالات TNS!
echo.
echo Troubleshooting steps:
echo خطوات استكشاف الأخطاء:
echo 1. Check if Oracle database is running
echo 2. Verify TNS configuration files
echo 3. Check network connectivity
echo 4. Verify Oracle client installation
echo.
goto :end

:success
echo.
echo ========================================
echo    TEST COMPLETED SUCCESSFULLY
echo    اكتمل الاختبار بنجاح
echo ========================================
echo.
echo [SUCCESS] TNS connections test completed!
echo [نجح] اكتمل اختبار اتصالات TNS!
echo.
echo Your TNS configuration is working properly.
echo تكوين TNS يعمل بشكل صحيح.
echo.
echo You can now use TNS names in your applications:
echo يمكنك الآن استخدام أسماء TNS في تطبيقاتك:
echo.
echo Java example:
echo مثال Java:
echo   TNSConnectionManager manager = TNSConnectionManager.getInstance();
echo   Connection conn = manager.getShipErpConnection();
echo.
echo JDBC URL example:
echo مثال JDBC URL:
echo   **************************
echo.

:end
pause
