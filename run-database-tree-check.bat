@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 🗄️ فحص جدول شجرة الأنظمة في قاعدة البيانات
echo    Database System Tree Check
echo ================================================
echo.

cd /d "d:\java\java"

echo 🔍 فحص الاتصال بقاعدة البيانات...
echo.

echo [1] معلومات الاتصال:
echo • الخادم: localhost:1521
echo • قاعدة البيانات: ORCL
echo • المستخدم: SHIP_ERP
echo • الجدول: ERP_SYSTEM_TREE
echo.

echo [2] فحص ملف SQL...
if not exist "check-system-tree-database.sql" (
    echo ❌ ملف check-system-tree-database.sql مفقود!
    pause
    exit /b 1
)
echo ✅ ملف SQL متوفر
echo.

echo [3] تشغيل فحص قاعدة البيانات...
echo.

set /p password="أدخل كلمة مرور SHIP_ERP: "

echo.
echo 🚀 تشغيل فحص شامل لجدول شجرة الأنظمة...
echo.

sqlplus SHIP_ERP/%password%@localhost:1521/ORCL @check-system-tree-database.sql

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في الاتصال بقاعدة البيانات!
    echo.
    echo 🔧 حلول مقترحة:
    echo • تأكد من تشغيل Oracle Database
    echo • تحقق من صحة كلمة المرور
    echo • تأكد من وجود المستخدم SHIP_ERP
    echo • تحقق من إعدادات الشبكة
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================
echo ✅ تم إكمال فحص جدول شجرة الأنظمة
echo ================================================
echo.

echo 📊 ما تم فحصه:
echo • هيكل الجدول والأعمدة
echo • الإحصائيات العامة للعقد
echo • توزيع أنواع العقد والمستويات
echo • الهيكل الهرمي للشجرة
echo • المشاكل المحتملة (عقد يتيمة، أسماء مكررة)
echo • معلومات الإنشاء والتحديث
echo • الفهارس والقيود
echo • توصيات التحسين
echo.

echo 💡 للحصول على تحليل أكثر تفصيلاً:
echo • استخدم: run-system-tree-analyzer.bat
echo • للواجهة الرسومية المتقدمة مع إمكانيات الإصلاح
echo.

echo 📄 لحفظ النتائج في ملف:
echo • أعد تشغيل الأمر مع إعادة التوجيه:
echo • sqlplus SHIP_ERP/password@localhost:1521/ORCL @check-system-tree-database.sql > tree_report.txt
echo.

pause
