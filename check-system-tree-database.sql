-- ================================================
-- فحص جدول شجرة الأنظمة - System Tree Database Check
-- ================================================
-- تاريخ الإنشاء: 2025-01-20
-- الهدف: فحص شامل لجدول ERP_SYSTEM_TREE
-- ================================================

PROMPT '================================================'
PROMPT '📊 فحص جدول شجرة الأنظمة - System Tree Check'
PROMPT '================================================'

-- تعيين إعدادات العرض
SET PAGESIZE 50
SET LINESIZE 120
SET FEEDBACK ON
SET HEADING ON

PROMPT
PROMPT '🔍 [1] فحص وجود الجدول والهيكل...'
PROMPT '=================================='

-- فحص وجود الجدول
SELECT 
    'ERP_SYSTEM_TREE' AS TABLE_NAME,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ موجود'
        ELSE '❌ غير موجود'
    END AS STATUS
FROM USER_TABLES 
WHERE TABLE_NAME = 'ERP_SYSTEM_TREE';

-- عرض هيكل الجدول
PROMPT
PROMPT '📋 هيكل الجدول:'
PROMPT '==============='

DESCRIBE ERP_SYSTEM_TREE;

PROMPT
PROMPT '🔢 [2] الإحصائيات العامة...'
PROMPT '========================='

-- إحصائيات عامة
SELECT 
    '📊 إجمالي العقد' AS STATISTIC,
    COUNT(*) AS VALUE
FROM ERP_SYSTEM_TREE
UNION ALL
SELECT 
    '✅ العقد النشطة',
    COUNT(*)
FROM ERP_SYSTEM_TREE 
WHERE IS_ACTIVE = 'Y'
UNION ALL
SELECT 
    '👁️ العقد المرئية',
    COUNT(*)
FROM ERP_SYSTEM_TREE 
WHERE IS_VISIBLE = 'Y'
UNION ALL
SELECT 
    '🌳 العقد الجذرية',
    COUNT(*)
FROM ERP_SYSTEM_TREE 
WHERE PARENT_ID IS NULL
UNION ALL
SELECT 
    '📱 النوافذ',
    COUNT(*)
FROM ERP_SYSTEM_TREE 
WHERE NODE_TYPE = 'WINDOW'
UNION ALL
SELECT 
    '📂 الفئات',
    COUNT(*)
FROM ERP_SYSTEM_TREE 
WHERE NODE_TYPE = 'CATEGORY'
UNION ALL
SELECT 
    '🛠️ الأدوات',
    COUNT(*)
FROM ERP_SYSTEM_TREE 
WHERE NODE_TYPE = 'TOOL';

PROMPT
PROMPT '📊 [3] توزيع أنواع العقد...'
PROMPT '========================='

SELECT 
    NODE_TYPE AS 'نوع العقدة',
    COUNT(*) AS 'العدد',
    ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ERP_SYSTEM_TREE)), 1) || '%' AS 'النسبة'
FROM ERP_SYSTEM_TREE
GROUP BY NODE_TYPE
ORDER BY COUNT(*) DESC;

PROMPT
PROMPT '📏 [4] توزيع المستويات...'
PROMPT '======================'

SELECT 
    'المستوى ' || TREE_LEVEL AS 'المستوى',
    COUNT(*) AS 'العدد',
    ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ERP_SYSTEM_TREE)), 1) || '%' AS 'النسبة'
FROM ERP_SYSTEM_TREE
GROUP BY TREE_LEVEL
ORDER BY TREE_LEVEL;

PROMPT
PROMPT '🌳 [5] الهيكل الهرمي للشجرة...'
PROMPT '============================'

SELECT 
    TREE_ID AS 'المعرف',
    LPAD(' ', (TREE_LEVEL * 4)) || NODE_NAME_AR AS 'الهيكل الهرمي',
    NODE_TYPE AS 'النوع',
    CASE WHEN IS_ACTIVE = 'Y' THEN '✅' ELSE '❌' END AS 'نشط',
    CASE WHEN IS_VISIBLE = 'Y' THEN '👁️' ELSE '🚫' END AS 'مرئي',
    DISPLAY_ORDER AS 'الترتيب'
FROM ERP_SYSTEM_TREE
START WITH PARENT_ID IS NULL
CONNECT BY PRIOR TREE_ID = PARENT_ID
ORDER SIBLINGS BY DISPLAY_ORDER;

PROMPT
PROMPT '⚠️ [6] فحص المشاكل المحتملة...'
PROMPT '============================'

PROMPT
PROMPT '🔍 العقد اليتيمة (بدون أب صحيح):'
SELECT 
    TREE_ID AS 'معرف العقدة',
    NODE_NAME_AR AS 'اسم العقدة',
    PARENT_ID AS 'معرف الأب المفقود'
FROM ERP_SYSTEM_TREE
WHERE PARENT_ID IS NOT NULL 
AND PARENT_ID NOT IN (SELECT TREE_ID FROM ERP_SYSTEM_TREE);

PROMPT
PROMPT '🔄 الأسماء المكررة:'
SELECT 
    NODE_NAME_AR AS 'الاسم المكرر',
    COUNT(*) AS 'عدد التكرار'
FROM ERP_SYSTEM_TREE
GROUP BY NODE_NAME_AR
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC;

PROMPT
PROMPT '📱 النوافذ بدون كلاس:'
SELECT 
    TREE_ID AS 'معرف العقدة',
    NODE_NAME_AR AS 'اسم النافذة',
    NODE_TYPE AS 'النوع'
FROM ERP_SYSTEM_TREE
WHERE NODE_TYPE = 'WINDOW' 
AND (WINDOW_CLASS IS NULL OR TRIM(WINDOW_CLASS) = '');

PROMPT
PROMPT '📝 العقد بدون وصف:'
SELECT 
    COUNT(*) AS 'عدد العقد بدون وصف'
FROM ERP_SYSTEM_TREE
WHERE NODE_DESCRIPTION IS NULL OR TRIM(NODE_DESCRIPTION) = '';

PROMPT
PROMPT '🎨 العقد بدون أيقونة:'
SELECT 
    COUNT(*) AS 'عدد العقد بدون أيقونة'
FROM ERP_SYSTEM_TREE
WHERE ICON_PATH IS NULL OR TRIM(ICON_PATH) = '';

PROMPT
PROMPT '📅 [7] معلومات الإنشاء والتحديث...'
PROMPT '==============================='

SELECT 
    'أقدم عقدة' AS 'النوع',
    TO_CHAR(MIN(CREATED_DATE), 'YYYY-MM-DD HH24:MI:SS') AS 'التاريخ',
    '' AS 'المستخدم'
FROM ERP_SYSTEM_TREE
WHERE CREATED_DATE IS NOT NULL
UNION ALL
SELECT 
    'أحدث عقدة',
    TO_CHAR(MAX(CREATED_DATE), 'YYYY-MM-DD HH24:MI:SS'),
    ''
FROM ERP_SYSTEM_TREE
WHERE CREATED_DATE IS NOT NULL
UNION ALL
SELECT 
    'آخر تحديث',
    TO_CHAR(MAX(LAST_UPDATED), 'YYYY-MM-DD HH24:MI:SS'),
    ''
FROM ERP_SYSTEM_TREE
WHERE LAST_UPDATED IS NOT NULL;

PROMPT
PROMPT '👥 المستخدمون المنشئون:'
SELECT 
    CREATED_BY AS 'المستخدم',
    COUNT(*) AS 'عدد العقد المنشأة'
FROM ERP_SYSTEM_TREE
WHERE CREATED_BY IS NOT NULL
GROUP BY CREATED_BY
ORDER BY COUNT(*) DESC;

PROMPT
PROMPT '🔧 [8] فحص الفهارس والقيود...'
PROMPT '=========================='

PROMPT
PROMPT 'الفهارس المتاحة:'
SELECT 
    INDEX_NAME AS 'اسم الفهرس',
    COLUMN_NAME AS 'العمود',
    UNIQUENESS AS 'فريد'
FROM USER_IND_COLUMNS ic
JOIN USER_INDEXES i ON ic.INDEX_NAME = i.INDEX_NAME
WHERE ic.TABLE_NAME = 'ERP_SYSTEM_TREE'
ORDER BY ic.INDEX_NAME, ic.COLUMN_POSITION;

PROMPT
PROMPT 'القيود المطبقة:'
SELECT 
    CONSTRAINT_NAME AS 'اسم القيد',
    CONSTRAINT_TYPE AS 'النوع',
    STATUS AS 'الحالة'
FROM USER_CONSTRAINTS
WHERE TABLE_NAME = 'ERP_SYSTEM_TREE'
ORDER BY CONSTRAINT_TYPE;

PROMPT
PROMPT '💡 [9] توصيات التحسين...'
PROMPT '======================'

PROMPT
PROMPT '📋 ملخص التوصيات:'
PROMPT '• فحص العقد اليتيمة وإصلاحها'
PROMPT '• إضافة أوصاف للعقد المفقودة'
PROMPT '• إضافة أيقونات للعقد'
PROMPT '• مراجعة الأسماء المكررة'
PROMPT '• التأكد من صحة كلاسات النوافذ'
PROMPT '• إجراء نسخ احتياطي دوري'
PROMPT '• مراقبة نمو حجم الجدول'

PROMPT
PROMPT '================================================'
PROMPT '✅ تم إكمال فحص جدول شجرة الأنظمة'
PROMPT '================================================'

-- إعادة تعيين الإعدادات
SET PAGESIZE 14
SET LINESIZE 80
SET FEEDBACK 6
