@echo off
echo Downloading Icon Libraries...
cd /d "d:\java\java\lib"

echo [1] Downloading FontAwesome Icons...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-fontawesome-pack/12.3.1/ikonli-fontawesome-pack-12.3.1.jar' -OutFile 'ikonli-fontawesome-pack-12.3.1.jar'"

echo [2] Downloading Material Design Icons...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-material-pack/12.3.1/ikonli-material-pack-12.3.1.jar' -OutFile 'ikonli-material-pack-12.3.1.jar'"

echo [3] Downloading Ikonli Core...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-core/12.3.1/ikonli-core-12.3.1.jar' -OutFile 'ikonli-core-12.3.1.jar'"

echo [4] Downloading Ikonli Swing...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-swing/12.3.1/ikonli-swing-12.3.1.jar' -OutFile 'ikonli-swing-12.3.1.jar'"

echo [5] Downloading Commons CSV...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/apache/commons/commons-csv/1.10.0/commons-csv-1.10.0.jar' -OutFile 'commons-csv-1.10.0.jar'"

echo [6] Downloading Commons Text...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/apache/commons/commons-text/1.10.0/commons-text-1.10.0.jar' -OutFile 'commons-text-1.10.0.jar'"

echo Download completed!
echo Checking downloaded files...
dir /b *.jar | findstr /i "ikonli"
dir /b *.jar | findstr /i "commons-csv"
dir /b *.jar | findstr /i "commons-text"

pause
