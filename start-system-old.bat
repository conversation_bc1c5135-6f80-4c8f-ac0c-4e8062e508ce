@echo off
echo ========================================
echo    SHIP ERP SYSTEM - OLD VERSION (BACKUP)
echo ========================================

cd /d "e:\ship_erp\java"

set CP=.;lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\ojdbc11.jar;lib\orai18n.jar;lib\commons-logging-1.2.jar;lib\*

echo [INFO] Starting Ship ERP System (Old Version)...
echo.

echo [1] Checking libraries...
if not exist "lib\ojdbc11.jar" (
    echo ERROR: ojdbc11.jar missing!
    pause
    exit /b 1
)

if not exist "lib\orai18n.jar" (
    echo ERROR: orai18n.jar missing!
    pause
    exit /b 1
)

if not exist "lib\javax.mail-1.6.2.jar" (
    echo ERROR: javax.mail-1.6.2.jar missing!
    pause
    exit /b 1
)

if not exist "lib\activation.jar" (
    echo ERROR: activation.jar missing!
    pause
    exit /b 1
)

echo OK: All libraries found (including JavaMail)
echo.

echo [2] Compiling core components...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\DatabaseConfig.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\SettingsManager.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UIUtils.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ItemData.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\User.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserFormDialog.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserPermissionsDialog.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\GeneralSettingsWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserManagementWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\RealItemDataWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ComprehensiveItemDataWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ItemGroupsManagementWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\MeasurementUnitsWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\TreeMenuPanel.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\CompleteEmailAccountsWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\EnhancedMainWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\CompleteOracleSystemTest.java

echo OK: Compilation completed
echo.

echo [3] System Information:
echo - Database: Oracle ORCL (localhost:1521)
echo - SHIP_ERP: ship_erp_password
echo - IAS20251: ys123
echo - Items: 4647 records
echo - Measurement Units: 18 units
echo - Item Groups: 70+ groups
echo.

echo [4] Starting Complete Oracle System (Old Version)...
echo.

java -cp "%CP%" -Dfile.encoding=UTF-8 -Doracle.jdbc.defaultNChar=true CompleteOracleSystemTest

echo.
echo System shutdown complete.
pause
