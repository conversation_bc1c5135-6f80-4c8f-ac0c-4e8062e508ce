import java.sql.*;
import java.util.Properties;

/**
 * تسجيل نافذة إعدادات الواجهة والمظهر في شجرة النظام
 * Register Advanced UI Settings Window in System Tree
 */
public class RegisterUISettingsWindow {
    
    private static final String DB_URL = "*************************************";
    private static final String DB_USER = "ias20251";
    private static final String DB_PASSWORD = "ys123";
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  REGISTERING UI SETTINGS WINDOW");
        System.out.println("  تسجيل نافذة إعدادات الواجهة");
        System.out.println("========================================");
        
        RegisterUISettingsWindow registrar = new RegisterUISettingsWindow();
        registrar.registerUISettingsWindow();
    }
    
    /**
     * تسجيل نافذة إعدادات الواجهة في شجرة النظام
     */
    public void registerUISettingsWindow() {
        try {
            System.out.println("🔗 الاتصال بقاعدة البيانات...");
            
            Connection connection = getConnection();
            if (connection == null) {
                System.err.println("❌ فشل الاتصال بقاعدة البيانات");
                return;
            }
            
            System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح");
            
            // التحقق من وجود جدول شجرة النظام
            if (!checkSystemTreeTable(connection)) {
                createSystemTreeTable(connection);
            }
            
            // تسجيل نافذة إعدادات الواجهة
            registerWindow(connection);
            
            connection.close();
            System.out.println("✅ تم الانتهاء من تسجيل نافذة إعدادات الواجهة");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تسجيل النافذة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    private Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", DB_USER);
            props.setProperty("password", DB_PASSWORD);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            return DriverManager.getConnection(DB_URL, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    /**
     * التحقق من وجود جدول شجرة النظام
     */
    private boolean checkSystemTreeTable(Connection connection) {
        try {
            String checkQuery = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'SYSTEM_TREE'";
            try (PreparedStatement stmt = connection.prepareStatement(checkQuery);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في فحص جدول شجرة النظام: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * إنشاء جدول شجرة النظام
     */
    private void createSystemTreeTable(Connection connection) {
        try {
            System.out.println("📋 إنشاء جدول شجرة النظام...");
            
            String createTableSQL = """
                CREATE TABLE SYSTEM_TREE (
                    NODE_ID NUMBER PRIMARY KEY,
                    NODE_NAME_AR NVARCHAR2(100) NOT NULL,
                    NODE_NAME_EN VARCHAR2(100) NOT NULL,
                    PARENT_ID NUMBER,
                    NODE_LEVEL NUMBER DEFAULT 0,
                    NODE_ORDER NUMBER DEFAULT 0,
                    WINDOW_CLASS VARCHAR2(200),
                    DESCRIPTION NVARCHAR2(500),
                    ICON_PATH VARCHAR2(200),
                    IS_ACTIVE NUMBER(1) DEFAULT 1,
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    CONSTRAINT FK_SYSTEM_TREE_PARENT FOREIGN KEY (PARENT_ID) REFERENCES SYSTEM_TREE(NODE_ID)
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(createTableSQL)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول SYSTEM_TREE");
            }
            
            // إنشاء sequence للمفاتيح الأساسية
            String createSequenceSQL = "CREATE SEQUENCE SYSTEM_TREE_SEQ START WITH 1 INCREMENT BY 1";
            try (PreparedStatement stmt = connection.prepareStatement(createSequenceSQL)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء SYSTEM_TREE_SEQ");
            }
            
            // إدراج العقد الأساسية
            insertBasicNodes(connection);
            
        } catch (SQLException e) {
            if (e.getErrorCode() != 955) { // Table already exists
                System.err.println("خطأ في إنشاء جدول شجرة النظام: " + e.getMessage());
            }
        }
    }
    
    /**
     * إدراج العقد الأساسية
     */
    private void insertBasicNodes(Connection connection) {
        try {
            System.out.println("📝 إدراج العقد الأساسية...");
            
            // العقدة الجذر
            insertNode(connection, "نظام إدارة الشحنات", "Ship ERP System", null, 0, 1, null, "النظام الرئيسي لإدارة الشحنات");
            
            // العقد الرئيسية
            int rootId = getNodeId(connection, "Ship ERP System");
            
            insertNode(connection, "إدارة الأصناف", "Items Management", rootId, 1, 1, null, "إدارة الأصناف والمنتجات");
            insertNode(connection, "الإعدادات العامة", "General Settings", rootId, 1, 2, null, "الإعدادات العامة للنظام");
            insertNode(connection, "التقارير", "Reports", rootId, 1, 3, null, "التقارير والإحصائيات");
            insertNode(connection, "إدارة المستخدمين", "User Management", rootId, 1, 4, null, "إدارة المستخدمين والصلاحيات");
            
            System.out.println("✅ تم إدراج العقد الأساسية");
            
        } catch (SQLException e) {
            System.err.println("خطأ في إدراج العقد الأساسية: " + e.getMessage());
        }
    }
    
    /**
     * تسجيل نافذة إعدادات الواجهة
     */
    private void registerWindow(Connection connection) {
        try {
            System.out.println("🎨 تسجيل نافذة إعدادات الواجهة والمظهر...");
            
            // البحث عن عقدة الإعدادات العامة
            int parentId = getNodeId(connection, "General Settings");
            if (parentId == -1) {
                System.err.println("❌ لم يتم العثور على عقدة الإعدادات العامة");
                return;
            }
            
            // التحقق من عدم وجود النافذة مسبقاً
            if (getNodeId(connection, "Advanced UI Settings") != -1) {
                System.out.println("⚠️ نافذة إعدادات الواجهة موجودة مسبقاً");
                return;
            }
            
            // إدراج نافذة إعدادات الواجهة
            insertNode(connection, 
                "إعدادات الواجهة والمظهر", 
                "Advanced UI Settings", 
                parentId, 
                2, 
                1, 
                "AdvancedUISettingsWindow", 
                "إعدادات الواجهة والمظهر المتطورة مع دعم 19+ ثيم وتخصيص شامل للخطوط والألوان");
            
            System.out.println("✅ تم تسجيل نافذة إعدادات الواجهة والمظهر بنجاح");
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تسجيل نافذة إعدادات الواجهة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * إدراج عقدة جديدة
     */
    private void insertNode(Connection connection, String nameAr, String nameEn, Integer parentId, 
                           int level, int order, String windowClass, String description) throws SQLException {
        
        String insertSQL = """
            INSERT INTO SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, 
                                   NODE_ORDER, WINDOW_CLASS, DESCRIPTION, IS_ACTIVE, CREATED_DATE, CREATED_BY)
            VALUES (SYSTEM_TREE_SEQ.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, 1, SYSDATE, USER)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
            stmt.setString(1, nameAr);
            stmt.setString(2, nameEn);
            if (parentId != null) {
                stmt.setInt(3, parentId);
            } else {
                stmt.setNull(3, Types.INTEGER);
            }
            stmt.setInt(4, level);
            stmt.setInt(5, order);
            stmt.setString(6, windowClass);
            stmt.setString(7, description);
            
            stmt.executeUpdate();
            System.out.println("  ✅ تم إدراج: " + nameAr + " (" + nameEn + ")");
        }
    }
    
    /**
     * الحصول على معرف العقدة
     */
    private int getNodeId(Connection connection, String nameEn) throws SQLException {
        String selectSQL = "SELECT NODE_ID FROM SYSTEM_TREE WHERE NODE_NAME_EN = ? AND IS_ACTIVE = 1";
        
        try (PreparedStatement stmt = connection.prepareStatement(selectSQL)) {
            stmt.setString(1, nameEn);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("NODE_ID");
                }
            }
        }
        
        return -1;
    }
}
