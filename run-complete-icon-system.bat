@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 🎨 نظام الأيقونات الشامل لشجرة الأنظمة
echo    Complete Icon System for System Tree
echo ================================================
echo.

cd /d "d:\java\java"

echo 🔍 فحص المتطلبات...
echo.

echo [1] فحص المكتبات المطلوبة...
if not exist "lib\ojdbc11.jar" (
    echo ❌ ojdbc11.jar مفقود!
    pause
    exit /b 1
)

if not exist "lib\flatlaf-3.2.5.jar" (
    echo ❌ flatlaf-3.2.5.jar مفقود!
    pause
    exit /b 1
)

echo ✅ جميع المكتبات متوفرة
echo.

echo [2] فحص ملفات الأيقونات...
if not exist "resources\icons\folder.png" (
    echo ⚠️ ملفات الأيقونات مفقودة، سيتم إنشاؤها...
    goto CREATE_ICONS
) else (
    echo ✅ ملفات الأيقونات موجودة
    goto COMPILE_AND_RUN
)

:CREATE_ICONS
echo.
echo ================================================
echo 🎨 إنشاء ملفات الأيقونات
echo ================================================
echo.

echo [3] تجميع مولد الأيقونات...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\SystemTreeIconGenerator.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع SystemTreeIconGenerator
    pause
    exit /b 1
)

echo [4] إنشاء ملفات الأيقونات...
java -cp "lib\*;." SystemTreeIconGenerator

echo [5] تحديث مسارات الأيقونات في قاعدة البيانات...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\IconPathUpdater.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع IconPathUpdater
    pause
    exit /b 1
)

java -cp "lib\*;." IconPathUpdater

:COMPILE_AND_RUN
echo.
echo ================================================
echo 🔨 تجميع وتشغيل أدوات عرض الأيقونات
echo ================================================
echo.

echo [6] تجميع أدوات عرض الأيقونات...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\SystemTreeWithIcons.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع SystemTreeWithIcons
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\IconTestViewer.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع IconTestViewer
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح
echo.

echo ================================================
echo 🚀 خيارات التشغيل
echo ================================================
echo.
echo اختر الأداة التي تريد تشغيلها:
echo.
echo [1] شجرة الأنظمة الكاملة مع الأيقونات
echo [2] عارض اختبار الأيقونات
echo [3] تشغيل جميع الأدوات
echo [4] عرض حالة الأيقونات فقط
echo [5] إعادة إنشاء الأيقونات
echo [0] خروج
echo.

set /p choice="أدخل اختيارك (0-5): "

if "%choice%"=="1" goto RUN_FULL_TREE
if "%choice%"=="2" goto RUN_TEST_VIEWER
if "%choice%"=="3" goto RUN_ALL_TOOLS
if "%choice%"=="4" goto SHOW_ICON_STATUS
if "%choice%"=="5" goto RECREATE_ICONS
if "%choice%"=="0" goto END
goto INVALID_CHOICE

:RUN_FULL_TREE
echo.
echo 🌳 تشغيل شجرة الأنظمة الكاملة مع الأيقونات...
java -cp "lib\*;." SystemTreeWithIcons
goto END

:RUN_TEST_VIEWER
echo.
echo 🎨 تشغيل عارض اختبار الأيقونات...
java -cp "lib\*;." IconTestViewer
goto END

:RUN_ALL_TOOLS
echo.
echo 🚀 تشغيل جميع الأدوات...
echo.
echo تشغيل شجرة الأنظمة الكاملة...
start java -cp "lib\*;." SystemTreeWithIcons
timeout /t 2 /nobreak >nul

echo تشغيل عارض اختبار الأيقونات...
start java -cp "lib\*;." IconTestViewer
timeout /t 2 /nobreak >nul

echo ✅ تم تشغيل جميع الأدوات
goto END

:SHOW_ICON_STATUS
echo.
echo 📊 حالة ملفات الأيقونات:
echo =============================
echo.

for %%f in (resources\icons\*.png) do (
    echo ✅ %%f - موجود
)

echo.
echo 📋 إحصائيات الأيقونات:
dir resources\icons\*.png | find "File(s)"
goto END

:RECREATE_ICONS
echo.
echo 🔄 إعادة إنشاء جميع الأيقونات...
java -cp "lib\*;." SystemTreeIconGenerator
java -cp "lib\*;." IconPathUpdater
echo ✅ تم إعادة إنشاء الأيقونات
goto END

:INVALID_CHOICE
echo.
echo ❌ اختيار غير صحيح!
pause
goto COMPILE_AND_RUN

:END
echo.
echo ================================================
echo 📋 ملخص نظام الأيقونات
echo ================================================
echo.
echo 🎨 الأيقونات المتاحة:
echo • folder.png - للفئات والمجلدات (📂)
echo • window.png - للنوافذ التطبيقية (🪟)
echo • tool.png - للأدوات المساعدة (🛠️)
echo • report.png - للتقارير (📊)
echo • user.png - لإدارة المستخدمين (👤)
echo • settings.png - للإعدادات (⚙️)
echo • email.png - للبريد الإلكتروني (📧)
echo • database.png - لقاعدة البيانات (🗄️)
echo • default.png - الأيقونة الافتراضية (❓)
echo.
echo 🗄️ قاعدة البيانات: ERP_SYSTEM_TREE.ICON_PATH
echo 📁 موقع الأيقونات: resources/icons/
echo 🔧 عدد العقد المحدثة: 91 عقدة
echo.
echo 💡 لرؤية الأيقونات في التطبيق الأصلي:
echo • أعد تشغيل نظام إدارة الشحنات
echo • افتح أي نافذة تحتوي على شجرة الأنظمة
echo • ستظهر الأيقونات بجانب أسماء العقد
echo.
echo 🔧 للتخصيص الإضافي:
echo • يمكن تعديل الأيقونات في resources/icons/
echo • يمكن إضافة أيقونات جديدة
echo • استخدم IconPathUpdater لتحديث المسارات
echo.

pause
