# LISTENER.ORA Network Configuration File
# Ship ERP System - Oracle Listener Configuration
# تكوين مستمع Oracle لنظام إدارة الشحنات
# Generated on: 2025-07-18

# =============================================================================
# DEFAULT LISTENER CONFIGURATION
# تكوين المستمع الافتراضي
# =============================================================================
LISTENER =
  (DESCRIPTION_LIST =
    (DESCRIPTION =
      (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
      (ADDRESS = (PROTOCOL = IPC)(KEY = EXTPROC1521))
    )
  )

# =============================================================================
# SHIP ERP DEDICATED LISTENER
# مستمع مخصص لنظام إدارة الشحنات
# =============================================================================
SHIP_ERP_LISTENER =
  (DESCRIPTION_LIST =
    (DESCRIPTION =
      (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    )
  )

# =============================================================================
# SSL LISTENER CONFIGURATION
# تكوين مستمع SSL
# =============================================================================
SSL_LISTENER =
  (DESCRIPTION_LIST =
    (DESCRIPTION =
      (ADDRESS = (PROTOCOL = TCPS)(HOST = localhost)(PORT = 2484))
    )
  )

# =============================================================================
# SERVICE REGISTRATION
# تسجيل الخدمات
# =============================================================================
SID_LIST_LISTENER =
  (SID_LIST =
    (SID_DESC =
      (SID_NAME = ORCL)
      (ORACLE_HOME = C:\oracle\product\19.0.0\dbhome_1)
      (PROGRAM = oracle)
    )
    (SID_DESC =
      (SID_NAME = SHIP_ERP)
      (ORACLE_HOME = C:\oracle\product\19.0.0\dbhome_1)
      (PROGRAM = oracle)
    )
    (SID_DESC =
      (SID_NAME = IAS20251)
      (ORACLE_HOME = C:\oracle\product\19.0.0\dbhome_1)
      (PROGRAM = oracle)
    )
  )

# =============================================================================
# LISTENER CONTROL PARAMETERS
# معاملات التحكم في المستمع
# =============================================================================
LISTENER_ADMIN_PASSWORD_LISTENER = 
STARTUP_WAIT_TIME_LISTENER = 0
CONNECT_TIMEOUT_LISTENER = 10
TRACE_LEVEL_LISTENER = OFF
TRACE_DIRECTORY_LISTENER = E:\ship_erp\java\network\admin\trace
TRACE_FILE_LISTENER = listener
LOG_DIRECTORY_LISTENER = E:\ship_erp\java\network\admin\log
LOG_FILE_LISTENER = listener

# =============================================================================
# SECURITY SETTINGS
# إعدادات الأمان
# =============================================================================
SECURE_REGISTER_LISTENER = (TCP)
SECURE_CONTROL_LISTENER = (TCP)
ADMIN_RESTRICTIONS_LISTENER = ON
LOCAL_LISTENER = LISTENER

# =============================================================================
# DYNAMIC SERVICE REGISTRATION
# تسجيل الخدمات الديناميكي
# =============================================================================
# The following parameters enable dynamic service registration
# المعاملات التالية تفعل تسجيل الخدمات الديناميكي

# In init.ora or spfile, set:
# في init.ora أو spfile، قم بتعيين:
# LOCAL_LISTENER = LISTENER
# SERVICE_NAMES = ORCL, SHIP_ERP, IAS20251

# =============================================================================
# CONFIGURATION NOTES
# ملاحظات التكوين
# =============================================================================
#
# This listener configuration supports:
# تكوين المستمع هذا يدعم:
# - Standard TCP connections on port 1521
# - SSL connections on port 2484
# - Multiple database instances
# - Secure administration
# - Comprehensive logging
#
# To start the listener:
# لبدء تشغيل المستمع:
# lsnrctl start LISTENER
#
# To check listener status:
# للتحقق من حالة المستمع:
# lsnrctl status LISTENER
#
# To reload the configuration:
# لإعادة تحميل التكوين:
# lsnrctl reload LISTENER
#
# =============================================================================
