import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * تقرير مفصل لجدول شجرة الأنظمة مع تفاصيل المشاكل
 */
public class DetailedTreeReport {
    
    public static void main(String[] args) {
        System.out.println("🔍 تقرير مفصل لجدول شجرة الأنظمة");
        System.out.println("==========================================");
        
        Connection conn = null;
        try {
            // الاتصال بقاعدة البيانات
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            conn = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات");
            
            // طباعة رأس التقرير
            printHeader();
            
            // تفاصيل المشاكل
            printDuplicateNames(conn);
            printOrphanNodes(conn);
            printWindowsWithoutClass(conn);
            printNodesWithoutDescription(conn);
            printNodesWithoutIcons(conn);
            
            // تفاصيل الهيكل
            printFullStructure(conn);
            
            // إحصائيات المستخدمين
            printUserStatistics(conn);
            
            System.out.println("\n✅ تم إكمال التقرير المفصل!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
                }
            }
        }
    }
    
    private static void printHeader() {
        System.out.println("\n📊 التقرير المفصل لشجرة الأنظمة");
        System.out.println("==========================================");
        System.out.println("التاريخ: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        System.out.println("==========================================");
    }
    
    private static void printDuplicateNames(Connection conn) throws SQLException {
        System.out.println("\n🔄 [1] الأسماء المكررة:");
        System.out.println("======================");
        
        String sql = """
            SELECT NODE_NAME_AR, COUNT(*) as COUNT
            FROM ERP_SYSTEM_TREE
            GROUP BY NODE_NAME_AR
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                String name = rs.getString("NODE_NAME_AR");
                int count = rs.getInt("COUNT");
                System.out.println("⚠️ \"" + name + "\" - مكرر " + count + " مرات");
                
                // عرض تفاصيل العقد المكررة
                printDuplicateDetails(conn, name);
            }
            
            if (!found) {
                System.out.println("✅ لا توجد أسماء مكررة");
            }
        }
    }
    
    private static void printDuplicateDetails(Connection conn, String nodeName) throws SQLException {
        String sql = """
            SELECT TREE_ID, PARENT_ID, NODE_TYPE, WINDOW_CLASS, TREE_LEVEL
            FROM ERP_SYSTEM_TREE
            WHERE NODE_NAME_AR = ?
            ORDER BY TREE_ID
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, nodeName);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    int treeId = rs.getInt("TREE_ID");
                    Integer parentId = rs.getObject("PARENT_ID") != null ? rs.getInt("PARENT_ID") : null;
                    String nodeType = rs.getString("NODE_TYPE");
                    String windowClass = rs.getString("WINDOW_CLASS");
                    int level = rs.getInt("TREE_LEVEL");
                    
                    System.out.printf("   └─ ID: %d, الأب: %s, النوع: %s, المستوى: %d%s\n",
                        treeId, 
                        parentId != null ? parentId.toString() : "جذر",
                        nodeType,
                        level,
                        windowClass != null ? ", الكلاس: " + windowClass : "");
                }
            }
        }
    }
    
    private static void printOrphanNodes(Connection conn) throws SQLException {
        System.out.println("\n👤 [2] العقد اليتيمة:");
        System.out.println("==================");
        
        String sql = """
            SELECT TREE_ID, NODE_NAME_AR, PARENT_ID, NODE_TYPE
            FROM ERP_SYSTEM_TREE
            WHERE PARENT_ID IS NOT NULL 
            AND PARENT_ID NOT IN (SELECT TREE_ID FROM ERP_SYSTEM_TREE)
            ORDER BY TREE_ID
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                int treeId = rs.getInt("TREE_ID");
                String nodeName = rs.getString("NODE_NAME_AR");
                int parentId = rs.getInt("PARENT_ID");
                String nodeType = rs.getString("NODE_TYPE");
                
                System.out.printf("❌ [%d] %s (النوع: %s) - يشير للأب المفقود: %d\n",
                    treeId, nodeName, nodeType, parentId);
            }
            
            if (!found) {
                System.out.println("✅ لا توجد عقد يتيمة");
            }
        }
    }
    
    private static void printWindowsWithoutClass(Connection conn) throws SQLException {
        System.out.println("\n🪟 [3] النوافذ بدون كلاس:");
        System.out.println("========================");
        
        String sql = """
            SELECT TREE_ID, NODE_NAME_AR, TREE_LEVEL
            FROM ERP_SYSTEM_TREE
            WHERE NODE_TYPE = 'WINDOW' 
            AND (WINDOW_CLASS IS NULL OR TRIM(WINDOW_CLASS) = '')
            ORDER BY TREE_LEVEL, TREE_ID
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                int treeId = rs.getInt("TREE_ID");
                String nodeName = rs.getString("NODE_NAME_AR");
                int level = rs.getInt("TREE_LEVEL");
                
                System.out.printf("❌ [%d] %s (المستوى: %d)\n", treeId, nodeName, level);
            }
            
            if (!found) {
                System.out.println("✅ جميع النوافذ لها كلاسات");
            }
        }
    }
    
    private static void printNodesWithoutDescription(Connection conn) throws SQLException {
        System.out.println("\n📝 [4] العقد بدون وصف:");
        System.out.println("====================");
        
        String sql = """
            SELECT COUNT(*) as TOTAL,
                   SUM(CASE WHEN NODE_TYPE = 'WINDOW' THEN 1 ELSE 0 END) as WINDOWS,
                   SUM(CASE WHEN NODE_TYPE = 'CATEGORY' THEN 1 ELSE 0 END) as CATEGORIES
            FROM ERP_SYSTEM_TREE
            WHERE NODE_DESCRIPTION IS NULL OR TRIM(NODE_DESCRIPTION) = ''
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                int total = rs.getInt("TOTAL");
                int windows = rs.getInt("WINDOWS");
                int categories = rs.getInt("CATEGORIES");
                
                System.out.println("📊 إجمالي العقد بدون وصف: " + total);
                System.out.println("   🪟 النوافذ: " + windows);
                System.out.println("   📂 الفئات: " + categories);
                
                if (total > 0) {
                    System.out.println("💡 يُنصح بإضافة أوصاف لتحسين فهم الوظائف");
                }
            }
        }
    }
    
    private static void printNodesWithoutIcons(Connection conn) throws SQLException {
        System.out.println("\n🎨 [5] العقد بدون أيقونات:");
        System.out.println("========================");
        
        String sql = """
            SELECT COUNT(*) as TOTAL
            FROM ERP_SYSTEM_TREE
            WHERE ICON_PATH IS NULL OR TRIM(ICON_PATH) = ''
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                int total = rs.getInt("TOTAL");
                System.out.println("📊 العقد بدون أيقونات: " + total);
                
                if (total > 0) {
                    System.out.println("💡 يُنصح بإضافة أيقونات لتحسين الواجهة");
                }
            }
        }
    }
    
    private static void printFullStructure(Connection conn) throws SQLException {
        System.out.println("\n🌳 [6] الهيكل الكامل للشجرة:");
        System.out.println("============================");
        
        String sql = """
            SELECT TREE_ID, LPAD(' ', (TREE_LEVEL * 2)) || NODE_NAME_AR AS TREE_DISPLAY,
                   NODE_TYPE, WINDOW_CLASS,
                   CASE WHEN IS_ACTIVE = 'Y' THEN '✅' ELSE '❌' END AS ACTIVE_STATUS,
                   DISPLAY_ORDER
            FROM ERP_SYSTEM_TREE
            START WITH PARENT_ID IS NULL
            CONNECT BY PRIOR TREE_ID = PARENT_ID
            ORDER SIBLINGS BY DISPLAY_ORDER
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            int count = 0;
            while (rs.next() && count < 30) { // عرض أول 30 عقدة
                String treeDisplay = rs.getString("TREE_DISPLAY");
                String nodeType = rs.getString("NODE_TYPE");
                String windowClass = rs.getString("WINDOW_CLASS");
                String activeStatus = rs.getString("ACTIVE_STATUS");
                int treeId = rs.getInt("TREE_ID");
                
                String icon = getIcon(nodeType);
                String classInfo = (windowClass != null && !windowClass.trim().isEmpty()) 
                    ? " [" + windowClass + "]" : "";
                
                System.out.printf("%s %s %s (%s)%s\n", 
                    activeStatus, treeDisplay, icon, nodeType, classInfo);
                count++;
            }
            
            if (count == 30) {
                System.out.println("... (عرض أول 30 عقدة فقط)");
            }
        }
    }
    
    private static void printUserStatistics(Connection conn) throws SQLException {
        System.out.println("\n👥 [7] إحصائيات المستخدمين:");
        System.out.println("==========================");
        
        String sql = """
            SELECT CREATED_BY, COUNT(*) as COUNT
            FROM ERP_SYSTEM_TREE
            WHERE CREATED_BY IS NOT NULL
            GROUP BY CREATED_BY
            ORDER BY COUNT(*) DESC
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String user = rs.getString("CREATED_BY");
                int count = rs.getInt("COUNT");
                System.out.println("👤 " + user + ": " + count + " عقدة");
            }
        }
    }
    
    private static String getIcon(String nodeType) {
        if (nodeType == null) return "📄";
        return switch (nodeType) {
            case "CATEGORY" -> "📂";
            case "WINDOW" -> "🪟";
            case "TOOL" -> "🛠️";
            case "REPORT" -> "📊";
            default -> "📄";
        };
    }
}
