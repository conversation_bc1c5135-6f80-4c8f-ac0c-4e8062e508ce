@echo off
chcp 65001 >nul
echo ========================================
echo   ADVANCED UI SETTINGS WINDOW
echo   نافذة إعدادات الواجهة والمظهر المتطورة
echo ========================================

cd /d "d:\java\java"

echo.
echo [INFO] Starting Advanced UI Settings Window...
echo [INFO] بدء تشغيل نافذة إعدادات الواجهة المتطورة...
echo.

REM التحقق من وجود المكتبات المطلوبة
echo [1] Checking required libraries...
echo [1] فحص المكتبات المطلوبة...

if not exist "lib\flatlaf-3.2.5.jar" (
    echo ❌ FlatLaf library not found!
    echo ❌ مكتبة FlatLaf غير موجودة!
    pause
    exit /b 1
)

if not exist "lib\ojdbc11.jar" (
    echo ❌ Oracle JDBC library not found!
    echo ❌ مكتبة Oracle JDBC غير موجودة!
    pause
    exit /b 1
)

if not exist "lib\jiconfont-1.0.0.jar" (
    echo ❌ JIconFont library not found!
    echo ❌ مكتبة JIconFont غير موجودة!
    pause
    exit /b 1
)

echo ✅ All required libraries found!
echo ✅ تم العثور على جميع المكتبات المطلوبة!

echo.
echo [2] Compiling Advanced UI Settings...
echo [2] تجميع نافذة الإعدادات المتطورة...

javac -encoding UTF-8 -cp "lib\*" EnhancedSettingsManager.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile EnhancedSettingsManager
    echo ❌ فشل تجميع مدير الإعدادات المحسن
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib\*" AdvancedUISettingsWindow.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile AdvancedUISettingsWindow
    echo ❌ فشل تجميع نافذة الإعدادات المتطورة
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo ✅ تم التجميع بنجاح!

echo.
echo [3] Initializing database settings...
echo [3] تهيئة إعدادات قاعدة البيانات...

REM تهيئة جدول الإعدادات في قاعدة البيانات
java -cp "lib\*;." -Dfile.encoding=UTF-8 -c "EnhancedSettingsManager.initializeSettingsTable()" 2>nul

echo.
echo [4] Starting Advanced UI Settings Window...
echo [4] تشغيل نافذة إعدادات الواجهة المتطورة...

REM تشغيل النافذة مع جميع الخيارات المحسنة
java -cp "lib\*;." ^
     -Djava.awt.headless=false ^
     -Dfile.encoding=UTF-8 ^
     -Duser.language=ar ^
     -Duser.country=SA ^
     -Djava.awt.font.useSystemFontSettings=on ^
     -Dswing.aatext=true ^
     -Dswing.plaf.metal.controlFont=Tahoma-12 ^
     -Dswing.plaf.metal.userFont=Tahoma-12 ^
     AdvancedUISettingsWindow

if %errorlevel% equ 0 (
    echo.
    echo ✅ Advanced UI Settings Window closed successfully!
    echo ✅ تم إغلاق نافذة الإعدادات المتطورة بنجاح!
) else (
    echo.
    echo ❌ Advanced UI Settings Window encountered an error
    echo ❌ واجهت نافذة الإعدادات المتطورة خطأ
)

echo.
echo ========================================
echo   ADVANCED UI SETTINGS COMPLETED
echo   تم الانتهاء من إعدادات الواجهة المتطورة
echo ========================================

echo.
echo Features available in Advanced UI Settings:
echo الميزات المتاحة في إعدادات الواجهة المتطورة:
echo.
echo 🎨 THEMES (20+ themes available):
echo    الثيمات (أكثر من 20 ثيم متاح):
echo    • FlatLaf Collection (Light, Dark, IntelliJ, Darcula)
echo    • Material UI Design
echo    • JTattoo Collection (16 different themes)
echo    • DarkLaf Advanced Dark Theme
echo    • SeaGlass Transparent Theme
echo    • System Default Themes
echo.
echo 🔤 FONTS:
echo    الخطوط:
echo    • Font family selection from all system fonts
echo    • Font size adjustment (8-72)
echo    • Arabic font support with RTL
echo    • Live font preview
echo.
echo 🖥️ INTERFACE OPTIONS:
echo    خيارات الواجهة:
echo    • Enable/disable animations and transitions
echo    • Enable/disable system sounds
echo    • Enable/disable tooltips and hints
echo    • RTL (Right-to-Left) text support
echo    • Window transparency control
echo.
echo 🌈 COLOR CUSTOMIZATION:
echo    تخصيص الألوان:
echo    • Full color picker for accent colors
echo    • Real-time color preview
echo    • Theme-based color schemes
echo.
echo 👁️ LIVE PREVIEW:
echo    المعاينة المباشرة:
echo    • Instant theme application
echo    • Real-time component preview
echo    • All windows update automatically
echo.
echo 💾 DATABASE INTEGRATION:
echo    تكامل قاعدة البيانات:
echo    • Settings saved to Oracle database
echo    • Automatic file backup
echo    • Settings persistence across sessions
echo    • Reset to defaults option
echo.

pause
