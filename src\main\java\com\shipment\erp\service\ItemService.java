package com.shipment.erp.service;

import com.shipment.erp.model.Item;
import java.math.BigDecimal;
import java.util.List;

/**
 * خدمة إدارة الأصناف
 * Item Management Service
 */
public interface ItemService extends BaseService<Item> {
    
    /**
     * البحث عن الأصناف النشطة
     * Find active items
     */
    List<Item> findActiveItems();
    
    /**
     * البحث عن صنف بالكود
     * Find item by code
     */
    Item findByCode(String code);
    
    /**
     * البحث عن الأصناف بالاسم (يحتوي على)
     * Find items by name containing
     */
    List<Item> findByNameContaining(String name);
    
    /**
     * البحث عن الأصناف بالفئة
     * Find items by category
     */
    List<Item> findByCategory(String category);
    
    /**
     * البحث عن الأصناف بالباركود
     * Find item by barcode
     */
    Item findByBarcode(String barcode);
    
    /**
     * البحث عن الأصناف بـ SKU
     * Find item by SKU
     */
    Item findBySku(String sku);
    
    /**
     * التحقق من وجود كود الصنف
     * Check if item code exists
     */
    boolean existsByCode(String code);
    
    /**
     * التحقق من وجود الباركود
     * Check if barcode exists
     */
    boolean existsByBarcode(String barcode);
    
    /**
     * الحصول على الأصناف ذات المخزون المنخفض
     * Get items with low stock
     */
    List<Item> findLowStockItems();
    
    /**
     * الحصول على الأصناف ذات المخزون الزائد
     * Get items with over stock
     */
    List<Item> findOverStockItems();
    
    /**
     * الحصول على الأصناف القابلة للتلف
     * Get perishable items
     */
    List<Item> findPerishableItems();
    
    /**
     * الحصول على الأصناف القابلة للتتبع
     * Get trackable items
     */
    List<Item> findTrackableItems();
    
    /**
     * البحث عن الأصناف بنطاق سعر
     * Find items by price range
     */
    List<Item> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice);
    
    /**
     * البحث عن الأصناف بالمورد
     * Find items by supplier
     */
    List<Item> findBySupplierCode(String supplierCode);
    
    /**
     * تحديث مخزون الصنف
     * Update item stock
     */
    void updateStock(Long itemId, Integer newStock);
    
    /**
     * إضافة مخزون للصنف
     * Add stock to item
     */
    void addStock(Long itemId, Integer quantity);
    
    /**
     * خصم مخزون من الصنف
     * Subtract stock from item
     */
    void subtractStock(Long itemId, Integer quantity);
    
    /**
     * تحديث سعر الصنف
     * Update item price
     */
    void updatePrice(Long itemId, BigDecimal newPrice);
    
    /**
     * تحديث تكلفة الصنف
     * Update item cost
     */
    void updateCost(Long itemId, BigDecimal newCost);
    
    /**
     * الحصول على جميع الفئات
     * Get all categories
     */
    List<String> getAllCategories();
    
    /**
     * الحصول على جميع الوحدات
     * Get all units
     */
    List<String> getAllUnits();
    
    /**
     * الحصول على إحصائيات المخزون
     * Get stock statistics
     */
    ItemStockStatistics getStockStatistics();
    
    /**
     * التحقق من إمكانية حذف الصنف
     * Check if item can be deleted
     */
    boolean canDelete(Long itemId);
    
    /**
     * تفعيل/إلغاء تفعيل الصنف
     * Activate/Deactivate item
     */
    void toggleActive(Long itemId);
    
    /**
     * فئة إحصائيات المخزون
     * Stock Statistics Class
     */
    class ItemStockStatistics {
        private long totalItems;
        private long activeItems;
        private long lowStockItems;
        private long overStockItems;
        private long perishableItems;
        private BigDecimal totalInventoryValue;
        private BigDecimal averageItemPrice;
        
        // Constructors
        public ItemStockStatistics() {}
        
        public ItemStockStatistics(long totalItems, long activeItems, long lowStockItems, 
                                 long overStockItems, long perishableItems, 
                                 BigDecimal totalInventoryValue, BigDecimal averageItemPrice) {
            this.totalItems = totalItems;
            this.activeItems = activeItems;
            this.lowStockItems = lowStockItems;
            this.overStockItems = overStockItems;
            this.perishableItems = perishableItems;
            this.totalInventoryValue = totalInventoryValue;
            this.averageItemPrice = averageItemPrice;
        }
        
        // Getters and Setters
        public long getTotalItems() { return totalItems; }
        public void setTotalItems(long totalItems) { this.totalItems = totalItems; }
        
        public long getActiveItems() { return activeItems; }
        public void setActiveItems(long activeItems) { this.activeItems = activeItems; }
        
        public long getLowStockItems() { return lowStockItems; }
        public void setLowStockItems(long lowStockItems) { this.lowStockItems = lowStockItems; }
        
        public long getOverStockItems() { return overStockItems; }
        public void setOverStockItems(long overStockItems) { this.overStockItems = overStockItems; }
        
        public long getPerishableItems() { return perishableItems; }
        public void setPerishableItems(long perishableItems) { this.perishableItems = perishableItems; }
        
        public BigDecimal getTotalInventoryValue() { return totalInventoryValue; }
        public void setTotalInventoryValue(BigDecimal totalInventoryValue) { this.totalInventoryValue = totalInventoryValue; }
        
        public BigDecimal getAverageItemPrice() { return averageItemPrice; }
        public void setAverageItemPrice(BigDecimal averageItemPrice) { this.averageItemPrice = averageItemPrice; }
    }
}
