import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.HashMap;
import java.util.Map;
import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTree;
import javax.swing.SwingUtilities;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;

/**
 * لوحة قائمة الشجرة المطوية - تعرض شجرة الأنظمة في وضع التجميع
 * Collapsed Tree Menu Panel - Shows system tree in collapsed state
 */
public class CollapsedTreeMenuPanel extends JPanel {
    
    private JTree menuTree;
    private DefaultMutableTreeNode rootNode;
    private DefaultTreeModel treeModel;
    private Font arabicFont;
    private JFrame parentFrame;
    private Map<String, Runnable> menuActions;
    private SystemTreeManager systemTreeManager;
    
    public CollapsedTreeMenuPanel(JFrame parentFrame) {
        this.parentFrame = parentFrame;
        
        // تهيئة الخط العربي
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        this.menuActions = new HashMap<>();
        
        // تهيئة مدير شجرة النظام
        initializeSystemTreeManager();
        
        initializeMenuActions();
        createTreeStructureFromDatabase();
        setupTreeComponents();
        setupLayout();
        setupEventHandlers();
    }
    
    /**
     * تهيئة مدير شجرة النظام
     */
    private void initializeSystemTreeManager() {
        try {
            systemTreeManager = SystemTreeManager.getInstance();
            System.out.println("✅ تم تهيئة مدير شجرة النظام");
        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة مدير شجرة النظام: " + e.getMessage());
            systemTreeManager = null;
        }
    }
    
    /**
     * إنشاء هيكل الشجرة من قاعدة البيانات
     */
    private void createTreeStructureFromDatabase() {
        try {
            if (systemTreeManager != null) {
                treeModel = systemTreeManager.getSystemTreeModel();
                rootNode = (DefaultMutableTreeNode) treeModel.getRoot();
                System.out.println("✅ تم تحميل شجرة النظام من قاعدة البيانات");
            } else {
                // استخدام الشجرة الافتراضية في حالة عدم توفر قاعدة البيانات
                createDefaultTreeStructure();
                System.out.println("⚠️ تم استخدام الشجرة الافتراضية");
            }
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحميل شجرة النظام من قاعدة البيانات: " + e.getMessage());
            // استخدام الشجرة الافتراضية كبديل
            createDefaultTreeStructure();
        }
    }
    
    /**
     * إنشاء الشجرة الافتراضية
     */
    private void createDefaultTreeStructure() {
        rootNode = new DefaultMutableTreeNode("🚢 نظام إدارة الشحنات");
        treeModel = new DefaultTreeModel(rootNode);
        
        // إدارة الأصناف
        DefaultMutableTreeNode itemsNode = new DefaultMutableTreeNode("📦 إدارة الأصناف");
        itemsNode.add(new DefaultMutableTreeNode("📋 بيانات الأصناف"));
        itemsNode.add(new DefaultMutableTreeNode("🏷️ مجموعات الأصناف"));
        itemsNode.add(new DefaultMutableTreeNode("📏 وحدات القياس"));
        itemsNode.add(new DefaultMutableTreeNode("💰 أسعار الأصناف"));
        rootNode.add(itemsNode);
        
        // إدارة المستخدمين
        DefaultMutableTreeNode usersNode = new DefaultMutableTreeNode("👥 إدارة المستخدمين");
        usersNode.add(new DefaultMutableTreeNode("👤 بيانات المستخدمين"));
        usersNode.add(new DefaultMutableTreeNode("🔐 صلاحيات المستخدمين"));
        usersNode.add(new DefaultMutableTreeNode("📊 تقارير المستخدمين"));
        rootNode.add(usersNode);
        
        // إدارة الشحنات
        DefaultMutableTreeNode shipmentsNode = new DefaultMutableTreeNode("🚛 إدارة الشحنات");
        shipmentsNode.add(new DefaultMutableTreeNode("📝 إنشاء شحنة جديدة"));
        shipmentsNode.add(new DefaultMutableTreeNode("📋 قائمة الشحنات"));
        shipmentsNode.add(new DefaultMutableTreeNode("🔍 تتبع الشحنات"));
        shipmentsNode.add(new DefaultMutableTreeNode("📊 تقارير الشحنات"));
        rootNode.add(shipmentsNode);
        
        // إدارة العملاء
        DefaultMutableTreeNode customersNode = new DefaultMutableTreeNode("🏢 إدارة العملاء");
        customersNode.add(new DefaultMutableTreeNode("👥 بيانات العملاء"));
        customersNode.add(new DefaultMutableTreeNode("📞 جهات الاتصال"));
        customersNode.add(new DefaultMutableTreeNode("💳 حسابات العملاء"));
        customersNode.add(new DefaultMutableTreeNode("📈 تقارير العملاء"));
        rootNode.add(customersNode);
        
        // التقارير والإحصائيات
        DefaultMutableTreeNode reportsNode = new DefaultMutableTreeNode("📊 التقارير والإحصائيات");
        reportsNode.add(new DefaultMutableTreeNode("📈 تقارير المبيعات"));
        reportsNode.add(new DefaultMutableTreeNode("📉 تقارير المصروفات"));
        reportsNode.add(new DefaultMutableTreeNode("💹 تقارير الأرباح"));
        reportsNode.add(new DefaultMutableTreeNode("📋 تقارير شاملة"));
        rootNode.add(reportsNode);
        
        // الإعدادات العامة
        DefaultMutableTreeNode settingsNode = new DefaultMutableTreeNode("⚙️ الإعدادات العامة");
        settingsNode.add(new DefaultMutableTreeNode("🔧 المتغيرات العامة"));
        settingsNode.add(new DefaultMutableTreeNode("💰 إعدادات العملة (قيد التطوير)"));
        settingsNode.add(new DefaultMutableTreeNode("🎨 إعدادات المظهر"));
        settingsNode.add(new DefaultMutableTreeNode("🌐 إعدادات اللغة"));
        settingsNode.add(new DefaultMutableTreeNode("🔧 إعدادات النظام"));
        settingsNode.add(new DefaultMutableTreeNode("💾 النسخ الاحتياطي"));
        rootNode.add(settingsNode);
    }
    
    /**
     * إعداد مكونات الشجرة - في وضع التجميع (مطوية)
     */
    private void setupTreeComponents() {
        menuTree = new JTree(treeModel);
        
        // إعداد الشجرة للنص العربي
        menuTree.setFont(arabicFont);
        menuTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        menuTree.setRootVisible(true);
        menuTree.setShowsRootHandles(true);
        menuTree.setRowHeight(25);
        
        // تخصيص مظهر الشجرة
        menuTree.setCellRenderer(new CustomTreeCellRenderer());
        
        // ⭐ هنا الجزء المهم: عرض الشجرة في وضع التجميع (مطوية)
        collapseAllNodes();
        
        // تحديد العقدة الجذر فقط
        menuTree.setSelectionRow(0);
        
        System.out.println("✅ تم إعداد الشجرة في وضع التجميع (مطوية)");
    }
    
    /**
     * طي جميع العقد - عرض الشجرة في وضع التجميع
     */
    public void collapseAllNodes() {
        // طي جميع العقد ما عدا الجذر
        for (int i = menuTree.getRowCount() - 1; i >= 1; i--) {
            menuTree.collapseRow(i);
        }
        
        // التأكد من أن العقدة الجذر مرئية ومحددة
        menuTree.expandRow(0);
        
        System.out.println("🔽 تم طي جميع العقد - الشجرة في وضع التجميع");
    }
    
    /**
     * توسيع جميع العقد
     */
    public void expandAllNodes() {
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }
        System.out.println("🔼 تم توسيع جميع العقد");
    }
    
    /**
     * توسيع العقد الرئيسية فقط
     */
    public void expandMainNodes() {
        // طي جميع العقد أولاً
        collapseAllNodes();
        
        // توسيع العقد الرئيسية فقط (المستوى الأول)
        DefaultMutableTreeNode root = (DefaultMutableTreeNode) treeModel.getRoot();
        for (int i = 0; i < root.getChildCount(); i++) {
            TreePath path = new TreePath(new Object[]{root, root.getChildAt(i)});
            menuTree.expandPath(path);
        }
        
        System.out.println("📂 تم توسيع العقد الرئيسية فقط");
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setBackground(new Color(248, 249, 250));
        
        // إضافة الشجرة مع شريط التمرير
        JScrollPane scrollPane = new JScrollPane(menuTree);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.getVerticalScrollBar().setUnitIncrement(16);
        
        add(scrollPane, BorderLayout.CENTER);
    }
    
    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        // النقر المزدوج لفتح العناصر
        menuTree.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    TreePath path = menuTree.getPathForLocation(e.getX(), e.getY());
                    if (path != null) {
                        DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();
                        handleNodeDoubleClick(node);
                    }
                }
            }
        });
    }
    
    /**
     * معالجة النقر المزدوج على العقد
     */
    private void handleNodeDoubleClick(DefaultMutableTreeNode node) {
        String nodeText = node.toString();
        System.out.println("🖱️ نقر مزدوج على: " + nodeText);
        
        // إذا كانت العقدة لها أطفال، قم بتوسيعها/طيها
        if (node.getChildCount() > 0) {
            TreePath path = new TreePath(node.getPath());
            if (menuTree.isExpanded(path)) {
                menuTree.collapsePath(path);
                System.out.println("🔽 تم طي: " + nodeText);
            } else {
                menuTree.expandPath(path);
                System.out.println("🔼 تم توسيع: " + nodeText);
            }
        } else {
            // تنفيذ الإجراء المرتبط بالعقدة
            executeNodeAction(nodeText);
        }
    }
    
    /**
     * تنفيذ الإجراء المرتبط بالعقدة
     */
    private void executeNodeAction(String nodeText) {
        Runnable action = menuActions.get(nodeText);
        if (action != null) {
            action.run();
        } else {
            System.out.println("ℹ️ لا يوجد إجراء محدد لـ: " + nodeText);
        }
    }
    
    /**
     * تهيئة إجراءات القائمة
     */
    private void initializeMenuActions() {
        // إدارة الأصناف
        menuActions.put("📋 بيانات الأصناف", () -> openItemsManagement());
        menuActions.put("🏷️ مجموعات الأصناف", () -> openItemGroups());
        menuActions.put("📏 وحدات القياس", () -> openUnitsManagement());

        // إدارة المستخدمين
        menuActions.put("👤 بيانات المستخدمين", () -> openUsersManagement());
        menuActions.put("🔐 صلاحيات المستخدمين", () -> openPermissionsManagement());

        // إدارة الشحنات
        menuActions.put("📝 إنشاء شحنة جديدة", () -> createNewShipment());
        menuActions.put("📋 قائمة الشحنات", () -> openShipmentsList());
        menuActions.put("🔍 تتبع الشحنات", () -> openShipmentTracking());

        // الإعدادات العامة
        menuActions.put("🔧 المتغيرات العامة", () -> openGlobalVariables());
        menuActions.put("💰 إعدادات العملة (قيد التطوير)", () -> openCurrencySettings());
        menuActions.put("🎨 إعدادات المظهر", () -> openThemeSettings());
        menuActions.put("🔧 إعدادات النظام", () -> openSystemSettings());
        
        System.out.println("✅ تم تهيئة إجراءات القائمة");
    }
    
    // إجراءات القائمة
    private void openItemsManagement() {
        System.out.println("🔄 فتح إدارة الأصناف...");
        // هنا يمكن إضافة الكود لفتح نافذة إدارة الأصناف
    }
    
    private void openItemGroups() {
        System.out.println("🔄 فتح مجموعات الأصناف...");
        // هنا يمكن إضافة الكود لفتح نافذة مجموعات الأصناف
    }
    
    private void openUnitsManagement() {
        System.out.println("🔄 فتح إدارة وحدات القياس...");
    }
    
    private void openUsersManagement() {
        System.out.println("🔄 فتح إدارة المستخدمين...");
    }
    
    private void openPermissionsManagement() {
        System.out.println("🔄 فتح إدارة الصلاحيات...");
    }
    
    private void createNewShipment() {
        System.out.println("🔄 إنشاء شحنة جديدة...");
    }
    
    private void openShipmentsList() {
        System.out.println("🔄 فتح قائمة الشحنات...");
    }
    
    private void openShipmentTracking() {
        System.out.println("🔄 فتح تتبع الشحنات...");
    }
    
    private void openThemeSettings() {
        System.out.println("🔄 فتح إعدادات المظهر...");
        // يمكن فتح نافذة إعدادات المظهر
        try {
            ProcessBuilder pb = new ProcessBuilder("java", "-cp", "lib\\*;.", "ThemeTestWindow");
            pb.start();
        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح إعدادات المظهر: " + e.getMessage());
        }
    }
    
    private void openGlobalVariables() {
        System.out.println("🔄 فتح المتغيرات العامة...");
        // هنا يمكن إضافة الكود لفتح نافذة المتغيرات العامة
    }

    private void openCurrencySettings() {
        System.out.println("🔄 فتح إعدادات العملة (قيد التطوير)...");
        try {
            SwingUtilities.invokeLater(() -> {
                CurrencySettingsWindow currencyWindow = new CurrencySettingsWindow();
                currencyWindow.setVisible(true);
            });
        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح نافذة إعدادات العملة: " + e.getMessage());
        }
    }

    private void openSystemSettings() {
        System.out.println("🔄 فتح إعدادات النظام...");
    }
    
    /**
     * البحث في الشجرة
     */
    public void searchInTree(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            collapseAllNodes();
            return;
        }
        
        // توسيع العقد التي تحتوي على النص المطلوب
        searchAndExpand(rootNode, searchText.toLowerCase());
    }
    
    /**
     * البحث وتوسيع العقد المطابقة
     */
    private boolean searchAndExpand(DefaultMutableTreeNode node, String searchText) {
        boolean found = false;
        
        // فحص النص الحالي
        if (node.toString().toLowerCase().contains(searchText)) {
            TreePath path = new TreePath(node.getPath());
            menuTree.expandPath(path);
            found = true;
        }
        
        // فحص العقد الفرعية
        for (int i = 0; i < node.getChildCount(); i++) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) node.getChildAt(i);
            if (searchAndExpand(child, searchText)) {
                TreePath path = new TreePath(node.getPath());
                menuTree.expandPath(path);
                found = true;
            }
        }
        
        return found;
    }
    
    /**
     * تحديث الشجرة من قاعدة البيانات
     */
    public void refreshTree() {
        try {
            if (systemTreeManager != null) {
                DefaultTreeModel newModel = systemTreeManager.getSystemTreeModel();
                menuTree.setModel(newModel);
                
                // عرض الشجرة في وضع التجميع بعد التحديث
                collapseAllNodes();
                
                System.out.println("✅ تم تحديث الشجرة وعرضها في وضع التجميع");
            } else {
                System.out.println("⚠️ مدير شجرة النظام غير متاح");
            }
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحديث الشجرة: " + e.getMessage());
        }
    }

    /**
     * مُعرض خلايا الشجرة المخصص
     */
    private class CustomTreeCellRenderer extends javax.swing.tree.DefaultTreeCellRenderer {

        @Override
        public java.awt.Component getTreeCellRendererComponent(
                JTree tree, Object value, boolean sel, boolean expanded,
                boolean leaf, int row, boolean hasFocus) {

            super.getTreeCellRendererComponent(tree, value, sel, expanded, leaf, row, hasFocus);

            // تطبيق الخط العربي
            setFont(arabicFont);
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            // تخصيص الألوان
            if (sel) {
                setBackgroundSelectionColor(new Color(0, 123, 255, 100));
                setTextSelectionColor(Color.WHITE);
            } else {
                setBackgroundNonSelectionColor(Color.WHITE);
                setTextNonSelectionColor(new Color(33, 37, 41));
            }

            // إخفاء الأيقونات الافتراضية
            setIcon(null);
            setOpenIcon(null);
            setClosedIcon(null);
            setLeafIcon(null);

            return this;
        }
    }
}
