package com.shipment.erp.service;

import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.shipment.erp.model.Role;
import com.shipment.erp.repository.RoleRepository;
import jakarta.validation.Validator;

/**
 * تنفيذ خدمة إدارة الأدوار Role Management Service Implementation
 */
@Service
@Transactional
public class RoleServiceImpl extends BaseServiceImpl<Role> implements RoleService {

    private final RoleRepository roleRepository;

    @Autowired
    public RoleServiceImpl(RoleRepository roleRepository, Validator validator) {
        super(roleRepository, validator);
        this.roleRepository = roleRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Role> findActiveRoles() {
        return roleRepository.findByIsActiveTrue();
    }

    @Override
    @Transactional(readOnly = true)
    public Role findByName(String name) {
        Optional<Role> role = roleRepository.findByName(name);
        return role.orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Role> findByNameContaining(String name) {
        return roleRepository.findByNameContainingIgnoreCase(name);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name) {
        return roleRepository.existsByName(name);
    }

    @Override
    public void toggleActive(Long roleId) {
        Optional<Role> roleOpt = roleRepository.findById(roleId);
        if (roleOpt.isPresent()) {
            Role role = roleOpt.get();
            role.setIsActive(!role.getIsActive());
            roleRepository.save(role);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public long getUsersCount(Long roleId) {
        return roleRepository.getUsersCount(roleId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canDelete(Long roleId) {
        // لا يمكن حذف الدور إذا كان له مستخدمين
        return getUsersCount(roleId) == 0;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Role> getDefaultRoles() {
        return roleRepository.findDefaultRoles();
    }

    @Override
    public Role save(Role role) {
        // التحقق من عدم تكرار الاسم
        if (role.getId() == null && existsByName(role.getName())) {
            throw new RuntimeException("اسم الدور موجود مسبقاً: " + role.getName());
        }

        // التحقق من عدم تكرار الاسم عند التحديث
        if (role.getId() != null) {
            Role existingRole = findByName(role.getName());
            if (existingRole != null && !existingRole.getId().equals(role.getId())) {
                throw new RuntimeException("اسم الدور موجود مسبقاً: " + role.getName());
            }
        }

        return super.save(role);
    }

    @Override
    public void deleteById(Long id) {
        if (!canDelete(id)) {
            throw new RuntimeException("لا يمكن حذف الدور لأنه مرتبط بمستخدمين");
        }
        super.deleteById(id);
    }
}
