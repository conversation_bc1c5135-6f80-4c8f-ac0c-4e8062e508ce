@echo off
echo ========================================
echo   ADVANCED UI SETTINGS WINDOW
echo   نافذة إعدادات الواجهة المتطورة
echo ========================================

cd /d "d:\java\java"

echo.
echo [1] Compiling Advanced UI Settings...
javac -encoding UTF-8 -cp "lib\*" AdvancedUISettingsWindow.java EnhancedSettingsManager.java

if %errorlevel% neq 0 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)

echo ✅ Compilation successful!

echo.
echo [2] Starting Advanced UI Settings Window...
echo    The window will open in a few seconds...
echo    النافذة ستفتح خلال ثوانٍ قليلة...

java -cp "lib\*;." AdvancedUISettingsWindow

echo.
echo ✅ Advanced UI Settings Window closed.
echo ✅ تم إغلاق نافذة إعدادات الواجهة المتطورة.

echo.
echo ========================================
echo   SUMMARY OF FEATURES
echo   ملخص الميزات
echo ========================================
echo.
echo The Advanced UI Settings Window includes:
echo تتضمن نافذة إعدادات الواجهة المتطورة:
echo.
echo 🎨 THEMES (19 themes loaded):
echo    الثيمات (تم تحميل 19 ثيم):
echo    ✅ FlatLaf Light, Dark, IntelliJ, Darcula
echo    ✅ JTattoo Collection (11 themes)
echo    ✅ SeaGlass
echo    ✅ System Default themes
echo.
echo 🔤 FONTS:
echo    الخطوط:
echo    ✅ All system fonts available
echo    ✅ Size adjustment (8-72)
echo    ✅ Arabic support with RTL
echo    ✅ Live preview
echo.
echo 🖥️ INTERFACE:
echo    الواجهة:
echo    ✅ Animations control
echo    ✅ Sounds control
echo    ✅ Tooltips control
echo    ✅ RTL support
echo    ✅ Transparency control
echo.
echo 🌈 COLORS:
echo    الألوان:
echo    ✅ Full color picker
echo    ✅ Accent color selection
echo    ✅ Real-time preview
echo.
echo 💾 DATABASE:
echo    قاعدة البيانات:
echo    ✅ Settings saved to Oracle DB
echo    ✅ Automatic file backup
echo    ✅ Persistent across sessions
echo.

pause
