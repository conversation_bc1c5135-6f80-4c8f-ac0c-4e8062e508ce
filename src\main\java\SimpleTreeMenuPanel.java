import javax.swing.*;
import javax.swing.tree.*;
import java.awt.*;
import java.awt.event.*;

/**
 * لوحة القائمة الشجرية المبسطة
 * Simple Tree Menu Panel
 */
public class SimpleTreeMenuPanel extends JFrame {
    
    private Font arabicFont;
    private JTree menuTree;
    private DefaultMutableTreeNode rootNode;
    
    public SimpleTreeMenuPanel() {
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        initializeComponents();
        setupLayout();
        
        setTitle("نظام إدارة الشحنات - الإصدار المبسط");
        setSize(1200, 800);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    private void initializeComponents() {
        createMenuTree();
    }
    
    private void createMenuTree() {
        rootNode = new DefaultMutableTreeNode("نظام إدارة الشحنات");
        
        // إدارة الشركات والفروع
        DefaultMutableTreeNode companyNode = new DefaultMutableTreeNode("إدارة الشركات والفروع");
        companyNode.add(new DefaultMutableTreeNode("إدارة الشركات"));
        companyNode.add(new DefaultMutableTreeNode("إدارة الفروع"));
        companyNode.add(new DefaultMutableTreeNode("إدارة الأدوار"));
        rootNode.add(companyNode);
        
        // الإعدادات المالية
        DefaultMutableTreeNode financialNode = new DefaultMutableTreeNode("الإعدادات المالية");
        financialNode.add(new DefaultMutableTreeNode("إدارة العملات"));
        financialNode.add(new DefaultMutableTreeNode("إدارة السنوات المالية"));
        financialNode.add(new DefaultMutableTreeNode("سجل التدقيق"));
        rootNode.add(financialNode);
        
        menuTree = new JTree(rootNode);
        menuTree.setFont(arabicFont);
        menuTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إضافة مستمع النقر
        menuTree.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    handleMenuSelection();
                }
            }
        });
        
        // توسيع العقد
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // شريط القوائم
        JMenuBar menuBar = createMenuBar();
        setJMenuBar(menuBar);
        
        // اللوحة الرئيسية
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // الشجرة
        JScrollPane treeScrollPane = new JScrollPane(menuTree);
        treeScrollPane.setPreferredSize(new Dimension(300, 600));
        treeScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // المنطقة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createTitledBorder("المنطقة الرئيسية"));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel welcomeLabel = new JLabel("مرحباً بك في نظام إدارة الشحنات", SwingConstants.CENTER);
        welcomeLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        welcomeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.add(welcomeLabel, BorderLayout.CENTER);
        
        splitPane.setLeftComponent(treeScrollPane);
        splitPane.setRightComponent(mainPanel);
        splitPane.setDividerLocation(300);
        
        add(splitPane, BorderLayout.CENTER);
        
        // شريط الحالة
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        statusPanel.setBorder(BorderFactory.createEtchedBorder());
        statusPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);
        statusPanel.add(statusLabel);
        
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    private JMenuBar createMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        menuBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // قائمة الملف
        JMenu fileMenu = new JMenu("ملف");
        fileMenu.setFont(arabicFont);
        fileMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JMenuItem exitItem = new JMenuItem("خروج");
        exitItem.setFont(arabicFont);
        exitItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        exitItem.addActionListener(e -> System.exit(0));
        
        fileMenu.add(exitItem);
        menuBar.add(fileMenu);
        
        // قائمة المساعدة
        JMenu helpMenu = new JMenu("مساعدة");
        helpMenu.setFont(arabicFont);
        helpMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JMenuItem aboutItem = new JMenuItem("حول البرنامج");
        aboutItem.setFont(arabicFont);
        aboutItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        aboutItem.addActionListener(e -> showAboutDialog());
        
        helpMenu.add(aboutItem);
        menuBar.add(helpMenu);
        
        return menuBar;
    }
    
    private void handleMenuSelection() {
        DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) menuTree.getLastSelectedPathComponent();
        
        if (selectedNode == null) return;
        
        String nodeName = selectedNode.toString();
        
        try {
            switch (nodeName) {
                case "إدارة الشركات":
                    openCompanyManagementWindow();
                    break;
                case "إدارة الفروع":
                    openBranchManagementWindow();
                    break;
                case "إدارة الأدوار":
                    openRoleManagementWindow();
                    break;
                case "إدارة العملات":
                    openCurrencyManagementWindow();
                    break;
                case "إدارة السنوات المالية":
                    openFiscalYearManagementWindow();
                    break;
                case "سجل التدقيق":
                    openAuditLogWindow();
                    break;
                default:
                    showMessage("النافذة: " + nodeName + " قيد التطوير");
                    break;
            }
        } catch (Exception e) {
            showMessage("خطأ في فتح النافذة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void openCompanyManagementWindow() {
        com.shipment.erp.view.CompanyManagementWindow window = 
            new com.shipment.erp.view.CompanyManagementWindow(this);
        window.setVisible(true);
    }
    
    private void openBranchManagementWindow() {
        com.shipment.erp.view.BranchManagementWindow window = 
            new com.shipment.erp.view.BranchManagementWindow(this);
        window.setVisible(true);
    }
    
    private void openRoleManagementWindow() {
        com.shipment.erp.view.RoleManagementWindow window = 
            new com.shipment.erp.view.RoleManagementWindow(this);
        window.setVisible(true);
    }
    
    private void openCurrencyManagementWindow() {
        com.shipment.erp.view.CurrencyManagementWindow window = 
            new com.shipment.erp.view.CurrencyManagementWindow(this);
        window.setVisible(true);
    }
    
    private void openFiscalYearManagementWindow() {
        com.shipment.erp.view.FiscalYearManagementWindow window = 
            new com.shipment.erp.view.FiscalYearManagementWindow(this);
        window.setVisible(true);
    }
    
    private void openAuditLogWindow() {
        com.shipment.erp.view.AuditLogWindow window = 
            new com.shipment.erp.view.AuditLogWindow(this);
        window.setVisible(true);
    }
    
    private void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "رسالة", JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void showAboutDialog() {
        String aboutText = "نظام إدارة الشحنات المتقدم\n" + 
                          "الإصدار: 2.0\n" +
                          "تطوير: فريق التطوير\n" + 
                          "حقوق الطبع محفوظة © 2025";
        
        JOptionPane.showMessageDialog(this, aboutText, "حول البرنامج", JOptionPane.INFORMATION_MESSAGE);
    }
    
    public static void main(String[] args) {
        // تعيين Look and Feel
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        // تشغيل التطبيق
        SwingUtilities.invokeLater(() -> {
            new SimpleTreeMenuPanel().setVisible(true);
        });
    }
}
