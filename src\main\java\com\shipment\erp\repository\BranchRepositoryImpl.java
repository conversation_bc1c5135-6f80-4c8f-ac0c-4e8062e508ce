package com.shipment.erp.repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import javax.sql.DataSource;
import com.shipment.erp.model.Branch;

/**
 * تنفيذ مستودع بيانات الفروع Branch Data Repository Implementation
 */
public class BranchRepositoryImpl extends SimpleBaseRepositoryImpl<Branch>
        implements SimpleBranchRepository {

    public BranchRepositoryImpl(DataSource dataSource) {
        super(dataSource);
    }

    @Override
    protected String getTableName() {
        return "BRANCHES";
    }

    @Override
    protected String getSequenceName() {
        return "SEQ_BRANCH";
    }

    @Override
    protected Branch mapResultSetToEntity(ResultSet rs) throws SQLException {
        Branch branch = new Branch();
        branch.setId(rs.getLong("ID"));
        branch.setCompanyId(rs.getLong("COMPANY_ID"));
        branch.setCode(rs.getString("CODE"));
        branch.setName(rs.getString("NAME"));
        branch.setNameEn(rs.getString("NAME_EN"));
        branch.setAddress(rs.getString("ADDRESS"));
        branch.setManagerName(rs.getString("MANAGER_NAME"));
        branch.setPhone(rs.getString("PHONE"));
        branch.setEmail(rs.getString("EMAIL"));
        branch.setActive(rs.getBoolean("IS_ACTIVE"));
        branch.setCreatedBy(rs.getLong("CREATED_BY"));
        branch.setCreatedDate(rs.getTimestamp("CREATED_DATE"));
        branch.setModifiedBy(rs.getLong("MODIFIED_BY"));
        branch.setModifiedDate(rs.getTimestamp("MODIFIED_DATE"));
        return branch;
    }

    @Override
    protected void setEntityParameters(PreparedStatement ps, Branch branch, boolean isUpdate)
            throws SQLException {
        int index = 1;

        if (!isUpdate) {
            ps.setLong(index++, branch.getCompanyId());
        }

        ps.setString(index++, branch.getCode());
        ps.setString(index++, branch.getName());
        ps.setString(index++, branch.getNameEn());
        ps.setString(index++, branch.getAddress());
        ps.setString(index++, branch.getManagerName());
        ps.setString(index++, branch.getPhone());
        ps.setString(index++, branch.getEmail());
        ps.setBoolean(index++, branch.isActive());

        if (isUpdate) {
            ps.setLong(index++, branch.getModifiedBy());
            ps.setTimestamp(index++, new Timestamp(System.currentTimeMillis()));
            ps.setLong(index++, branch.getId());
        } else {
            ps.setLong(index++, branch.getCreatedBy());
            ps.setTimestamp(index++, new Timestamp(System.currentTimeMillis()));
        }
    }

    @Override
    protected String getInsertSql() {
        return "INSERT INTO BRANCHES (ID, COMPANY_ID, CODE, NAME, NAME_EN, ADDRESS, MANAGER_NAME, PHONE, EMAIL, IS_ACTIVE, CREATED_BY, CREATED_DATE) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    }

    @Override
    protected String getUpdateSql() {
        return "UPDATE BRANCHES SET CODE = ?, NAME = ?, NAME_EN = ?, ADDRESS = ?, MANAGER_NAME = ?, PHONE = ?, EMAIL = ?, IS_ACTIVE = ?, MODIFIED_BY = ?, MODIFIED_DATE = ? WHERE ID = ?";
    }

    @Override
    public List<Branch> findByCompanyId(Long companyId) {
        String sql = "SELECT * FROM BRANCHES WHERE COMPANY_ID = ? ORDER BY NAME";
        return executeQuery(sql, companyId);
    }

    @Override
    public List<Branch> findByCompanyIdAndIsActiveTrue(Long companyId) {
        String sql = "SELECT * FROM BRANCHES WHERE COMPANY_ID = ? AND IS_ACTIVE = 1 ORDER BY NAME";
        return executeQuery(sql, companyId);
    }

    @Override
    public Optional<Branch> findByCode(String code) {
        String sql = "SELECT * FROM BRANCHES WHERE CODE = ?";
        List<Branch> branches = executeQuery(sql, code);
        return branches.isEmpty() ? Optional.empty() : Optional.of(branches.get(0));
    }

    @Override
    public List<Branch> findByNameContainingIgnoreCase(String name) {
        String sql = "SELECT * FROM BRANCHES WHERE UPPER(NAME) LIKE UPPER(?) ORDER BY NAME";
        return executeQuery(sql, "%" + name + "%");
    }

    @Override
    public boolean existsByCode(String code) {
        String sql = "SELECT COUNT(*) FROM BRANCHES WHERE CODE = ?";
        try (Connection conn = dataSource.getConnection();
                PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setString(1, code);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() && rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            throw new RuntimeException("خطأ في التحقق من وجود كود الفرع", e);
        }
    }

    @Override
    public long countByCompanyIdAndIsActiveTrue(Long companyId) {
        String sql = "SELECT COUNT(*) FROM BRANCHES WHERE COMPANY_ID = ? AND IS_ACTIVE = 1";
        try (Connection conn = dataSource.getConnection();
                PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setLong(1, companyId);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? rs.getLong(1) : 0;
            }
        } catch (SQLException e) {
            throw new RuntimeException("خطأ في عد الفروع النشطة", e);
        }
    }

    @Override
    public List<Branch> findByManagerNameContainingIgnoreCase(String managerName) {
        String sql = "SELECT * FROM BRANCHES WHERE UPPER(MANAGER_NAME) LIKE UPPER(?) ORDER BY NAME";
        return executeQuery(sql, "%" + managerName + "%");
    }

    @Override
    public Optional<Branch> findByPhone(String phone) {
        String sql = "SELECT * FROM BRANCHES WHERE PHONE = ?";
        List<Branch> branches = executeQuery(sql, phone);
        return branches.isEmpty() ? Optional.empty() : Optional.of(branches.get(0));
    }

    @Override
    public Optional<Branch> findByEmail(String email) {
        String sql = "SELECT * FROM BRANCHES WHERE EMAIL = ?";
        List<Branch> branches = executeQuery(sql, email);
        return branches.isEmpty() ? Optional.empty() : Optional.of(branches.get(0));
    }
}
