import java.sql.*;
import java.util.Properties;

/**
 * فحص بنية جدول ERP_MEASUREMENT
 * Check ERP_MEASUREMENT Table Structure
 */
public class CheckERPMeasurementTable {
    
    public static void main(String[] args) {
        try {
            // تحميل Oracle JDBC Driver
            Class.forName("oracle.jdbc.OracleDriver");
            System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            
            // إعداد خصائص الاتصال
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            
            try (Connection conn = DriverManager.getConnection(url, props)) {
                System.out.println("✅ تم الاتصال بقاعدة البيانات SHIP_ERP بنجاح");
                
                // فحص وجود الجدول
                checkTableExists(conn);
                
                // فحص بنية الجدول
                checkTableStructure(conn);
                
                // فحص البيانات الموجودة
                checkTableData(conn);
                
            }
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkTableExists(Connection conn) throws SQLException {
        System.out.println("\n🔍 فحص وجود جدول ERP_MEASUREMENT...");
        
        String sql = "SELECT COUNT(*) FROM user_tables WHERE table_name = 'ERP_MEASUREMENT'";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next() && rs.getInt(1) > 0) {
                System.out.println("✅ جدول ERP_MEASUREMENT موجود");
            } else {
                System.out.println("❌ جدول ERP_MEASUREMENT غير موجود");
                return;
            }
        }
    }
    
    private static void checkTableStructure(Connection conn) throws SQLException {
        System.out.println("\n📋 بنية جدول ERP_MEASUREMENT:");
        System.out.println("=" + "=".repeat(80));
        System.out.printf("%-25s %-15s %-10s %-10s%n", "اسم العمود", "نوع البيانات", "الطول", "NULL؟");
        System.out.println("-".repeat(80));
        
        String sql = """
            SELECT column_name, data_type, data_length, nullable, column_id
            FROM user_tab_columns 
            WHERE table_name = 'ERP_MEASUREMENT' 
            ORDER BY column_id
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String columnName = rs.getString("COLUMN_NAME");
                String dataType = rs.getString("DATA_TYPE");
                int dataLength = rs.getInt("DATA_LENGTH");
                String nullable = rs.getString("NULLABLE");
                
                System.out.printf("%-25s %-15s %-10d %-10s%n", 
                    columnName, dataType, dataLength, nullable);
            }
        }
    }
    
    private static void checkTableData(Connection conn) throws SQLException {
        System.out.println("\n📊 بيانات جدول ERP_MEASUREMENT:");
        System.out.println("=" + "=".repeat(80));
        
        // عد الصفوف
        String countSql = "SELECT COUNT(*) FROM ERP_MEASUREMENT";
        try (PreparedStatement stmt = conn.prepareStatement(countSql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                int rowCount = rs.getInt(1);
                System.out.println("📈 إجمالي الصفوف: " + rowCount);
            }
        }
        
        // عرض عينة من البيانات
        System.out.println("\n🔍 عينة من البيانات (أول 5 صفوف):");
        String dataSql = "SELECT * FROM ERP_MEASUREMENT WHERE ROWNUM <= 5";
        
        try (PreparedStatement stmt = conn.prepareStatement(dataSql);
             ResultSet rs = stmt.executeQuery()) {
            
            // الحصول على metadata
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            // طباعة أسماء الأعمدة
            for (int i = 1; i <= columnCount; i++) {
                System.out.printf("%-15s ", metaData.getColumnName(i));
            }
            System.out.println();
            System.out.println("-".repeat(columnCount * 16));
            
            // طباعة البيانات
            while (rs.next()) {
                for (int i = 1; i <= columnCount; i++) {
                    String value = rs.getString(i);
                    if (value == null) value = "NULL";
                    if (value.length() > 14) value = value.substring(0, 11) + "...";
                    System.out.printf("%-15s ", value);
                }
                System.out.println();
            }
        }
        
        System.out.println("\n✅ تم فحص جدول ERP_MEASUREMENT بنجاح!");
    }
}
