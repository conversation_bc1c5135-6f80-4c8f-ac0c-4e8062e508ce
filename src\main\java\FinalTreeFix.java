import java.sql.*;
import java.util.Properties;

/**
 * إصلاح نهائي لشجرة النظام
 * Final Tree Fix
 */
public class FinalTreeFix {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔧 الإصلاح النهائي لشجرة النظام...");
            
            Connection connection = getConnection();
            
            // إصلاح PARENT_ID للفئات
            fixCategoryParents(connection);
            
            connection.close();
            
            System.out.println("✅ تم الإصلاح النهائي بنجاح!");
            
            // اختبار النتيجة
            testResult();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في الإصلاح النهائي: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    private static void fixCategoryParents(Connection conn) throws SQLException {
        System.out.println("📋 إصلاح PARENT_ID للفئات...");
        
        // إصلاح إدارة المستخدمين
        String sql1 = "UPDATE ERP_SYSTEM_TREE SET PARENT_ID = 1 WHERE NODE_NAME_AR = 'إدارة المستخدمين' AND NODE_TYPE = 'CATEGORY'";
        conn.createStatement().executeUpdate(sql1);
        System.out.println("  ✅ تم إصلاح إدارة المستخدمين");
        
        // إصلاح الإعدادات العامة
        String sql2 = "UPDATE ERP_SYSTEM_TREE SET PARENT_ID = 1 WHERE NODE_NAME_AR = 'الإعدادات العامة' AND NODE_TYPE = 'CATEGORY'";
        conn.createStatement().executeUpdate(sql2);
        System.out.println("  ✅ تم إصلاح الإعدادات العامة");
        
        // إصلاح التقارير
        String sql3 = "UPDATE ERP_SYSTEM_TREE SET PARENT_ID = 1 WHERE NODE_NAME_AR = 'التقارير' AND NODE_TYPE = 'CATEGORY'";
        conn.createStatement().executeUpdate(sql3);
        System.out.println("  ✅ تم إصلاح التقارير");
        
        // إصلاح أدوات النظام
        String sql4 = "UPDATE ERP_SYSTEM_TREE SET PARENT_ID = 1 WHERE NODE_NAME_AR = 'أدوات النظام' AND NODE_TYPE = 'CATEGORY'";
        conn.createStatement().executeUpdate(sql4);
        System.out.println("  ✅ تم إصلاح أدوات النظام");
        
        // إصلاح إدارة الأصناف
        String sql5 = "UPDATE ERP_SYSTEM_TREE SET PARENT_ID = 1 WHERE NODE_NAME_AR = 'إدارة الأصناف' AND NODE_TYPE = 'CATEGORY'";
        conn.createStatement().executeUpdate(sql5);
        System.out.println("  ✅ تم إصلاح إدارة الأصناف");
        
        System.out.println("✅ تم إصلاح جميع الفئات");
    }
    
    private static void testResult() {
        System.out.println("\n🔍 اختبار النتيجة النهائية...");
        
        try {
            SystemTreeManager manager = SystemTreeManager.getInstance();
            manager.printSystemTree();
            manager.close();
        } catch (Exception e) {
            System.err.println("❌ خطأ في اختبار النتيجة: " + e.getMessage());
        }
    }
}
