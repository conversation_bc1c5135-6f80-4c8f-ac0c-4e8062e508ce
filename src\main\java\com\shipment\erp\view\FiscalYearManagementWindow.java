package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.text.SimpleDateFormat;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSeparator;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumnModel;
import com.shipment.erp.model.FiscalYear;
import com.shipment.erp.service.FiscalYearService;

/**
 * نافذة إدارة السنوات المالية Fiscal Year Management Window
 */
public class FiscalYearManagementWindow extends JDialog {

    private Font arabicFont;
    private JTable fiscalYearsTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> statusFilterCombo;

    private FiscalYearService fiscalYearService;
    private List<FiscalYear> fiscalYearsList;

    public FiscalYearManagementWindow(JFrame parent) {
        super(parent, "إدارة السنوات المالية", true);

        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeServices();
        initializeComponents();
        setupLayout();
        loadFiscalYearsData();

        setSize(1100, 700);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(true);
        setMinimumSize(new Dimension(900, 500));
    }

    private void initializeServices() {
        // TODO: Initialize FiscalYearService through dependency injection
        // fiscalYearService = ApplicationContext.getBean(FiscalYearService.class);
    }

    private void initializeComponents() {
        // شريط الأدوات العلوي
        JPanel toolbarPanel = createToolbarPanel();

        // جدول السنوات المالية
        createFiscalYearsTable();
        JScrollPane tableScrollPane = new JScrollPane(fiscalYearsTable);
        tableScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();

        // تخطيط النافذة
        setLayout(new BorderLayout());
        add(toolbarPanel, BorderLayout.NORTH);
        add(tableScrollPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }

    private JPanel createToolbarPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // أزرار العمليات
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addButton = new JButton("إضافة سنة مالية");
        addButton.setFont(arabicFont);
        addButton.addActionListener(e -> addFiscalYear());

        JButton editButton = new JButton("تعديل");
        editButton.setFont(arabicFont);
        editButton.addActionListener(e -> editFiscalYear());

        JButton deleteButton = new JButton("حذف");
        deleteButton.setFont(arabicFont);
        deleteButton.addActionListener(e -> deleteFiscalYear());

        JButton setCurrentButton = new JButton("تعيين كحالية");
        setCurrentButton.setFont(arabicFont);
        setCurrentButton.addActionListener(e -> setCurrentFiscalYear());

        JButton closeButton = new JButton("إغلاق السنة");
        closeButton.setFont(arabicFont);
        closeButton.addActionListener(e -> closeFiscalYear());

        JButton openButton = new JButton("فتح السنة");
        openButton.setFont(arabicFont);
        openButton.addActionListener(e -> openFiscalYear());

        JButton refreshButton = new JButton("تحديث");
        refreshButton.setFont(arabicFont);
        refreshButton.addActionListener(e -> loadFiscalYearsData());

        buttonsPanel.add(addButton);
        buttonsPanel.add(editButton);
        buttonsPanel.add(deleteButton);
        buttonsPanel.add(new JSeparator(SwingConstants.VERTICAL));
        buttonsPanel.add(setCurrentButton);
        buttonsPanel.add(closeButton);
        buttonsPanel.add(openButton);
        buttonsPanel.add(new JSeparator(SwingConstants.VERTICAL));
        buttonsPanel.add(refreshButton);

        // شريط البحث والفلترة
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);

        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                filterFiscalYears();
            }
        });

        JLabel statusLabel = new JLabel("الحالة:");
        statusLabel.setFont(arabicFont);

        statusFilterCombo = new JComboBox<>(new String[] {"الكل", "مفتوحة", "مغلقة", "حالية"});
        statusFilterCombo.setFont(arabicFont);
        statusFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusFilterCombo.addActionListener(e -> filterFiscalYears());

        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        searchPanel.add(Box.createHorizontalStrut(10));
        searchPanel.add(statusLabel);
        searchPanel.add(statusFilterCombo);

        panel.add(buttonsPanel, BorderLayout.EAST);
        panel.add(searchPanel, BorderLayout.WEST);

        return panel;
    }

    private void createFiscalYearsTable() {
        String[] columnNames = {"الرقم", "اسم السنة", "تاريخ البداية", "تاريخ النهاية", "حالية",
                "مغلقة", "المدة (أيام)", "تاريخ الإنشاء"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        fiscalYearsTable = new JTable(tableModel);
        fiscalYearsTable.setFont(arabicFont);
        fiscalYearsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        fiscalYearsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        fiscalYearsTable.setRowHeight(25);

        // تخصيص عرض الأعمدة
        TableColumnModel columnModel = fiscalYearsTable.getColumnModel();
        columnModel.getColumn(0).setPreferredWidth(60); // الرقم
        columnModel.getColumn(1).setPreferredWidth(120); // اسم السنة
        columnModel.getColumn(2).setPreferredWidth(120); // تاريخ البداية
        columnModel.getColumn(3).setPreferredWidth(120); // تاريخ النهاية
        columnModel.getColumn(4).setPreferredWidth(80); // حالية
        columnModel.getColumn(5).setPreferredWidth(80); // مغلقة
        columnModel.getColumn(6).setPreferredWidth(100); // المدة
        columnModel.getColumn(7).setPreferredWidth(120); // تاريخ الإنشاء

        // تخصيص رأس الجدول
        JTableHeader header = fiscalYearsTable.getTableHeader();
        header.setFont(arabicFont);
        header.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إضافة مستمع النقر المزدوج
        fiscalYearsTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    editFiscalYear();
                }
            }
        });
    }

    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);

        panel.add(statusLabel);
        return panel;
    }

    private void loadFiscalYearsData() {
        // TODO: Load data from FiscalYearService
        // fiscalYearsList = fiscalYearService.findAll();

        // بيانات تجريبية
        fiscalYearsList = createSampleData();
        updateTableData();
    }

    private List<FiscalYear> createSampleData() {
        FiscalYear fy1 = new FiscalYear();
        fy1.setId(1L);
        fy1.setYearName("2024");
        fy1.setStartDate(java.time.LocalDate.of(2024, 1, 1));
        fy1.setEndDate(java.time.LocalDate.of(2024, 12, 31));
        fy1.setCurrent(false);
        fy1.setClosed(true);
        fy1.setCreatedDate(java.time.LocalDateTime.now());

        FiscalYear fy2 = new FiscalYear();
        fy2.setId(2L);
        fy2.setYearName("2025");
        fy2.setStartDate(java.time.LocalDate.of(2025, 1, 1));
        fy2.setEndDate(java.time.LocalDate.of(2025, 12, 31));
        fy2.setCurrent(true);
        fy2.setClosed(false);
        fy2.setCreatedDate(java.time.LocalDateTime.now());

        return java.util.Arrays.asList(fy1, fy2);
    }

    private void updateTableData() {
        tableModel.setRowCount(0);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (FiscalYear fiscalYear : fiscalYearsList) {
            long duration = 0;
            if (fiscalYear.getStartDate() != null && fiscalYear.getEndDate() != null) {
                duration = java.time.temporal.ChronoUnit.DAYS.between(fiscalYear.getStartDate(),
                        fiscalYear.getEndDate());
            }

            Object[] row = {fiscalYear.getId(), fiscalYear.getYearName(),
                    fiscalYear.getStartDate() != null ? dateFormat.format(fiscalYear.getStartDate())
                            : "",
                    fiscalYear.getEndDate() != null ? dateFormat.format(fiscalYear.getEndDate())
                            : "",
                    fiscalYear.isCurrent() ? "نعم" : "لا", fiscalYear.isClosed() ? "نعم" : "لا",
                    duration + " يوم",
                    fiscalYear.getCreatedDate() != null
                            ? dateFormat.format(fiscalYear.getCreatedDate())
                            : ""};
            tableModel.addRow(row);
        }
    }

    private void filterFiscalYears() {
        // TODO: Implement filtering logic
        updateTableData();
    }

    private void addFiscalYear() {
        FiscalYearFormDialog dialog = new FiscalYearFormDialog(this, "إضافة سنة مالية جديدة", null);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            FiscalYear newFiscalYear = dialog.getFiscalYear();
            // TODO: Save using FiscalYearService
            // fiscalYearService.save(newFiscalYear);
            loadFiscalYearsData();
        }
    }

    private void editFiscalYear() {
        int selectedRow = fiscalYearsTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار سنة مالية للتعديل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        FiscalYear selectedFiscalYear = fiscalYearsList.get(selectedRow);
        FiscalYearFormDialog dialog =
                new FiscalYearFormDialog(this, "تعديل السنة المالية", selectedFiscalYear);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            FiscalYear updatedFiscalYear = dialog.getFiscalYear();
            // TODO: Update using FiscalYearService
            // fiscalYearService.save(updatedFiscalYear);
            loadFiscalYearsData();
        }
    }

    private void deleteFiscalYear() {
        int selectedRow = fiscalYearsTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار سنة مالية للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        FiscalYear selectedFiscalYear = fiscalYearsList.get(selectedRow);

        if (selectedFiscalYear.isCurrent()) {
            JOptionPane.showMessageDialog(this, "لا يمكن حذف السنة المالية الحالية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return;
        }

        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف السنة المالية: " + selectedFiscalYear.getYearName() + "؟\n"
                        + "تحذير: سيتم حذف جميع البيانات المرتبطة بهذه السنة",
                "تأكيد الحذف", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Delete using FiscalYearService
            // fiscalYearService.delete(selectedFiscalYear.getId());
            loadFiscalYearsData();
        }
    }

    private void setCurrentFiscalYear() {
        int selectedRow = fiscalYearsTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار سنة مالية لتعيينها كحالية", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        FiscalYear selectedFiscalYear = fiscalYearsList.get(selectedRow);

        int result =
                JOptionPane.showConfirmDialog(
                        this, "هل أنت متأكد من تعيين السنة المالية: "
                                + selectedFiscalYear.getYearName() + " كسنة حالية؟",
                        "تأكيد التعيين", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Set current using FiscalYearService
            // fiscalYearService.setCurrentFiscalYear(selectedFiscalYear.getId());
            loadFiscalYearsData();
        }
    }

    private void closeFiscalYear() {
        int selectedRow = fiscalYearsTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار سنة مالية لإغلاقها", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        FiscalYear selectedFiscalYear = fiscalYearsList.get(selectedRow);

        if (selectedFiscalYear.isClosed()) {
            JOptionPane.showMessageDialog(this, "السنة المالية مغلقة بالفعل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من إغلاق السنة المالية: " + selectedFiscalYear.getYearName() + "؟\n"
                        + "تحذير: لن تتمكن من إجراء تعديلات على هذه السنة بعد الإغلاق",
                "تأكيد الإغلاق", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Close using FiscalYearService
            // fiscalYearService.closeFiscalYear(selectedFiscalYear.getId());
            loadFiscalYearsData();
        }
    }

    private void openFiscalYear() {
        int selectedRow = fiscalYearsTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار سنة مالية لفتحها", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        FiscalYear selectedFiscalYear = fiscalYearsList.get(selectedRow);

        if (!selectedFiscalYear.isClosed()) {
            JOptionPane.showMessageDialog(this, "السنة المالية مفتوحة بالفعل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من فتح السنة المالية: " + selectedFiscalYear.getYearName() + "؟",
                "تأكيد الفتح", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Open using FiscalYearService
            // fiscalYearService.openFiscalYear(selectedFiscalYear.getId());
            loadFiscalYearsData();
        }
    }
}
