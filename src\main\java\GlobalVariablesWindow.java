import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JComponent;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;

/**
 * نافذة المتغيرات العامة للتطبيق Global Variables Window
 */
public class GlobalVariablesWindow extends JFrame {

    private JTable variablesTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> categoryFilter;
    private JComboBox<String> groupFilter;
    private JButton addButton, editButton, deleteButton, refreshButton, saveButton;
    private JPanel detailsPanel;
    private Map<String, JComponent> detailFields;
    private Connection connection;
    private TNSConnectionManager tnsManager;
    private Font arabicFont;

    public GlobalVariablesWindow() {
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        connectToDatabase();
        loadVariables();

        setTitle("المتغيرات العامة للتطبيق - Global Variables");
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
    }

    private void initializeComponents() {
        // إعداد الخط العربي
        try {
            arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        } catch (Exception e) {
            arabicFont = new Font(Font.SANS_SERIF, Font.PLAIN, 12);
        }

        // إعداد الجدول
        String[] columnNames = {"المعرف", "اسم المتغير", "الاسم العربي", "القيمة", "النوع", "الفئة",
                "المجموعة", "الأولوية", "مطلوب", "نشط"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // منع التعديل المباشر في الجدول
            }
        };

        variablesTable = new JTable(tableModel);
        variablesTable.setFont(arabicFont);
        variablesTable.setRowHeight(25);
        variablesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        variablesTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إعداد عرض الأعمدة
        TableColumnModel columnModel = variablesTable.getColumnModel();
        columnModel.getColumn(0).setPreferredWidth(60); // المعرف
        columnModel.getColumn(1).setPreferredWidth(120); // اسم المتغير
        columnModel.getColumn(2).setPreferredWidth(150); // الاسم العربي
        columnModel.getColumn(3).setPreferredWidth(200); // القيمة
        columnModel.getColumn(4).setPreferredWidth(80); // النوع
        columnModel.getColumn(5).setPreferredWidth(100); // الفئة
        columnModel.getColumn(6).setPreferredWidth(100); // المجموعة
        columnModel.getColumn(7).setPreferredWidth(80); // الأولوية
        columnModel.getColumn(8).setPreferredWidth(60); // مطلوب
        columnModel.getColumn(9).setPreferredWidth(60); // نشط

        // إعداد مكونات البحث والفلترة
        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        categoryFilter = new JComboBox<>(
                new String[] {"جميع الفئات", "SYSTEM", "UI", "DATABASE", "SECURITY", "BUSINESS"});
        categoryFilter.setFont(arabicFont);

        groupFilter = new JComboBox<>(
                new String[] {"جميع المجموعات", "APPLICATION", "LOCALIZATION", "FORMATTING",
                        "SESSION", "AUTHENTICATION", "BACKUP", "LOGGING", "COMPANY", "FINANCIAL"});
        groupFilter.setFont(arabicFont);

        // إعداد الأزرار
        addButton = new JButton("إضافة متغير جديد");
        editButton = new JButton("تعديل المتغير");
        deleteButton = new JButton("حذف المتغير");
        refreshButton = new JButton("تحديث البيانات");
        saveButton = new JButton("حفظ التغييرات");

        JButton[] buttons = {addButton, editButton, deleteButton, refreshButton, saveButton};
        for (JButton button : buttons) {
            button.setFont(arabicFont);
            button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        }

        // إعداد لوحة التفاصيل
        setupDetailsPanel();
    }

    private void setupDetailsPanel() {
        detailsPanel = new JPanel(new GridBagLayout());
        detailsPanel.setBorder(new TitledBorder("تفاصيل المتغير"));
        detailsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        detailFields = new HashMap<>();
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // الصف الأول
        gbc.gridx = 0;
        gbc.gridy = 0;
        detailsPanel.add(new JLabel("اسم المتغير:"), gbc);
        gbc.gridx = 1;
        JTextField varNameField = new JTextField(15);
        varNameField.setFont(arabicFont);
        detailFields.put("VAR_NAME", varNameField);
        detailsPanel.add(varNameField, gbc);

        gbc.gridx = 2;
        detailsPanel.add(new JLabel("الاسم العربي:"), gbc);
        gbc.gridx = 3;
        JTextField varNameArField = new JTextField(15);
        varNameArField.setFont(arabicFont);
        varNameArField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailFields.put("VAR_NAME_AR", varNameArField);
        detailsPanel.add(varNameArField, gbc);

        // الصف الثاني
        gbc.gridx = 0;
        gbc.gridy = 1;
        detailsPanel.add(new JLabel("الاسم الإنجليزي:"), gbc);
        gbc.gridx = 1;
        JTextField varNameEnField = new JTextField(15);
        varNameEnField.setFont(arabicFont);
        detailFields.put("VAR_NAME_EN", varNameEnField);
        detailsPanel.add(varNameEnField, gbc);

        gbc.gridx = 2;
        detailsPanel.add(new JLabel("القيمة:"), gbc);
        gbc.gridx = 3;
        JTextField varValueField = new JTextField(15);
        varValueField.setFont(arabicFont);
        varValueField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailFields.put("VAR_VALUE", varValueField);
        detailsPanel.add(varValueField, gbc);

        // الصف الثالث
        gbc.gridx = 0;
        gbc.gridy = 2;
        detailsPanel.add(new JLabel("نوع البيانات:"), gbc);
        gbc.gridx = 1;
        JComboBox<String> dataTypeCombo = new JComboBox<>(
                new String[] {"STRING", "NUMBER", "BOOLEAN", "DATE", "JSON", "XML"});
        dataTypeCombo.setFont(arabicFont);
        detailFields.put("DATA_TYPE", dataTypeCombo);
        detailsPanel.add(dataTypeCombo, gbc);

        gbc.gridx = 2;
        detailsPanel.add(new JLabel("الفئة:"), gbc);
        gbc.gridx = 3;
        JComboBox<String> categoryCombo =
                new JComboBox<>(new String[] {"SYSTEM", "UI", "DATABASE", "SECURITY", "BUSINESS"});
        categoryCombo.setFont(arabicFont);
        detailFields.put("VAR_CATEGORY", categoryCombo);
        detailsPanel.add(categoryCombo, gbc);

        // الصف الرابع
        gbc.gridx = 0;
        gbc.gridy = 3;
        detailsPanel.add(new JLabel("المجموعة:"), gbc);
        gbc.gridx = 1;
        JTextField varGroupField = new JTextField(15);
        varGroupField.setFont(arabicFont);
        detailFields.put("VAR_GROUP", varGroupField);
        detailsPanel.add(varGroupField, gbc);

        gbc.gridx = 2;
        detailsPanel.add(new JLabel("الأولوية:"), gbc);
        gbc.gridx = 3;
        JComboBox<String> priorityCombo =
                new JComboBox<>(new String[] {"1 - عالي", "2 - متوسط", "3 - منخفض"});
        priorityCombo.setFont(arabicFont);
        detailFields.put("PRIORITY_LEVEL", priorityCombo);
        detailsPanel.add(priorityCombo, gbc);

        // الصف الخامس - الوصف
        gbc.gridx = 0;
        gbc.gridy = 4;
        detailsPanel.add(new JLabel("الوصف:"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        JTextArea descriptionArea = new JTextArea(3, 30);
        descriptionArea.setFont(arabicFont);
        descriptionArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);
        JScrollPane descScrollPane = new JScrollPane(descriptionArea);
        detailFields.put("VAR_DESCRIPTION", descriptionArea);
        detailsPanel.add(descScrollPane, gbc);

        // الصف السادس - خيارات
        gbc.gridx = 0;
        gbc.gridy = 5;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        JCheckBox requiredCheck = new JCheckBox("مطلوب");
        requiredCheck.setFont(arabicFont);
        requiredCheck.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailFields.put("IS_REQUIRED", requiredCheck);
        detailsPanel.add(requiredCheck, gbc);

        gbc.gridx = 1;
        JCheckBox readonlyCheck = new JCheckBox("للقراءة فقط");
        readonlyCheck.setFont(arabicFont);
        readonlyCheck.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailFields.put("IS_READONLY", readonlyCheck);
        detailsPanel.add(readonlyCheck, gbc);

        gbc.gridx = 2;
        JCheckBox encryptedCheck = new JCheckBox("مشفر");
        encryptedCheck.setFont(arabicFont);
        encryptedCheck.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailFields.put("IS_ENCRYPTED", encryptedCheck);
        detailsPanel.add(encryptedCheck, gbc);

        gbc.gridx = 3;
        JCheckBox activeCheck = new JCheckBox("نشط");
        activeCheck.setFont(arabicFont);
        activeCheck.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        activeCheck.setSelected(true);
        detailFields.put("IS_ACTIVE", activeCheck);
        detailsPanel.add(activeCheck, gbc);
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // لوحة البحث والفلترة
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.setBorder(new TitledBorder("البحث والفلترة"));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        searchPanel.add(new JLabel("البحث:"));
        searchPanel.add(searchField);
        searchPanel.add(new JLabel("الفئة:"));
        searchPanel.add(categoryFilter);
        searchPanel.add(new JLabel("المجموعة:"));
        searchPanel.add(groupFilter);

        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonPanel.add(addButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(refreshButton);
        buttonPanel.add(saveButton);

        // لوحة الجدول
        JScrollPane tableScrollPane = new JScrollPane(variablesTable);
        tableScrollPane.setBorder(new TitledBorder("قائمة المتغيرات"));

        // التخطيط الرئيسي
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.add(searchPanel, BorderLayout.NORTH);
        topPanel.add(buttonPanel, BorderLayout.SOUTH);

        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        splitPane.setTopComponent(tableScrollPane);
        splitPane.setBottomComponent(detailsPanel);
        splitPane.setDividerLocation(400);

        add(topPanel, BorderLayout.NORTH);
        add(splitPane, BorderLayout.CENTER);
    }

    private void setupEventHandlers() {
        // معالج تحديد الصف في الجدول
        variablesTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedVariableDetails();
            }
        });

        // معالجات البحث والفلترة
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                filterVariables();
            }
        });

        categoryFilter.addActionListener(e -> filterVariables());
        groupFilter.addActionListener(e -> filterVariables());

        // معالجات الأزرار
        addButton.addActionListener(e -> addNewVariable());
        editButton.addActionListener(e -> editSelectedVariable());
        deleteButton.addActionListener(e -> deleteSelectedVariable());
        refreshButton.addActionListener(e -> loadVariables());
        saveButton.addActionListener(e -> saveVariableChanges());
    }

    private void connectToDatabase() {
        try {
            tnsManager = TNSConnectionManager.getInstance();
            connection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات لنافذة المتغيرات العامة");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                    "خطأ في الاتصال بقاعدة البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    private void loadVariables() {
        try {
            String sql = """
                        SELECT VAR_ID, VAR_NAME, VAR_NAME_AR, VAR_VALUE, DATA_TYPE,
                               VAR_CATEGORY, VAR_GROUP, PRIORITY_LEVEL, IS_REQUIRED, IS_ACTIVE
                        FROM ERP_APP_VARIABLES
                        WHERE IS_ACTIVE = 'Y'
                        ORDER BY VAR_CATEGORY, VAR_GROUP, DISPLAY_ORDER
                    """;

            tableModel.setRowCount(0);

            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                while (rs.next()) {
                    Object[] row = {rs.getInt("VAR_ID"), rs.getString("VAR_NAME"),
                            rs.getString("VAR_NAME_AR"), rs.getString("VAR_VALUE"),
                            rs.getString("DATA_TYPE"), rs.getString("VAR_CATEGORY"),
                            rs.getString("VAR_GROUP"), rs.getInt("PRIORITY_LEVEL"),
                            "Y".equals(rs.getString("IS_REQUIRED")) ? "نعم" : "لا",
                            "Y".equals(rs.getString("IS_ACTIVE")) ? "نعم" : "لا"};
                    tableModel.addRow(row);
                }
            }

            System.out.println("✅ تم تحميل " + tableModel.getRowCount() + " متغير");

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المتغيرات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ في تحميل المتغيرات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    private void loadSelectedVariableDetails() {
        int selectedRow = variablesTable.getSelectedRow();
        if (selectedRow == -1)
            return;

        try {
            int varId = (Integer) tableModel.getValueAt(selectedRow, 0);

            String sql = """
                        SELECT * FROM ERP_APP_VARIABLES WHERE VAR_ID = ?
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setInt(1, varId);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        // تحميل البيانات في حقول التفاصيل
                        ((JTextField) detailFields.get("VAR_NAME"))
                                .setText(rs.getString("VAR_NAME"));
                        ((JTextField) detailFields.get("VAR_NAME_AR"))
                                .setText(rs.getString("VAR_NAME_AR"));
                        ((JTextField) detailFields.get("VAR_NAME_EN"))
                                .setText(rs.getString("VAR_NAME_EN"));
                        ((JTextField) detailFields.get("VAR_VALUE"))
                                .setText(rs.getString("VAR_VALUE"));
                        ((JComboBox<?>) detailFields.get("DATA_TYPE"))
                                .setSelectedItem(rs.getString("DATA_TYPE"));
                        ((JComboBox<?>) detailFields.get("VAR_CATEGORY"))
                                .setSelectedItem(rs.getString("VAR_CATEGORY"));
                        ((JTextField) detailFields.get("VAR_GROUP"))
                                .setText(rs.getString("VAR_GROUP"));

                        int priority = rs.getInt("PRIORITY_LEVEL");
                        String priorityText = priority + " - "
                                + (priority == 1 ? "عالي" : priority == 2 ? "متوسط" : "منخفض");
                        ((JComboBox<?>) detailFields.get("PRIORITY_LEVEL"))
                                .setSelectedItem(priorityText);

                        ((JTextArea) detailFields.get("VAR_DESCRIPTION"))
                                .setText(rs.getString("VAR_DESCRIPTION"));
                        ((JCheckBox) detailFields.get("IS_REQUIRED"))
                                .setSelected("Y".equals(rs.getString("IS_REQUIRED")));
                        ((JCheckBox) detailFields.get("IS_READONLY"))
                                .setSelected("Y".equals(rs.getString("IS_READONLY")));
                        ((JCheckBox) detailFields.get("IS_ENCRYPTED"))
                                .setSelected("Y".equals(rs.getString("IS_ENCRYPTED")));
                        ((JCheckBox) detailFields.get("IS_ACTIVE"))
                                .setSelected("Y".equals(rs.getString("IS_ACTIVE")));
                    }
                }
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل تفاصيل المتغير: " + e.getMessage());
        }
    }

    private void filterVariables() {
        // تطبيق الفلاتر والبحث
        // سيتم تطويرها لاحقاً
        System.out.println("🔍 تطبيق الفلاتر...");
    }

    private void addNewVariable() {
        // مسح حقول التفاصيل للإدخال الجديد
        clearDetailsFields();
        JOptionPane.showMessageDialog(this, "قم بملء تفاصيل المتغير الجديد ثم اضغط 'حفظ التغييرات'",
                "إضافة متغير جديد", JOptionPane.INFORMATION_MESSAGE);
    }

    private void editSelectedVariable() {
        int selectedRow = variablesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار متغير للتعديل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        JOptionPane.showMessageDialog(this, "قم بتعديل تفاصيل المتغير ثم اضغط 'حفظ التغييرات'",
                "تعديل المتغير", JOptionPane.INFORMATION_MESSAGE);
    }

    private void deleteSelectedVariable() {
        int selectedRow = variablesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار متغير للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        int confirm = JOptionPane.showConfirmDialog(this, "هل أنت متأكد من حذف هذا المتغير؟",
                "تأكيد الحذف", JOptionPane.YES_NO_OPTION);

        if (confirm == JOptionPane.YES_OPTION) {
            try {
                int varId = (Integer) tableModel.getValueAt(selectedRow, 0);

                String sql = "UPDATE ERP_APP_VARIABLES SET IS_ACTIVE = 'N' WHERE VAR_ID = ?";

                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setInt(1, varId);
                    stmt.executeUpdate();
                }

                loadVariables();
                clearDetailsFields();

                JOptionPane.showMessageDialog(this, "تم حذف المتغير بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);

            } catch (SQLException e) {
                System.err.println("❌ خطأ في حذف المتغير: " + e.getMessage());
                JOptionPane.showMessageDialog(this, "خطأ في حذف المتغير:\n" + e.getMessage(), "خطأ",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private void saveVariableChanges() {
        try {
            String varName = ((JTextField) detailFields.get("VAR_NAME")).getText().trim();
            if (varName.isEmpty()) {
                JOptionPane.showMessageDialog(this, "يرجى إدخال اسم المتغير", "تنبيه",
                        JOptionPane.WARNING_MESSAGE);
                return;
            }

            // تحديد ما إذا كان هذا متغير جديد أم تعديل
            int selectedRow = variablesTable.getSelectedRow();
            boolean isNewVariable = selectedRow == -1;

            if (isNewVariable) {
                insertNewVariable();
            } else {
                updateExistingVariable();
            }

            loadVariables();

            JOptionPane.showMessageDialog(this, "تم حفظ المتغير بنجاح", "نجح",
                    JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            System.err.println("❌ خطأ في حفظ المتغير: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ في حفظ المتغير:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    private void insertNewVariable() throws SQLException {
        String sql = """
                    INSERT INTO ERP_APP_VARIABLES (
                        VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION, VAR_VALUE,
                        DATA_TYPE, VAR_CATEGORY, VAR_GROUP, PRIORITY_LEVEL,
                        IS_REQUIRED, IS_READONLY, IS_ENCRYPTED, IS_ACTIVE
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            setStatementParameters(stmt);
            stmt.executeUpdate();
        }
    }

    private void updateExistingVariable() throws SQLException {
        int selectedRow = variablesTable.getSelectedRow();
        int varId = (Integer) tableModel.getValueAt(selectedRow, 0);

        String sql =
                """
                            UPDATE ERP_APP_VARIABLES SET
                                VAR_NAME = ?, VAR_NAME_AR = ?, VAR_NAME_EN = ?, VAR_DESCRIPTION = ?, VAR_VALUE = ?,
                                DATA_TYPE = ?, VAR_CATEGORY = ?, VAR_GROUP = ?, PRIORITY_LEVEL = ?,
                                IS_REQUIRED = ?, IS_READONLY = ?, IS_ENCRYPTED = ?, IS_ACTIVE = ?
                            WHERE VAR_ID = ?
                        """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            setStatementParameters(stmt);
            stmt.setInt(14, varId);
            stmt.executeUpdate();
        }
    }

    private void setStatementParameters(PreparedStatement stmt) throws SQLException {
        stmt.setString(1, ((JTextField) detailFields.get("VAR_NAME")).getText().trim());
        stmt.setString(2, ((JTextField) detailFields.get("VAR_NAME_AR")).getText().trim());
        stmt.setString(3, ((JTextField) detailFields.get("VAR_NAME_EN")).getText().trim());
        stmt.setString(4, ((JTextArea) detailFields.get("VAR_DESCRIPTION")).getText().trim());
        stmt.setString(5, ((JTextField) detailFields.get("VAR_VALUE")).getText().trim());
        stmt.setString(6,
                (String) ((JComboBox<?>) detailFields.get("DATA_TYPE")).getSelectedItem());
        stmt.setString(7,
                (String) ((JComboBox<?>) detailFields.get("VAR_CATEGORY")).getSelectedItem());
        stmt.setString(8, ((JTextField) detailFields.get("VAR_GROUP")).getText().trim());

        String priorityText =
                (String) ((JComboBox<?>) detailFields.get("PRIORITY_LEVEL")).getSelectedItem();
        int priority = Integer.parseInt(priorityText.substring(0, 1));
        stmt.setInt(9, priority);

        stmt.setString(10, ((JCheckBox) detailFields.get("IS_REQUIRED")).isSelected() ? "Y" : "N");
        stmt.setString(11, ((JCheckBox) detailFields.get("IS_READONLY")).isSelected() ? "Y" : "N");
        stmt.setString(12, ((JCheckBox) detailFields.get("IS_ENCRYPTED")).isSelected() ? "Y" : "N");
        stmt.setString(13, ((JCheckBox) detailFields.get("IS_ACTIVE")).isSelected() ? "Y" : "N");
    }

    private void clearDetailsFields() {
        ((JTextField) detailFields.get("VAR_NAME")).setText("");
        ((JTextField) detailFields.get("VAR_NAME_AR")).setText("");
        ((JTextField) detailFields.get("VAR_NAME_EN")).setText("");
        ((JTextField) detailFields.get("VAR_VALUE")).setText("");
        ((JComboBox<?>) detailFields.get("DATA_TYPE")).setSelectedIndex(0);
        ((JComboBox<?>) detailFields.get("VAR_CATEGORY")).setSelectedIndex(0);
        ((JTextField) detailFields.get("VAR_GROUP")).setText("");
        ((JComboBox<?>) detailFields.get("PRIORITY_LEVEL")).setSelectedIndex(1);
        ((JTextArea) detailFields.get("VAR_DESCRIPTION")).setText("");
        ((JCheckBox) detailFields.get("IS_REQUIRED")).setSelected(false);
        ((JCheckBox) detailFields.get("IS_READONLY")).setSelected(false);
        ((JCheckBox) detailFields.get("IS_ENCRYPTED")).setSelected(false);
        ((JCheckBox) detailFields.get("IS_ACTIVE")).setSelected(true);
    }

    @Override
    public void dispose() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إغلاق الاتصال: " + e.getMessage());
        }
        super.dispose();
    }

    // دالة اختبار
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new GlobalVariablesWindow().setVisible(true);
        });
    }
}
