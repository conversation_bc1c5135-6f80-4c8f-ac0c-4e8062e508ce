@echo off
echo ========================================
echo   ADVANCED UI SETTINGS WINDOW
echo   نافذة إعدادات الواجهة المتقدمة
echo ========================================

cd /d "d:\java\java"

echo.
echo [INFO] Starting Advanced UI Settings Window...
echo [INFO] بدء تشغيل نافذة إعدادات الواجهة المتقدمة...
echo.

REM تجميع الملفات إذا لزم الأمر
echo [1] Compiling Enhanced Settings Manager...
echo [1] تجميع مدير الإعدادات المحسن...

javac -encoding UTF-8 -cp "lib\*" EnhancedSettingsManager.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile EnhancedSettingsManager
    pause
    exit /b 1
)

echo [2] Compiling Advanced UI Settings Window...
echo [2] تجميع نافذة إعدادات الواجهة المتقدمة...

javac -encoding UTF-8 -cp "lib\*" AdvancedUISettingsWindow.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile AdvancedUISettingsWindow
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo ✅ تم التجميع بنجاح!

echo.
echo [3] Testing database connection...
echo [3] اختبار اتصال قاعدة البيانات...

REM اختبار الاتصال بقاعدة البيانات أولاً

echo.
echo [4] Starting Advanced UI Settings Window...
echo [4] تشغيل نافذة إعدادات الواجهة المتقدمة...

java -cp "lib\*;." -Djava.awt.headless=false -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA AdvancedUISettingsWindow

if %errorlevel% equ 0 (
    echo ✅ UI Settings Window closed successfully!
    echo ✅ تم إغلاق نافذة الإعدادات بنجاح!
) else (
    echo ❌ UI Settings Window encountered an error
    echo ❌ واجهت نافذة الإعدادات خطأ
)

echo.
echo ========================================
echo   ADVANCED UI SETTINGS COMPLETED
echo   تم الانتهاء من إعدادات الواجهة المتقدمة
echo ========================================

echo.
echo Available features in the UI Settings Window:
echo الميزات المتاحة في نافذة إعدادات الواجهة:
echo.
echo 🎨 Theme Selection:
echo    اختيار الثيم:
echo    • FlatLaf (Light/Dark/IntelliJ/Darcula)
echo    • Material UI
echo    • JTattoo (16 different themes)
echo    • DarkLaf
echo    • SeaGlass
echo    • System Default themes
echo.
echo 🔤 Font Settings:
echo    إعدادات الخطوط:
echo    • Font family selection
echo    • Font size adjustment
echo    • Arabic font support
echo.
echo 🖥️ Interface Options:
echo    خيارات الواجهة:
echo    • Enable/disable animations
echo    • Enable/disable sounds
echo    • Enable/disable tooltips
echo    • RTL (Right-to-Left) support
echo    • Transparency level control
echo.
echo 🎨 Color Customization:
echo    تخصيص الألوان:
echo    • Accent color picker
echo    • Theme color preview
echo.
echo 👁️ Live Preview:
echo    المعاينة المباشرة:
echo    • Real-time theme application
echo    • Component preview
echo    • Instant visual feedback
echo.
echo 💾 Database Integration:
echo    تكامل قاعدة البيانات:
echo    • Settings saved to Oracle database
echo    • Automatic backup to file
echo    • Settings persistence across sessions
echo.

pause
