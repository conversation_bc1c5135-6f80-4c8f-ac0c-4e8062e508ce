import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * تحديث شجرة الأنظمة لإضافة نافذة إدارة العملات
 * Update System Tree to Add Currency Management Window
 */
public class UpdateSystemTreeForCurrency {
    
    private static final String DB_URL = "*************************************";
    private static final String DB_USER = "ship_erp";
    private static final String DB_PASSWORD = "ship_erp_password";
    
    public static void main(String[] args) {
        System.out.println("💰 تحديث شجرة الأنظمة لإضافة نافذة إدارة العملات");
        System.out.println("Update System Tree to Add Currency Management Window");
        System.out.println("===================================================");
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            
            // 1. فحص الهيكل الحالي
            checkCurrentStructure(conn);
            
            // 2. تحديث أو إدراج النافذة
            updateOrInsertCurrencyWindow(conn);
            
            // 3. التحقق من التحديث
            verifyUpdate(conn);
            
            System.out.println("\n✅ تم تحديث شجرة الأنظمة بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkCurrentStructure(Connection conn) throws SQLException {
        System.out.println("[1] فحص الهيكل الحالي...");
        
        String sql = """
            SELECT 
                TREE_ID,
                NODE_NAME_AR,
                NODE_TYPE,
                WINDOW_CLASS
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR LIKE '%إعدادات%' OR NODE_NAME_AR LIKE '%عملة%'
            ORDER BY TREE_ID
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            System.out.println("العقد الموجودة المرتبطة بالإعدادات والعملات:");
            System.out.println("============================================");
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.println("🆔 " + rs.getInt("TREE_ID") + 
                                 " | 🏷️ " + rs.getString("NODE_NAME_AR") + 
                                 " | 📱 " + rs.getString("NODE_TYPE") + 
                                 " | 💻 " + rs.getString("WINDOW_CLASS"));
            }
            
            if (!found) {
                System.out.println("⚠️ لم يتم العثور على عقد مرتبطة");
            }
        }
    }
    
    private static void updateOrInsertCurrencyWindow(Connection conn) throws SQLException {
        System.out.println("\n[2] تحديث أو إدراج نافذة إدارة العملات...");
        
        // البحث عن النافذة الموجودة
        String checkSql = """
            SELECT TREE_ID FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR LIKE '%عملة%' OR WINDOW_CLASS LIKE '%Currency%'
        """;
        
        try (PreparedStatement checkStmt = conn.prepareStatement(checkSql);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next()) {
                // تحديث النافذة الموجودة
                updateExistingWindow(conn, rs.getInt("TREE_ID"));
            } else {
                // إدراج نافذة جديدة
                insertNewWindow(conn);
            }
        }
    }
    
    private static void updateExistingWindow(Connection conn, int treeId) throws SQLException {
        System.out.println("تحديث النافذة الموجودة - ID: " + treeId);
        
        String updateSql = """
            UPDATE ERP_SYSTEM_TREE 
            SET 
                NODE_NAME_AR = 'إدارة العملات',
                NODE_NAME_EN = 'Currency Management',
                NODE_DESCRIPTION = 'نظام إدارة العملات الشامل مع أسعار الصرف والإعدادات',
                WINDOW_CLASS = 'CurrencyManagementWindow',
                ADDITIONAL_INFO = 'STATUS:FULLY_DEVELOPED;PRIORITY:HIGH;MODULE:CURRENCY;VERSION:2.0;FEATURES:CRUD,EXCHANGE_RATES,SETTINGS,REPORTS',
                LAST_UPDATED = SYSDATE,
                UPDATED_BY = 'SYSTEM'
            WHERE TREE_ID = ?
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(updateSql)) {
            stmt.setInt(1, treeId);
            
            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                conn.commit();
                System.out.println("✅ تم تحديث النافذة الموجودة");
            } else {
                throw new SQLException("فشل في تحديث النافذة");
            }
        }
    }
    
    private static void insertNewWindow(Connection conn) throws SQLException {
        System.out.println("إدراج نافذة جديدة...");
        
        // البحث عن فئة الإعدادات العامة
        String findParentSql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'الإعدادات العامة'";
        
        try (PreparedStatement findStmt = conn.prepareStatement(findParentSql);
             ResultSet rs = findStmt.executeQuery()) {
            
            if (!rs.next()) {
                throw new SQLException("لم يتم العثور على فئة الإعدادات العامة");
            }
            
            int parentId = rs.getInt("TREE_ID");
            System.out.println("تم العثور على فئة الإعدادات العامة - ID: " + parentId);
            
            String insertSql = """
                INSERT INTO ERP_SYSTEM_TREE (
                    TREE_ID,
                    PARENT_ID,
                    NODE_NAME_AR,
                    NODE_NAME_EN,
                    NODE_DESCRIPTION,
                    NODE_TYPE,
                    WINDOW_CLASS,
                    ICON_PATH,
                    DISPLAY_ORDER,
                    TREE_LEVEL,
                    IS_ACTIVE,
                    IS_VISIBLE,
                    ACCESS_PERMISSIONS,
                    ADDITIONAL_INFO,
                    CREATED_DATE,
                    CREATED_BY,
                    LAST_UPDATED,
                    UPDATED_BY,
                    VERSION_NUMBER
                ) VALUES (
                    (SELECT NVL(MAX(TREE_ID), 0) + 1 FROM ERP_SYSTEM_TREE),
                    ?,
                    'إدارة العملات',
                    'Currency Management',
                    'نظام إدارة العملات الشامل مع أسعار الصرف والإعدادات والتقارير',
                    'WINDOW',
                    'CurrencyManagementWindow',
                    'icons/currency_management.png',
                    (SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID = ?),
                    2,
                    'Y',
                    'Y',
                    'ADMIN,SETTINGS_MANAGER,FINANCE_MANAGER,CURRENCY_MANAGER',
                    'STATUS:FULLY_DEVELOPED;PRIORITY:HIGH;MODULE:CURRENCY;VERSION:2.0;FEATURES:CRUD,EXCHANGE_RATES,SETTINGS,REPORTS,INTEGRATION',
                    SYSDATE,
                    'SYSTEM',
                    SYSDATE,
                    'SYSTEM',
                    1
                )
            """;
            
            try (PreparedStatement stmt = conn.prepareStatement(insertSql)) {
                stmt.setInt(1, parentId);
                stmt.setInt(2, parentId);
                
                int rowsAffected = stmt.executeUpdate();
                if (rowsAffected > 0) {
                    conn.commit();
                    System.out.println("✅ تم إدراج النافذة الجديدة");
                } else {
                    throw new SQLException("فشل في إدراج النافذة");
                }
            }
        }
    }
    
    private static void verifyUpdate(Connection conn) throws SQLException {
        System.out.println("\n[3] التحقق من التحديث...");
        
        String sql = """
            SELECT 
                TREE_ID,
                NODE_NAME_AR,
                NODE_NAME_EN,
                WINDOW_CLASS,
                ADDITIONAL_INFO,
                TO_CHAR(LAST_UPDATED, 'YYYY-MM-DD HH24:MI:SS') AS LAST_UPDATED
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR = 'إدارة العملات'
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                System.out.println("📋 تفاصيل النافذة المحدثة:");
                System.out.println("==========================");
                System.out.println("🆔 معرف العقدة: " + rs.getInt("TREE_ID"));
                System.out.println("🏷️ الاسم العربي: " + rs.getString("NODE_NAME_AR"));
                System.out.println("🏷️ الاسم الإنجليزي: " + rs.getString("NODE_NAME_EN"));
                System.out.println("💻 كلاس النافذة: " + rs.getString("WINDOW_CLASS"));
                System.out.println("📅 آخر تحديث: " + rs.getString("LAST_UPDATED"));
                
                // تحليل الميزات
                String additionalInfo = rs.getString("ADDITIONAL_INFO");
                if (additionalInfo != null) {
                    System.out.println("ℹ️ معلومات إضافية: " + additionalInfo);
                    
                    if (additionalInfo.contains("FULLY_DEVELOPED")) {
                        System.out.println("\n🎉 الحالة: مطورة بالكامل");
                        System.out.println("✅ الميزات المتاحة:");
                        if (additionalInfo.contains("CRUD")) {
                            System.out.println("   • إدارة العملات (إضافة، تعديل، حذف)");
                        }
                        if (additionalInfo.contains("EXCHANGE_RATES")) {
                            System.out.println("   • إدارة أسعار الصرف");
                        }
                        if (additionalInfo.contains("SETTINGS")) {
                            System.out.println("   • الإعدادات العامة");
                        }
                        if (additionalInfo.contains("REPORTS")) {
                            System.out.println("   • التقارير والإحصائيات");
                        }
                        if (additionalInfo.contains("INTEGRATION")) {
                            System.out.println("   • التكامل مع المصادر الخارجية");
                        }
                    }
                }
                
                // عرض الهيكل المحدث
                showUpdatedStructure(conn);
                
            } else {
                throw new SQLException("❌ لم يتم العثور على النافذة المحدثة!");
            }
        }
    }
    
    private static void showUpdatedStructure(Connection conn) throws SQLException {
        System.out.println("\n📊 الهيكل المحدث للإعدادات العامة:");
        System.out.println("===================================");
        
        String sql = """
            SELECT 
                TREE_ID,
                NODE_NAME_AR,
                NODE_TYPE,
                WINDOW_CLASS,
                CASE 
                    WHEN ADDITIONAL_INFO LIKE '%FULLY_DEVELOPED%' THEN '✅ مطورة'
                    WHEN ADDITIONAL_INFO LIKE '%UNDER_DEVELOPMENT%' THEN '🚧 قيد التطوير'
                    WHEN IS_ACTIVE = 'Y' THEN '✅ نشطة'
                    ELSE '❌ غير نشطة'
                END AS STATUS
            FROM ERP_SYSTEM_TREE
            WHERE TREE_ID IN (
                -- فئة الإعدادات العامة
                SELECT TREE_ID FROM ERP_SYSTEM_TREE 
                WHERE NODE_NAME_AR = 'الإعدادات العامة'
                UNION
                -- جميع النوافذ تحت الإعدادات العامة
                SELECT TREE_ID FROM ERP_SYSTEM_TREE 
                WHERE PARENT_ID = (
                    SELECT TREE_ID FROM ERP_SYSTEM_TREE 
                    WHERE NODE_NAME_AR = 'الإعدادات العامة'
                )
            )
            ORDER BY TREE_LEVEL, DISPLAY_ORDER
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String nodeType = rs.getString("NODE_TYPE");
                String prefix = "CATEGORY".equals(nodeType) ? "📂" : "📱";
                String indent = "CATEGORY".equals(nodeType) ? "" : "  ├── ";
                
                System.out.println(indent + prefix + " " + rs.getString("NODE_NAME_AR") + 
                                 " | " + rs.getString("STATUS"));
                
                if (rs.getString("WINDOW_CLASS") != null) {
                    System.out.println("      💻 " + rs.getString("WINDOW_CLASS"));
                }
            }
        }
    }
}
