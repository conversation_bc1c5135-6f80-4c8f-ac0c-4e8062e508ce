import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.Window;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;
import javax.swing.LookAndFeel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import com.formdev.flatlaf.FlatDarculaLaf;
import com.formdev.flatlaf.FlatDarkLaf;
import com.formdev.flatlaf.FlatIntelliJLaf;
import com.formdev.flatlaf.FlatLightLaf;

/**
 * مدير المظاهر الموحد للتطبيق
 * Unified Theme Manager for the Application
 */
public class UnifiedThemeManager {
    
    private static UnifiedThemeManager instance;
    private static final String SETTINGS_FILE = "theme-settings.properties";
    private static final String THEME_KEY = "current.theme";
    private static final String FONT_SIZE_KEY = "font.size";
    private static final String RTL_KEY = "rtl.enabled";
    
    // المظاهر المتاحة
    public enum Theme {
        FLAT_LIGHT("FlatLaf Light", "com.formdev.flatlaf.FlatLightLaf"),
        FLAT_DARK("FlatLaf Dark", "com.formdev.flatlaf.FlatDarkLaf"),
        FLAT_INTELLIJ("FlatLaf IntelliJ", "com.formdev.flatlaf.FlatIntelliJLaf"),
        FLAT_DARCULA("FlatLaf Darcula", "com.formdev.flatlaf.FlatDarculaLaf"),
        SYSTEM_DEFAULT("System Default", "system"),
        WINDOWS("Windows", "com.sun.java.swing.plaf.windows.WindowsLookAndFeel"),
        METAL("Metal", "javax.swing.plaf.metal.MetalLookAndFeel"),
        NIMBUS("Nimbus", "javax.swing.plaf.nimbus.NimbusLookAndFeel");
        
        private final String displayName;
        private final String className;
        
        Theme(String displayName, String className) {
            this.displayName = displayName;
            this.className = className;
        }
        
        public String getDisplayName() { return displayName; }
        public String getClassName() { return className; }
    }
    
    private Theme currentTheme = Theme.FLAT_LIGHT;
    private int fontSize = 12;
    private boolean rtlEnabled = true;
    
    private UnifiedThemeManager() {
        loadSettings();
    }
    
    public static UnifiedThemeManager getInstance() {
        if (instance == null) {
            instance = new UnifiedThemeManager();
        }
        return instance;
    }
    
    /**
     * تطبيق المظهر الافتراضي عند بدء التطبيق
     */
    public static void initializeDefaultTheme() {
        try {
            UnifiedThemeManager manager = getInstance();
            manager.applyStoredTheme();
            System.out.println("✅ تم تطبيق المظهر الافتراضي: " + manager.currentTheme.getDisplayName());
        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر الافتراضي: " + e.getMessage());
            // تطبيق مظهر احتياطي
            try {
                FlatLightLaf.setup();
                System.out.println("✅ تم تطبيق المظهر الاحتياطي: FlatLaf Light");
            } catch (Exception ex) {
                System.err.println("❌ فشل في تطبيق المظهر الاحتياطي");
            }
        }
    }
    
    /**
     * تطبيق مظهر محدد
     */
    public boolean applyTheme(Theme theme) {
        try {
            System.out.println("🎨 تطبيق المظهر: " + theme.getDisplayName());
            
            boolean success = false;
            
            switch (theme) {
                case FLAT_LIGHT:
                    success = FlatLightLaf.setup();
                    break;
                case FLAT_DARK:
                    success = FlatDarkLaf.setup();
                    break;
                case FLAT_INTELLIJ:
                    success = FlatIntelliJLaf.setup();
                    break;
                case FLAT_DARCULA:
                    success = FlatDarculaLaf.setup();
                    break;
                case SYSTEM_DEFAULT:
                    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
                    success = true;
                    break;
                default:
                    UIManager.setLookAndFeel(theme.getClassName());
                    success = true;
                    break;
            }
            
            if (success) {
                currentTheme = theme;
                applyCustomizations();
                updateAllWindows();
                saveSettings();
                System.out.println("✅ تم تطبيق المظهر بنجاح: " + theme.getDisplayName());
                return true;
            } else {
                System.err.println("❌ فشل في تطبيق المظهر: " + theme.getDisplayName());
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * تطبيق التخصيصات الإضافية
     */
    private void applyCustomizations() {
        // إعدادات الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, fontSize);
        UIManager.put("defaultFont", arabicFont);
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("TextArea.font", arabicFont);
        UIManager.put("ComboBox.font", arabicFont);
        UIManager.put("Table.font", arabicFont);
        UIManager.put("Tree.font", arabicFont);
        UIManager.put("Menu.font", arabicFont);
        UIManager.put("MenuItem.font", arabicFont);
        
        // إعدادات FlatLaf المتقدمة
        if (currentTheme.name().startsWith("FLAT_")) {
            UIManager.put("Button.arc", 8);
            UIManager.put("Component.arc", 8);
            UIManager.put("ProgressBar.arc", 8);
            UIManager.put("TextComponent.arc", 8);
            UIManager.put("ScrollBar.width", 12);
            UIManager.put("Table.showHorizontalLines", true);
            UIManager.put("Table.showVerticalLines", true);
        }
        
        // إعدادات اللغة العربية
        if (rtlEnabled) {
            UIManager.put("ComponentOrientation", ComponentOrientation.RIGHT_TO_LEFT);
        }
        
        System.out.println("✅ تم تطبيق التخصيصات الإضافية");
    }
    
    /**
     * تحديث جميع النوافذ المفتوحة
     */
    private void updateAllWindows() {
        SwingUtilities.invokeLater(() -> {
            for (Window window : Window.getWindows()) {
                if (window.isDisplayable()) {
                    SwingUtilities.updateComponentTreeUI(window);
                    
                    // تطبيق اتجاه اللغة العربية
                    if (rtlEnabled) {
                        window.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                    }
                    
                    window.repaint();
                }
            }
            System.out.println("✅ تم تحديث جميع النوافذ المفتوحة");
        });
    }
    
    /**
     * تطبيق المظهر المحفوظ
     */
    public void applyStoredTheme() {
        applyTheme(currentTheme);
    }
    
    /**
     * تحميل الإعدادات
     */
    private void loadSettings() {
        try {
            Properties props = new Properties();
            props.load(new FileInputStream(SETTINGS_FILE));
            
            String themeName = props.getProperty(THEME_KEY, "FLAT_LIGHT");
            try {
                currentTheme = Theme.valueOf(themeName);
            } catch (IllegalArgumentException e) {
                currentTheme = Theme.FLAT_LIGHT;
            }
            
            fontSize = Integer.parseInt(props.getProperty(FONT_SIZE_KEY, "12"));
            rtlEnabled = Boolean.parseBoolean(props.getProperty(RTL_KEY, "true"));
            
            System.out.println("✅ تم تحميل الإعدادات: " + currentTheme.getDisplayName());
            
        } catch (IOException e) {
            System.out.println("ℹ️ لم يتم العثور على ملف الإعدادات، سيتم استخدام الإعدادات الافتراضية");
            currentTheme = Theme.FLAT_LIGHT;
            fontSize = 12;
            rtlEnabled = true;
        }
    }
    
    /**
     * حفظ الإعدادات
     */
    private void saveSettings() {
        try {
            Properties props = new Properties();
            props.setProperty(THEME_KEY, currentTheme.name());
            props.setProperty(FONT_SIZE_KEY, String.valueOf(fontSize));
            props.setProperty(RTL_KEY, String.valueOf(rtlEnabled));
            
            props.store(new FileOutputStream(SETTINGS_FILE), "Theme Settings");
            System.out.println("✅ تم حفظ الإعدادات");
            
        } catch (IOException e) {
            System.err.println("❌ خطأ في حفظ الإعدادات: " + e.getMessage());
        }
    }
    
    // Getters and Setters
    public Theme getCurrentTheme() { return currentTheme; }
    public int getFontSize() { return fontSize; }
    public boolean isRtlEnabled() { return rtlEnabled; }
    
    public void setFontSize(int fontSize) {
        this.fontSize = fontSize;
        applyCustomizations();
        updateAllWindows();
        saveSettings();
    }
    
    public void setRtlEnabled(boolean rtlEnabled) {
        this.rtlEnabled = rtlEnabled;
        applyCustomizations();
        updateAllWindows();
        saveSettings();
    }
    
    /**
     * الحصول على معلومات المظهر الحالي
     */
    public String getCurrentThemeInfo() {
        LookAndFeel laf = UIManager.getLookAndFeel();
        if (laf != null) {
            return String.format("المظهر: %s | الكلاس: %s | حجم الخط: %d | العربية: %s",
                currentTheme.getDisplayName(),
                laf.getClass().getSimpleName(),
                fontSize,
                rtlEnabled ? "مفعل" : "معطل"
            );
        }
        return "لا يوجد مظهر محدد";
    }
    
    /**
     * طباعة تقرير المظاهر المتاحة
     */
    public void printAvailableThemes() {
        System.out.println("🎨 المظاهر المتاحة:");
        System.out.println("==================");
        
        for (Theme theme : Theme.values()) {
            boolean available = isThemeAvailable(theme);
            String status = available ? "✅" : "❌";
            System.out.println(status + " " + theme.getDisplayName() + 
                " (" + theme.getClassName() + ")");
        }
    }
    
    /**
     * فحص توفر مظهر معين
     */
    private boolean isThemeAvailable(Theme theme) {
        try {
            if (theme == Theme.SYSTEM_DEFAULT) {
                return true;
            }
            Class.forName(theme.getClassName());
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * إصلاح جميع النوافذ لتستخدم المدير الموحد
     */
    public static void fixAllWindowsThemes() {
        System.out.println("🔧 إصلاح مظاهر جميع النوافذ...");

        UnifiedThemeManager manager = getInstance();
        manager.applyStoredTheme();

        // تحديث النوافذ المفتوحة
        SwingUtilities.invokeLater(() -> {
            for (Window window : Window.getWindows()) {
                if (window.isDisplayable()) {
                    SwingUtilities.updateComponentTreeUI(window);
                    if (manager.rtlEnabled) {
                        window.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                    }
                    window.repaint();
                }
            }
        });

        System.out.println("✅ تم إصلاح مظاهر جميع النوافذ");
    }
}
