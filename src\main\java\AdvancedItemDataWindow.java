import java.awt.BasicStroke;
import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.ComponentOrientation;
import java.awt.Cursor;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Image;
import java.awt.Insets;
import java.awt.RenderingHints;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.awt.image.BufferedImage;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JSpinner;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SpinnerNumberModel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة بيانات الأصناف الشاملة المتقدمة Advanced Comprehensive Item Data Window
 * 
 * نافذة متقدمة وشاملة لإدارة بيانات الأصناف مع ميزات متطورة
 */
public class AdvancedItemDataWindow extends JFrame {

    private static final int WINDOW_WIDTH = 1400;
    private static final int WINDOW_HEIGHT = 900;

    // مكونات الواجهة الرئيسية
    private JTabbedPane mainTabbedPane;
    private JTable itemsTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JLabel statusLabel;
    private JLabel statsLabel;
    private JProgressBar progressBar;

    // مكونات متقدمة
    private JComboBox<String> filterComboBox;
    private JSpinner pageSizeSpinner;
    private JButton exportButton;
    private JButton importButton;
    private JButton refreshButton;

    // قاعدة البيانات
    private Connection connection;

    // إحصائيات
    private int totalItems = 0;
    private int activeItems = 0;
    private int serviceItems = 0;
    private int totalCategories = 0;

    public AdvancedItemDataWindow() {
        initializeWindow();
        initializeComponents();
        setupLayout();
        connectToDatabase();
        loadData();
        setupEventHandlers();
    }

    private void initializeWindow() {
        setTitle("نافذة بيانات الأصناف الشاملة المتقدمة - Advanced Comprehensive Item Data");
        setSize(WINDOW_WIDTH, WINDOW_HEIGHT);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        // إعداد الخط العربي المحسن
        Font arabicFont = new Font("Segoe UI", Font.PLAIN, 12);
        Font arabicBoldFont = new Font("Segoe UI", Font.BOLD, 12);

        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("Table.font", arabicFont);
        UIManager.put("TabbedPane.font", arabicBoldFont);
        UIManager.put("ComboBox.font", arabicFont);

        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إعداد أيقونة النافذة
        try {
            setIconImage(createWindowIcon());
        } catch (Exception e) {
            System.err.println("تعذر تحميل أيقونة النافذة: " + e.getMessage());
        }
    }

    private Image createWindowIcon() {
        // إنشاء أيقونة بسيطة للنافذة
        BufferedImage icon = new BufferedImage(32, 32, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = icon.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // خلفية زرقاء
        g2d.setColor(new Color(52, 152, 219));
        g2d.fillRoundRect(2, 2, 28, 28, 8, 8);

        // رمز الجدول
        g2d.setColor(Color.WHITE);
        g2d.setStroke(new BasicStroke(2));
        g2d.drawRect(8, 8, 16, 12);
        g2d.drawLine(8, 12, 24, 12);
        g2d.drawLine(8, 16, 24, 16);
        g2d.drawLine(14, 8, 14, 20);
        g2d.drawLine(20, 8, 20, 20);

        g2d.dispose();
        return icon;
    }

    private void initializeComponents() {
        // التبويبات الرئيسية
        mainTabbedPane = new JTabbedPane();
        mainTabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط البحث المتقدم
        searchField = new JTextField(25);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.setToolTipText("ابحث في أكواد الأصناف، الأسماء، أو الأوصاف");

        // فلتر متقدم
        String[] filterOptions = {"جميع الأصناف", "الأصناف النشطة", "الأصناف غير النشطة",
                "أصناف الخدمة", "الأصناف العادية"};
        filterComboBox = new JComboBox<>(filterOptions);
        filterComboBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // حجم الصفحة
        pageSizeSpinner = new JSpinner(new SpinnerNumberModel(50, 10, 1000, 10));

        // أزرار العمليات
        exportButton = createStyledButton("تصدير Excel", new Color(34, 139, 34));
        importButton = createStyledButton("استيراد البيانات", new Color(30, 144, 255));
        refreshButton = createStyledButton("تحديث", new Color(255, 140, 0));

        // شريط التقدم
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setString("جاري التحميل...");

        // تسميات الحالة
        statusLabel = new JLabel("جاري التحميل...");
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        statsLabel = new JLabel("الإحصائيات: ");
        statsLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // جدول البيانات المتقدم
        createAdvancedTable();
    }

    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setOpaque(true);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // تأثير hover
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(color.brighter());
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(color);
            }
        });

        return button;
    }

    private void createAdvancedTable() {
        String[] columnNames = {"كود الصنف", "اسم الصنف", "الاسم الإنجليزي", "كود المجموعة",
                "اسم المجموعة", "سعر التكلفة", "سعر البيع", "نوع الصنف", "الحالة", "صنف خدمة",
                "الوحدة الرئيسية", "الكمية المتاحة", "تاريخ الإنشاء", "آخر تحديث"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }

            @Override
            public Class<?> getColumnClass(int columnIndex) {
                switch (columnIndex) {
                    case 5:
                    case 6:
                    case 11: // أسعار وكميات
                        return Double.class;
                    case 12:
                    case 13: // تواريخ
                        return java.util.Date.class;
                    default:
                        return String.class;
                }
            }
        };

        itemsTable = new JTable(tableModel);
        itemsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        itemsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        itemsTable.setRowHeight(28);
        itemsTable.setGridColor(new Color(230, 230, 230));
        itemsTable.setSelectionBackground(new Color(184, 207, 229));

        // إعداد عرض الأعمدة
        setupColumnWidths();

        // إعداد ألوان الصفوف المتناوبة
        itemsTable.setDefaultRenderer(Object.class, new AlternatingRowRenderer());
    }

    private void setupColumnWidths() {
        int[] columnWidths = {80, 150, 150, 80, 120, 80, 80, 80, 60, 80, 100, 80, 100, 100};

        for (int i = 0; i < columnWidths.length && i < itemsTable.getColumnCount(); i++) {
            itemsTable.getColumnModel().getColumn(i).setPreferredWidth(columnWidths[i]);
        }
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة العلوية - أدوات البحث والفلترة
        JPanel topPanel = createTopPanel();
        add(topPanel, BorderLayout.NORTH);

        // التبويبات الرئيسية
        createMainTabs();
        add(mainTabbedPane, BorderLayout.CENTER);

        // اللوحة السفلية - الحالة والإحصائيات
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }

    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "أدوات البحث والفلترة المتقدمة", TitledBorder.RIGHT, TitledBorder.TOP));

        // لوحة البحث
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        searchPanel.add(new JLabel("البحث:"));
        searchPanel.add(searchField);
        searchPanel.add(new JLabel("الفلتر:"));
        searchPanel.add(filterComboBox);
        searchPanel.add(new JLabel("حجم الصفحة:"));
        searchPanel.add(pageSizeSpinner);

        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        buttonPanel.add(refreshButton);
        buttonPanel.add(exportButton);
        buttonPanel.add(importButton);

        panel.add(searchPanel, BorderLayout.CENTER);
        panel.add(buttonPanel, BorderLayout.EAST);
        panel.add(progressBar, BorderLayout.SOUTH);

        return panel;
    }

    private void createMainTabs() {
        // تبويب قائمة الأصناف
        JScrollPane itemsScrollPane = new JScrollPane(itemsTable);
        itemsScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainTabbedPane.addTab("📋 قائمة الأصناف", itemsScrollPane);

        // تبويب الإحصائيات المتقدمة
        JPanel statsPanel = createAdvancedStatsPanel();
        mainTabbedPane.addTab("📊 الإحصائيات المتقدمة", statsPanel);

        // تبويب إعدادات العرض
        JPanel settingsPanel = createDisplaySettingsPanel();
        mainTabbedPane.addTab("⚙️ إعدادات العرض", settingsPanel);
    }

    private JPanel createAdvancedStatsPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;

        // إحصائيات الأصناف
        addStatRow(panel, gbc, 0, "إجمالي الأصناف:", "0");
        addStatRow(panel, gbc, 1, "الأصناف النشطة:", "0");
        addStatRow(panel, gbc, 2, "أصناف الخدمة:", "0");
        addStatRow(panel, gbc, 3, "إجمالي المجموعات:", "0");

        return panel;
    }

    private void addStatRow(JPanel panel, GridBagConstraints gbc, int row, String label,
            String value) {
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(new JLabel(label), gbc);

        gbc.gridx = 1;
        JLabel valueLabel = new JLabel(value);
        valueLabel.setFont(valueLabel.getFont().deriveFont(Font.BOLD, 14f));
        valueLabel.setForeground(new Color(52, 152, 219));
        panel.add(valueLabel, gbc);
    }

    private JPanel createDisplaySettingsPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;

        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("إعدادات العرض والتخصيص"), gbc);

        gbc.gridy = 1;
        panel.add(new JLabel("(قيد التطوير)"), gbc);

        return panel;
    }

    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEtchedBorder());

        panel.add(statusLabel, BorderLayout.WEST);
        panel.add(statsLabel, BorderLayout.EAST);

        return panel;
    }

    private void connectToDatabase() {
        try {
            // إعداد خصائص Oracle لدعم الترميز العربي
            java.util.Properties props = new java.util.Properties();
            props.setProperty("user", "IAS20251");
            props.setProperty("password", "IAS20251");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");

            String url = "***********************************";
            connection = DriverManager.getConnection(url, props);
            statusLabel.setText("✅ تم الاتصال بقاعدة البيانات بنجاح مع دعم الترميز العربي");

        } catch (SQLException e) {
            statusLabel.setText("❌ خطأ في الاتصال بقاعدة البيانات");
            System.err.println("خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());

            // إنشاء بيانات تجريبية في حالة عدم توفر قاعدة البيانات
            createSampleData();
        }
    }

    private void loadData() {
        SwingUtilities.invokeLater(() -> {
            progressBar.setIndeterminate(true);
            progressBar.setString("جاري تحميل البيانات...");
        });

        new Thread(() -> {
            try {
                if (connection != null) {
                    loadDataFromDatabase();
                } else {
                    createSampleData();
                }
                updateStats();

                SwingUtilities.invokeLater(() -> {
                    progressBar.setIndeterminate(false);
                    progressBar.setValue(100);
                    progressBar.setString("تم التحميل بنجاح");
                });

            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    progressBar.setIndeterminate(false);
                    progressBar.setValue(0);
                    progressBar.setString("خطأ في التحميل");
                    statusLabel.setText("❌ خطأ في تحميل البيانات: " + e.getMessage());
                });
            }
        }).start();
    }

    private void loadDataFromDatabase() {
        // تحميل البيانات من قاعدة البيانات (مبسط للمثال)
        createSampleData();
    }

    private void createSampleData() {
        tableModel.setRowCount(0);

        // بيانات تجريبية متقدمة
        Object[][] sampleData = {
                {"001", "صنف تجريبي 1", "Sample Item 1", "G001", "مجموعة أساسية", 100.00, 120.00,
                        "عادي", "نشط", "لا", "قطعة", 50.0, new java.util.Date(),
                        new java.util.Date()},
                {"002", "صنف تجريبي 2", "Sample Item 2", "G002", "مجموعة خدمات", 150.50, 180.00,
                        "خدمة", "نشط", "نعم", "ساعة", 0.0, new java.util.Date(),
                        new java.util.Date()},
                {"003", "صنف تجريبي 3", "Sample Item 3", "G001", "مجموعة أساسية", 75.25, 90.00,
                        "عادي", "غير نشط", "لا", "كيلو", 25.5, new java.util.Date(),
                        new java.util.Date()}};

        for (Object[] row : sampleData) {
            tableModel.addRow(row);
        }

        totalItems = sampleData.length;
        activeItems = 2;
        serviceItems = 1;
        totalCategories = 2;
    }

    private void updateStats() {
        SwingUtilities.invokeLater(() -> {
            statsLabel.setText(String.format(
                    "📊 الإحصائيات: إجمالي الأصناف: %d | النشطة: %d | أصناف الخدمة: %d | المجموعات: %d",
                    totalItems, activeItems, serviceItems, totalCategories));
        });
    }

    private void setupEventHandlers() {
        // معالج البحث
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                performSearch();
            }
        });

        // معالج الفلتر
        filterComboBox.addActionListener(e -> applyFilter());

        // معالج الأزرار
        refreshButton.addActionListener(e -> loadData());
        exportButton.addActionListener(e -> exportToExcel());
        importButton.addActionListener(e -> importData());

        // معالج إغلاق النافذة
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                if (connection != null) {
                    try {
                        connection.close();
                    } catch (SQLException ex) {
                        System.err.println("خطأ في إغلاق الاتصال: " + ex.getMessage());
                    }
                }
            }
        });
    }

    private void performSearch() {
        String searchText = searchField.getText().trim();
        statusLabel.setText(searchText.isEmpty() ? "جاهز" : "البحث عن: " + searchText);
        // تطبيق البحث (مبسط)
    }

    private void applyFilter() {
        String selectedFilter = (String) filterComboBox.getSelectedItem();
        statusLabel.setText("تطبيق الفلتر: " + selectedFilter);
        // تطبيق الفلتر (مبسط)
    }

    private void exportToExcel() {
        JOptionPane.showMessageDialog(this, "ميزة التصدير إلى Excel قيد التطوير", "تصدير",
                JOptionPane.INFORMATION_MESSAGE);
    }

    private void importData() {
        JOptionPane.showMessageDialog(this, "ميزة استيراد البيانات قيد التطوير", "استيراد",
                JOptionPane.INFORMATION_MESSAGE);
    }

    // فئة لتلوين الصفوف المتناوبة
    private class AlternatingRowRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value,
                boolean isSelected, boolean hasFocus, int row, int column) {
            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus,
                    row, column);

            if (!isSelected) {
                if (row % 2 == 0) {
                    c.setBackground(Color.WHITE);
                } else {
                    c.setBackground(new Color(248, 248, 248));
                }
            }

            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            return c;
        }
    }

    // طريقة main للاختبار المستقل
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new AdvancedItemDataWindow().setVisible(true);
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, "خطأ في تشغيل النافذة: " + e.getMessage());
            }
        });
    }
}
