import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JComponent;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JScrollPane;
import javax.swing.JSpinner;
import javax.swing.JTextField;
import javax.swing.SpinnerNumberModel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * نافذة إعداد حساب البريد الإلكتروني Email Account Setup Window
 */
public class EmailAccountSetupWindow extends JFrame {

    private Font arabicFont;
    private Font boldArabicFont;

    // حقول الإدخال
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JComboBox<String> protocolCombo;
    private JTextField imapServerField;
    private JSpinner imapPortSpinner;
    private JTextField pop3ServerField;
    private JSpinner pop3PortSpinner;
    private JTextField smtpServerField;
    private JSpinner smtpPortSpinner;
    private JCheckBox sslCheckBox;
    private JCheckBox defaultAccountCheckBox;

    // اتصال قاعدة البيانات
    private Connection connection;

    public EmailAccountSetupWindow() {
        initializeFonts();
        initializeDatabase();
        createUI();
        loadExistingAccount();
    }

    private void initializeFonts() {
        try {
            arabicFont = new Font("Tahoma", Font.PLAIN, 12);
            boldArabicFont = new Font("Tahoma", Font.BOLD, 12);
        } catch (Exception e) {
            arabicFont = new Font(Font.SANS_SERIF, Font.PLAIN, 12);
            boldArabicFont = new Font(Font.SANS_SERIF, Font.BOLD, 12);
        }
    }

    private void initializeDatabase() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "SHIP_ERP";
            String password = "ship_erp_password";
            connection = DriverManager.getConnection(url, username, password);

            // إنشاء جدول حسابات البريد الإلكتروني إذا لم يكن موجوداً
            createEmailAccountsTable();

        } catch (Exception e) {
            System.err.println("خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
        }
    }

    private void createEmailAccountsTable() {
        try {
            String sql = """
                    CREATE TABLE EMAIL_ACCOUNTS (
                        ACCOUNT_ID NUMBER PRIMARY KEY,
                        USERNAME VARCHAR2(200) NOT NULL,
                        PASSWORD VARCHAR2(500) NOT NULL,
                        PROTOCOL VARCHAR2(10) DEFAULT 'IMAP',
                        IMAP_SERVER VARCHAR2(200),
                        IMAP_PORT NUMBER DEFAULT 993,
                        POP3_SERVER VARCHAR2(200),
                        POP3_PORT NUMBER DEFAULT 995,
                        SMTP_SERVER VARCHAR2(200),
                        SMTP_PORT NUMBER DEFAULT 587,
                        USE_SSL CHAR(1) DEFAULT 'Y',
                        IS_DEFAULT CHAR(1) DEFAULT 'N',
                        CREATED_DATE DATE DEFAULT SYSDATE
                    )
                    """;

            try (Statement stmt = connection.createStatement()) {
                stmt.execute(sql);
                System.out.println("تم إنشاء جدول EMAIL_ACCOUNTS");
            }

            // إنشاء sequence
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("CREATE SEQUENCE SEQ_EMAIL_ACCOUNTS START WITH 1 INCREMENT BY 1");
                System.out.println("تم إنشاء SEQ_EMAIL_ACCOUNTS");
            }

        } catch (SQLException e) {
            if (e.getErrorCode() != 955) { // ORA-00955: name is already used
                System.err.println("خطأ في إنشاء جدول EMAIL_ACCOUNTS: " + e.getMessage());
            }
        }
    }

    private void createUI() {
        setTitle("إعداد حساب البريد الإلكتروني");
        setSize(600, 500);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLayout(new BorderLayout());

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // معلومات الحساب الأساسية
        addSectionTitle(mainPanel, gbc, "معلومات الحساب الأساسية", row++);

        addField(mainPanel, gbc, "عنوان البريد الإلكتروني:", usernameField = new JTextField(20),
                row++);
        addField(mainPanel, gbc, "كلمة المرور:", passwordField = new JPasswordField(20), row++);

        protocolCombo = new JComboBox<>(new String[] {"IMAP", "POP3"});
        protocolCombo.setFont(arabicFont);
        protocolCombo.addActionListener(e -> updateServerFields());
        addField(mainPanel, gbc, "البروتوكول:", protocolCombo, row++);

        // إعدادات خادم IMAP
        addSectionTitle(mainPanel, gbc, "إعدادات خادم IMAP", row++);

        addField(mainPanel, gbc, "خادم IMAP:", imapServerField = new JTextField(20), row++);
        addField(mainPanel, gbc, "منفذ IMAP:",
                imapPortSpinner = new JSpinner(new SpinnerNumberModel(993, 1, 65535, 1)), row++);

        // إعدادات خادم POP3
        addSectionTitle(mainPanel, gbc, "إعدادات خادم POP3", row++);

        addField(mainPanel, gbc, "خادم POP3:", pop3ServerField = new JTextField(20), row++);
        addField(mainPanel, gbc, "منفذ POP3:",
                pop3PortSpinner = new JSpinner(new SpinnerNumberModel(995, 1, 65535, 1)), row++);

        // إعدادات خادم SMTP
        addSectionTitle(mainPanel, gbc, "إعدادات خادم SMTP", row++);

        addField(mainPanel, gbc, "خادم SMTP:", smtpServerField = new JTextField(20), row++);
        addField(mainPanel, gbc, "منفذ SMTP:",
                smtpPortSpinner = new JSpinner(new SpinnerNumberModel(587, 1, 65535, 1)), row++);

        // خيارات إضافية
        addSectionTitle(mainPanel, gbc, "خيارات إضافية", row++);

        sslCheckBox = new JCheckBox("استخدام SSL/TLS");
        sslCheckBox.setFont(arabicFont);
        sslCheckBox.setSelected(true);
        addField(mainPanel, gbc, "", sslCheckBox, row++);

        defaultAccountCheckBox = new JCheckBox("جعل هذا الحساب افتراضي");
        defaultAccountCheckBox.setFont(arabicFont);
        addField(mainPanel, gbc, "", defaultAccountCheckBox, row++);

        // أزرار العمليات
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton saveButton = new JButton("حفظ الإعدادات");
        saveButton.setFont(boldArabicFont);
        saveButton.addActionListener(this::saveAccount);

        JButton testButton = new JButton("اختبار الاتصال");
        testButton.setFont(arabicFont);
        testButton.addActionListener(this::testConnection);

        JButton cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.addActionListener(e -> dispose());

        buttonPanel.add(saveButton);
        buttonPanel.add(testButton);
        buttonPanel.add(cancelButton);

        add(new JScrollPane(mainPanel), BorderLayout.CENTER);
        add(buttonPanel, BorderLayout.SOUTH);

        // تحديث حقول الخادم حسب البروتوكول المختار
        updateServerFields();
    }

    private void addSectionTitle(JPanel panel, GridBagConstraints gbc, String title, int row) {
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;

        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(boldArabicFont);
        titleLabel.setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, Color.GRAY));
        panel.add(titleLabel, gbc);

        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
    }

    private void addField(JPanel panel, GridBagConstraints gbc, String labelText, JComponent field,
            int row) {
        gbc.gridy = row;

        if (!labelText.isEmpty()) {
            gbc.gridx = 0;
            gbc.weightx = 0;
            JLabel label = new JLabel(labelText);
            label.setFont(arabicFont);
            panel.add(label, gbc);
        }

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        field.setFont(arabicFont);
        panel.add(field, gbc);

        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
    }

    private void updateServerFields() {
        String protocol = (String) protocolCombo.getSelectedItem();

        if ("IMAP".equals(protocol)) {
            // تفعيل حقول IMAP
            imapServerField.setEnabled(true);
            imapPortSpinner.setEnabled(true);

            // تعطيل حقول POP3
            pop3ServerField.setEnabled(false);
            pop3PortSpinner.setEnabled(false);

            // إعدادات افتراضية لـ Gmail IMAP
            if (usernameField.getText().contains("@gmail.com")) {
                imapServerField.setText("imap.gmail.com");
                imapPortSpinner.setValue(993);
                smtpServerField.setText("smtp.gmail.com");
                smtpPortSpinner.setValue(587);
            }
        } else {
            // تفعيل حقول POP3
            pop3ServerField.setEnabled(true);
            pop3PortSpinner.setEnabled(true);

            // تعطيل حقول IMAP
            imapServerField.setEnabled(false);
            imapPortSpinner.setEnabled(false);

            // إعدادات افتراضية لـ Gmail POP3
            if (usernameField.getText().contains("@gmail.com")) {
                pop3ServerField.setText("pop.gmail.com");
                pop3PortSpinner.setValue(995);
                smtpServerField.setText("smtp.gmail.com");
                smtpPortSpinner.setValue(587);
            }
        }
    }

    private void loadExistingAccount() {
        if (connection == null)
            return;

        try {
            String sql = "SELECT * FROM EMAIL_ACCOUNTS WHERE IS_DEFAULT = 'Y' AND ROWNUM = 1";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    usernameField.setText(rs.getString("USERNAME"));
                    passwordField.setText(rs.getString("PASSWORD"));
                    protocolCombo.setSelectedItem(rs.getString("PROTOCOL"));
                    imapServerField.setText(rs.getString("IMAP_SERVER"));
                    imapPortSpinner.setValue(rs.getInt("IMAP_PORT"));
                    pop3ServerField.setText(rs.getString("POP3_SERVER"));
                    pop3PortSpinner.setValue(rs.getInt("POP3_PORT"));
                    smtpServerField.setText(rs.getString("SMTP_SERVER"));
                    smtpPortSpinner.setValue(rs.getInt("SMTP_PORT"));
                    sslCheckBox.setSelected("Y".equals(rs.getString("USE_SSL")));
                    defaultAccountCheckBox.setSelected("Y".equals(rs.getString("IS_DEFAULT")));
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في تحميل إعدادات الحساب: " + e.getMessage());
        }
    }

    private void saveAccount(ActionEvent e) {
        if (!validateInput())
            return;

        try {
            // حذف الحساب الافتراضي السابق إذا كان هذا الحساب افتراضي
            if (defaultAccountCheckBox.isSelected()) {
                String updateSql = "UPDATE EMAIL_ACCOUNTS SET IS_DEFAULT = 'N'";
                try (PreparedStatement stmt = connection.prepareStatement(updateSql)) {
                    stmt.executeUpdate();
                }
            }

            // إدراج الحساب الجديد
            String sql = """
                    INSERT INTO EMAIL_ACCOUNTS (
                        ACCOUNT_ID, USERNAME, PASSWORD, PROTOCOL,
                        IMAP_SERVER, IMAP_PORT, POP3_SERVER, POP3_PORT,
                        SMTP_SERVER, SMTP_PORT, USE_SSL, IS_DEFAULT
                    ) VALUES (
                        SEQ_EMAIL_ACCOUNTS.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                    )
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, usernameField.getText().trim());
                stmt.setString(2, new String(passwordField.getPassword()));
                stmt.setString(3, (String) protocolCombo.getSelectedItem());
                stmt.setString(4, imapServerField.getText().trim());
                stmt.setInt(5, (Integer) imapPortSpinner.getValue());
                stmt.setString(6, pop3ServerField.getText().trim());
                stmt.setInt(7, (Integer) pop3PortSpinner.getValue());
                stmt.setString(8, smtpServerField.getText().trim());
                stmt.setInt(9, (Integer) smtpPortSpinner.getValue());
                stmt.setString(10, sslCheckBox.isSelected() ? "Y" : "N");
                stmt.setString(11, defaultAccountCheckBox.isSelected() ? "Y" : "N");

                stmt.executeUpdate();

                JOptionPane.showMessageDialog(this, "تم حفظ إعدادات الحساب بنجاح!", "نجح الحفظ",
                        JOptionPane.INFORMATION_MESSAGE);

                dispose();
            }

        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "خطأ في حفظ إعدادات الحساب: " + ex.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void testConnection(ActionEvent e) {
        JOptionPane.showMessageDialog(this,
                "اختبار الاتصال قيد التطوير\nسيتم تطبيقه عند توفر مكتبة JavaMail", "اختبار الاتصال",
                JOptionPane.INFORMATION_MESSAGE);
    }

    private boolean validateInput() {
        if (usernameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال عنوان البريد الإلكتروني", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        if (passwordField.getPassword().length == 0) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كلمة المرور", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        return true;
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // تعيين مظهر النظام
            try {
                UIManager.setLookAndFeel("javax.swing.plaf.nimbus.NimbusLookAndFeel");
            } catch (Exception e) {
                // استخدام المظهر الافتراضي في حالة الخطأ
            }

            new EmailAccountSetupWindow().setVisible(true);
        });
    }
}
