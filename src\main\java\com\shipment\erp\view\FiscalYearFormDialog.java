package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dialog;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.text.SimpleDateFormat;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import com.shipment.erp.model.FiscalYear;

/**
 * نموذج إدخال/تعديل بيانات السنة المالية Fiscal Year Form Dialog
 */
public class FiscalYearFormDialog extends JDialog {

    private Font arabicFont;
    private FiscalYear fiscalYear;
    private boolean confirmed = false;

    // حقول الإدخال
    private JTextField yearNameField;
    private JTextField startDateField;
    private JTextField endDateField;
    private JCheckBox currentCheckBox;
    private JCheckBox closedCheckBox;
    private JTextArea descriptionArea;

    public FiscalYearFormDialog(Dialog parent, String title, FiscalYear fiscalYear) {
        super(parent, title, true);

        this.fiscalYear = fiscalYear;
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeComponents();
        setupLayout();
        loadData();

        setSize(500, 450);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }

    private void initializeComponents() {
        // اسم السنة المالية
        yearNameField = new JTextField(20);
        yearNameField.setFont(arabicFont);
        yearNameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تاريخ البداية
        startDateField = new JTextField(15);
        startDateField.setFont(arabicFont);
        startDateField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        startDateField.setToolTipText("تنسيق التاريخ: yyyy-MM-dd (مثال: 2025-01-01)");

        // تاريخ النهاية
        endDateField = new JTextField(15);
        endDateField.setFont(arabicFont);
        endDateField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        endDateField.setToolTipText("تنسيق التاريخ: yyyy-MM-dd (مثال: 2025-12-31)");

        // السنة الحالية
        currentCheckBox = new JCheckBox("سنة مالية حالية");
        currentCheckBox.setFont(arabicFont);
        currentCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // مغلقة
        closedCheckBox = new JCheckBox("مغلقة");
        closedCheckBox.setFont(arabicFont);
        closedCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الوصف
        descriptionArea = new JTextArea(3, 30);
        descriptionArea.setFont(arabicFont);
        descriptionArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // اسم السنة المالية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("اسم السنة المالية:*"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(yearNameField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // تاريخ البداية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("تاريخ البداية:*"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        JPanel startDatePanel = new JPanel(new BorderLayout());
        startDatePanel.add(startDateField, BorderLayout.CENTER);
        JButton startDateButton = new JButton("📅");
        startDateButton.setPreferredSize(new Dimension(30, 25));
        startDateButton.addActionListener(e -> selectStartDate());
        startDatePanel.add(startDateButton, BorderLayout.EAST);
        mainPanel.add(startDatePanel, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // تاريخ النهاية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("تاريخ النهاية:*"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        JPanel endDatePanel = new JPanel(new BorderLayout());
        endDatePanel.add(endDateField, BorderLayout.CENTER);
        JButton endDateButton = new JButton("📅");
        endDateButton.setPreferredSize(new Dimension(30, 25));
        endDateButton.addActionListener(e -> selectEndDate());
        endDatePanel.add(endDateButton, BorderLayout.EAST);
        mainPanel.add(endDatePanel, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // ملاحظة التاريخ
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel dateNoteLabel = new JLabel("تنسيق التاريخ: yyyy-MM-dd (مثال: 2025-01-01)");
        dateNoteLabel.setFont(new Font("Tahoma", Font.ITALIC, 10));
        dateNoteLabel.setForeground(Color.GRAY);
        dateNoteLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.add(dateNoteLabel, gbc);
        gbc.gridwidth = 1;
        row++;

        // السنة الحالية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel(""), gbc);
        gbc.gridx = 0;
        mainPanel.add(currentCheckBox, gbc);
        row++;

        // مغلقة
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel(""), gbc);
        gbc.gridx = 0;
        mainPanel.add(closedCheckBox, gbc);
        row++;

        // الوصف
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.anchor = GridBagConstraints.NORTHEAST;
        mainPanel.add(createLabel("الوصف:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JScrollPane(descriptionArea), gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // أزرار التحكم
        JPanel buttonsPanel = createButtonsPanel();

        add(mainPanel, BorderLayout.CENTER);
        add(buttonsPanel, BorderLayout.SOUTH);
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    private JPanel createButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton saveButton = new JButton("حفظ");
        saveButton.setFont(arabicFont);
        saveButton.setPreferredSize(new Dimension(100, 30));
        saveButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (validateInput()) {
                    saveData();
                    confirmed = true;
                    dispose();
                }
            }
        });

        JButton cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.setPreferredSize(new Dimension(100, 30));
        cancelButton.addActionListener(e -> dispose());

        panel.add(saveButton);
        panel.add(cancelButton);

        return panel;
    }

    private void selectStartDate() {
        // TODO: Implement date picker
        JOptionPane.showMessageDialog(this,
                "منتقي التاريخ قيد التطوير\nيرجى إدخال التاريخ يدوياً بالتنسيق: yyyy-MM-dd",
                "منتقي التاريخ", JOptionPane.INFORMATION_MESSAGE);
    }

    private void selectEndDate() {
        // TODO: Implement date picker
        JOptionPane.showMessageDialog(this,
                "منتقي التاريخ قيد التطوير\nيرجى إدخال التاريخ يدوياً بالتنسيق: yyyy-MM-dd",
                "منتقي التاريخ", JOptionPane.INFORMATION_MESSAGE);
    }

    private void loadData() {
        if (fiscalYear != null) {
            yearNameField.setText(fiscalYear.getYearName());

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            if (fiscalYear.getStartDate() != null) {
                startDateField.setText(dateFormat.format(fiscalYear.getStartDate()));
            }
            if (fiscalYear.getEndDate() != null) {
                endDateField.setText(dateFormat.format(fiscalYear.getEndDate()));
            }

            currentCheckBox.setSelected(fiscalYear.isCurrent());
            closedCheckBox.setSelected(fiscalYear.isClosed());
            descriptionArea.setText(fiscalYear.getDescription());
        }
    }

    private boolean validateInput() {
        if (yearNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم السنة المالية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            yearNameField.requestFocus();
            return false;
        }

        if (startDateField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال تاريخ البداية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            startDateField.requestFocus();
            return false;
        }

        if (endDateField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال تاريخ النهاية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            endDateField.requestFocus();
            return false;
        }

        // التحقق من صحة التواريخ
        try {
            java.time.LocalDate startDate =
                    java.time.LocalDate.parse(startDateField.getText().trim());
            java.time.LocalDate endDate = java.time.LocalDate.parse(endDateField.getText().trim());

            if (startDate.isAfter(endDate)) {
                JOptionPane.showMessageDialog(this, "تاريخ البداية يجب أن يكون قبل تاريخ النهاية",
                        "خطأ", JOptionPane.ERROR_MESSAGE);
                startDateField.requestFocus();
                return false;
            }

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                    "تنسيق التاريخ غير صحيح\nيجب أن يكون بالتنسيق: yyyy-MM-dd", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        return true;
    }

    private void saveData() {
        if (fiscalYear == null) {
            fiscalYear = new FiscalYear();
        }

        fiscalYear.setYearName(yearNameField.getText().trim());
        fiscalYear.setCurrent(currentCheckBox.isSelected());
        fiscalYear.setClosed(closedCheckBox.isSelected());
        fiscalYear.setDescription(descriptionArea.getText().trim());

        try {
            fiscalYear.setStartDate(java.time.LocalDate.parse(startDateField.getText().trim()));
            fiscalYear.setEndDate(java.time.LocalDate.parse(endDateField.getText().trim()));
        } catch (Exception e) {
            // هذا لن يحدث لأننا تحققنا من التواريخ في validateInput
        }
    }

    public FiscalYear getFiscalYear() {
        return fiscalYear;
    }

    public boolean isConfirmed() {
        return confirmed;
    }
}
