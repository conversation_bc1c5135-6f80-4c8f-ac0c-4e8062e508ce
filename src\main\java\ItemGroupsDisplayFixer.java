import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Font;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import javax.swing.JFrame;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

/**
 * أداة إصلاح عرض بيانات مجموعات الأصناف
 * Item Groups Display Fixer
 */
public class ItemGroupsDisplayFixer extends JFrame {
    
    private Connection shipErpConnection;
    private JTabbedPane tabbedPane;
    private Font arabicFont;
    
    public ItemGroupsDisplayFixer() {
        try {
            initializeConnections();
            initializeUI();
            loadAndDisplayData();
        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة أداة الإصلاح: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void initializeConnections() throws Exception {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            shipErpConnection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات");
        } catch (Exception e) {
            System.err.println("❌ فشل الاتصال بقاعدة البيانات: " + e.getMessage());
            throw e;
        }
    }
    
    private void initializeUI() {
        setTitle("🔧 أداة إصلاح عرض بيانات مجموعات الأصناف");
        setSize(1400, 800);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        
        // إعداد الخط العربي
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        // تطبيق اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إنشاء التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.setFont(arabicFont);
        
        add(tabbedPane, BorderLayout.CENTER);
    }
    
    private void loadAndDisplayData() {
        System.out.println("📊 تحميل وعرض البيانات...");
        
        // تحميل المجموعات الرئيسية
        loadMainGroups();
        
        // تحميل المجموعات الفرعية الرئيسية
        loadMainSubGroups();
        
        // تحميل المجموعات تحت فرعية
        loadSubGroups();
        
        // تحميل المجموعات المساعدة
        loadAssistantGroups();
        
        // تحميل المجموعات التفصيلية
        loadDetailGroups();
        
        System.out.println("✅ تم تحميل جميع البيانات");
    }
    
    private void loadMainGroups() {
        try {
            System.out.println("📋 تحميل المجموعات الرئيسية...");
            
            String sql = "SELECT G_CODE, G_A_NAME, G_E_NAME, TAX_PRCNT_DFLT, ROL_LMT_QTY FROM ERP_GROUP_DETAILS ORDER BY G_CODE";
            
            DefaultTableModel model = createTableFromQuery(sql, "المجموعات الرئيسية");
            JTable table = createStyledTable(model);
            
            JScrollPane scrollPane = new JScrollPane(table);
            tabbedPane.addTab("المجموعات الرئيسية (" + model.getRowCount() + ")", scrollPane);
            
            System.out.println("✅ تم تحميل " + model.getRowCount() + " مجموعة رئيسية");
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الرئيسية: " + e.getMessage());
        }
    }
    
    private void loadMainSubGroups() {
        try {
            System.out.println("📋 تحميل المجموعات الفرعية الرئيسية...");
            
            String sql = """
                SELECT m.G_CODE, g.G_A_NAME, m.MNG_CODE, m.MNG_A_NAME, m.MNG_E_NAME 
                FROM ERP_MAINSUB_GRP_DTL m
                LEFT JOIN ERP_GROUP_DETAILS g ON m.G_CODE = g.G_CODE
                ORDER BY m.G_CODE, m.MNG_CODE
            """;
            
            DefaultTableModel model = createTableFromQuery(sql, "المجموعات الفرعية الرئيسية");
            JTable table = createStyledTable(model);
            
            JScrollPane scrollPane = new JScrollPane(table);
            tabbedPane.addTab("المجموعات الفرعية الرئيسية (" + model.getRowCount() + ")", scrollPane);
            
            System.out.println("✅ تم تحميل " + model.getRowCount() + " مجموعة فرعية رئيسية");
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الفرعية الرئيسية: " + e.getMessage());
        }
    }
    
    private void loadSubGroups() {
        try {
            System.out.println("📋 تحميل المجموعات تحت فرعية...");
            
            String sql = """
                SELECT s.G_CODE, g.G_A_NAME, s.MNG_CODE, m.MNG_A_NAME, s.SUBG_CODE, s.SUBG_A_NAME, s.SUBG_E_NAME
                FROM ERP_SUB_GRP_DTL s
                LEFT JOIN ERP_GROUP_DETAILS g ON s.G_CODE = g.G_CODE
                LEFT JOIN ERP_MAINSUB_GRP_DTL m ON s.G_CODE = m.G_CODE AND s.MNG_CODE = m.MNG_CODE
                ORDER BY s.G_CODE, s.MNG_CODE, s.SUBG_CODE
            """;
            
            DefaultTableModel model = createTableFromQuery(sql, "المجموعات تحت فرعية");
            JTable table = createStyledTable(model);
            
            JScrollPane scrollPane = new JScrollPane(table);
            tabbedPane.addTab("المجموعات تحت فرعية (" + model.getRowCount() + ")", scrollPane);
            
            System.out.println("✅ تم تحميل " + model.getRowCount() + " مجموعة تحت فرعية");
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات تحت فرعية: " + e.getMessage());
        }
    }
    
    private void loadAssistantGroups() {
        try {
            System.out.println("📋 تحميل المجموعات المساعدة...");
            
            String sql = """
                SELECT a.G_CODE, g.G_A_NAME, a.MNG_CODE, m.MNG_A_NAME, 
                       a.SUBG_CODE, s.SUBG_A_NAME, a.ASSISTANT_NO, a.ASSISTANT_A_NAME, a.ASSISTANT_E_NAME
                FROM ERP_ASSISTANT_GROUP a
                LEFT JOIN ERP_GROUP_DETAILS g ON a.G_CODE = g.G_CODE
                LEFT JOIN ERP_MAINSUB_GRP_DTL m ON a.G_CODE = m.G_CODE AND a.MNG_CODE = m.MNG_CODE
                LEFT JOIN ERP_SUB_GRP_DTL s ON a.G_CODE = s.G_CODE AND a.MNG_CODE = s.MNG_CODE AND a.SUBG_CODE = s.SUBG_CODE
                ORDER BY a.G_CODE, a.MNG_CODE, a.SUBG_CODE, a.ASSISTANT_NO
            """;
            
            DefaultTableModel model = createTableFromQuery(sql, "المجموعات المساعدة");
            JTable table = createStyledTable(model);
            
            JScrollPane scrollPane = new JScrollPane(table);
            tabbedPane.addTab("المجموعات المساعدة (" + model.getRowCount() + ")", scrollPane);
            
            System.out.println("✅ تم تحميل " + model.getRowCount() + " مجموعة مساعدة");
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات المساعدة: " + e.getMessage());
        }
    }
    
    private void loadDetailGroups() {
        try {
            System.out.println("📋 تحميل المجموعات التفصيلية...");
            
            String sql = """
                SELECT d.G_CODE, g.G_A_NAME, d.MNG_CODE, m.MNG_A_NAME, 
                       d.SUBG_CODE, s.SUBG_A_NAME, d.ASSISTANT_NO, a.ASSISTANT_A_NAME,
                       d.DETAIL_NO, d.DETAIL_A_NAME, d.DETAIL_E_NAME
                FROM ERP_DETAIL_GROUP d
                LEFT JOIN ERP_GROUP_DETAILS g ON d.G_CODE = g.G_CODE
                LEFT JOIN ERP_MAINSUB_GRP_DTL m ON d.G_CODE = m.G_CODE AND d.MNG_CODE = m.MNG_CODE
                LEFT JOIN ERP_SUB_GRP_DTL s ON d.G_CODE = s.G_CODE AND d.MNG_CODE = s.MNG_CODE AND d.SUBG_CODE = s.SUBG_CODE
                LEFT JOIN ERP_ASSISTANT_GROUP a ON d.G_CODE = a.G_CODE AND d.MNG_CODE = a.MNG_CODE AND d.SUBG_CODE = a.SUBG_CODE AND d.ASSISTANT_NO = a.ASSISTANT_NO
                ORDER BY d.G_CODE, d.MNG_CODE, d.SUBG_CODE, d.ASSISTANT_NO, d.DETAIL_NO
            """;
            
            DefaultTableModel model = createTableFromQuery(sql, "المجموعات التفصيلية");
            JTable table = createStyledTable(model);
            
            JScrollPane scrollPane = new JScrollPane(table);
            tabbedPane.addTab("المجموعات التفصيلية (" + model.getRowCount() + ")", scrollPane);
            
            System.out.println("✅ تم تحميل " + model.getRowCount() + " مجموعة تفصيلية");
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات التفصيلية: " + e.getMessage());
        }
    }
    
    private DefaultTableModel createTableFromQuery(String sql, String tableName) throws SQLException {
        try (PreparedStatement stmt = shipErpConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            // إنشاء أسماء الأعمدة
            String[] columnNames = new String[columnCount];
            for (int i = 1; i <= columnCount; i++) {
                columnNames[i - 1] = metaData.getColumnName(i);
            }
            
            // إنشاء النموذج
            DefaultTableModel model = new DefaultTableModel(columnNames, 0) {
                @Override
                public boolean isCellEditable(int row, int column) {
                    return false;
                }
            };
            
            // إضافة البيانات
            while (rs.next()) {
                Object[] row = new Object[columnCount];
                for (int i = 1; i <= columnCount; i++) {
                    row[i - 1] = rs.getObject(i);
                }
                model.addRow(row);
            }
            
            return model;
        }
    }
    
    private JTable createStyledTable(DefaultTableModel model) {
        JTable table = new JTable(model);
        
        // إعداد الخط والاتجاه
        table.setFont(arabicFont);
        table.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        table.getTableHeader().setFont(arabicFont);
        table.getTableHeader().setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إعداد عارض الخلايا للنص العربي
        DefaultTableCellRenderer renderer = new DefaultTableCellRenderer();
        renderer.setFont(arabicFont);
        renderer.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تطبيق العارض على جميع الأعمدة
        for (int i = 0; i < table.getColumnCount(); i++) {
            table.getColumnModel().getColumn(i).setCellRenderer(renderer);
        }
        
        // إعداد ارتفاع الصفوف
        table.setRowHeight(25);
        
        return table;
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                FinalThemeManager.initializeDefaultTheme();
            } catch (Exception e) {
                System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
            }
            
            System.out.println("🔧 تشغيل أداة إصلاح عرض بيانات مجموعات الأصناف");
            System.out.println("Item Groups Display Fixer Starting...");
            System.out.println("==========================================");
            
            ItemGroupsDisplayFixer fixer = new ItemGroupsDisplayFixer();
            fixer.setVisible(true);
            
            System.out.println("✅ تم تشغيل أداة الإصلاح");
        });
    }
}
