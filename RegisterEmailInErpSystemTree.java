import java.sql.*;

/**
 * تسجيل نافذة إدارة حسابات البريد الإلكتروني في جدول ERP_SYSTEM_TREE
 */
public class RegisterEmailInErpSystemTree {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  REGISTERING EMAIL WINDOW IN ERP_SYSTEM_TREE");
        System.out.println("  تسجيل نافذة البريد الإلكتروني في ERP_SYSTEM_TREE");
        System.out.println("========================================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";
            
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة بيانات ship_erp بنجاح");
            
            // فحص جدول ERP_SYSTEM_TREE
            checkErpSystemTreeTable(connection);
            
            // تسجيل نظام البريد الإلكتروني
            registerEmailSystem(connection);
            
            connection.close();
            System.out.println("\n🎉 تم تسجيل نافذة البريد الإلكتروني في ERP_SYSTEM_TREE بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تسجيل النافذة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkErpSystemTreeTable(Connection connection) {
        try {
            System.out.println("\n🔍 فحص جدول ERP_SYSTEM_TREE...");
            
            // فحص وجود الجدول
            String checkTableSql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'ERP_SYSTEM_TREE'";
            try (PreparedStatement stmt = connection.prepareStatement(checkTableSql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next() && rs.getInt(1) > 0) {
                    System.out.println("✅ جدول ERP_SYSTEM_TREE موجود");
                    
                    // عرض بعض البيانات الموجودة
                    showExistingNodes(connection);
                    
                } else {
                    System.out.println("❌ جدول ERP_SYSTEM_TREE غير موجود");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("خطأ في فحص جدول ERP_SYSTEM_TREE: " + e.getMessage());
        }
    }
    
    private static void showExistingNodes(Connection connection) {
        try {
            String sql = "SELECT NODE_ID, NODE_NAME_AR, NODE_NAME_EN, WINDOW_CLASS FROM ERP_SYSTEM_TREE WHERE ROWNUM <= 10 ORDER BY NODE_ID";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                System.out.println("📋 عينة من العقد الموجودة:");
                while (rs.next()) {
                    String nodeId = rs.getString("NODE_ID");
                    String nameAr = rs.getString("NODE_NAME_AR");
                    String nameEn = rs.getString("NODE_NAME_EN");
                    String windowClass = rs.getString("WINDOW_CLASS");
                    
                    System.out.println("   🔹 " + nodeId + ": " + nameAr + " (" + nameEn + ")" + 
                                     (windowClass != null ? " [" + windowClass + "]" : ""));
                }
            }
            
        } catch (SQLException e) {
            System.err.println("خطأ في عرض العقد الموجودة: " + e.getMessage());
        }
    }
    
    private static void registerEmailSystem(Connection connection) {
        try {
            System.out.println("\n📧 تسجيل نظام البريد الإلكتروني...");
            
            // البحث عن عقدة مناسبة للنظام الرئيسي
            int parentId = findSuitableParentNode(connection);
            if (parentId == -1) {
                System.err.println("❌ لم يتم العثور على عقدة مناسبة للنظام الرئيسي");
                return;
            }
            
            // إدراج عقدة نظام البريد الإلكتروني
            int emailSystemId = insertEmailSystemNode(connection, parentId);
            if (emailSystemId == -1) {
                return;
            }
            
            // إدراج نافذة إدارة حسابات البريد
            insertEmailAccountsWindow(connection, emailSystemId);
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تسجيل نظام البريد الإلكتروني: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static int findSuitableParentNode(Connection connection) throws SQLException {
        // البحث عن عقدة النظام الرئيسي أو الإعدادات
        String[] searchTerms = {
            "Ship ERP System",
            "نظام إدارة الشحنات", 
            "الإعدادات",
            "Settings",
            "System",
            "النظام"
        };
        
        for (String term : searchTerms) {
            String sql = "SELECT NODE_ID FROM ERP_SYSTEM_TREE WHERE (NODE_NAME_AR LIKE ? OR NODE_NAME_EN LIKE ?) AND ROWNUM = 1";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, "%" + term + "%");
                stmt.setString(2, "%" + term + "%");
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int nodeId = rs.getInt("NODE_ID");
                        System.out.println("✅ تم العثور على عقدة مناسبة: " + term + " (ID: " + nodeId + ")");
                        return nodeId;
                    }
                }
            }
        }
        
        // إذا لم نجد عقدة مناسبة، نأخذ أول عقدة متاحة
        String sql = "SELECT NODE_ID FROM ERP_SYSTEM_TREE WHERE ROWNUM = 1 ORDER BY NODE_ID";
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                int nodeId = rs.getInt("NODE_ID");
                System.out.println("⚠️ استخدام أول عقدة متاحة (ID: " + nodeId + ")");
                return nodeId;
            }
        }
        
        return -1;
    }
    
    private static int insertEmailSystemNode(Connection connection, int parentId) throws SQLException {
        // التحقق من عدم وجود العقدة مسبقاً
        String checkSql = "SELECT NODE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'Email Management System'";
        try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next()) {
                int nodeId = rs.getInt("NODE_ID");
                System.out.println("⚠️ عقدة نظام البريد الإلكتروني موجودة مسبقاً (ID: " + nodeId + ")");
                return nodeId;
            }
        }
        
        // الحصول على أكبر NODE_ID موجود
        String maxIdSql = "SELECT NVL(MAX(NODE_ID), 0) + 1 FROM ERP_SYSTEM_TREE";
        int newNodeId = 1;
        try (PreparedStatement stmt = connection.prepareStatement(maxIdSql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                newNodeId = rs.getInt(1);
            }
        }
        
        // إدراج عقدة نظام البريد الإلكتروني
        String sql = """
            INSERT INTO ERP_SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, NODE_ORDER, DESCRIPTION, IS_ACTIVE, CREATED_DATE, CREATED_BY)
            VALUES (?, 'نظام إدارة البريد الإلكتروني', 'Email Management System', ?, 2, 10, 'نظام شامل لإدارة البريد الإلكتروني والرسائل', 'Y', SYSDATE, USER)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, newNodeId);
            stmt.setInt(2, parentId);
            stmt.executeUpdate();
            System.out.println("✅ تم إدراج عقدة نظام البريد الإلكتروني (ID: " + newNodeId + ")");
            return newNodeId;
        }
    }
    
    private static void insertEmailAccountsWindow(Connection connection, int parentId) throws SQLException {
        // التحقق من عدم وجود النافذة مسبقاً
        String checkSql = "SELECT NODE_ID FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = 'CompleteEmailAccountsWindow'";
        try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next()) {
                System.out.println("⚠️ نافذة إدارة حسابات البريد موجودة مسبقاً");
                return;
            }
        }
        
        // الحصول على أكبر NODE_ID موجود
        String maxIdSql = "SELECT NVL(MAX(NODE_ID), 0) + 1 FROM ERP_SYSTEM_TREE";
        int newNodeId = 1;
        try (PreparedStatement stmt = connection.prepareStatement(maxIdSql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                newNodeId = rs.getInt(1);
            }
        }
        
        // إدراج نافذة إدارة حسابات البريد
        String sql = """
            INSERT INTO ERP_SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, NODE_ORDER, WINDOW_CLASS, DESCRIPTION, IS_ACTIVE, CREATED_DATE, CREATED_BY)
            VALUES (?, 'إدارة حسابات البريد', 'Email Accounts Management', ?, 3, 1, 'CompleteEmailAccountsWindow', 'إدارة شاملة لحسابات البريد الإلكتروني مع دعم IMAP وPOP3 وSMTP', 'Y', SYSDATE, USER)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, newNodeId);
            stmt.setInt(2, parentId);
            stmt.executeUpdate();
            System.out.println("✅ تم إدراج نافذة إدارة حسابات البريد (ID: " + newNodeId + ")");
        }
    }
}
