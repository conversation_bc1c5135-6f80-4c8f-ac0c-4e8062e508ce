import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Cursor;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة وحدات القياس Measurement Units Management Window
 */
public class MeasurementUnitsWindow extends JFrame {

    private static final int WINDOW_WIDTH = 1000;
    private static final int WINDOW_HEIGHT = 700;

    // مكونات الواجهة
    private JTable unitsTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JLabel statusLabel;
    private JProgressBar progressBar;

    // أزرار العمليات
    private JButton addButton;
    private JButton editButton;
    private JButton deleteButton;
    private JButton refreshButton;

    // قاعدة البيانات
    private Connection connection;
    // private ConnectionPoolManager poolManager;
    // private PerformanceMonitor performanceMonitor;
    // private EnhancedConfigManager configManager;

    public MeasurementUnitsWindow() {
        // تهيئة المدراء المحسنين
        // poolManager = ConnectionPoolManager.getInstance();
        // performanceMonitor = PerformanceMonitor.getInstance();
        // configManager = EnhancedConfigManager.getInstance();

        initializeWindow();
        initializeComponents();
        setupLayout();
        connectToDatabase();
        loadData();
        setupEventHandlers();
    }

    private void initializeWindow() {
        setTitle("إدارة وحدات القياس - Measurement Units Management");
        setSize(WINDOW_WIDTH, WINDOW_HEIGHT);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        // إعداد الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("Table.font", arabicFont);

        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void initializeComponents() {
        // شريط البحث
        searchField = new JTextField(20);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.setToolTipText("ابحث في أكواد أو أسماء وحدات القياس");

        // أزرار العمليات
        addButton = createStyledButton("إضافة وحدة", new Color(34, 139, 34));
        editButton = createStyledButton("تعديل", new Color(30, 144, 255));
        deleteButton = createStyledButton("حذف", new Color(220, 20, 60));
        refreshButton = createStyledButton("تحديث", new Color(255, 140, 0));

        // شريط التقدم
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setString("جاري التحميل...");

        // تسمية الحالة
        statusLabel = new JLabel("جاري التحميل...");
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // جدول وحدات القياس
        createUnitsTable();
    }

    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setOpaque(true);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // تأثير hover
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(color.brighter());
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(color);
            }
        });

        return button;
    }

    private void createUnitsTable() {
        String[] columnNames = {"كود الوحدة", "اسم الوحدة", "الاسم الإنجليزي", "الكود العالمي",
                "نوع الوحدة", "الحجم الافتراضي", "الحالة", "تاريخ الإنشاء"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        unitsTable = new JTable(tableModel);
        unitsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        unitsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        unitsTable.setRowHeight(25);
        unitsTable.setGridColor(new Color(230, 230, 230));
        unitsTable.setSelectionBackground(new Color(184, 207, 229));

        // إعداد عرض الأعمدة
        int[] columnWidths = {80, 120, 120, 60, 100, 80, 60, 120};
        for (int i = 0; i < columnWidths.length && i < unitsTable.getColumnCount(); i++) {
            unitsTable.getColumnModel().getColumn(i).setPreferredWidth(columnWidths[i]);
        }
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة العلوية - البحث والأزرار
        JPanel topPanel = createTopPanel();
        add(topPanel, BorderLayout.NORTH);

        // الجدول الرئيسي
        JScrollPane scrollPane = new JScrollPane(unitsTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        add(scrollPane, BorderLayout.CENTER);

        // اللوحة السفلية - الحالة
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }

    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "أدوات إدارة وحدات القياس", TitledBorder.RIGHT, TitledBorder.TOP));

        // لوحة البحث
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchPanel.add(new JLabel("البحث:"));
        searchPanel.add(searchField);

        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonPanel.add(addButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(refreshButton);

        panel.add(searchPanel, BorderLayout.CENTER);
        panel.add(buttonPanel, BorderLayout.EAST);
        panel.add(progressBar, BorderLayout.SOUTH);

        return panel;
    }

    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.add(statusLabel, BorderLayout.WEST);
        return panel;
    }

    private void connectToDatabase() {
        try {
            // إعداد خصائص Oracle لدعم الترميز العربي
            java.util.Properties props = new java.util.Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");

            String url = "*************************************";
            connection = DriverManager.getConnection(url, props);
            statusLabel.setText("✅ تم الاتصال بقاعدة البيانات بنجاح");

        } catch (SQLException e) {
            statusLabel.setText("❌ خطأ في الاتصال بقاعدة البيانات");
            System.err.println("خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());

            // إنشاء بيانات تجريبية
            createSampleData();
        }
    }

    private void loadData() {
        SwingUtilities.invokeLater(() -> {
            progressBar.setIndeterminate(true);
            progressBar.setString("جاري تحميل وحدات القياس...");
        });

        new Thread(() -> {
            try {
                if (connection != null) {
                    loadDataFromDatabase();
                } else {
                    createSampleData();
                }

                SwingUtilities.invokeLater(() -> {
                    progressBar.setIndeterminate(false);
                    progressBar.setValue(100);
                    progressBar.setString("تم التحميل بنجاح");
                    statusLabel.setText("✅ تم تحميل " + tableModel.getRowCount() + " وحدة قياس");
                });

            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    progressBar.setIndeterminate(false);
                    progressBar.setValue(0);
                    progressBar.setString("خطأ في التحميل");
                    statusLabel.setText("❌ خطأ في تحميل البيانات: " + e.getMessage());
                });
            }
        }).start();
    }

    private void loadDataFromDatabase() {
        try {
            String sql = "SELECT * FROM ERP_MEASUREMENT ORDER BY MEASURE";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                tableModel.setRowCount(0);

                while (rs.next()) {
                    Object[] row = {rs.getString("MEASURE_CODE"), // كود الوحدة
                            rs.getString("MEASURE"), // اسم الوحدة بالعربية
                            rs.getString("MEASURE_F_NM"), // الاسم الإنجليزي
                            rs.getString("MEASURE_CODE_GB"), // الكود العالمي (كرمز)
                            getMeasureTypeText(rs.getInt("MEASURE_TYPE")), // نوع الوحدة
                            rs.getDouble("DFLT_SIZE") != 0 ? rs.getDouble("DFLT_SIZE") : 1.0, // الحجم
                                                                                              // الافتراضي
                            rs.getInt("ALLOW_UPD") == 1 ? "نشط" : "غير نشط", // الحالة
                            rs.getTimestamp("AD_DATE") // تاريخ الإنشاء
                    };
                    tableModel.addRow(row);
                }

                System.out.println(
                        "✅ تم تحميل " + tableModel.getRowCount() + " وحدة قياس من قاعدة البيانات");

            }
        } catch (SQLException e) {
            System.err.println("خطأ في تحميل البيانات: " + e.getMessage());
            e.printStackTrace();
            createSampleData();
        }
    }

    /**
     * تحويل رقم نوع الوحدة إلى نص
     */
    private String getMeasureTypeText(int measureType) {
        switch (measureType) {
            case 1:
                return "عادي";
            case 2:
                return "وزن";
            case 3:
                return "حجم";
            case 4:
                return "طول";
            case 5:
                return "مساحة";
            default:
                return "غير محدد";
        }
    }

    private void createSampleData() {
        tableModel.setRowCount(0);

        // بيانات تجريبية لوحدات القياس
        Object[][] sampleData = {
                {"001", "قطعة", "Piece", "قطعة", "عدد", 1.0, "نشط", new java.util.Date()},
                {"002", "كيلوجرام", "Kilogram", "كجم", "وزن", 1.0, "نشط", new java.util.Date()},
                {"003", "متر", "Meter", "م", "طول", 1.0, "نشط", new java.util.Date()},
                {"004", "لتر", "Liter", "لتر", "حجم", 1.0, "نشط", new java.util.Date()},
                {"005", "صندوق", "Box", "صندوق", "تعبئة", 12.0, "نشط", new java.util.Date()}};

        for (Object[] row : sampleData) {
            tableModel.addRow(row);
        }
    }

    private void setupEventHandlers() {
        // معالج البحث
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                performSearch();
            }
        });

        // معالج الأزرار
        addButton.addActionListener(e -> addUnit());
        editButton.addActionListener(e -> editUnit());
        deleteButton.addActionListener(e -> deleteUnit());
        refreshButton.addActionListener(e -> loadData());

        // معالج إغلاق النافذة
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                if (connection != null) {
                    try {
                        connection.close();
                    } catch (SQLException ex) {
                        System.err.println("خطأ في إغلاق الاتصال: " + ex.getMessage());
                    }
                }
            }
        });
    }

    private void performSearch() {
        String searchText = searchField.getText().trim();
        statusLabel.setText(searchText.isEmpty() ? "جاهز" : "البحث عن: " + searchText);
        // تطبيق البحث (مبسط)
    }

    private void addUnit() {
        JOptionPane.showMessageDialog(this, "ميزة إضافة وحدة قياس قيد التطوير", "إضافة",
                JOptionPane.INFORMATION_MESSAGE);
    }

    private void editUnit() {
        int selectedRow = unitsTable.getSelectedRow();
        if (selectedRow >= 0) {
            JOptionPane.showMessageDialog(this, "ميزة تعديل وحدة القياس قيد التطوير", "تعديل",
                    JOptionPane.INFORMATION_MESSAGE);
        } else {
            JOptionPane.showMessageDialog(this, "يرجى اختيار وحدة قياس للتعديل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
        }
    }

    private void deleteUnit() {
        int selectedRow = unitsTable.getSelectedRow();
        if (selectedRow >= 0) {
            int result =
                    JOptionPane.showConfirmDialog(this, "هل أنت متأكد من حذف وحدة القياس المحددة؟",
                            "تأكيد الحذف", JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                tableModel.removeRow(selectedRow);
                statusLabel.setText("تم حذف وحدة القياس");
            }
        } else {
            JOptionPane.showMessageDialog(this, "يرجى اختيار وحدة قياس للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
        }
    }

    // طريقة main للاختبار المستقل
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new MeasurementUnitsWindow().setVisible(true);
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, "خطأ في تشغيل النافذة: " + e.getMessage());
            }
        });
    }
}
