import java.sql.*;

/**
 * فحص بنية جدول ERP_SYSTEM_TREE
 * Check ERP_SYSTEM_TREE Table Structure
 */
public class CheckSystemTreeStructure {
    
    public static void main(String[] args) {
        System.out.println("🔍 فحص بنية جدول ERP_SYSTEM_TREE...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            checkTableStructure(connection);
            checkThemeEntries(connection);
            
            connection.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فحص بنية الجدول: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * فحص بنية الجدول
     */
    private static void checkTableStructure(Connection connection) throws SQLException {
        System.out.println("📋 أعمدة جدول ERP_SYSTEM_TREE:");
        
        DatabaseMetaData metaData = connection.getMetaData();
        ResultSet columns = metaData.getColumns(null, null, "ERP_SYSTEM_TREE", null);
        
        while (columns.next()) {
            String columnName = columns.getString("COLUMN_NAME");
            String dataType = columns.getString("TYPE_NAME");
            int columnSize = columns.getInt("COLUMN_SIZE");
            String isNullable = columns.getString("IS_NULLABLE");
            
            System.out.println("  " + columnName + " - " + dataType + "(" + columnSize + ") - " + 
                             (isNullable.equals("YES") ? "NULL" : "NOT NULL"));
        }
        
        columns.close();
    }
    
    /**
     * فحص إدخالات المظاهر
     */
    private static void checkThemeEntries(Connection connection) throws SQLException {
        System.out.println("\n📋 البحث عن إدخالات المظاهر:");
        
        String sql = "SELECT * FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR LIKE '%مظهر%' OR NODE_NAME_AR LIKE '%واجهة%' OR NODE_NAME_EN LIKE '%Theme%'";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            ResultSetMetaData rsMetaData = rs.getMetaData();
            int columnCount = rsMetaData.getColumnCount();
            
            // طباعة أسماء الأعمدة
            System.out.print("  ");
            for (int i = 1; i <= columnCount; i++) {
                System.out.print(rsMetaData.getColumnName(i) + "\t");
            }
            System.out.println();
            
            // طباعة البيانات
            while (rs.next()) {
                System.out.print("  ");
                for (int i = 1; i <= columnCount; i++) {
                    Object value = rs.getObject(i);
                    System.out.print((value != null ? value.toString() : "NULL") + "\t");
                }
                System.out.println();
            }
        }
    }
}
