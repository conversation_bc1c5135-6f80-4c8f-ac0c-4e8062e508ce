package com.shipment.erp.model;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * كيان السنة المالية يمثل السنوات المالية في النظام
 */
@Entity
@Table(name = "FISCAL_YEARS")
@SequenceGenerator(name = "fiscal_year_seq", sequenceName = "SEQ_FISCAL_YEAR", allocationSize = 1)
public class FiscalYear extends BaseEntity {

    @Column(name = "YEAR_NAME", nullable = false, length = 20)
    @NotBlank(message = "اسم السنة المالية مطلوب")
    @Size(max = 20, message = "اسم السنة المالية يجب أن يكون أقل من 20 حرف")
    private String yearName;

    @Column(name = "START_DATE", nullable = false)
    @NotNull(message = "تاريخ بداية السنة المالية مطلوب")
    private LocalDate startDate;

    @Column(name = "END_DATE", nullable = false)
    @NotNull(message = "تاريخ نهاية السنة المالية مطلوب")
    private LocalDate endDate;

    @Column(name = "IS_CURRENT", nullable = false)
    private Boolean isCurrent = false;

    @Column(name = "IS_CLOSED", nullable = false)
    private Boolean isClosed = false;

    @Column(name = "DESCRIPTION", length = 500)
    private String description;

    /**
     * Constructor افتراضي
     */
    public FiscalYear() {
        super();
    }

    /**
     * Constructor مع اسم السنة
     */
    public FiscalYear(String yearName) {
        this();
        this.yearName = yearName;
    }

    /**
     * Constructor مع اسم السنة وتواريخ البداية والنهاية
     */
    public FiscalYear(String yearName, LocalDate startDate, LocalDate endDate) {
        this(yearName);
        this.startDate = startDate;
        this.endDate = endDate;
    }

    /**
     * Constructor لإنشاء سنة مالية بناءً على سنة ميلادية
     */
    public FiscalYear(int year) {
        this();
        this.yearName = String.valueOf(year);
        this.startDate = LocalDate.of(year, 1, 1);
        this.endDate = LocalDate.of(year, 12, 31);
    }

    // Getters and Setters

    public String getYearName() {
        return yearName;
    }

    public void setYearName(String yearName) {
        this.yearName = yearName;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Boolean getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Boolean isCurrent) {
        this.isCurrent = isCurrent;
    }

    public Boolean getIsClosed() {
        return isClosed;
    }

    public void setIsClosed(Boolean isClosed) {
        this.isClosed = isClosed;
    }

    // إضافة طرق للتوافق مع النوافذ
    public Boolean isCurrent() {
        return getIsCurrent();
    }

    public void setCurrent(Boolean current) {
        setIsCurrent(current);
    }

    public Boolean isClosed() {
        return getIsClosed();
    }

    public void setClosed(Boolean closed) {
        setIsClosed(closed);
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * التحقق من صحة تواريخ السنة المالية
     */
    @PrePersist
    @PreUpdate
    private void validateDates() {
        if (startDate != null && endDate != null && startDate.isAfter(endDate)) {
            throw new IllegalArgumentException(
                    "تاريخ بداية السنة المالية يجب أن يكون قبل تاريخ النهاية");
        }
    }

    /**
     * التحقق من وقوع تاريخ معين ضمن السنة المالية
     */
    public boolean containsDate(LocalDate date) {
        if (date == null || startDate == null || endDate == null) {
            return false;
        }
        return !date.isBefore(startDate) && !date.isAfter(endDate);
    }

    /**
     * التحقق من تداخل السنة المالية مع سنة أخرى
     */
    public boolean overlapsWith(FiscalYear other) {
        if (other == null || other.startDate == null || other.endDate == null
                || this.startDate == null || this.endDate == null) {
            return false;
        }

        return !(this.endDate.isBefore(other.startDate) || this.startDate.isAfter(other.endDate));
    }

    /**
     * حساب عدد الأيام في السنة المالية
     */
    public long getDaysCount() {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return startDate.until(endDate).getDays() + 1;
    }

    /**
     * حساب عدد الأشهر في السنة المالية
     */
    public long getMonthsCount() {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return startDate.until(endDate).getMonths();
    }

    /**
     * التحقق من انتهاء السنة المالية
     */
    public boolean isExpired() {
        return endDate != null && endDate.isBefore(LocalDate.now());
    }

    /**
     * التحقق من بداية السنة المالية
     */
    public boolean hasStarted() {
        return startDate != null && !startDate.isAfter(LocalDate.now());
    }

    /**
     * التحقق من كون السنة المالية نشطة حالياً
     */
    public boolean isActive() {
        return hasStarted() && !isExpired() && !isClosed;
    }

    /**
     * الحصول على تمثيل نصي للفترة
     */
    public String getPeriodString() {
        if (startDate == null || endDate == null) {
            return yearName;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        return startDate.format(formatter) + " - " + endDate.format(formatter);
    }

    /**
     * الحصول على الاسم الكامل للسنة المالية
     */
    public String getFullName() {
        return yearName + " (" + getPeriodString() + ")";
    }

    /**
     * إغلاق السنة المالية
     */
    public void close() {
        this.isClosed = true;
        this.isCurrent = false;
    }

    /**
     * فتح السنة المالية
     */
    public void open() {
        this.isClosed = false;
    }

    /**
     * تعيين السنة المالية كحالية
     */
    public void setAsCurrent() {
        this.isCurrent = true;
        this.isClosed = false;
    }

    @Override
    public String toString() {
        return "FiscalYear{" + "id=" + getId() + ", yearName='" + yearName + '\'' + ", startDate="
                + startDate + ", endDate=" + endDate + ", isCurrent=" + isCurrent + ", isClosed="
                + isClosed + '}';
    }
}
