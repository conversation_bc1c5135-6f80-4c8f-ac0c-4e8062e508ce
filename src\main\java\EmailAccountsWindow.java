import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة حسابات البريد الإلكتروني Email Accounts Management Window
 */
public class EmailAccountsWindow extends JFrame {

    // مكونات الواجهة
    private JTabbedPane mainTabbedPane;
    private JTable accountsTable;
    private DefaultTableModel tableModel;

    // حقول إدخال البيانات
    private JTextField accountNameArField;
    private JTextField accountNameEnField;
    private JTextField emailAddressField;
    private JTextField displayNameArField;
    private JTextField displayNameEnField;

    // إعدادات الخادم الوارد
    private JTextField incomingServerField;
    private JTextField incomingPortField;
    private JComboBox<String> incomingProtocolCombo;
    private JComboBox<String> incomingSecurityCombo;
    private JTextField incomingUsernameField;
    private JPasswordField incomingPasswordField;

    // إعدادات الخادم الصادر
    private JTextField outgoingServerField;
    private JTextField outgoingPortField;
    private JComboBox<String> outgoingSecurityCombo;
    private JTextField outgoingUsernameField;
    private JPasswordField outgoingPasswordField;
    private JCheckBox outgoingAuthRequiredCheck;

    // إعدادات متقدمة
    private JTextField maxMessageSizeField;
    private JTextField autoCheckIntervalField;
    private JCheckBox keepMessagesOnServerCheck;
    private JTextField deleteAfterDaysField;

    // أزرار التحكم
    private JButton saveButton;
    private JButton cancelButton;
    private JButton testConnectionButton;
    private JButton newAccountButton;
    private JButton deleteAccountButton;

    // اتصال قاعدة البيانات
    private Connection connection;
    private int currentAccountId = -1;

    public EmailAccountsWindow() {
        initializeConnection();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadAccounts();

        setTitle("📧 إدارة حسابات البريد الإلكتروني - Email Accounts Management");
        setSize(1000, 700);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        // تطبيق اتجاه اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        applyComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تطبيق الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        // setUIFont(arabicFont); // سيتم تطبيق الخط لاحقاً
    }

    private void initializeConnection() {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            connection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات لنظام البريد الإلكتروني");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                    "خطأ في الاتصال بقاعدة البيانات:\n" + e.getMessage(), "خطأ في الاتصال",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    private void initializeComponents() {
        // إنشاء التبويبات الرئيسية مع اتجاه عربي
        mainTabbedPane = new JTabbedPane(JTabbedPane.TOP);
        mainTabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainTabbedPane.applyComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إنشاء المكونات
        createAccountsListTab();
        createAccountDetailsTab();
        createServerSettingsTab();
        createAdvancedSettingsTab();

        // أزرار التحكم
        saveButton = new JButton("💾 حفظ - Save");
        cancelButton = new JButton("❌ إلغاء - Cancel");
        testConnectionButton = new JButton("🔗 اختبار الاتصال - Test Connection");
        newAccountButton = new JButton("➕ حساب جديد - New Account");
        deleteAccountButton = new JButton("🗑️ حذف - Delete");

        // تطبيق الاتجاه العربي على الأزرار
        saveButton.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        cancelButton.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        testConnectionButton.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        newAccountButton.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        deleteAccountButton.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void createAccountsListTab() {
        JPanel accountsPanel = new JPanel(new BorderLayout());
        accountsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        accountsPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // إنشاء جدول الحسابات
        String[] columnNames = {"المعرف", "اسم الحساب", "البريد الإلكتروني", "النوع", "الحالة"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        accountsTable = new JTable(tableModel);
        accountsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        accountsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        accountsTable.setRowHeight(25);

        JScrollPane scrollPane = new JScrollPane(accountsTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الأدوات
        JPanel toolbarPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        toolbarPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        toolbarPanel.add(newAccountButton);
        toolbarPanel.add(deleteAccountButton);

        accountsPanel.add(toolbarPanel, BorderLayout.NORTH);
        accountsPanel.add(scrollPane, BorderLayout.CENTER);

        mainTabbedPane.addTab("📋 قائمة الحسابات", accountsPanel);
    }

    private void createAccountDetailsTab() {
        JPanel detailsPanel = new JPanel(new GridBagLayout());
        detailsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // معلومات الحساب الأساسية
        gbc.gridx = 0;
        gbc.gridy = 0;
        JLabel accountNameArLabel = new JLabel("اسم الحساب بالعربية:");
        accountNameArLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(accountNameArLabel, gbc);
        gbc.gridx = 1;
        accountNameArField = new JTextField(30);
        accountNameArField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(accountNameArField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 1;
        JLabel accountNameEnLabel = new JLabel("اسم الحساب بالإنجليزية:");
        accountNameEnLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(accountNameEnLabel, gbc);
        gbc.gridx = 1;
        accountNameEnField = new JTextField(30);
        accountNameEnField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(accountNameEnField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 2;
        JLabel emailLabel = new JLabel("عنوان البريد الإلكتروني:");
        emailLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(emailLabel, gbc);
        gbc.gridx = 1;
        emailAddressField = new JTextField(30);
        emailAddressField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(emailAddressField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 3;
        JLabel displayNameArLabel = new JLabel("الاسم المعروض بالعربية:");
        displayNameArLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(displayNameArLabel, gbc);
        gbc.gridx = 1;
        displayNameArField = new JTextField(30);
        displayNameArField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(displayNameArField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 4;
        JLabel displayNameEnLabel = new JLabel("الاسم المعروض بالإنجليزية:");
        displayNameEnLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(displayNameEnLabel, gbc);
        gbc.gridx = 1;
        displayNameEnField = new JTextField(30);
        displayNameEnField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        detailsPanel.add(displayNameEnField, gbc);

        mainTabbedPane.addTab("📝 تفاصيل الحساب", detailsPanel);
    }

    private void createServerSettingsTab() {
        JPanel serverPanel = new JPanel(new GridBagLayout());
        serverPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        serverPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // إعدادات الخادم الوارد
        TitledBorder incomingBorder =
                BorderFactory.createTitledBorder("إعدادات الخادم الوارد - Incoming Server");
        // incomingBorder.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT); // غير مدعوم

        JPanel incomingPanel = new JPanel(new GridBagLayout());
        incomingPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        incomingPanel.setBorder(incomingBorder);

        gbc.gridx = 0;
        gbc.gridy = 0;
        JLabel incomingServerLabel = new JLabel("خادم البريد الوارد:");
        incomingServerLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        incomingPanel.add(incomingServerLabel, gbc);
        gbc.gridx = 1;
        incomingServerField = new JTextField(25);
        incomingServerField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        incomingPanel.add(incomingServerField, gbc);

        gbc.gridx = 2;
        JLabel incomingPortLabel = new JLabel("المنفذ:");
        incomingPortLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        incomingPanel.add(incomingPortLabel, gbc);
        gbc.gridx = 3;
        incomingPortField = new JTextField("993", 8);
        incomingPortField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        incomingPanel.add(incomingPortField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 1;
        JLabel protocolLabel = new JLabel("البروتوكول:");
        protocolLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        incomingPanel.add(protocolLabel, gbc);
        gbc.gridx = 1;
        incomingProtocolCombo = new JComboBox<>(new String[] {"IMAP", "POP3"});
        incomingProtocolCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        incomingPanel.add(incomingProtocolCombo, gbc);

        gbc.gridx = 2;
        JLabel incomingSecurityLabel = new JLabel("الأمان:");
        incomingSecurityLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        incomingPanel.add(incomingSecurityLabel, gbc);
        gbc.gridx = 3;
        incomingSecurityCombo = new JComboBox<>(new String[] {"SSL", "TLS", "NONE"});
        incomingSecurityCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        incomingPanel.add(incomingSecurityCombo, gbc);

        // إعدادات الخادم الصادر
        TitledBorder outgoingBorder =
                BorderFactory.createTitledBorder("إعدادات الخادم الصادر - Outgoing Server");
        // outgoingBorder.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT); // غير مدعوم

        JPanel outgoingPanel = new JPanel(new GridBagLayout());
        outgoingPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        outgoingPanel.setBorder(outgoingBorder);

        gbc.gridx = 0;
        gbc.gridy = 0;
        JLabel outgoingServerLabel = new JLabel("خادم البريد الصادر:");
        outgoingServerLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        outgoingPanel.add(outgoingServerLabel, gbc);
        gbc.gridx = 1;
        outgoingServerField = new JTextField(25);
        outgoingServerField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        outgoingPanel.add(outgoingServerField, gbc);

        gbc.gridx = 2;
        JLabel outgoingPortLabel = new JLabel("المنفذ:");
        outgoingPortLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        outgoingPanel.add(outgoingPortLabel, gbc);
        gbc.gridx = 3;
        outgoingPortField = new JTextField("587", 8);
        outgoingPortField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        outgoingPanel.add(outgoingPortField, gbc);

        // إضافة اللوحات إلى التبويب
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 4;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        serverPanel.add(incomingPanel, gbc);

        gbc.gridy = 1;
        serverPanel.add(outgoingPanel, gbc);

        mainTabbedPane.addTab("🔧 إعدادات الخادم", serverPanel);
    }

    private void createAdvancedSettingsTab() {
        JPanel advancedPanel = new JPanel(new GridBagLayout());
        advancedPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        advancedPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // الإعدادات المتقدمة
        gbc.gridx = 0;
        gbc.gridy = 0;
        JLabel maxSizeLabel = new JLabel("الحد الأقصى لحجم الرسالة (MB):");
        maxSizeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        advancedPanel.add(maxSizeLabel, gbc);
        gbc.gridx = 1;
        maxMessageSizeField = new JTextField("25", 10);
        maxMessageSizeField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        advancedPanel.add(maxMessageSizeField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 1;
        JLabel intervalLabel = new JLabel("فترة التحقق التلقائي (دقيقة):");
        intervalLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        advancedPanel.add(intervalLabel, gbc);
        gbc.gridx = 1;
        autoCheckIntervalField = new JTextField("15", 10);
        autoCheckIntervalField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        advancedPanel.add(autoCheckIntervalField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.gridwidth = 2;
        keepMessagesOnServerCheck = new JCheckBox("الاحتفاظ بالرسائل على الخادم");
        keepMessagesOnServerCheck.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        keepMessagesOnServerCheck.setSelected(true);
        advancedPanel.add(keepMessagesOnServerCheck, gbc);

        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.gridwidth = 1;
        JLabel deleteAfterLabel = new JLabel("حذف الرسائل بعد (يوم):");
        deleteAfterLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        advancedPanel.add(deleteAfterLabel, gbc);
        gbc.gridx = 1;
        deleteAfterDaysField = new JTextField("30", 10);
        deleteAfterDaysField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        advancedPanel.add(deleteAfterDaysField, gbc);

        mainTabbedPane.addTab("⚙️ الإعدادات المتقدمة", advancedPanel);
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // الشريط السفلي
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        bottomPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        bottomPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        bottomPanel.add(testConnectionButton);
        bottomPanel.add(saveButton);
        bottomPanel.add(cancelButton);

        add(mainTabbedPane, BorderLayout.CENTER);
        add(bottomPanel, BorderLayout.SOUTH);
    }

    private void setupEventHandlers() {
        // معالج حفظ البيانات
        saveButton.addActionListener(e -> saveAccount());

        // معالج الإلغاء
        cancelButton.addActionListener(e -> dispose());

        // معالج اختبار الاتصال
        testConnectionButton.addActionListener(e -> testConnection());

        // معالج حساب جديد
        newAccountButton.addActionListener(e -> newAccount());

        // معالج حذف الحساب
        deleteAccountButton.addActionListener(e -> deleteAccount());

        // معالج اختيار حساب من الجدول
        accountsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedAccount();
            }
        });
    }

    private void loadAccounts() {
        if (connection == null)
            return;

        try {
            String sql = """
                    SELECT ACCOUNT_ID, ACCOUNT_NAME_AR, EMAIL_ADDRESS, ACCOUNT_TYPE, ACCOUNT_STATUS
                    FROM ERP_EMAIL_ACCOUNTS
                    WHERE IS_ACTIVE = 'Y'
                    ORDER BY ACCOUNT_NAME_AR
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                // مسح البيانات الحالية
                tableModel.setRowCount(0);

                while (rs.next()) {
                    Object[] row = {rs.getInt("ACCOUNT_ID"), rs.getString("ACCOUNT_NAME_AR"),
                            rs.getString("EMAIL_ADDRESS"), rs.getString("ACCOUNT_TYPE"),
                            rs.getString("ACCOUNT_STATUS")};
                    tableModel.addRow(row);
                }

                System.out
                        .println("✅ تم تحميل " + tableModel.getRowCount() + " حساب بريد إلكتروني");
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل الحسابات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ في تحميل الحسابات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    private void loadSelectedAccount() {
        int selectedRow = accountsTable.getSelectedRow();
        if (selectedRow >= 0) {
            currentAccountId = (Integer) tableModel.getValueAt(selectedRow, 0);
            loadAccountDetails(currentAccountId);
        }
    }

    private void loadAccountDetails(int accountId) {
        if (connection == null)
            return;

        try {
            String sql = """
                    SELECT * FROM ERP_EMAIL_ACCOUNTS WHERE ACCOUNT_ID = ?
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setInt(1, accountId);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        // تحميل البيانات الأساسية
                        accountNameArField.setText(rs.getString("ACCOUNT_NAME_AR"));
                        accountNameEnField.setText(rs.getString("ACCOUNT_NAME_EN"));
                        emailAddressField.setText(rs.getString("EMAIL_ADDRESS"));
                        displayNameArField.setText(rs.getString("DISPLAY_NAME_AR"));
                        displayNameEnField.setText(rs.getString("DISPLAY_NAME_EN"));

                        // تحميل إعدادات الخادم
                        incomingServerField.setText(rs.getString("INCOMING_SERVER"));
                        incomingPortField.setText(String.valueOf(rs.getInt("INCOMING_PORT")));
                        incomingProtocolCombo.setSelectedItem(rs.getString("INCOMING_PROTOCOL"));
                        incomingSecurityCombo.setSelectedItem(rs.getString("INCOMING_SECURITY"));

                        outgoingServerField.setText(rs.getString("OUTGOING_SERVER"));
                        outgoingPortField.setText(String.valueOf(rs.getInt("OUTGOING_PORT")));
                        outgoingSecurityCombo.setSelectedItem(rs.getString("OUTGOING_SECURITY"));

                        // تحميل الإعدادات المتقدمة
                        maxMessageSizeField.setText(String.valueOf(rs.getInt("MAX_MESSAGE_SIZE")));
                        autoCheckIntervalField
                                .setText(String.valueOf(rs.getInt("AUTO_CHECK_INTERVAL")));
                        keepMessagesOnServerCheck
                                .setSelected("Y".equals(rs.getString("KEEP_MESSAGES_ON_SERVER")));
                        deleteAfterDaysField
                                .setText(String.valueOf(rs.getInt("DELETE_AFTER_DAYS")));

                        System.out.println(
                                "✅ تم تحميل تفاصيل الحساب: " + rs.getString("ACCOUNT_NAME_AR"));
                    }
                }
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل تفاصيل الحساب: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ في تحميل تفاصيل الحساب:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void saveAccount() {
        if (connection == null) {
            JOptionPane.showMessageDialog(this, "لا يوجد اتصال بقاعدة البيانات", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return;
        }

        try {
            String sql;
            if (currentAccountId == -1) {
                // إدراج حساب جديد
                sql = """
                        INSERT INTO ERP_EMAIL_ACCOUNTS (
                            ACCOUNT_ID, ACCOUNT_NAME_AR, ACCOUNT_NAME_EN, EMAIL_ADDRESS,
                            DISPLAY_NAME_AR, DISPLAY_NAME_EN, INCOMING_SERVER, INCOMING_PORT,
                            INCOMING_PROTOCOL, INCOMING_SECURITY, OUTGOING_SERVER, OUTGOING_PORT,
                            OUTGOING_SECURITY, MAX_MESSAGE_SIZE, AUTO_CHECK_INTERVAL,
                            KEEP_MESSAGES_ON_SERVER, DELETE_AFTER_DAYS, CREATED_BY
                        ) VALUES (
                            ERP_EMAIL_ACCOUNTS_SEQ.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'USER'
                        )
                        """;
            } else {
                // تحديث حساب موجود
                sql = """
                        UPDATE ERP_EMAIL_ACCOUNTS SET
                            ACCOUNT_NAME_AR = ?, ACCOUNT_NAME_EN = ?, EMAIL_ADDRESS = ?,
                            DISPLAY_NAME_AR = ?, DISPLAY_NAME_EN = ?, INCOMING_SERVER = ?, INCOMING_PORT = ?,
                            INCOMING_PROTOCOL = ?, INCOMING_SECURITY = ?, OUTGOING_SERVER = ?, OUTGOING_PORT = ?,
                            OUTGOING_SECURITY = ?, MAX_MESSAGE_SIZE = ?, AUTO_CHECK_INTERVAL = ?,
                            KEEP_MESSAGES_ON_SERVER = ?, DELETE_AFTER_DAYS = ?, LAST_UPDATED = SYSDATE, UPDATED_BY = 'USER'
                        WHERE ACCOUNT_ID = ?
                        """;
            }

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, accountNameArField.getText());
                stmt.setString(2, accountNameEnField.getText());
                stmt.setString(3, emailAddressField.getText());
                stmt.setString(4, displayNameArField.getText());
                stmt.setString(5, displayNameEnField.getText());
                stmt.setString(6, incomingServerField.getText());
                stmt.setInt(7, Integer.parseInt(incomingPortField.getText()));
                stmt.setString(8, (String) incomingProtocolCombo.getSelectedItem());
                stmt.setString(9, (String) incomingSecurityCombo.getSelectedItem());
                stmt.setString(10, outgoingServerField.getText());
                stmt.setInt(11, Integer.parseInt(outgoingPortField.getText()));
                stmt.setString(12, (String) outgoingSecurityCombo.getSelectedItem());
                stmt.setInt(13, Integer.parseInt(maxMessageSizeField.getText()));
                stmt.setInt(14, Integer.parseInt(autoCheckIntervalField.getText()));
                stmt.setString(15, keepMessagesOnServerCheck.isSelected() ? "Y" : "N");
                stmt.setInt(16, Integer.parseInt(deleteAfterDaysField.getText()));

                if (currentAccountId != -1) {
                    stmt.setInt(17, currentAccountId);
                }

                int result = stmt.executeUpdate();

                if (result > 0) {
                    JOptionPane.showMessageDialog(this, "تم حفظ الحساب بنجاح", "نجح",
                            JOptionPane.INFORMATION_MESSAGE);
                    loadAccounts(); // إعادة تحميل القائمة
                    if (currentAccountId == -1) {
                        clearFields(); // مسح الحقول للحساب الجديد
                    }
                } else {
                    JOptionPane.showMessageDialog(this, "فشل في حفظ الحساب", "خطأ",
                            JOptionPane.ERROR_MESSAGE);
                }
            }

        } catch (Exception e) {
            System.err.println("❌ خطأ في حفظ الحساب: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ في حفظ الحساب:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    private void testConnection() {
        JOptionPane.showMessageDialog(this,
                "اختبار الاتصال قيد التطوير\nTest Connection Under Development", "معلومات",
                JOptionPane.INFORMATION_MESSAGE);
    }

    private void newAccount() {
        currentAccountId = -1;
        clearFields();
        mainTabbedPane.setSelectedIndex(1); // الانتقال إلى تبويب التفاصيل
    }

    private void deleteAccount() {
        int selectedRow = accountsTable.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار حساب للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        int accountId = (Integer) tableModel.getValueAt(selectedRow, 0);
        String accountName = (String) tableModel.getValueAt(selectedRow, 1);

        int confirm = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف الحساب: " + accountName + "؟", "تأكيد الحذف",
                JOptionPane.YES_NO_OPTION);

        if (confirm == JOptionPane.YES_OPTION) {
            try {
                String sql = "UPDATE ERP_EMAIL_ACCOUNTS SET IS_ACTIVE = 'N' WHERE ACCOUNT_ID = ?";

                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setInt(1, accountId);
                    int result = stmt.executeUpdate();

                    if (result > 0) {
                        JOptionPane.showMessageDialog(this, "تم حذف الحساب بنجاح", "نجح",
                                JOptionPane.INFORMATION_MESSAGE);
                        loadAccounts();
                        clearFields();
                    } else {
                        JOptionPane.showMessageDialog(this, "فشل في حذف الحساب", "خطأ",
                                JOptionPane.ERROR_MESSAGE);
                    }
                }

            } catch (SQLException e) {
                System.err.println("❌ خطأ في حذف الحساب: " + e.getMessage());
                JOptionPane.showMessageDialog(this, "خطأ في حذف الحساب:\n" + e.getMessage(), "خطأ",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private void clearFields() {
        accountNameArField.setText("");
        accountNameEnField.setText("");
        emailAddressField.setText("");
        displayNameArField.setText("");
        displayNameEnField.setText("");
        incomingServerField.setText("");
        incomingPortField.setText("993");
        incomingProtocolCombo.setSelectedIndex(0);
        incomingSecurityCombo.setSelectedIndex(0);
        outgoingServerField.setText("");
        outgoingPortField.setText("587");
        outgoingSecurityCombo.setSelectedIndex(1);
        maxMessageSizeField.setText("25");
        autoCheckIntervalField.setText("15");
        keepMessagesOnServerCheck.setSelected(true);
        deleteAfterDaysField.setText("30");
        currentAccountId = -1;
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            } catch (Exception e) {
                e.printStackTrace();
            }

            new EmailAccountsWindow().setVisible(true);
        });
    }
}
