-- سكري<PERSON><PERSON> إعداد قاعدة البيانات لنظام إدارة الشحنات
-- Ship ERP Database Setup Script
-- Oracle SID: orcl

-- إنشاء مستخدم قاعدة البيانات
-- يج<PERSON> تشغيل هذا السكريبت بصلاحيات DBA

-- إنشاء Tablespace للتطبيق
CREATE TABLESPACE SHIP_ERP_DATA
DATAFILE 'ship_erp_data.dbf' SIZE 100M
AUTOEXTEND ON NEXT 10M MAXSIZE 1G
EXTENT MANAGEMENT LOCAL
SEGMENT SPACE MANAGEMENT AUTO;

CREATE TABLESPACE SHIP_ERP_INDEX
DATAFILE 'ship_erp_index.dbf' SIZE 50M
AUTOEXTEND ON NEXT 5M MAXSIZE 500M
EXTENT MANAGEMENT LOCAL
SEGMENT SPACE MANAGEMENT AUTO;

-- إنشاء مستخدم التطبيق
CREATE USER ship_erp IDENTIFIED BY ship_erp_password
DEFAULT TABLESPACE SHIP_ERP_DATA
TEMPORARY TABLESPACE TEMP
QUOTA UNLIMITED ON SHIP_ERP_DATA
QUOTA UNLIMITED ON SHIP_ERP_INDEX;

-- منح الصلاحيات
GRANT CONNECT TO ship_erp;
GRANT RESOURCE TO ship_erp;
GRANT CREATE SESSION TO ship_erp;
GRANT CREATE TABLE TO ship_erp;
GRANT CREATE VIEW TO ship_erp;
GRANT CREATE SEQUENCE TO ship_erp;
GRANT CREATE PROCEDURE TO ship_erp;
GRANT CREATE TRIGGER TO ship_erp;
GRANT CREATE SYNONYM TO ship_erp;

-- صلاحيات إضافية للنسخ الاحتياطي
GRANT EXP_FULL_DATABASE TO ship_erp;
GRANT IMP_FULL_DATABASE TO ship_erp;

-- الاتصال بمستخدم التطبيق
CONNECT ship_erp/ship_erp_password;

-- إنشاء Sequences
CREATE SEQUENCE SEQ_COMPANY START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_BRANCH START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_USER START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_ROLE START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_PERMISSION START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_CURRENCY START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_FISCAL_YEAR START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SYSTEM_SETTINGS START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SUPPLIER START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_ITEM START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SHIPMENT START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_CUSTOMS_ENTRY START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_COST START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_AUDIT_LOG START WITH 1 INCREMENT BY 1 NOCACHE;

-- جدول الشركات
CREATE TABLE COMPANIES (
    ID NUMBER(10) PRIMARY KEY,
    NAME NVARCHAR2(200) NOT NULL,
    NAME_EN VARCHAR2(200),
    ADDRESS NVARCHAR2(500),
    CITY NVARCHAR2(100),
    COUNTRY NVARCHAR2(100),
    PHONE VARCHAR2(50),
    FAX VARCHAR2(50),
    EMAIL VARCHAR2(100),
    WEBSITE VARCHAR2(200),
    TAX_NUMBER VARCHAR2(50),
    COMMERCIAL_REGISTER VARCHAR2(50),
    LOGO BLOB,
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_BY NUMBER(10),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MODIFIED_BY NUMBER(10),
    MODIFIED_DATE DATE
);

-- جدول الفروع
CREATE TABLE BRANCHES (
    ID NUMBER(10) PRIMARY KEY,
    COMPANY_ID NUMBER(10) NOT NULL,
    CODE VARCHAR2(20) NOT NULL,
    NAME NVARCHAR2(200) NOT NULL,
    NAME_EN VARCHAR2(200),
    ADDRESS NVARCHAR2(500),
    MANAGER_NAME NVARCHAR2(100),
    PHONE VARCHAR2(50),
    EMAIL VARCHAR2(100),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_BY NUMBER(10),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MODIFIED_BY NUMBER(10),
    MODIFIED_DATE DATE,
    CONSTRAINT FK_BRANCH_COMPANY FOREIGN KEY (COMPANY_ID) REFERENCES COMPANIES(ID)
);

-- جدول الأدوار
CREATE TABLE ROLES (
    ID NUMBER(10) PRIMARY KEY,
    NAME NVARCHAR2(100) NOT NULL,
    NAME_EN VARCHAR2(100),
    DESCRIPTION NVARCHAR2(500),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_BY NUMBER(10),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MODIFIED_BY NUMBER(10),
    MODIFIED_DATE DATE
);

-- جدول المستخدمين
CREATE TABLE USERS (
    ID NUMBER(10) PRIMARY KEY,
    USERNAME VARCHAR2(50) NOT NULL UNIQUE,
    PASSWORD_HASH VARCHAR2(255) NOT NULL,
    FULL_NAME NVARCHAR2(200) NOT NULL,
    EMAIL VARCHAR2(100),
    PHONE VARCHAR2(50),
    ROLE_ID NUMBER(10),
    BRANCH_ID NUMBER(10),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    LAST_LOGIN DATE,
    LOGIN_ATTEMPTS NUMBER(3) DEFAULT 0,
    LOCKED_UNTIL DATE,
    PASSWORD_CHANGED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY NUMBER(10),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MODIFIED_BY NUMBER(10),
    MODIFIED_DATE DATE,
    CONSTRAINT FK_USER_ROLE FOREIGN KEY (ROLE_ID) REFERENCES ROLES(ID),
    CONSTRAINT FK_USER_BRANCH FOREIGN KEY (BRANCH_ID) REFERENCES BRANCHES(ID)
);

-- جدول الصلاحيات
CREATE TABLE PERMISSIONS (
    ID NUMBER(10) PRIMARY KEY,
    ROLE_ID NUMBER(10) NOT NULL,
    MODULE_NAME VARCHAR2(100) NOT NULL,
    CAN_READ NUMBER(1) DEFAULT 0,
    CAN_WRITE NUMBER(1) DEFAULT 0,
    CAN_DELETE NUMBER(1) DEFAULT 0,
    CAN_PRINT NUMBER(1) DEFAULT 0,
    CAN_EXPORT NUMBER(1) DEFAULT 0,
    CREATED_BY NUMBER(10),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MODIFIED_BY NUMBER(10),
    MODIFIED_DATE DATE,
    CONSTRAINT FK_PERMISSION_ROLE FOREIGN KEY (ROLE_ID) REFERENCES ROLES(ID),
    CONSTRAINT UK_ROLE_MODULE UNIQUE (ROLE_ID, MODULE_NAME)
);

-- جدول العملات
CREATE TABLE CURRENCIES (
    ID NUMBER(10) PRIMARY KEY,
    CODE VARCHAR2(3) NOT NULL UNIQUE,
    NAME NVARCHAR2(100) NOT NULL,
    NAME_EN VARCHAR2(100),
    SYMBOL NVARCHAR2(10),
    EXCHANGE_RATE NUMBER(15,6) DEFAULT 1,
    IS_DEFAULT NUMBER(1) DEFAULT 0,
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_BY NUMBER(10),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MODIFIED_BY NUMBER(10),
    MODIFIED_DATE DATE
);

-- جدول السنوات المالية
CREATE TABLE FISCAL_YEARS (
    ID NUMBER(10) PRIMARY KEY,
    YEAR_NAME VARCHAR2(20) NOT NULL,
    START_DATE DATE NOT NULL,
    END_DATE DATE NOT NULL,
    IS_CURRENT NUMBER(1) DEFAULT 0,
    IS_CLOSED NUMBER(1) DEFAULT 0,
    CREATED_BY NUMBER(10),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MODIFIED_BY NUMBER(10),
    MODIFIED_DATE DATE
);

-- جدول الإعدادات العامة
CREATE TABLE SYSTEM_SETTINGS (
    ID NUMBER(10) PRIMARY KEY,
    SETTING_KEY VARCHAR2(100) NOT NULL UNIQUE,
    SETTING_VALUE NVARCHAR2(1000),
    SETTING_TYPE VARCHAR2(20) DEFAULT 'STRING',
    DESCRIPTION NVARCHAR2(500),
    IS_SYSTEM NUMBER(1) DEFAULT 0,
    CREATED_BY NUMBER(10),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MODIFIED_BY NUMBER(10),
    MODIFIED_DATE DATE
);

-- جدول سجل التدقيق
CREATE TABLE AUDIT_LOG (
    ID NUMBER(10) PRIMARY KEY,
    USER_ID NUMBER(10),
    ACTION VARCHAR2(50) NOT NULL,
    TABLE_NAME VARCHAR2(100),
    RECORD_ID NUMBER(10),
    OLD_VALUES CLOB,
    NEW_VALUES CLOB,
    IP_ADDRESS VARCHAR2(45),
    USER_AGENT VARCHAR2(500),
    ACTION_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_AUDIT_USER FOREIGN KEY (USER_ID) REFERENCES USERS(ID)
);

-- إنشاء Indexes
CREATE INDEX IDX_BRANCH_COMPANY ON BRANCHES(COMPANY_ID);
CREATE INDEX IDX_USER_ROLE ON USERS(ROLE_ID);
CREATE INDEX IDX_USER_BRANCH ON USERS(BRANCH_ID);
CREATE INDEX IDX_PERMISSION_ROLE ON PERMISSIONS(ROLE_ID);
CREATE INDEX IDX_AUDIT_USER ON AUDIT_LOG(USER_ID);
CREATE INDEX IDX_AUDIT_DATE ON AUDIT_LOG(ACTION_DATE);
CREATE INDEX IDX_AUDIT_TABLE ON AUDIT_LOG(TABLE_NAME);

-- إدخال البيانات الأساسية

-- إدخال شركة افتراضية
INSERT INTO COMPANIES (ID, NAME, NAME_EN, IS_ACTIVE, CREATED_DATE)
VALUES (SEQ_COMPANY.NEXTVAL, 'شركة الشحن المتحدة', 'United Shipping Company', 1, SYSDATE);

-- إدخال فرع رئيسي
INSERT INTO BRANCHES (ID, COMPANY_ID, CODE, NAME, NAME_EN, IS_ACTIVE, CREATED_DATE)
VALUES (SEQ_BRANCH.NEXTVAL, 1, 'MAIN', 'الفرع الرئيسي', 'Main Branch', 1, SYSDATE);

-- إدخال دور المدير العام
INSERT INTO ROLES (ID, NAME, NAME_EN, DESCRIPTION, IS_ACTIVE, CREATED_DATE)
VALUES (SEQ_ROLE.NEXTVAL, 'مدير عام', 'System Administrator', 'صلاحيات كاملة للنظام', 1, SYSDATE);

-- إدخال مستخدم المدير العام (كلمة المرور: admin123)
INSERT INTO USERS (ID, USERNAME, PASSWORD_HASH, FULL_NAME, ROLE_ID, BRANCH_ID, IS_ACTIVE, CREATED_DATE)
VALUES (SEQ_USER.NEXTVAL, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lIktFjhmkNjNfPDHG', 
        'مدير النظام', 1, 1, 1, SYSDATE);

-- إدخال صلاحيات المدير العام
INSERT INTO PERMISSIONS (ID, ROLE_ID, MODULE_NAME, CAN_READ, CAN_WRITE, CAN_DELETE, CAN_PRINT, CAN_EXPORT, CREATED_DATE)
VALUES (SEQ_PERMISSION.NEXTVAL, 1, 'SETTINGS', 1, 1, 1, 1, 1, SYSDATE);

INSERT INTO PERMISSIONS (ID, ROLE_ID, MODULE_NAME, CAN_READ, CAN_WRITE, CAN_DELETE, CAN_PRINT, CAN_EXPORT, CREATED_DATE)
VALUES (SEQ_PERMISSION.NEXTVAL, 1, 'USERS', 1, 1, 1, 1, 1, SYSDATE);

INSERT INTO PERMISSIONS (ID, ROLE_ID, MODULE_NAME, CAN_READ, CAN_WRITE, CAN_DELETE, CAN_PRINT, CAN_EXPORT, CREATED_DATE)
VALUES (SEQ_PERMISSION.NEXTVAL, 1, 'COMPANIES', 1, 1, 1, 1, 1, SYSDATE);

-- إدخال عملات أساسية
INSERT INTO CURRENCIES (ID, CODE, NAME, NAME_EN, SYMBOL, EXCHANGE_RATE, IS_DEFAULT, IS_ACTIVE, CREATED_DATE)
VALUES (SEQ_CURRENCY.NEXTVAL, 'SAR', 'ريال سعودي', 'Saudi Riyal', 'ر.س', 1, 1, 1, SYSDATE);

INSERT INTO CURRENCIES (ID, CODE, NAME, NAME_EN, SYMBOL, EXCHANGE_RATE, IS_DEFAULT, IS_ACTIVE, CREATED_DATE)
VALUES (SEQ_CURRENCY.NEXTVAL, 'USD', 'دولار أمريكي', 'US Dollar', '$', 3.75, 0, 1, SYSDATE);

INSERT INTO CURRENCIES (ID, CODE, NAME, NAME_EN, SYMBOL, EXCHANGE_RATE, IS_DEFAULT, IS_ACTIVE, CREATED_DATE)
VALUES (SEQ_CURRENCY.NEXTVAL, 'EUR', 'يورو', 'Euro', '€', 4.10, 0, 1, SYSDATE);

-- إدخال سنة مالية حالية
INSERT INTO FISCAL_YEARS (ID, YEAR_NAME, START_DATE, END_DATE, IS_CURRENT, IS_CLOSED, CREATED_DATE)
VALUES (SEQ_FISCAL_YEAR.NEXTVAL, '2025', DATE '2025-01-01', DATE '2025-12-31', 1, 0, SYSDATE);

-- إدخال إعدادات النظام الأساسية
INSERT INTO SYSTEM_SETTINGS (ID, SETTING_KEY, SETTING_VALUE, SETTING_TYPE, DESCRIPTION, IS_SYSTEM, CREATED_DATE)
VALUES (SEQ_SYSTEM_SETTINGS.NEXTVAL, 'APP_NAME', 'نظام إدارة الشحنات', 'STRING', 'اسم التطبيق', 1, SYSDATE);

INSERT INTO SYSTEM_SETTINGS (ID, SETTING_KEY, SETTING_VALUE, SETTING_TYPE, DESCRIPTION, IS_SYSTEM, CREATED_DATE)
VALUES (SEQ_SYSTEM_SETTINGS.NEXTVAL, 'APP_VERSION', '1.0.0', 'STRING', 'إصدار التطبيق', 1, SYSDATE);

INSERT INTO SYSTEM_SETTINGS (ID, SETTING_KEY, SETTING_VALUE, SETTING_TYPE, DESCRIPTION, IS_SYSTEM, CREATED_DATE)
VALUES (SEQ_SYSTEM_SETTINGS.NEXTVAL, 'DEFAULT_LOCALE', 'ar_SA', 'STRING', 'اللغة الافتراضية', 1, SYSDATE);

INSERT INTO SYSTEM_SETTINGS (ID, SETTING_KEY, SETTING_VALUE, SETTING_TYPE, DESCRIPTION, IS_SYSTEM, CREATED_DATE)
VALUES (SEQ_SYSTEM_SETTINGS.NEXTVAL, 'DECIMAL_PLACES', '2', 'INTEGER', 'عدد المنازل العشرية', 0, SYSDATE);

INSERT INTO SYSTEM_SETTINGS (ID, SETTING_KEY, SETTING_VALUE, SETTING_TYPE, DESCRIPTION, IS_SYSTEM, CREATED_DATE)
VALUES (SEQ_SYSTEM_SETTINGS.NEXTVAL, 'BACKUP_ENABLED', 'true', 'BOOLEAN', 'تفعيل النسخ الاحتياطي', 0, SYSDATE);

INSERT INTO SYSTEM_SETTINGS (ID, SETTING_KEY, SETTING_VALUE, SETTING_TYPE, DESCRIPTION, IS_SYSTEM, CREATED_DATE)
VALUES (SEQ_SYSTEM_SETTINGS.NEXTVAL, 'BACKUP_INTERVAL_HOURS', '24', 'INTEGER', 'فترة النسخ الاحتياطي بالساعات', 0, SYSDATE);

-- Commit التغييرات
COMMIT;

-- رسالة نجاح
SELECT 'تم إعداد قاعدة البيانات بنجاح!' AS MESSAGE FROM DUAL;
