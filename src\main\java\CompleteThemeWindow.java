import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;
import java.util.Map;
import javax.swing.BorderFactory;
import javax.swing.DefaultListModel;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JList;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;
import javax.swing.UIManager;
import javax.swing.border.TitledBorder;

/**
 * نافذة المظاهر الشاملة الكاملة Complete Comprehensive Theme Window
 */
public class CompleteThemeWindow extends JFrame {

    private CompleteThemeManager themeManager;
    private JComboBox<String> categoryComboBox;
    private JList<String> themeList;
    private DefaultListModel<String> themeListModel;
    private JTextArea themeInfoArea;
    private JCheckBox darkModeFilterCheckBox;
    private JCheckBox lightModeFilterCheckBox;
    private JButton applyButton;
    private JButton previewButton;
    private JButton resetButton;
    private JLabel themeCountLabel;
    private JLabel currentThemeLabel;

    private Map<String, List<CompleteThemeManager.ThemeInfo>> themesByCategory;

    public CompleteThemeWindow() {
        themeManager = CompleteThemeManager.getInstance();
        themesByCategory = themeManager.getThemesByCategory();

        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadThemeCategories();
        loadThemesForCategory();
        updateCurrentThemeDisplay();

        setTitle("🎨 نظام المظاهر الشامل - Complete Theme System");
        setSize(900, 700);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        // تطبيق الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        setUIFont(arabicFont);
    }

    private void initializeComponents() {
        // Category selection
        categoryComboBox = new JComboBox<>();
        categoryComboBox.setFont(new Font("Tahoma", Font.PLAIN, 12));

        // Theme list
        themeListModel = new DefaultListModel<>();
        themeList = new JList<>(themeListModel);
        themeList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        themeList.setFont(new Font("Tahoma", Font.PLAIN, 12));

        // Theme info area
        themeInfoArea = new JTextArea(8, 30);
        themeInfoArea.setEditable(false);
        themeInfoArea.setFont(new Font("Tahoma", Font.PLAIN, 11));
        themeInfoArea.setBackground(new Color(248, 249, 250));
        themeInfoArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // Filter checkboxes
        darkModeFilterCheckBox = new JCheckBox("المظاهر المظلمة فقط - Dark Themes Only");
        darkModeFilterCheckBox.setFont(new Font("Tahoma", Font.PLAIN, 11));

        lightModeFilterCheckBox = new JCheckBox("المظاهر الفاتحة فقط - Light Themes Only");
        lightModeFilterCheckBox.setFont(new Font("Tahoma", Font.PLAIN, 11));

        // Buttons
        applyButton = new JButton("تطبيق المظهر - Apply Theme");
        applyButton.setFont(new Font("Tahoma", Font.BOLD, 12));
        applyButton.setBackground(new Color(40, 167, 69));
        applyButton.setForeground(Color.WHITE);
        applyButton.setPreferredSize(new Dimension(150, 35));

        previewButton = new JButton("معاينة - Preview");
        previewButton.setFont(new Font("Tahoma", Font.PLAIN, 12));
        previewButton.setBackground(new Color(0, 123, 255));
        previewButton.setForeground(Color.WHITE);
        previewButton.setPreferredSize(new Dimension(120, 35));

        resetButton = new JButton("إعادة تعيين - Reset");
        resetButton.setFont(new Font("Tahoma", Font.PLAIN, 12));
        resetButton.setBackground(new Color(108, 117, 125));
        resetButton.setForeground(Color.WHITE);
        resetButton.setPreferredSize(new Dimension(120, 35));

        // Labels
        themeCountLabel = new JLabel("إجمالي المظاهر: 0");
        themeCountLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));

        currentThemeLabel = new JLabel("المظهر الحالي: غير محدد");
        currentThemeLabel.setFont(new Font("Tahoma", Font.BOLD, 12));
        currentThemeLabel.setForeground(new Color(40, 167, 69));
    }

    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));

        // Top panel - Category and filters
        JPanel topPanel = new JPanel(new BorderLayout(10, 10));
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 5, 10));

        // Category selection
        JPanel categoryPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        categoryPanel.add(new JLabel("الفئة - Category:"));
        categoryPanel.add(categoryComboBox);
        topPanel.add(categoryPanel, BorderLayout.NORTH);

        // Filters
        JPanel filterPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        filterPanel.add(lightModeFilterCheckBox);
        filterPanel.add(darkModeFilterCheckBox);
        topPanel.add(filterPanel, BorderLayout.SOUTH);

        add(topPanel, BorderLayout.NORTH);

        // Center panel - Theme list and info
        JPanel centerPanel = new JPanel(new BorderLayout(10, 10));
        centerPanel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));

        // Left side - Theme list
        JPanel listPanel = new JPanel(new BorderLayout());
        listPanel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "قائمة المظاهر - Theme List", TitledBorder.RIGHT, TitledBorder.TOP,
                new Font("Tahoma", Font.BOLD, 12)));

        JScrollPane listScrollPane = new JScrollPane(themeList);
        listScrollPane.setPreferredSize(new Dimension(300, 400));
        listPanel.add(listScrollPane, BorderLayout.CENTER);
        listPanel.add(themeCountLabel, BorderLayout.SOUTH);

        centerPanel.add(listPanel, BorderLayout.WEST);

        // Right side - Theme info
        JPanel infoPanel = new JPanel(new BorderLayout());
        infoPanel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "معلومات المظهر - Theme Information", TitledBorder.RIGHT, TitledBorder.TOP,
                new Font("Tahoma", Font.BOLD, 12)));

        JScrollPane infoScrollPane = new JScrollPane(themeInfoArea);
        infoPanel.add(infoScrollPane, BorderLayout.CENTER);

        centerPanel.add(infoPanel, BorderLayout.CENTER);

        add(centerPanel, BorderLayout.CENTER);

        // Bottom panel - Buttons and current theme
        JPanel bottomPanel = new JPanel(new BorderLayout(10, 10));
        bottomPanel.setBorder(BorderFactory.createEmptyBorder(5, 10, 10, 10));

        // Current theme display
        JPanel currentThemePanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        currentThemePanel.add(currentThemeLabel);
        bottomPanel.add(currentThemePanel, BorderLayout.NORTH);

        // Buttons
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 5));
        buttonPanel.add(resetButton);
        buttonPanel.add(previewButton);
        buttonPanel.add(applyButton);
        bottomPanel.add(buttonPanel, BorderLayout.SOUTH);

        add(bottomPanel, BorderLayout.SOUTH);
    }

    private void setupEventHandlers() {
        // Category selection handler
        categoryComboBox.addActionListener(e -> loadThemesForCategory());

        // Theme list selection handler
        themeList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                updateThemeInfo();
            }
        });

        // Filter checkboxes handlers
        darkModeFilterCheckBox.addActionListener(e -> {
            if (darkModeFilterCheckBox.isSelected()) {
                lightModeFilterCheckBox.setSelected(false);
            }
            loadThemesForCategory();
        });

        lightModeFilterCheckBox.addActionListener(e -> {
            if (lightModeFilterCheckBox.isSelected()) {
                darkModeFilterCheckBox.setSelected(false);
            }
            loadThemesForCategory();
        });

        // Apply button handler
        applyButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                applySelectedTheme();
            }
        });

        // Preview button handler
        previewButton.addActionListener(e -> previewSelectedTheme());

        // Reset button handler
        resetButton.addActionListener(e -> resetToDefaultTheme());
    }

    private void loadThemeCategories() {
        categoryComboBox.removeAllItems();
        categoryComboBox.addItem("جميع الفئات - All Categories");

        for (String category : themesByCategory.keySet()) {
            categoryComboBox.addItem(category);
        }
    }

    private void loadThemesForCategory() {
        themeListModel.clear();

        String selectedCategory = (String) categoryComboBox.getSelectedItem();

        if ("جميع الفئات - All Categories".equals(selectedCategory)) {
            // Load all themes
            for (List<CompleteThemeManager.ThemeInfo> themes : themesByCategory.values()) {
                for (CompleteThemeManager.ThemeInfo theme : themes) {
                    if (shouldIncludeTheme(theme)) {
                        themeListModel.addElement(theme.name);
                    }
                }
            }
        } else {
            // Load themes for specific category
            List<CompleteThemeManager.ThemeInfo> themes = themesByCategory.get(selectedCategory);
            if (themes != null) {
                for (CompleteThemeManager.ThemeInfo theme : themes) {
                    if (shouldIncludeTheme(theme)) {
                        themeListModel.addElement(theme.name);
                    }
                }
            }
        }

        // Update theme count
        updateThemeCount();
    }

    private boolean shouldIncludeTheme(CompleteThemeManager.ThemeInfo theme) {
        if (darkModeFilterCheckBox.isSelected() && !theme.isDark) {
            return false;
        }
        if (lightModeFilterCheckBox.isSelected() && theme.isDark) {
            return false;
        }
        return true;
    }

    private void updateThemeInfo() {
        String selectedTheme = themeList.getSelectedValue();
        if (selectedTheme == null) {
            themeInfoArea
                    .setText("اختر مظهراً لعرض معلوماته\nSelect a theme to view its information");
            return;
        }

        CompleteThemeManager.ThemeInfo themeInfo = themeManager.getThemeInfo(selectedTheme);
        if (themeInfo != null) {
            StringBuilder info = new StringBuilder();
            info.append("🎨 اسم المظهر: ").append(themeInfo.displayName).append("\n");
            info.append("📂 الفئة: ").append(themeInfo.category).append("\n");
            info.append("🌙 نوع المظهر: ").append(themeInfo.isDark ? "مظلم - Dark" : "فاتح - Light")
                    .append("\n");
            info.append("👨‍💻 المطور: ").append(themeInfo.author).append("\n");
            info.append("📋 الإصدار: ").append(themeInfo.version).append("\n");
            info.append("✅ متاح: ").append(themeInfo.isAvailable ? "نعم - Yes" : "لا - No")
                    .append("\n");
            info.append("🔧 الكلاس: ").append(themeInfo.className).append("\n\n");
            info.append("📝 الوصف:\n").append(themeInfo.description);

            themeInfoArea.setText(info.toString());
        }
    }

    private void updateThemeCount() {
        themeCountLabel.setText("إجمالي المظاهر: " + themeListModel.getSize());
    }

    private void updateCurrentThemeDisplay() {
        String currentTheme = themeManager.getCurrentTheme();
        CompleteThemeManager.ThemeInfo themeInfo = themeManager.getThemeInfo(currentTheme);
        if (themeInfo != null) {
            currentThemeLabel.setText("المظهر الحالي: " + themeInfo.displayName);
        }
    }

    private void applySelectedTheme() {
        String selectedTheme = themeList.getSelectedValue();
        if (selectedTheme == null) {
            JOptionPane.showMessageDialog(this,
                    "يرجى اختيار مظهر أولاً\nPlease select a theme first",
                    "لم يتم اختيار مظهر - No Theme Selected", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Show progress dialog
        JDialog progressDialog = new JDialog(this, "تطبيق المظهر - Applying Theme", true);
        JProgressBar progressBar = new JProgressBar();
        progressBar.setIndeterminate(true);
        progressBar.setString("جاري تطبيق المظهر... Applying theme...");
        progressBar.setStringPainted(true);

        progressDialog.add(progressBar);
        progressDialog.setSize(300, 80);
        progressDialog.setLocationRelativeTo(this);

        SwingWorker<Boolean, Void> worker = new SwingWorker<Boolean, Void>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                return themeManager.applyTheme(selectedTheme);
            }

            @Override
            protected void done() {
                progressDialog.dispose();
                try {
                    boolean success = get();
                    if (success) {
                        updateCurrentThemeDisplay();
                        CompleteThemeManager.ThemeInfo themeInfo =
                                themeManager.getThemeInfo(selectedTheme);
                        JOptionPane.showMessageDialog(CompleteThemeWindow.this,
                                "تم تطبيق المظهر بنجاح!\nTheme applied successfully!\n\n"
                                        + "المظهر: " + themeInfo.displayName + "\n" + "الفئة: "
                                        + themeInfo.category,
                                "تم التطبيق بنجاح - Success", JOptionPane.INFORMATION_MESSAGE);
                    } else {
                        JOptionPane.showMessageDialog(CompleteThemeWindow.this,
                                "فشل في تطبيق المظهر\nFailed to apply theme", "خطأ - Error",
                                JOptionPane.ERROR_MESSAGE);
                    }
                } catch (Exception e) {
                    JOptionPane.showMessageDialog(CompleteThemeWindow.this,
                            "حدث خطأ أثناء تطبيق المظهر:\n" + e.getMessage(), "خطأ - Error",
                            JOptionPane.ERROR_MESSAGE);
                }
            }
        };

        progressDialog.setVisible(true);
        worker.execute();
    }

    private void previewSelectedTheme() {
        String selectedTheme = themeList.getSelectedValue();
        if (selectedTheme == null) {
            JOptionPane.showMessageDialog(this,
                    "يرجى اختيار مظهر أولاً\nPlease select a theme first",
                    "لم يتم اختيار مظهر - No Theme Selected", JOptionPane.WARNING_MESSAGE);
            return;
        }

        CompleteThemeManager.ThemeInfo themeInfo = themeManager.getThemeInfo(selectedTheme);
        if (themeInfo != null) {
            // Create preview dialog
            JDialog previewDialog = new JDialog(this, "معاينة المظهر - Theme Preview", true);
            previewDialog.setSize(400, 300);
            previewDialog.setLocationRelativeTo(this);

            JPanel previewPanel = new JPanel(new BorderLayout(10, 10));
            previewPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

            // Theme info
            JTextArea previewInfo = new JTextArea();
            previewInfo.setEditable(false);
            previewInfo.setText("🎨 " + themeInfo.displayName + "\n" + "📂 " + themeInfo.category
                    + "\n" + "🌙 "
                    + (themeInfo.isDark ? "مظهر مظلم - Dark Theme" : "مظهر فاتح - Light Theme")
                    + "\n" + "👨‍💻 " + themeInfo.author + "\n\n" + "📝 " + themeInfo.description
                    + "\n\n" + "هل تريد تطبيق هذا المظهر؟\nDo you want to apply this theme?");
            previewInfo.setFont(new Font("Tahoma", Font.PLAIN, 12));

            previewPanel.add(new JScrollPane(previewInfo), BorderLayout.CENTER);

            // Buttons
            JPanel buttonPanel = new JPanel(new FlowLayout());
            JButton applyBtn = new JButton("تطبيق - Apply");
            JButton cancelBtn = new JButton("إلغاء - Cancel");

            applyBtn.addActionListener(e -> {
                previewDialog.dispose();
                themeList.setSelectedValue(selectedTheme, true);
                applySelectedTheme();
            });

            cancelBtn.addActionListener(e -> previewDialog.dispose());

            buttonPanel.add(applyBtn);
            buttonPanel.add(cancelBtn);
            previewPanel.add(buttonPanel, BorderLayout.SOUTH);

            previewDialog.add(previewPanel);
            previewDialog.setVisible(true);
        }
    }

    private void resetToDefaultTheme() {
        int result = JOptionPane.showConfirmDialog(this,
                "هل تريد إعادة تعيين المظهر إلى الافتراضي؟\nDo you want to reset to default theme?",
                "إعادة تعيين - Reset", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            themeManager.applyTheme("FlatLaf Light");
            updateCurrentThemeDisplay();
            themeList.setSelectedValue("FlatLaf Light", true);

            JOptionPane.showMessageDialog(this,
                    "تم إعادة تعيين المظهر إلى الافتراضي\nTheme reset to default",
                    "تم الإعادة - Reset Complete", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    private void setUIFont(Font font) {
        java.util.Enumeration<Object> keys = UIManager.getDefaults().keys();
        while (keys.hasMoreElements()) {
            Object key = keys.nextElement();
            Object value = UIManager.get(key);
            if (value instanceof javax.swing.plaf.FontUIResource) {
                UIManager.put(key, font);
            }
        }
    }

    /**
     * الدالة الرئيسية للاختبار
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق مظهر افتراضي
                com.formdev.flatlaf.FlatLightLaf.setup();

                CompleteThemeWindow window = new CompleteThemeWindow();
                window.setVisible(true);

                System.out.println("✅ تم تشغيل نافذة المظاهر الشاملة الكاملة");

            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null,
                        "خطأ في تشغيل نافذة المظاهر:\n" + e.getMessage(), "خطأ - Error",
                        JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
