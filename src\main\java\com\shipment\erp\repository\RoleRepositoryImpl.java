package com.shipment.erp.repository;

import com.shipment.erp.model.Role;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.*;
import java.util.List;
import java.util.Optional;

/**
 * تنفيذ مستودع بيانات الأدوار
 * Role Data Repository Implementation
 */
@Repository
public class RoleRepositoryImpl extends BaseRepositoryImpl<Role> implements RoleRepository {
    
    public RoleRepositoryImpl(DataSource dataSource) {
        super(dataSource);
    }
    
    @Override
    protected String getTableName() {
        return "ROLES";
    }
    
    @Override
    protected String getSequenceName() {
        return "SEQ_ROLE";
    }
    
    @Override
    protected Role mapResultSetToEntity(ResultSet rs) throws SQLException {
        Role role = new Role();
        role.setId(rs.getLong("ID"));
        role.setName(rs.getString("NAME"));
        role.setNameEn(rs.getString("NAME_EN"));
        role.setDescription(rs.getString("DESCRIPTION"));
        role.setActive(rs.getBoolean("IS_ACTIVE"));
        role.setCreatedBy(rs.getLong("CREATED_BY"));
        role.setCreatedDate(rs.getTimestamp("CREATED_DATE"));
        role.setModifiedBy(rs.getLong("MODIFIED_BY"));
        role.setModifiedDate(rs.getTimestamp("MODIFIED_DATE"));
        return role;
    }
    
    @Override
    protected void setEntityParameters(PreparedStatement ps, Role role, boolean isUpdate) throws SQLException {
        int index = 1;
        
        ps.setString(index++, role.getName());
        ps.setString(index++, role.getNameEn());
        ps.setString(index++, role.getDescription());
        ps.setBoolean(index++, role.isActive());
        
        if (isUpdate) {
            ps.setLong(index++, role.getModifiedBy());
            ps.setTimestamp(index++, new Timestamp(System.currentTimeMillis()));
            ps.setLong(index++, role.getId());
        } else {
            ps.setLong(index++, role.getCreatedBy());
            ps.setTimestamp(index++, new Timestamp(System.currentTimeMillis()));
        }
    }
    
    @Override
    protected String getInsertSql() {
        return "INSERT INTO ROLES (ID, NAME, NAME_EN, DESCRIPTION, IS_ACTIVE, CREATED_BY, CREATED_DATE) " +
               "VALUES (?, ?, ?, ?, ?, ?, ?)";
    }
    
    @Override
    protected String getUpdateSql() {
        return "UPDATE ROLES SET NAME = ?, NAME_EN = ?, DESCRIPTION = ?, IS_ACTIVE = ?, MODIFIED_BY = ?, MODIFIED_DATE = ? WHERE ID = ?";
    }
    
    @Override
    public List<Role> findByIsActiveTrue() {
        String sql = "SELECT * FROM ROLES WHERE IS_ACTIVE = 1 ORDER BY NAME";
        return executeQuery(sql);
    }
    
    @Override
    public Optional<Role> findByName(String name) {
        String sql = "SELECT * FROM ROLES WHERE NAME = ?";
        List<Role> roles = executeQuery(sql, name);
        return roles.isEmpty() ? Optional.empty() : Optional.of(roles.get(0));
    }
    
    @Override
    public List<Role> findByNameContainingIgnoreCase(String name) {
        String sql = "SELECT * FROM ROLES WHERE UPPER(NAME) LIKE UPPER(?) ORDER BY NAME";
        return executeQuery(sql, "%" + name + "%");
    }
    
    @Override
    public boolean existsByName(String name) {
        String sql = "SELECT COUNT(*) FROM ROLES WHERE NAME = ?";
        try (Connection conn = dataSource.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            
            ps.setString(1, name);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() && rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            throw new RuntimeException("خطأ في التحقق من وجود اسم الدور", e);
        }
    }
    
    @Override
    public long getUsersCount(Long roleId) {
        String sql = "SELECT COUNT(*) FROM USERS WHERE ROLE_ID = ?";
        try (Connection conn = dataSource.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            
            ps.setLong(1, roleId);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() ? rs.getLong(1) : 0;
            }
        } catch (SQLException e) {
            throw new RuntimeException("خطأ في عد المستخدمين للدور", e);
        }
    }
    
    @Override
    public List<Role> findDefaultRoles() {
        String sql = "SELECT * FROM ROLES WHERE NAME IN ('مدير عام', 'مدير', 'مستخدم') ORDER BY NAME";
        return executeQuery(sql);
    }
    
    @Override
    public List<Role> findByDescriptionContainingIgnoreCase(String description) {
        String sql = "SELECT * FROM ROLES WHERE UPPER(DESCRIPTION) LIKE UPPER(?) ORDER BY NAME";
        return executeQuery(sql, "%" + description + "%");
    }
}
