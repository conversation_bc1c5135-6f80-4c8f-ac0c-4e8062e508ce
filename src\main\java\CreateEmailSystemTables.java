import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء جداول نظام إدارة البريد الإلكتروني الشامل Create Comprehensive Email Management System
 * Tables
 */
public class CreateEmailSystemTables {

    public static void main(String[] args) {
        System.out.println("📧 إنشاء جداول نظام إدارة البريد الإلكتروني الشامل...");

        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();

            // إنشاء الجداول بالترتيب الصحيح
            createEmailAccountsTable(connection);
            createEmailTemplatesTable(connection);
            createEmailCampaignsTable(connection);
            createEmailMessagesTable(connection);
            createEmailAttachmentsTable(connection);
            createEmailRecipientsTable(connection);
            createEmailLogsTable(connection);
            createEmailSettingsTable(connection);
            createEmailFiltersTable(connection);
            createEmailSignaturesTable(connection);

            // إدراج بيانات تجريبية
            insertSampleData(connection);

            connection.close();
            System.out.println("✅ تم إنشاء نظام البريد الإلكتروني الشامل بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء جداول نظام البريد الإلكتروني: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void createEmailAccountsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول حسابات البريد الإلكتروني...");

        // حذف الجدول إذا كان موجوداً
        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_ACCOUNTS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_ACCOUNTS_SEQ");
            System.out.println("🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL =
                """
                        CREATE TABLE ERP_EMAIL_ACCOUNTS (
                            ACCOUNT_ID NUMBER(10) NOT NULL,
                            ACCOUNT_NAME_AR NVARCHAR2(200) NOT NULL,
                            ACCOUNT_NAME_EN VARCHAR2(200) NOT NULL,
                            EMAIL_ADDRESS VARCHAR2(200) NOT NULL UNIQUE,
                            DISPLAY_NAME_AR NVARCHAR2(200),
                            DISPLAY_NAME_EN VARCHAR2(200),

                            -- إعدادات الخادم الوارد (IMAP/POP3)
                            INCOMING_SERVER VARCHAR2(200) NOT NULL,
                            INCOMING_PORT NUMBER(5) DEFAULT 993,
                            INCOMING_PROTOCOL VARCHAR2(10) DEFAULT 'IMAP', -- IMAP, POP3
                            INCOMING_SECURITY VARCHAR2(10) DEFAULT 'SSL', -- SSL, TLS, NONE
                            INCOMING_USERNAME VARCHAR2(200),
                            INCOMING_PASSWORD VARCHAR2(500), -- مشفر

                            -- إعدادات الخادم الصادر (SMTP)
                            OUTGOING_SERVER VARCHAR2(200) NOT NULL,
                            OUTGOING_PORT NUMBER(5) DEFAULT 587,
                            OUTGOING_SECURITY VARCHAR2(10) DEFAULT 'TLS', -- SSL, TLS, NONE
                            OUTGOING_USERNAME VARCHAR2(200),
                            OUTGOING_PASSWORD VARCHAR2(500), -- مشفر
                            OUTGOING_AUTH_REQUIRED CHAR(1) DEFAULT 'Y',

                            -- إعدادات متقدمة
                            MAX_MESSAGE_SIZE NUMBER(10) DEFAULT 25, -- بالميجابايت
                            AUTO_CHECK_INTERVAL NUMBER(5) DEFAULT 15, -- بالدقائق
                            KEEP_MESSAGES_ON_SERVER CHAR(1) DEFAULT 'Y',
                            DELETE_AFTER_DAYS NUMBER(3) DEFAULT 30,

                            -- إعدادات الأمان
                            ENABLE_OAUTH CHAR(1) DEFAULT 'N',
                            OAUTH_TOKEN CLOB,
                            OAUTH_REFRESH_TOKEN CLOB,
                            OAUTH_EXPIRES_AT DATE,

                            -- إعدادات التوقيع
                            DEFAULT_SIGNATURE_ID NUMBER(10),
                            AUTO_SIGNATURE CHAR(1) DEFAULT 'Y',

                            -- إعدادات الإشعارات
                            ENABLE_NOTIFICATIONS CHAR(1) DEFAULT 'Y',
                            NOTIFICATION_SOUND VARCHAR2(200),
                            DESKTOP_NOTIFICATIONS CHAR(1) DEFAULT 'Y',

                            -- الحالة والتصنيف
                            ACCOUNT_STATUS VARCHAR2(20) DEFAULT 'ACTIVE', -- ACTIVE, INACTIVE, ERROR
                            ACCOUNT_TYPE VARCHAR2(20) DEFAULT 'BUSINESS', -- PERSONAL, BUSINESS, SYSTEM
                            IS_DEFAULT_ACCOUNT CHAR(1) DEFAULT 'N',
                            IS_SHARED_ACCOUNT CHAR(1) DEFAULT 'N',

                            -- معلومات الاتصال
                            LAST_SYNC_DATE DATE,
                            LAST_ERROR_MESSAGE NCLOB,
                            TOTAL_MESSAGES_SENT NUMBER(10) DEFAULT 0,
                            TOTAL_MESSAGES_RECEIVED NUMBER(10) DEFAULT 0,

                            -- معلومات التتبع
                            IS_ACTIVE CHAR(1) DEFAULT 'Y',
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,
                            LAST_UPDATED DATE DEFAULT SYSDATE,
                            UPDATED_BY VARCHAR2(50) DEFAULT USER,
                            VERSION_NUMBER NUMBER(10) DEFAULT 1,

                            -- ملاحظات
                            NOTES NCLOB,

                            CONSTRAINT PK_ERP_EMAIL_ACCOUNTS PRIMARY KEY (ACCOUNT_ID),
                            CONSTRAINT CHK_ACCOUNT_STATUS CHECK (ACCOUNT_STATUS IN ('ACTIVE', 'INACTIVE', 'ERROR')),
                            CONSTRAINT CHK_ACCOUNT_TYPE CHECK (ACCOUNT_TYPE IN ('PERSONAL', 'BUSINESS', 'SYSTEM')),
                            CONSTRAINT CHK_INCOMING_PROTOCOL CHECK (INCOMING_PROTOCOL IN ('IMAP', 'POP3')),
                            CONSTRAINT CHK_INCOMING_SECURITY CHECK (INCOMING_SECURITY IN ('SSL', 'TLS', 'NONE')),
                            CONSTRAINT CHK_OUTGOING_SECURITY CHECK (OUTGOING_SECURITY IN ('SSL', 'TLS', 'NONE')),
                            CONSTRAINT CHK_IS_DEFAULT CHECK (IS_DEFAULT_ACCOUNT IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_SHARED CHECK (IS_SHARED_ACCOUNT IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_ACTIVE_ACC CHECK (IS_ACTIVE IN ('Y', 'N')),
                            CONSTRAINT CHK_ENABLE_OAUTH CHECK (ENABLE_OAUTH IN ('Y', 'N')),
                            CONSTRAINT CHK_AUTO_SIGNATURE CHECK (AUTO_SIGNATURE IN ('Y', 'N')),
                            CONSTRAINT CHK_ENABLE_NOTIFICATIONS CHECK (ENABLE_NOTIFICATIONS IN ('Y', 'N')),
                            CONSTRAINT CHK_DESKTOP_NOTIFICATIONS CHECK (DESKTOP_NOTIFICATIONS IN ('Y', 'N')),
                            CONSTRAINT CHK_OUTGOING_AUTH CHECK (OUTGOING_AUTH_REQUIRED IN ('Y', 'N')),
                            CONSTRAINT CHK_KEEP_MESSAGES CHECK (KEEP_MESSAGES_ON_SERVER IN ('Y', 'N'))
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_ACCOUNTS");
        }

        // إنشاء المتسلسل
        String createSequenceSQL = """
                CREATE SEQUENCE ERP_EMAIL_ACCOUNTS_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createSequenceSQL);
            System.out.println("✅ تم إنشاء المتسلسل ERP_EMAIL_ACCOUNTS_SEQ");
        }

        // إنشاء الفهارس
        createEmailAccountsIndexes(connection);
    }

    private static void createEmailAccountsIndexes(Connection connection) throws SQLException {
        System.out.println("📊 إنشاء فهارس جدول حسابات البريد الإلكتروني...");

        String[] indexes = {"CREATE INDEX IDX_EMAIL_ADDRESS ON ERP_EMAIL_ACCOUNTS(EMAIL_ADDRESS)",
                "CREATE INDEX IDX_ACCOUNT_STATUS ON ERP_EMAIL_ACCOUNTS(ACCOUNT_STATUS)",
                "CREATE INDEX IDX_ACCOUNT_TYPE ON ERP_EMAIL_ACCOUNTS(ACCOUNT_TYPE)",
                "CREATE INDEX IDX_IS_DEFAULT ON ERP_EMAIL_ACCOUNTS(IS_DEFAULT_ACCOUNT)",
                "CREATE INDEX IDX_LAST_SYNC ON ERP_EMAIL_ACCOUNTS(LAST_SYNC_DATE)"};

        try (Statement stmt = connection.createStatement()) {
            for (String indexSQL : indexes) {
                try {
                    stmt.executeUpdate(indexSQL);
                } catch (SQLException e) {
                    // تجاهل إذا كان الفهرس موجود
                }
            }
            System.out.println("✅ تم إنشاء فهارس جدول حسابات البريد الإلكتروني");
        }
    }

    private static void createEmailTemplatesTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول قوالب البريد الإلكتروني...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_TEMPLATES CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_TEMPLATES_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL =
                """
                        CREATE TABLE ERP_EMAIL_TEMPLATES (
                            TEMPLATE_ID NUMBER(10) NOT NULL,
                            TEMPLATE_NAME_AR NVARCHAR2(300) NOT NULL,
                            TEMPLATE_NAME_EN VARCHAR2(300) NOT NULL,
                            TEMPLATE_CATEGORY VARCHAR2(50) DEFAULT 'GENERAL',

                            -- محتوى القالب
                            SUBJECT_AR NVARCHAR2(500),
                            SUBJECT_EN VARCHAR2(500),
                            BODY_HTML_AR NCLOB,
                            BODY_HTML_EN CLOB,
                            BODY_TEXT_AR NCLOB,
                            BODY_TEXT_EN CLOB,

                            -- إعدادات القالب
                            IS_HTML CHAR(1) DEFAULT 'Y',
                            TEMPLATE_TYPE VARCHAR2(20) DEFAULT 'STANDARD',
                            PRIORITY VARCHAR2(10) DEFAULT 'NORMAL',

                            -- متغيرات القالب
                            TEMPLATE_VARIABLES CLOB, -- JSON format
                            SAMPLE_DATA CLOB, -- JSON format for preview

                            -- الحالة
                            IS_ACTIVE CHAR(1) DEFAULT 'Y',
                            IS_SYSTEM_TEMPLATE CHAR(1) DEFAULT 'N',

                            -- معلومات التتبع
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,
                            LAST_UPDATED DATE DEFAULT SYSDATE,
                            UPDATED_BY VARCHAR2(50) DEFAULT USER,

                            CONSTRAINT PK_ERP_EMAIL_TEMPLATES PRIMARY KEY (TEMPLATE_ID),
                            CONSTRAINT CHK_TEMPLATE_TYPE CHECK (TEMPLATE_TYPE IN ('STANDARD', 'NEWSLETTER', 'NOTIFICATION', 'MARKETING')),
                            CONSTRAINT CHK_TEMPLATE_PRIORITY CHECK (PRIORITY IN ('LOW', 'NORMAL', 'HIGH', 'URGENT')),
                            CONSTRAINT CHK_IS_HTML_TEMPLATE CHECK (IS_HTML IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_ACTIVE_TEMPLATE CHECK (IS_ACTIVE IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_SYSTEM_TEMPLATE CHECK (IS_SYSTEM_TEMPLATE IN ('Y', 'N'))
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_EMAIL_TEMPLATES_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_TEMPLATES");
        }
    }

    private static void createEmailCampaignsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول حملات البريد الإلكتروني...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_CAMPAIGNS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_CAMPAIGNS_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL =
                """
                        CREATE TABLE ERP_EMAIL_CAMPAIGNS (
                            CAMPAIGN_ID NUMBER(10) NOT NULL,
                            CAMPAIGN_NAME_AR NVARCHAR2(300) NOT NULL,
                            CAMPAIGN_NAME_EN VARCHAR2(300) NOT NULL,
                            CAMPAIGN_DESCRIPTION_AR NCLOB,
                            CAMPAIGN_DESCRIPTION_EN CLOB,

                            -- إعدادات الحملة
                            TEMPLATE_ID NUMBER(10),
                            SENDER_ACCOUNT_ID NUMBER(10) NOT NULL,
                            CAMPAIGN_TYPE VARCHAR2(20) DEFAULT 'MARKETING',
                            CAMPAIGN_STATUS VARCHAR2(20) DEFAULT 'DRAFT',

                            -- جدولة الإرسال
                            SCHEDULED_DATE DATE,
                            START_DATE DATE,
                            END_DATE DATE,
                            SEND_IMMEDIATELY CHAR(1) DEFAULT 'N',

                            -- إحصائيات
                            TOTAL_RECIPIENTS NUMBER(10) DEFAULT 0,
                            MESSAGES_SENT NUMBER(10) DEFAULT 0,
                            MESSAGES_DELIVERED NUMBER(10) DEFAULT 0,
                            MESSAGES_OPENED NUMBER(10) DEFAULT 0,
                            MESSAGES_CLICKED NUMBER(10) DEFAULT 0,
                            MESSAGES_BOUNCED NUMBER(10) DEFAULT 0,
                            MESSAGES_FAILED NUMBER(10) DEFAULT 0,

                            -- الحالة
                            IS_ACTIVE CHAR(1) DEFAULT 'Y',

                            -- معلومات التتبع
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,
                            LAST_UPDATED DATE DEFAULT SYSDATE,
                            UPDATED_BY VARCHAR2(50) DEFAULT USER,

                            CONSTRAINT PK_ERP_EMAIL_CAMPAIGNS PRIMARY KEY (CAMPAIGN_ID),
                            CONSTRAINT FK_CAMPAIGN_TEMPLATE FOREIGN KEY (TEMPLATE_ID)
                                REFERENCES ERP_EMAIL_TEMPLATES(TEMPLATE_ID),
                            CONSTRAINT FK_CAMPAIGN_SENDER FOREIGN KEY (SENDER_ACCOUNT_ID)
                                REFERENCES ERP_EMAIL_ACCOUNTS(ACCOUNT_ID),
                            CONSTRAINT CHK_CAMPAIGN_TYPE CHECK (CAMPAIGN_TYPE IN ('MARKETING', 'NEWSLETTER', 'NOTIFICATION', 'ANNOUNCEMENT')),
                            CONSTRAINT CHK_CAMPAIGN_STATUS CHECK (CAMPAIGN_STATUS IN ('DRAFT', 'SCHEDULED', 'SENDING', 'SENT', 'PAUSED', 'CANCELLED')),
                            CONSTRAINT CHK_SEND_IMMEDIATELY CHECK (SEND_IMMEDIATELY IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_ACTIVE_CAMPAIGN CHECK (IS_ACTIVE IN ('Y', 'N'))
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_EMAIL_CAMPAIGNS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_CAMPAIGNS");
        }
    }

    private static void createEmailMessagesTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول رسائل البريد الإلكتروني...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_MESSAGES CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_MESSAGES_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL =
                """
                        CREATE TABLE ERP_EMAIL_MESSAGES (
                            MESSAGE_ID NUMBER(10) NOT NULL,
                            ACCOUNT_ID NUMBER(10) NOT NULL,
                            CAMPAIGN_ID NUMBER(10),
                            TEMPLATE_ID NUMBER(10),

                            -- معلومات الرسالة
                            SUBJECT NVARCHAR2(500) NOT NULL,
                            BODY_HTML NCLOB,
                            BODY_TEXT NCLOB,
                            MESSAGE_TYPE VARCHAR2(20) DEFAULT 'OUTGOING',
                            PRIORITY VARCHAR2(10) DEFAULT 'NORMAL',

                            -- معلومات المرسل والمستقبل
                            FROM_EMAIL VARCHAR2(200) NOT NULL,
                            FROM_NAME NVARCHAR2(200),
                            TO_EMAILS CLOB NOT NULL, -- JSON array
                            CC_EMAILS CLOB, -- JSON array
                            BCC_EMAILS CLOB, -- JSON array
                            REPLY_TO_EMAIL VARCHAR2(200),

                            -- حالة الرسالة
                            MESSAGE_STATUS VARCHAR2(20) DEFAULT 'DRAFT',
                            SEND_DATE DATE,
                            DELIVERY_DATE DATE,
                            READ_DATE DATE,

                            -- معلومات إضافية
                            MESSAGE_ID_HEADER VARCHAR2(200),
                            THREAD_ID VARCHAR2(200),
                            IN_REPLY_TO VARCHAR2(200),
                            REFERENCES CLOB,

                            -- إحصائيات
                            ATTACHMENT_COUNT NUMBER(3) DEFAULT 0,
                            MESSAGE_SIZE NUMBER(10) DEFAULT 0, -- بالبايت
                            RETRY_COUNT NUMBER(3) DEFAULT 0,

                            -- معلومات الخطأ
                            ERROR_MESSAGE NCLOB,
                            LAST_ERROR_DATE DATE,

                            -- الحالة
                            IS_ACTIVE CHAR(1) DEFAULT 'Y',
                            IS_ENCRYPTED CHAR(1) DEFAULT 'N',
                            IS_SIGNED CHAR(1) DEFAULT 'N',

                            -- معلومات التتبع
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,
                            LAST_UPDATED DATE DEFAULT SYSDATE,
                            UPDATED_BY VARCHAR2(50) DEFAULT USER,

                            CONSTRAINT PK_ERP_EMAIL_MESSAGES PRIMARY KEY (MESSAGE_ID),
                            CONSTRAINT FK_MESSAGE_ACCOUNT FOREIGN KEY (ACCOUNT_ID)
                                REFERENCES ERP_EMAIL_ACCOUNTS(ACCOUNT_ID),
                            CONSTRAINT FK_MESSAGE_CAMPAIGN FOREIGN KEY (CAMPAIGN_ID)
                                REFERENCES ERP_EMAIL_CAMPAIGNS(CAMPAIGN_ID),
                            CONSTRAINT FK_MESSAGE_TEMPLATE FOREIGN KEY (TEMPLATE_ID)
                                REFERENCES ERP_EMAIL_TEMPLATES(TEMPLATE_ID),
                            CONSTRAINT CHK_MESSAGE_TYPE CHECK (MESSAGE_TYPE IN ('INCOMING', 'OUTGOING', 'DRAFT')),
                            CONSTRAINT CHK_MESSAGE_PRIORITY CHECK (PRIORITY IN ('LOW', 'NORMAL', 'HIGH', 'URGENT')),
                            CONSTRAINT CHK_MESSAGE_STATUS CHECK (MESSAGE_STATUS IN ('DRAFT', 'QUEUED', 'SENDING', 'SENT', 'DELIVERED', 'READ', 'FAILED', 'BOUNCED')),
                            CONSTRAINT CHK_IS_ACTIVE_MESSAGE CHECK (IS_ACTIVE IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_ENCRYPTED CHECK (IS_ENCRYPTED IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_SIGNED CHECK (IS_SIGNED IN ('Y', 'N'))
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_EMAIL_MESSAGES_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_MESSAGES");
        }
    }

    private static void createEmailAttachmentsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول مرفقات البريد الإلكتروني...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_ATTACHMENTS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_ATTACHMENTS_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL = """
                CREATE TABLE ERP_EMAIL_ATTACHMENTS (
                    ATTACHMENT_ID NUMBER(10) NOT NULL,
                    MESSAGE_ID NUMBER(10) NOT NULL,

                    -- معلومات الملف
                    FILE_NAME NVARCHAR2(500) NOT NULL,
                    FILE_PATH VARCHAR2(1000),
                    FILE_SIZE NUMBER(15) NOT NULL, -- بالبايت
                    FILE_TYPE VARCHAR2(100),
                    MIME_TYPE VARCHAR2(200),

                    -- محتوى الملف
                    FILE_CONTENT BLOB,
                    FILE_HASH VARCHAR2(64), -- SHA-256

                    -- معلومات إضافية
                    IS_INLINE CHAR(1) DEFAULT 'N',
                    CONTENT_ID VARCHAR2(200),
                    DISPOSITION VARCHAR2(50) DEFAULT 'ATTACHMENT',

                    -- الحالة
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    IS_SCANNED CHAR(1) DEFAULT 'N',
                    SCAN_RESULT VARCHAR2(50),

                    -- معلومات التتبع
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,

                    CONSTRAINT PK_ERP_EMAIL_ATTACHMENTS PRIMARY KEY (ATTACHMENT_ID),
                    CONSTRAINT FK_ATTACHMENT_MESSAGE FOREIGN KEY (MESSAGE_ID)
                        REFERENCES ERP_EMAIL_MESSAGES(MESSAGE_ID) ON DELETE CASCADE,
                    CONSTRAINT CHK_IS_INLINE CHECK (IS_INLINE IN ('Y', 'N')),
                    CONSTRAINT CHK_IS_ACTIVE_ATTACHMENT CHECK (IS_ACTIVE IN ('Y', 'N')),
                    CONSTRAINT CHK_IS_SCANNED CHECK (IS_SCANNED IN ('Y', 'N'))
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_EMAIL_ATTACHMENTS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_ATTACHMENTS");
        }
    }

    private static void createEmailRecipientsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول مستقبلي البريد الإلكتروني...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_RECIPIENTS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_RECIPIENTS_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL =
                """
                        CREATE TABLE ERP_EMAIL_RECIPIENTS (
                            RECIPIENT_ID NUMBER(10) NOT NULL,
                            MESSAGE_ID NUMBER(10),
                            CAMPAIGN_ID NUMBER(10),

                            -- معلومات المستقبل
                            EMAIL_ADDRESS VARCHAR2(200) NOT NULL,
                            RECIPIENT_NAME_AR NVARCHAR2(200),
                            RECIPIENT_NAME_EN VARCHAR2(200),
                            RECIPIENT_TYPE VARCHAR2(10) DEFAULT 'TO', -- TO, CC, BCC

                            -- حالة التسليم
                            DELIVERY_STATUS VARCHAR2(20) DEFAULT 'PENDING',
                            SENT_DATE DATE,
                            DELIVERED_DATE DATE,
                            OPENED_DATE DATE,
                            CLICKED_DATE DATE,
                            BOUNCED_DATE DATE,
                            UNSUBSCRIBED_DATE DATE,

                            -- معلومات إضافية
                            BOUNCE_REASON NVARCHAR2(500),
                            USER_AGENT VARCHAR2(500),
                            IP_ADDRESS VARCHAR2(45),
                            CLICK_COUNT NUMBER(5) DEFAULT 0,
                            OPEN_COUNT NUMBER(5) DEFAULT 0,

                            -- الحالة
                            IS_ACTIVE CHAR(1) DEFAULT 'Y',
                            IS_SUBSCRIBED CHAR(1) DEFAULT 'Y',

                            -- معلومات التتبع
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,

                            CONSTRAINT PK_ERP_EMAIL_RECIPIENTS PRIMARY KEY (RECIPIENT_ID),
                            CONSTRAINT FK_RECIPIENT_MESSAGE FOREIGN KEY (MESSAGE_ID)
                                REFERENCES ERP_EMAIL_MESSAGES(MESSAGE_ID) ON DELETE CASCADE,
                            CONSTRAINT FK_RECIPIENT_CAMPAIGN FOREIGN KEY (CAMPAIGN_ID)
                                REFERENCES ERP_EMAIL_CAMPAIGNS(CAMPAIGN_ID) ON DELETE CASCADE,
                            CONSTRAINT CHK_RECIPIENT_TYPE CHECK (RECIPIENT_TYPE IN ('TO', 'CC', 'BCC')),
                            CONSTRAINT CHK_DELIVERY_STATUS CHECK (DELIVERY_STATUS IN ('PENDING', 'SENT', 'DELIVERED', 'OPENED', 'CLICKED', 'BOUNCED', 'FAILED')),
                            CONSTRAINT CHK_IS_ACTIVE_RECIPIENT CHECK (IS_ACTIVE IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_SUBSCRIBED CHECK (IS_SUBSCRIBED IN ('Y', 'N'))
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_EMAIL_RECIPIENTS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_RECIPIENTS");
        }
    }

    private static void createEmailLogsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول سجلات البريد الإلكتروني...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_LOGS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_LOGS_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL =
                """
                        CREATE TABLE ERP_EMAIL_LOGS (
                            LOG_ID NUMBER(10) NOT NULL,
                            ACCOUNT_ID NUMBER(10),
                            MESSAGE_ID NUMBER(10),

                            -- معلومات السجل
                            LOG_TYPE VARCHAR2(20) NOT NULL, -- SEND, RECEIVE, ERROR, INFO
                            LOG_LEVEL VARCHAR2(10) DEFAULT 'INFO', -- DEBUG, INFO, WARN, ERROR
                            LOG_MESSAGE NCLOB NOT NULL,
                            LOG_DETAILS CLOB,

                            -- معلومات إضافية
                            ERROR_CODE VARCHAR2(50),
                            STACK_TRACE CLOB,
                            EXECUTION_TIME NUMBER(10), -- بالميلي ثانية

                            -- معلومات التتبع
                            LOG_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,
                            IP_ADDRESS VARCHAR2(45),
                            USER_AGENT VARCHAR2(500),

                            CONSTRAINT PK_ERP_EMAIL_LOGS PRIMARY KEY (LOG_ID),
                            CONSTRAINT FK_LOG_ACCOUNT FOREIGN KEY (ACCOUNT_ID)
                                REFERENCES ERP_EMAIL_ACCOUNTS(ACCOUNT_ID) ON DELETE SET NULL,
                            CONSTRAINT FK_LOG_MESSAGE FOREIGN KEY (MESSAGE_ID)
                                REFERENCES ERP_EMAIL_MESSAGES(MESSAGE_ID) ON DELETE SET NULL,
                            CONSTRAINT CHK_LOG_TYPE CHECK (LOG_TYPE IN ('SEND', 'RECEIVE', 'ERROR', 'INFO', 'DEBUG')),
                            CONSTRAINT CHK_LOG_LEVEL CHECK (LOG_LEVEL IN ('DEBUG', 'INFO', 'WARN', 'ERROR'))
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_EMAIL_LOGS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_LOGS");
        }
    }

    private static void createEmailSettingsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول إعدادات البريد الإلكتروني...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_SETTINGS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_SETTINGS_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL =
                """
                        CREATE TABLE ERP_EMAIL_SETTINGS (
                            SETTING_ID NUMBER(10) NOT NULL,
                            SETTING_CATEGORY VARCHAR2(50) NOT NULL,
                            SETTING_KEY VARCHAR2(100) NOT NULL,
                            SETTING_VALUE_AR NCLOB,
                            SETTING_VALUE_EN CLOB,
                            SETTING_TYPE VARCHAR2(20) DEFAULT 'STRING',
                            DEFAULT_VALUE CLOB,
                            DESCRIPTION_AR NVARCHAR2(500),
                            DESCRIPTION_EN VARCHAR2(500),
                            IS_SYSTEM_SETTING CHAR(1) DEFAULT 'N',
                            IS_ACTIVE CHAR(1) DEFAULT 'Y',
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,
                            LAST_UPDATED DATE DEFAULT SYSDATE,
                            UPDATED_BY VARCHAR2(50) DEFAULT USER,

                            CONSTRAINT PK_ERP_EMAIL_SETTINGS PRIMARY KEY (SETTING_ID),
                            CONSTRAINT UK_EMAIL_SETTING UNIQUE (SETTING_CATEGORY, SETTING_KEY),
                            CONSTRAINT CHK_SETTING_TYPE CHECK (SETTING_TYPE IN ('STRING', 'NUMBER', 'BOOLEAN', 'JSON', 'XML')),
                            CONSTRAINT CHK_IS_SYSTEM_SETTING CHECK (IS_SYSTEM_SETTING IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_ACTIVE_SETTING CHECK (IS_ACTIVE IN ('Y', 'N'))
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_EMAIL_SETTINGS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_SETTINGS");
        }
    }

    private static void createEmailFiltersTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول مرشحات البريد الإلكتروني...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_FILTERS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_FILTERS_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL = """
                CREATE TABLE ERP_EMAIL_FILTERS (
                    FILTER_ID NUMBER(10) NOT NULL,
                    ACCOUNT_ID NUMBER(10) NOT NULL,
                    FILTER_NAME_AR NVARCHAR2(200) NOT NULL,
                    FILTER_NAME_EN VARCHAR2(200) NOT NULL,
                    FILTER_TYPE VARCHAR2(20) DEFAULT 'INBOX',
                    FILTER_CONDITIONS CLOB NOT NULL, -- JSON format
                    FILTER_ACTIONS CLOB NOT NULL, -- JSON format
                    EXECUTION_ORDER NUMBER(3) DEFAULT 1,
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    LAST_UPDATED DATE DEFAULT SYSDATE,
                    UPDATED_BY VARCHAR2(50) DEFAULT USER,

                    CONSTRAINT PK_ERP_EMAIL_FILTERS PRIMARY KEY (FILTER_ID),
                    CONSTRAINT FK_FILTER_ACCOUNT FOREIGN KEY (ACCOUNT_ID)
                        REFERENCES ERP_EMAIL_ACCOUNTS(ACCOUNT_ID) ON DELETE CASCADE,
                    CONSTRAINT CHK_FILTER_TYPE CHECK (FILTER_TYPE IN ('INBOX', 'SPAM', 'CUSTOM')),
                    CONSTRAINT CHK_IS_ACTIVE_FILTER CHECK (IS_ACTIVE IN ('Y', 'N'))
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_EMAIL_FILTERS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_FILTERS");
        }
    }

    private static void createEmailSignaturesTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول توقيعات البريد الإلكتروني...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_EMAIL_SIGNATURES CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_EMAIL_SIGNATURES_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL = """
                CREATE TABLE ERP_EMAIL_SIGNATURES (
                    SIGNATURE_ID NUMBER(10) NOT NULL,
                    ACCOUNT_ID NUMBER(10) NOT NULL,
                    SIGNATURE_NAME_AR NVARCHAR2(200) NOT NULL,
                    SIGNATURE_NAME_EN VARCHAR2(200) NOT NULL,
                    SIGNATURE_HTML_AR NCLOB,
                    SIGNATURE_HTML_EN CLOB,
                    SIGNATURE_TEXT_AR NCLOB,
                    SIGNATURE_TEXT_EN CLOB,
                    IS_DEFAULT CHAR(1) DEFAULT 'N',
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,
                    LAST_UPDATED DATE DEFAULT SYSDATE,
                    UPDATED_BY VARCHAR2(50) DEFAULT USER,

                    CONSTRAINT PK_ERP_EMAIL_SIGNATURES PRIMARY KEY (SIGNATURE_ID),
                    CONSTRAINT FK_SIGNATURE_ACCOUNT FOREIGN KEY (ACCOUNT_ID)
                        REFERENCES ERP_EMAIL_ACCOUNTS(ACCOUNT_ID) ON DELETE CASCADE,
                    CONSTRAINT CHK_IS_DEFAULT_SIG CHECK (IS_DEFAULT IN ('Y', 'N')),
                    CONSTRAINT CHK_IS_ACTIVE_SIG CHECK (IS_ACTIVE IN ('Y', 'N'))
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_EMAIL_SIGNATURES_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_EMAIL_SIGNATURES");
        }
    }

    private static void insertSampleData(Connection connection) throws SQLException {
        System.out.println("📊 إدراج بيانات تجريبية...");

        // إدراج حساب بريد إلكتروني تجريبي
        String insertAccountSQL = """
                INSERT INTO ERP_EMAIL_ACCOUNTS (
                    ACCOUNT_ID, ACCOUNT_NAME_AR, ACCOUNT_NAME_EN, EMAIL_ADDRESS,
                    DISPLAY_NAME_AR, DISPLAY_NAME_EN, INCOMING_SERVER, OUTGOING_SERVER,
                    ACCOUNT_TYPE, IS_DEFAULT_ACCOUNT, CREATED_BY
                ) VALUES (
                    ERP_EMAIL_ACCOUNTS_SEQ.NEXTVAL,
                    'البريد الرسمي للشركة', 'Company Official Email',
                    '<EMAIL>',
                    'شركة الشحن المتقدمة', 'Advanced Shipping Company',
                    'mail.advancedshipping.com', 'smtp.advancedshipping.com',
                    'BUSINESS', 'Y', 'SYSTEM'
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(insertAccountSQL);
            System.out.println("✅ تم إدراج حساب بريد إلكتروني تجريبي");
        }

        // إدراج قالب بريد إلكتروني تجريبي
        String insertTemplateSQL = """
                INSERT INTO ERP_EMAIL_TEMPLATES (
                    TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN,
                    SUBJECT_AR, SUBJECT_EN, BODY_HTML_AR, BODY_HTML_EN,
                    TEMPLATE_TYPE, CREATED_BY
                ) VALUES (
                    ERP_EMAIL_TEMPLATES_SEQ.NEXTVAL,
                    'قالب الترحيب', 'Welcome Template',
                    'مرحباً بك في شركة الشحن المتقدمة', 'Welcome to Advanced Shipping Company',
                    '<h2>مرحباً بك!</h2><p>نرحب بك في شركة الشحن المتقدمة</p>',
                    '<h2>Welcome!</h2><p>Welcome to Advanced Shipping Company</p>',
                    'STANDARD', 'SYSTEM'
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(insertTemplateSQL);
            System.out.println("✅ تم إدراج قالب بريد إلكتروني تجريبي");
        }
    }
}
