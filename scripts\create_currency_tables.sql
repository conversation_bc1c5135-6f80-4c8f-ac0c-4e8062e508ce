-- =====================================================
-- إنشاء جداول نظام إدارة العملات الشامل
-- Create Comprehensive Currency Management System Tables
-- =====================================================

PROMPT '💰 إنشاء جداول نظام إدارة العملات الشامل'
PROMPT 'Creating Comprehensive Currency Management System Tables'
PROMPT '========================================================='

SET SERVEROUTPUT ON

-- =====================================================
-- 1. جدو<PERSON> العملات الرئيسي
-- =====================================================

PROMPT '[1] إنشاء جدول العملات الرئيسي...'

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE ERP_CURRENCIES CASCADE CONSTRAINTS';
    DBMS_OUTPUT.PUT_LINE('تم حذف الجدول السابق');
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('الجدول غير موجود مسبقاً');
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE ERP_CURRENCIES_SEQ';
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

CREATE TABLE ERP_CURRENCIES (
    CURRENCY_ID NUMBER(10) PRIMARY KEY,
    CURRENCY_CODE VARCHAR2(10) NOT NULL UNIQUE,
    CURRENCY_NAME_AR VARCHAR2(100) NOT NULL,
    CURRENCY_NAME_EN VARCHAR2(100) NOT NULL,
    CURRENCY_SYMBOL VARCHAR2(10),
    SYMBOL_POSITION VARCHAR2(10) DEFAULT 'BEFORE',
    DECIMAL_PLACES NUMBER(2) DEFAULT 2,
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    IS_DEFAULT CHAR(1) DEFAULT 'N' CHECK (IS_DEFAULT IN ('Y', 'N')),
    COUNTRY_CODE VARCHAR2(5),
    DISPLAY_FORMAT VARCHAR2(50) DEFAULT '#,##0.00',
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    LAST_UPDATED DATE DEFAULT SYSDATE,
    UPDATED_BY VARCHAR2(50) DEFAULT USER
);

CREATE SEQUENCE ERP_CURRENCIES_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول العملات الرئيسي'

-- =====================================================
-- 2. جدول أسعار الصرف
-- =====================================================

PROMPT '[2] إنشاء جدول أسعار الصرف...'

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE ERP_EXCHANGE_RATES CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE ERP_EXCHANGE_RATES_SEQ';
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

CREATE TABLE ERP_EXCHANGE_RATES (
    RATE_ID NUMBER(10) PRIMARY KEY,
    FROM_CURRENCY_ID NUMBER(10) NOT NULL,
    TO_CURRENCY_ID NUMBER(10) NOT NULL,
    EXCHANGE_RATE NUMBER(15,6) NOT NULL,
    RATE_DATE DATE DEFAULT SYSDATE,
    RATE_SOURCE VARCHAR2(50),
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    CONSTRAINT FK_EXCHANGE_FROM_CURRENCY FOREIGN KEY (FROM_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID),
    CONSTRAINT FK_EXCHANGE_TO_CURRENCY FOREIGN KEY (TO_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID)
);

CREATE SEQUENCE ERP_EXCHANGE_RATES_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول أسعار الصرف'

-- =====================================================
-- 3. جدول تاريخ أسعار الصرف
-- =====================================================

PROMPT '[3] إنشاء جدول تاريخ أسعار الصرف...'

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE ERP_EXCHANGE_RATE_HISTORY CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE ERP_EXCHANGE_RATE_HISTORY_SEQ';
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

CREATE TABLE ERP_EXCHANGE_RATE_HISTORY (
    HISTORY_ID NUMBER(10) PRIMARY KEY,
    FROM_CURRENCY_ID NUMBER(10) NOT NULL,
    TO_CURRENCY_ID NUMBER(10) NOT NULL,
    OLD_RATE NUMBER(15,6),
    NEW_RATE NUMBER(15,6) NOT NULL,
    CHANGE_DATE DATE DEFAULT SYSDATE,
    CHANGE_REASON VARCHAR2(200),
    RATE_SOURCE VARCHAR2(50),
    CHANGED_BY VARCHAR2(50) DEFAULT USER,
    CONSTRAINT FK_HISTORY_FROM_CURRENCY FOREIGN KEY (FROM_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID),
    CONSTRAINT FK_HISTORY_TO_CURRENCY FOREIGN KEY (TO_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID)
);

CREATE SEQUENCE ERP_EXCHANGE_RATE_HISTORY_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول تاريخ أسعار الصرف'

-- =====================================================
-- 4. جدول إعدادات النظام للعملات
-- =====================================================

PROMPT '[4] إنشاء جدول إعدادات النظام...'

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE ERP_CURRENCY_SETTINGS CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE ERP_CURRENCY_SETTINGS_SEQ';
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

CREATE TABLE ERP_CURRENCY_SETTINGS (
    SETTING_ID NUMBER(10) PRIMARY KEY,
    SETTING_KEY VARCHAR2(100) NOT NULL UNIQUE,
    SETTING_VALUE VARCHAR2(500),
    SETTING_DESCRIPTION VARCHAR2(200),
    SETTING_TYPE VARCHAR2(20) DEFAULT 'STRING',
    IS_SYSTEM CHAR(1) DEFAULT 'N' CHECK (IS_SYSTEM IN ('Y', 'N')),
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    LAST_UPDATED DATE DEFAULT SYSDATE,
    UPDATED_BY VARCHAR2(50) DEFAULT USER
);

CREATE SEQUENCE ERP_CURRENCY_SETTINGS_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول إعدادات النظام'

-- =====================================================
-- 5. جدول مصادر أسعار الصرف الخارجية
-- =====================================================

PROMPT '[5] إنشاء جدول مصادر أسعار الصرف الخارجية...'

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE ERP_EXCHANGE_RATE_SOURCES CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE ERP_EXCHANGE_RATE_SOURCES_SEQ';
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

CREATE TABLE ERP_EXCHANGE_RATE_SOURCES (
    SOURCE_ID NUMBER(10) PRIMARY KEY,
    SOURCE_NAME VARCHAR2(100) NOT NULL,
    SOURCE_URL VARCHAR2(500),
    API_KEY VARCHAR2(200),
    UPDATE_FREQUENCY NUMBER(5) DEFAULT 60,
    LAST_UPDATE DATE,
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    PRIORITY_ORDER NUMBER(3) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50) DEFAULT USER
);

CREATE SEQUENCE ERP_EXCHANGE_RATE_SOURCES_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول مصادر أسعار الصرف الخارجية'

-- =====================================================
-- 6. إدراج البيانات الافتراضية
-- =====================================================

PROMPT '[6] إدراج البيانات الافتراضية...'

-- العملات الافتراضية
INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, IS_DEFAULT, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'USD', 'الدولار الأمريكي', 'US Dollar', '$', 'BEFORE', 2, 'Y', 'US');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'EUR', 'اليورو', 'Euro', '€', 'BEFORE', 2, 'EU');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'SAR', 'الريال السعودي', 'Saudi Riyal', 'ر.س', 'AFTER', 2, 'SA');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'AED', 'الدرهم الإماراتي', 'UAE Dirham', 'د.إ', 'AFTER', 2, 'AE');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'EGP', 'الجنيه المصري', 'Egyptian Pound', 'ج.م', 'AFTER', 2, 'EG');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'JOD', 'الدينار الأردني', 'Jordanian Dinar', 'د.أ', 'AFTER', 3, 'JO');

-- أسعار الصرف الافتراضية
INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
VALUES (ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 1, 3, 3.75, 'SYSTEM');

INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
VALUES (ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 1, 4, 3.67, 'SYSTEM');

INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
VALUES (ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 1, 5, 30.9, 'SYSTEM');

INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
VALUES (ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 1, 6, 0.71, 'SYSTEM');

INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
VALUES (ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 2, 1, 1.18, 'SYSTEM');

-- الإعدادات الافتراضية
INSERT INTO ERP_CURRENCY_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESCRIPTION, SETTING_TYPE, IS_SYSTEM)
VALUES (ERP_CURRENCY_SETTINGS_SEQ.NEXTVAL, 'DEFAULT_CURRENCY', 'USD', 'العملة الافتراضية للنظام', 'STRING', 'Y');

INSERT INTO ERP_CURRENCY_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESCRIPTION, SETTING_TYPE, IS_SYSTEM)
VALUES (ERP_CURRENCY_SETTINGS_SEQ.NEXTVAL, 'AUTO_UPDATE_RATES', 'Y', 'تحديث أسعار الصرف تلقائياً', 'BOOLEAN', 'Y');

INSERT INTO ERP_CURRENCY_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESCRIPTION, SETTING_TYPE, IS_SYSTEM)
VALUES (ERP_CURRENCY_SETTINGS_SEQ.NEXTVAL, 'UPDATE_FREQUENCY', '60', 'تكرار التحديث بالدقائق', 'NUMBER', 'Y');

INSERT INTO ERP_CURRENCY_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESCRIPTION, SETTING_TYPE, IS_SYSTEM)
VALUES (ERP_CURRENCY_SETTINGS_SEQ.NEXTVAL, 'DECIMAL_PLACES', '2', 'عدد الخانات العشرية الافتراضي', 'NUMBER', 'Y');

-- مصادر أسعار الصرف
INSERT INTO ERP_EXCHANGE_RATE_SOURCES (SOURCE_ID, SOURCE_NAME, SOURCE_URL, UPDATE_FREQUENCY, PRIORITY_ORDER)
VALUES (ERP_EXCHANGE_RATE_SOURCES_SEQ.NEXTVAL, 'البنك المركزي السعودي', 'https://www.sama.gov.sa', 1440, 1);

INSERT INTO ERP_EXCHANGE_RATE_SOURCES (SOURCE_ID, SOURCE_NAME, SOURCE_URL, UPDATE_FREQUENCY, PRIORITY_ORDER)
VALUES (ERP_EXCHANGE_RATE_SOURCES_SEQ.NEXTVAL, 'European Central Bank', 'https://www.ecb.europa.eu', 1440, 2);

COMMIT;

PROMPT '✅ تم إدراج البيانات الافتراضية'

PROMPT
PROMPT '🎉 تم إنشاء نظام إدارة العملات الشامل بنجاح!'
PROMPT '=============================================='
