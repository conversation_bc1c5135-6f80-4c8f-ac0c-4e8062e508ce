-- ========================================
-- إنشاء جدول الموردين بنفس بنية V_DETAILS من IAS20251
-- Creating Suppliers Table with EXACT V_DETAILS Structure from IAS20251
-- ========================================

-- إنشاء جدول الموردين بنفس بنية V_DETAILS تماماً
CREATE TABLE ERP_SUPPLIERS (
    ID NUMBER(10) PRIMARY KEY,
    CODE VARCHAR2(50) UNIQUE NOT NULL,
    NAME VARCHAR2(200) NOT NULL,
    NAME_EN VARCHAR2(200),
    TYPE VARCHAR2(50),
    DESCRIPTION VARCHAR2(500),
    DESCRIPTION_EN VARCHAR2(500),
    CATEGORY VARCHAR2(100),
    SU<PERSON>ATEGORY VARCHAR2(100),
    STATUS VARCHAR2(20) DEFAULT 'ACTIVE',
    PRIORITY NUMBER(3) DEFAULT 1,
    CONTACT_PERSON VARCHAR2(100),
    PHONE VARCHAR2(50),
    MOBILE VARCHAR2(50),
    FAX VARCHAR2(50),
    EMAIL VARCHAR2(100),
    WEBSITE VARCHAR2(200),
    ADDRESS_LINE1 VARCHAR2(200),
    ADDRESS_LINE2 VARCHAR2(200),
    CITY VARCHAR2(100),
    STATE VARCHAR2(100),
    POSTAL_CODE VARCHAR2(20),
    COUNTRY VARCHAR2(100) DEFAULT 'Saudi Arabia',
    TAX_NUMBER VARCHAR2(50),
    COMMERCIAL_REGISTER VARCHAR2(50),
    LICENSE_NUMBER VARCHAR2(50),
    LICENSE_EXPIRY DATE,
    BANK_NAME VARCHAR2(100),
    BANK_ACCOUNT VARCHAR2(50),
    IBAN VARCHAR2(50),
    SWIFT_CODE VARCHAR2(20),
    PAYMENT_TERMS VARCHAR2(100),
    CREDIT_LIMIT NUMBER(15,2) DEFAULT 0,
    CURRENCY_CODE VARCHAR2(10) DEFAULT 'SAR',
    DISCOUNT_PERCENTAGE NUMBER(5,2) DEFAULT 0,
    TAX_PERCENTAGE NUMBER(5,2) DEFAULT 15,
    RATING NUMBER(3,1) DEFAULT 0,
    NOTES CLOB,
    INTERNAL_NOTES CLOB,
    TAGS VARCHAR2(500),
    REFERENCE_NUMBER VARCHAR2(50),
    PARENT_ID NUMBER(10),
    LEVEL_NUMBER NUMBER(3) DEFAULT 1,
    SORT_ORDER NUMBER(10) DEFAULT 0,
    IS_ACTIVE VARCHAR2(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    IS_APPROVED VARCHAR2(1) DEFAULT 'N' CHECK (IS_APPROVED IN ('Y', 'N')),
    IS_BLOCKED VARCHAR2(1) DEFAULT 'N' CHECK (IS_BLOCKED IN ('Y', 'N')),
    IS_DELETED VARCHAR2(1) DEFAULT 'N' CHECK (IS_DELETED IN ('Y', 'N')),
    APPROVAL_DATE DATE,
    APPROVED_BY VARCHAR2(50),
    BLOCKED_DATE DATE,
    BLOCKED_BY VARCHAR2(50),
    BLOCKED_REASON VARCHAR2(500),
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    CREATED_DATE DATE DEFAULT SYSDATE,
    LAST_UPDATED DATE DEFAULT SYSDATE,
    UPDATED_BY VARCHAR2(50) DEFAULT USER,
    VERSION_NUMBER NUMBER(10) DEFAULT 1,
    EXTERNAL_ID VARCHAR2(100),
    EXTERNAL_SYSTEM VARCHAR2(50),
    SYNC_STATUS VARCHAR2(20) DEFAULT 'PENDING',
    SYNC_DATE DATE,
    METADATA CLOB,
    CUSTOM_FIELD1 VARCHAR2(200),
    CUSTOM_FIELD2 VARCHAR2(200),
    CUSTOM_FIELD3 VARCHAR2(200),
    CUSTOM_FIELD4 NUMBER(15,2),
    CUSTOM_FIELD5 NUMBER(15,2),
    CUSTOM_FIELD6 DATE,
    CUSTOM_FIELD7 DATE,
    CUSTOM_FIELD8 CLOB,
    CUSTOM_FIELD9 CLOB,
    CUSTOM_FIELD10 VARCHAR2(1) CHECK (CUSTOM_FIELD10 IN ('Y', 'N'))
);

-- إنشاء فهارس للبحث السريع (بنفس نمط V_DETAILS)
CREATE INDEX IDX_SUPPLIERS_CODE ON ERP_SUPPLIERS(CODE);
CREATE INDEX IDX_SUPPLIERS_NAME ON ERP_SUPPLIERS(NAME);
CREATE INDEX IDX_SUPPLIERS_TYPE ON ERP_SUPPLIERS(TYPE);
CREATE INDEX IDX_SUPPLIERS_STATUS ON ERP_SUPPLIERS(STATUS);
CREATE INDEX IDX_SUPPLIERS_CATEGORY ON ERP_SUPPLIERS(CATEGORY);
CREATE INDEX IDX_SUPPLIERS_ACTIVE ON ERP_SUPPLIERS(IS_ACTIVE);
CREATE INDEX IDX_SUPPLIERS_PARENT ON ERP_SUPPLIERS(PARENT_ID);
CREATE INDEX IDX_SUPPLIERS_EXTERNAL ON ERP_SUPPLIERS(EXTERNAL_ID);

-- إنشاء sequence للمعرف الفريد
CREATE SEQUENCE ERP_SUPPLIERS_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- إنشاء جدول فئات الموردين
CREATE TABLE ERP_SUPPLIER_CATEGORIES (
    CATEGORY_ID NUMBER(10) PRIMARY KEY,
    CATEGORY_CODE VARCHAR2(20) UNIQUE NOT NULL,
    CATEGORY_NAME VARCHAR2(100) NOT NULL,
    CATEGORY_NAME_EN VARCHAR2(100),
    PARENT_CATEGORY_ID NUMBER(10),
    DESCRIPTION VARCHAR2(500),
    IS_ACTIVE VARCHAR2(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_SUPPLIER_CAT_PARENT FOREIGN KEY (PARENT_CATEGORY_ID) REFERENCES ERP_SUPPLIER_CATEGORIES(CATEGORY_ID)
);

CREATE SEQUENCE ERP_SUPPLIER_CATEGORIES_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- إنشاء جدول ربط الموردين بالفئات
CREATE TABLE ERP_SUPPLIER_CATEGORY_MAPPING (
    MAPPING_ID NUMBER(10) PRIMARY KEY,
    SUPPLIER_ID NUMBER(10) NOT NULL,
    CATEGORY_ID NUMBER(10) NOT NULL,
    IS_PRIMARY VARCHAR2(1) DEFAULT 'N' CHECK (IS_PRIMARY IN ('Y', 'N')),
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_SUP_CAT_MAP_SUPPLIER FOREIGN KEY (SUPPLIER_ID) REFERENCES ERP_SUPPLIERS(SUPPLIER_ID) ON DELETE CASCADE,
    CONSTRAINT FK_SUP_CAT_MAP_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES ERP_SUPPLIER_CATEGORIES(CATEGORY_ID),
    CONSTRAINT UK_SUPPLIER_CATEGORY UNIQUE (SUPPLIER_ID, CATEGORY_ID)
);

CREATE SEQUENCE ERP_SUPPLIER_CAT_MAP_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- إنشاء جدول جهات الاتصال للموردين
CREATE TABLE ERP_SUPPLIER_CONTACTS (
    CONTACT_ID NUMBER(10) PRIMARY KEY,
    SUPPLIER_ID NUMBER(10) NOT NULL,
    CONTACT_NAME VARCHAR2(100) NOT NULL,
    CONTACT_TITLE VARCHAR2(100),
    DEPARTMENT VARCHAR2(100),
    PHONE VARCHAR2(50),
    MOBILE VARCHAR2(50),
    EMAIL VARCHAR2(100),
    IS_PRIMARY VARCHAR2(1) DEFAULT 'N' CHECK (IS_PRIMARY IN ('Y', 'N')),
    IS_ACTIVE VARCHAR2(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    NOTES VARCHAR2(500),
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_SUP_CONTACT_SUPPLIER FOREIGN KEY (SUPPLIER_ID) REFERENCES ERP_SUPPLIERS(SUPPLIER_ID) ON DELETE CASCADE
);

CREATE SEQUENCE ERP_SUPPLIER_CONTACTS_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- إنشاء جدول عناوين الموردين
CREATE TABLE ERP_SUPPLIER_ADDRESSES (
    ADDRESS_ID NUMBER(10) PRIMARY KEY,
    SUPPLIER_ID NUMBER(10) NOT NULL,
    ADDRESS_TYPE VARCHAR2(50) DEFAULT 'MAIN',
    ADDRESS_LINE1 VARCHAR2(200) NOT NULL,
    ADDRESS_LINE2 VARCHAR2(200),
    CITY VARCHAR2(100),
    STATE VARCHAR2(100),
    POSTAL_CODE VARCHAR2(20),
    COUNTRY VARCHAR2(100) DEFAULT 'Saudi Arabia',
    IS_PRIMARY VARCHAR2(1) DEFAULT 'N' CHECK (IS_PRIMARY IN ('Y', 'N')),
    IS_ACTIVE VARCHAR2(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_SUP_ADDRESS_SUPPLIER FOREIGN KEY (SUPPLIER_ID) REFERENCES ERP_SUPPLIERS(SUPPLIER_ID) ON DELETE CASCADE,
    CONSTRAINT CHK_ADDRESS_TYPE CHECK (ADDRESS_TYPE IN ('MAIN', 'BILLING', 'SHIPPING', 'WAREHOUSE', 'OFFICE'))
);

CREATE SEQUENCE ERP_SUPPLIER_ADDRESSES_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- إنشاء جدول المرفقات للموردين
CREATE TABLE ERP_SUPPLIER_ATTACHMENTS (
    ATTACHMENT_ID NUMBER(10) PRIMARY KEY,
    SUPPLIER_ID NUMBER(10) NOT NULL,
    ATTACHMENT_NAME VARCHAR2(200) NOT NULL,
    ATTACHMENT_TYPE VARCHAR2(50),
    FILE_PATH VARCHAR2(500),
    FILE_SIZE NUMBER(15),
    MIME_TYPE VARCHAR2(100),
    DESCRIPTION VARCHAR2(500),
    IS_ACTIVE VARCHAR2(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    UPLOADED_BY VARCHAR2(50) DEFAULT USER,
    UPLOAD_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_SUP_ATTACH_SUPPLIER FOREIGN KEY (SUPPLIER_ID) REFERENCES ERP_SUPPLIERS(SUPPLIER_ID) ON DELETE CASCADE
);

CREATE SEQUENCE ERP_SUPPLIER_ATTACHMENTS_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- إنشاء جدول تقييم الموردين
CREATE TABLE ERP_SUPPLIER_EVALUATIONS (
    EVALUATION_ID NUMBER(10) PRIMARY KEY,
    SUPPLIER_ID NUMBER(10) NOT NULL,
    EVALUATION_DATE DATE DEFAULT SYSDATE,
    QUALITY_RATING NUMBER(3,1) CHECK (QUALITY_RATING BETWEEN 1 AND 5),
    DELIVERY_RATING NUMBER(3,1) CHECK (DELIVERY_RATING BETWEEN 1 AND 5),
    SERVICE_RATING NUMBER(3,1) CHECK (SERVICE_RATING BETWEEN 1 AND 5),
    PRICE_RATING NUMBER(3,1) CHECK (PRICE_RATING BETWEEN 1 AND 5),
    OVERALL_RATING NUMBER(3,1) CHECK (OVERALL_RATING BETWEEN 1 AND 5),
    COMMENTS CLOB,
    EVALUATED_BY VARCHAR2(50) DEFAULT USER,
    EVALUATION_PERIOD VARCHAR2(50),
    CONSTRAINT FK_SUP_EVAL_SUPPLIER FOREIGN KEY (SUPPLIER_ID) REFERENCES ERP_SUPPLIERS(SUPPLIER_ID) ON DELETE CASCADE
);

CREATE SEQUENCE ERP_SUPPLIER_EVALUATIONS_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- إدراج فئات الموردين الافتراضية
INSERT INTO ERP_SUPPLIER_CATEGORIES (CATEGORY_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_NAME_EN, DESCRIPTION) VALUES
(ERP_SUPPLIER_CATEGORIES_SEQ.NEXTVAL, 'RAW_MAT', 'المواد الخام', 'Raw Materials', 'موردو المواد الخام والمكونات الأساسية');

INSERT INTO ERP_SUPPLIER_CATEGORIES (CATEGORY_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_NAME_EN, DESCRIPTION) VALUES
(ERP_SUPPLIER_CATEGORIES_SEQ.NEXTVAL, 'SERVICES', 'الخدمات', 'Services', 'مقدمو الخدمات المختلفة');

INSERT INTO ERP_SUPPLIER_CATEGORIES (CATEGORY_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_NAME_EN, DESCRIPTION) VALUES
(ERP_SUPPLIER_CATEGORIES_SEQ.NEXTVAL, 'EQUIPMENT', 'المعدات', 'Equipment', 'موردو المعدات والآلات');

INSERT INTO ERP_SUPPLIER_CATEGORIES (CATEGORY_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_NAME_EN, DESCRIPTION) VALUES
(ERP_SUPPLIER_CATEGORIES_SEQ.NEXTVAL, 'OFFICE', 'المكتبية', 'Office Supplies', 'موردو المستلزمات المكتبية');

INSERT INTO ERP_SUPPLIER_CATEGORIES (CATEGORY_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_NAME_EN, DESCRIPTION) VALUES
(ERP_SUPPLIER_CATEGORIES_SEQ.NEXTVAL, 'IT', 'تقنية المعلومات', 'IT Services', 'موردو خدمات وحلول تقنية المعلومات');

-- إدراج بيانات موردين تجريبية بنفس تنسيق V_DETAILS
INSERT INTO ERP_SUPPLIERS (
    ID, CODE, NAME, NAME_EN, TYPE, DESCRIPTION, CATEGORY, STATUS,
    CONTACT_PERSON, PHONE, EMAIL, CITY, COUNTRY,
    IS_ACTIVE, IS_APPROVED, PRIORITY, LEVEL_NUMBER
) VALUES (
    ERP_SUPPLIERS_SEQ.NEXTVAL, 'SUP001', 'شركة المواد الأولى المحدودة', 'First Materials Company Ltd',
    'VENDOR', 'مورد المواد الخام والمكونات الأساسية', 'RAW_MATERIALS', 'ACTIVE',
    'أحمد محمد', '+966-11-1234567', '<EMAIL>', 'الرياض', 'Saudi Arabia',
    'Y', 'Y', 1, 1
);

INSERT INTO ERP_SUPPLIERS (
    ID, CODE, NAME, NAME_EN, TYPE, DESCRIPTION, CATEGORY, STATUS,
    CONTACT_PERSON, PHONE, EMAIL, CITY, COUNTRY,
    IS_ACTIVE, IS_APPROVED, PRIORITY, LEVEL_NUMBER
) VALUES (
    ERP_SUPPLIERS_SEQ.NEXTVAL, 'SUP002', 'مؤسسة الخدمات التقنية', 'Tech Services Foundation',
    'SERVICE_PROVIDER', 'مقدم خدمات تقنية المعلومات والحلول الرقمية', 'IT_SERVICES', 'ACTIVE',
    'سارة أحمد', '+966-12-2345678', '<EMAIL>', 'جدة', 'Saudi Arabia',
    'Y', 'Y', 2, 1
);

INSERT INTO ERP_SUPPLIERS (
    ID, CODE, NAME, NAME_EN, TYPE, DESCRIPTION, CATEGORY, STATUS,
    CONTACT_PERSON, PHONE, EMAIL, CITY, COUNTRY,
    IS_ACTIVE, IS_APPROVED, PRIORITY, LEVEL_NUMBER
) VALUES (
    ERP_SUPPLIERS_SEQ.NEXTVAL, 'SUP003', 'شركة المعدات الصناعية', 'Industrial Equipment Co',
    'MANUFACTURER', 'مصنع ومورد المعدات والآلات الصناعية', 'EQUIPMENT', 'ACTIVE',
    'محمد علي', '+966-13-3456789', '<EMAIL>', 'الدمام', 'Saudi Arabia',
    'Y', 'Y', 1, 1
);

COMMIT;

-- إنشاء view شامل للموردين بنفس تنسيق V_DETAILS
CREATE OR REPLACE VIEW V_SUPPLIERS_DETAILS AS
SELECT
    s.ID,
    s.CODE,
    s.NAME,
    s.NAME_EN,
    s.TYPE,
    s.DESCRIPTION,
    s.DESCRIPTION_EN,
    s.CATEGORY,
    s.SUBCATEGORY,
    s.STATUS,
    s.PRIORITY,
    s.CONTACT_PERSON,
    s.PHONE,
    s.MOBILE,
    s.FAX,
    s.EMAIL,
    s.WEBSITE,
    s.ADDRESS_LINE1,
    s.ADDRESS_LINE2,
    s.CITY,
    s.STATE,
    s.POSTAL_CODE,
    s.COUNTRY,
    s.TAX_NUMBER,
    s.COMMERCIAL_REGISTER,
    s.LICENSE_NUMBER,
    s.LICENSE_EXPIRY,
    s.BANK_NAME,
    s.BANK_ACCOUNT,
    s.IBAN,
    s.SWIFT_CODE,
    s.PAYMENT_TERMS,
    s.CREDIT_LIMIT,
    s.CURRENCY_CODE,
    s.DISCOUNT_PERCENTAGE,
    s.TAX_PERCENTAGE,
    s.RATING,
    s.NOTES,
    s.INTERNAL_NOTES,
    s.TAGS,
    s.REFERENCE_NUMBER,
    s.PARENT_ID,
    s.LEVEL_NUMBER,
    s.SORT_ORDER,
    s.IS_ACTIVE,
    s.IS_APPROVED,
    s.IS_BLOCKED,
    s.IS_DELETED,
    s.APPROVAL_DATE,
    s.APPROVED_BY,
    s.BLOCKED_DATE,
    s.BLOCKED_BY,
    s.BLOCKED_REASON,
    s.CREATED_BY,
    s.CREATED_DATE,
    s.LAST_UPDATED,
    s.UPDATED_BY,
    s.VERSION_NUMBER,
    s.EXTERNAL_ID,
    s.EXTERNAL_SYSTEM,
    s.SYNC_STATUS,
    s.SYNC_DATE,
    s.METADATA,
    s.CUSTOM_FIELD1,
    s.CUSTOM_FIELD2,
    s.CUSTOM_FIELD3,
    s.CUSTOM_FIELD4,
    s.CUSTOM_FIELD5,
    s.CUSTOM_FIELD6,
    s.CUSTOM_FIELD7,
    s.CUSTOM_FIELD8,
    s.CUSTOM_FIELD9,
    s.CUSTOM_FIELD10
FROM ERP_SUPPLIERS s;

COMMIT;
