package com.shipment.erp.repository;

import com.shipment.erp.model.Role;
import java.util.List;
import java.util.Optional;

/**
 * مستودع بيانات الأدوار
 * Role Data Repository
 */
public interface RoleRepository extends BaseRepository<Role> {
    
    /**
     * البحث عن الأدوار النشطة
     * Find active roles
     */
    List<Role> findByIsActiveTrue();
    
    /**
     * البحث عن دور بالاسم
     * Find role by name
     */
    Optional<Role> findByName(String name);
    
    /**
     * البحث عن الأدوار بالاسم (يحتوي على)
     * Find roles by name containing
     */
    List<Role> findByNameContainingIgnoreCase(String name);
    
    /**
     * التحقق من وجود اسم الدور
     * Check if role name exists
     */
    boolean existsByName(String name);
    
    /**
     * الحصول على عدد المستخدمين للدور
     * Get users count for role
     */
    long getUsersCount(Long roleId);
    
    /**
     * البحث عن الأدوار الافتراضية
     * Find default roles
     */
    List<Role> findDefaultRoles();
    
    /**
     * البحث عن الأدوار حسب الوصف
     * Find roles by description containing
     */
    List<Role> findByDescriptionContainingIgnoreCase(String description);
}
