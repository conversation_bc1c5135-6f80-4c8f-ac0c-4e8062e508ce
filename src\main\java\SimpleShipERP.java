import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.GridLayout;
import java.awt.Insets;
import javax.swing.BorderFactory;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenu;
import javax.swing.JMenuBar;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;

/**
 * تطبيق Ship ERP البسيط والفعال
 * Simple and Working Ship ERP Application
 */
public class SimpleShipERP extends JFrame {
    
    private TreeMenuPanel treeMenuPanel;
    
    public SimpleShipERP() {
        initializeApplication();
    }
    
    private void initializeApplication() {
        setTitle("🚢 نظام إدارة الشحنات - Ship ERP System");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        
        // تطبيق اتجاه اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        try {
            // تطبيق مظهر حديث
            FinalThemeManager.initializeDefaultTheme();
        } catch (Exception e) {
            System.out.println("تعذر تطبيق المظهر الحديث، سيتم استخدام المظهر الافتراضي");
        }
        
        setupLayout();
        setupMenuBar();
        
        System.out.println("✅ تم تشغيل نظام Ship ERP بنجاح!");
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // إنشاء لوحة القائمة الشجرية
        try {
            treeMenuPanel = new TreeMenuPanel(this);
            treeMenuPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            
            // إضافة اللوحة في منطقة التمرير
            JScrollPane treeScrollPane = new JScrollPane(treeMenuPanel);
            treeScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            treeScrollPane.setPreferredSize(new Dimension(300, 600));
            
            // إضافة اللوحة إلى الجانب الأيمن (للعربية)
            add(treeScrollPane, BorderLayout.EAST);
            
            // إضافة لوحة المحتوى الرئيسي
            JPanel mainContentPanel = createMainContentPanel();
            add(mainContentPanel, BorderLayout.CENTER);
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء القائمة الشجرية: " + e.getMessage());
            e.printStackTrace();
            
            // إنشاء لوحة بديلة في حالة الخطأ
            JLabel errorLabel = new JLabel("خطأ في تحميل القائمة الشجرية", SwingConstants.CENTER);
            errorLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
            add(errorLabel, BorderLayout.CENTER);
        }
    }
    
    private JPanel createMainContentPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // لوحة الترحيب
        JPanel welcomePanel = new JPanel(new GridBagLayout());
        welcomePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        welcomePanel.setBackground(new Color(240, 248, 255));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(20, 20, 20, 20);
        gbc.anchor = GridBagConstraints.CENTER;
        
        // عنوان الترحيب
        JLabel welcomeTitle = new JLabel("🚢 مرحباً بك في نظام إدارة الشحنات");
        welcomeTitle.setFont(new Font("Tahoma", Font.BOLD, 24));
        welcomeTitle.setForeground(new Color(0, 102, 204));
        welcomeTitle.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        gbc.gridy = 0;
        welcomePanel.add(welcomeTitle, gbc);
        
        // وصف النظام
        JLabel descriptionLabel = new JLabel("<html><div style='text-align: center;'>" +
            "نظام شامل لإدارة الشحنات والعملاء والموردين والمخازن<br>" +
            "مع دعم كامل للغة العربية وقاعدة بيانات Oracle" +
            "</div></html>");
        descriptionLabel.setFont(new Font("Tahoma", Font.PLAIN, 16));
        descriptionLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        gbc.gridy = 1;
        welcomePanel.add(descriptionLabel, gbc);
        
        // إحصائيات النظام
        JPanel statsPanel = createStatsPanel();
        gbc.gridy = 2;
        gbc.insets = new Insets(30, 20, 20, 20);
        welcomePanel.add(statsPanel, gbc);
        
        mainPanel.add(welcomePanel, BorderLayout.CENTER);
        
        // شريط الحالة
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        return mainPanel;
    }
    
    private JPanel createStatsPanel() {
        JPanel statsPanel = new JPanel(new GridLayout(2, 2, 20, 10));
        statsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statsPanel.setOpaque(false);
        
        // إحصائية الفئات
        JPanel categoriesCard = createStatCard("📁 الفئات النشطة", "14", "فئة");
        statsPanel.add(categoriesCard);
        
        // إحصائية النوافذ
        JPanel windowsCard = createStatCard("🪟 النوافذ المتاحة", "73", "نافذة");
        statsPanel.add(windowsCard);
        
        // إحصائية الأنظمة
        JPanel systemsCard = createStatCard("⚙️ الأنظمة الفرعية", "9", "نظام");
        statsPanel.add(systemsCard);
        
        // إحصائية قاعدة البيانات
        JPanel dbCard = createStatCard("🗄️ قاعدة البيانات", "متصل", "Oracle");
        statsPanel.add(dbCard);
        
        return statsPanel;
    }
    
    private JPanel createStatCard(String title, String value, String unit) {
        JPanel card = new JPanel(new BorderLayout());
        card.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createRaisedBevelBorder(),
            BorderFactory.createEmptyBorder(15, 15, 15, 15)
        ));
        card.setBackground(Color.WHITE);
        
        JLabel titleLabel = new JLabel(title, SwingConstants.CENTER);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 12));
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        card.add(titleLabel, BorderLayout.NORTH);
        
        JLabel valueLabel = new JLabel(value, SwingConstants.CENTER);
        valueLabel.setFont(new Font("Tahoma", Font.BOLD, 20));
        valueLabel.setForeground(new Color(0, 153, 76));
        valueLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        card.add(valueLabel, BorderLayout.CENTER);
        
        JLabel unitLabel = new JLabel(unit, SwingConstants.CENTER);
        unitLabel.setFont(new Font("Tahoma", Font.PLAIN, 10));
        unitLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        card.add(unitLabel, BorderLayout.SOUTH);
        
        return card;
    }
    
    private JPanel createStatusPanel() {
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        statusPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        JLabel statusLabel = new JLabel("✅ النظام جاهز - جميع الأنظمة تعمل بشكل طبيعي");
        statusLabel.setFont(new Font("Tahoma", Font.PLAIN, 12));
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusPanel.add(statusLabel);
        
        return statusPanel;
    }
    
    private void setupMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        menuBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // قائمة النظام
        JMenu systemMenu = new JMenu("النظام");
        systemMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JMenuItem exitItem = new JMenuItem("خروج");
        exitItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        exitItem.addActionListener(e -> System.exit(0));
        systemMenu.add(exitItem);
        
        // قائمة المساعدة
        JMenu helpMenu = new JMenu("المساعدة");
        helpMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JMenuItem aboutItem = new JMenuItem("حول النظام");
        aboutItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);
        
        menuBar.add(systemMenu);
        menuBar.add(helpMenu);
        
        setJMenuBar(menuBar);
    }
    
    private void showAboutDialog() {
        String aboutText = """
            🚢 نظام إدارة الشحنات - Ship ERP System
            
            الإصدار: 1.0
            
            نظام شامل لإدارة:
            • الشحنات والحاويات
            • العملاء والموردين  
            • المخازن والمخزون
            • الحسابات والمالية
            • الموظفين والرواتب
            • التقارير والإحصائيات
            
            مع دعم كامل للغة العربية وقاعدة بيانات Oracle
            """;
        
        JOptionPane.showMessageDialog(this, aboutText, "حول النظام", JOptionPane.INFORMATION_MESSAGE);
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                SimpleShipERP app = new SimpleShipERP();
                app.setVisible(true);
                System.out.println("🚀 تم تشغيل التطبيق بنجاح!");
            } catch (Exception e) {
                System.err.println("❌ خطأ في تشغيل التطبيق: " + e.getMessage());
                e.printStackTrace();
                
                // عرض رسالة خطأ للمستخدم
                JOptionPane.showMessageDialog(null, 
                    "خطأ في تشغيل التطبيق:\n" + e.getMessage(), 
                    "خطأ", 
                    JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
