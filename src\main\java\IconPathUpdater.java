import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * محدث مسارات الأيقونات في قاعدة البيانات
 * Icon Path Updater for Database
 */
public class IconPathUpdater {
    
    private Connection dbConnection;
    private Map<String, String> iconMappings;
    
    public static void main(String[] args) {
        System.out.println("🔄 محدث مسارات الأيقونات");
        System.out.println("Icon Path Updater");
        System.out.println("==========================================");
        
        IconPathUpdater updater = new IconPathUpdater();
        updater.updateIconPaths();
    }
    
    public void updateIconPaths() {
        try {
            // الاتصال بقاعدة البيانات
            connectToDatabase();
            
            // إعداد خريطة الأيقونات
            setupIconMappings();
            
            // تحديث مسارات الأيقونات
            updateDatabaseIconPaths();
            
            // تأكيد التغييرات
            dbConnection.commit();
            
            System.out.println("\n✅ تم تحديث جميع مسارات الأيقونات بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحديث مسارات الأيقونات: " + e.getMessage());
            e.printStackTrace();
            
            try {
                if (dbConnection != null) {
                    dbConnection.rollback();
                    System.out.println("🔄 تم التراجع عن التغييرات");
                }
            } catch (SQLException rollbackEx) {
                System.err.println("خطأ في التراجع: " + rollbackEx.getMessage());
            }
        } finally {
            closeConnection();
        }
    }
    
    private void connectToDatabase() throws Exception {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            dbConnection = tnsManager.getShipErpConnection();
            dbConnection.setAutoCommit(false);
            System.out.println("✅ تم الاتصال بقاعدة البيانات SHIP_ERP");
        } catch (Exception e) {
            System.err.println("❌ فشل الاتصال بقاعدة البيانات: " + e.getMessage());
            throw e;
        }
    }
    
    private void setupIconMappings() {
        iconMappings = new HashMap<>();
        
        // خريطة أنواع العقد إلى الأيقونات
        iconMappings.put("CATEGORY", "resources/icons/folder.png");
        iconMappings.put("WINDOW", "resources/icons/window.png");
        iconMappings.put("TOOL", "resources/icons/tool.png");
        iconMappings.put("REPORT", "resources/icons/report.png");
        
        // خريطة أسماء محددة إلى أيقونات خاصة
        iconMappings.put("إدارة المستخدمين", "resources/icons/user.png");
        iconMappings.put("إدارة المستخدمين (2)", "resources/icons/user.png");
        iconMappings.put("الإعدادات العامة", "resources/icons/settings.png");
        iconMappings.put("الإعدادات العامة (2)", "resources/icons/settings.png");
        iconMappings.put("إعدادات النظام العامة", "resources/icons/settings.png");
        iconMappings.put("إعدادات الواجهة والمظهر", "resources/icons/settings.png");
        iconMappings.put("إعدادات الشركات الشاملة", "resources/icons/settings.png");
        iconMappings.put("إعدادات المخزون", "resources/icons/settings.png");
        iconMappings.put("إعدادات قاعدة البيانات", "resources/icons/database.png");
        iconMappings.put("إعدادات الأمان", "resources/icons/settings.png");
        iconMappings.put("إعدادات التقارير", "resources/icons/settings.png");
        
        // أيقونات البريد الإلكتروني
        iconMappings.put("إدارة حسابات البريد", "resources/icons/email.png");
        iconMappings.put("إدارة حسابات البريد (2)", "resources/icons/email.png");
        iconMappings.put("صندوق البريد الوارد الشامل", "resources/icons/email.png");
        iconMappings.put("صندوق البريد الوارد الشامل (2)", "resources/icons/email.png");
        iconMappings.put("سجلات النظام", "resources/icons/database.png");
        iconMappings.put("سجلات النظام (2)", "resources/icons/database.png");
        
        System.out.println("📋 تم إعداد " + iconMappings.size() + " خريطة أيقونة");
    }
    
    private void updateDatabaseIconPaths() throws SQLException {
        System.out.println("\n🔄 تحديث مسارات الأيقونات في قاعدة البيانات...");
        
        // الحصول على جميع العقد
        String selectSql = "SELECT TREE_ID, NODE_NAME_AR, NODE_TYPE FROM ERP_SYSTEM_TREE";
        String updateSql = "UPDATE ERP_SYSTEM_TREE SET ICON_PATH = ? WHERE TREE_ID = ?";
        
        int updatedCount = 0;
        
        try (PreparedStatement selectStmt = dbConnection.prepareStatement(selectSql);
             PreparedStatement updateStmt = dbConnection.prepareStatement(updateSql);
             ResultSet rs = selectStmt.executeQuery()) {
            
            while (rs.next()) {
                int treeId = rs.getInt("TREE_ID");
                String nodeName = rs.getString("NODE_NAME_AR");
                String nodeType = rs.getString("NODE_TYPE");
                
                String iconPath = determineIconPath(nodeName, nodeType);
                
                updateStmt.setString(1, iconPath);
                updateStmt.setInt(2, treeId);
                updateStmt.executeUpdate();
                
                System.out.println("  • [" + treeId + "] " + nodeName + " → " + iconPath);
                updatedCount++;
            }
        }
        
        System.out.println("✅ تم تحديث " + updatedCount + " مسار أيقونة");
    }
    
    private String determineIconPath(String nodeName, String nodeType) {
        // البحث عن أيقونة خاصة بالاسم أولاً
        if (iconMappings.containsKey(nodeName)) {
            return iconMappings.get(nodeName);
        }
        
        // البحث عن أيقونة حسب النوع
        if (iconMappings.containsKey(nodeType)) {
            return iconMappings.get(nodeType);
        }
        
        // الأيقونة الافتراضية
        return "resources/icons/default.png";
    }
    
    private void closeConnection() {
        try {
            if (dbConnection != null && !dbConnection.isClosed()) {
                dbConnection.close();
                System.out.println("\n🔌 تم إغلاق الاتصال بقاعدة البيانات");
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
        }
    }
}
