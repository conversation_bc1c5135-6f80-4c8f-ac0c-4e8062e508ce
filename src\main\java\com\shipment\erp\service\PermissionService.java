package com.shipment.erp.service;

import com.shipment.erp.model.Permission;
import java.util.List;
import java.util.Map;

/**
 * خدمة إدارة الصلاحيات
 * Permission Management Service
 */
public interface PermissionService extends BaseService<Permission> {
    
    /**
     * البحث عن الصلاحيات حسب الدور
     * Find permissions by role
     */
    List<Permission> findByRoleId(Long roleId);
    
    /**
     * البحث عن صلاحية محددة للدور والوحدة
     * Find specific permission for role and module
     */
    Permission findByRoleIdAndModuleName(Long roleId, String moduleName);
    
    /**
     * البحث عن الصلاحيات حسب الوحدة
     * Find permissions by module
     */
    List<Permission> findByModuleName(String moduleName);
    
    /**
     * التحقق من وجود صلاحية للدور والوحدة
     * Check if permission exists for role and module
     */
    boolean hasPermission(Long roleId, String moduleName, String permissionType);
    
    /**
     * إنشاء صلاحيات افتراضية للدور
     * Create default permissions for role
     */
    void createDefaultPermissions(Long roleId);
    
    /**
     * تحديث صلاحيات الدور
     * Update role permissions
     */
    void updateRolePermissions(Long roleId, Map<String, Permission> permissions);
    
    /**
     * نسخ صلاحيات من دور إلى آخر
     * Copy permissions from one role to another
     */
    void copyPermissions(Long fromRoleId, Long toRoleId);
    
    /**
     * حذف جميع صلاحيات الدور
     * Delete all permissions for role
     */
    void deleteByRoleId(Long roleId);
    
    /**
     * الحصول على جميع الوحدات المتاحة
     * Get all available modules
     */
    List<String> getAvailableModules();
    
    /**
     * الحصول على صلاحيات الدور كخريطة
     * Get role permissions as map
     */
    Map<String, Permission> getRolePermissionsMap(Long roleId);
}
