import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.io.File;
import javax.swing.Icon;
import javax.swing.ImageIcon;
import javax.swing.JFrame;
import javax.swing.JScrollPane;
import javax.swing.JTree;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeCellRenderer;
import javax.swing.tree.DefaultTreeModel;

/**
 * عارض اختبار الأيقونات في شجرة الأنظمة
 * Icon Test Viewer for System Tree
 */
public class IconTestViewer extends JFrame {
    
    private JTree testTree;
    private Font arabicFont;
    private SystemTreeManager systemTreeManager;
    
    public IconTestViewer() {
        setTitle("🎨 عارض اختبار الأيقونات - Icon Test Viewer");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);
        
        // إعداد الخط العربي
        try {
            arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        } catch (Exception e) {
            arabicFont = new Font("SansSerif", Font.PLAIN, 12);
        }
        
        initializeComponents();
        setupUI();
        loadTreeData();
    }
    
    private void initializeComponents() {
        try {
            // إنشاء مدير شجرة النظام
            systemTreeManager = SystemTreeManager.getInstance();
            System.out.println("✅ تم إنشاء مدير شجرة النظام");
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء مدير شجرة النظام: " + e.getMessage());
        }
    }
    
    private void setupUI() {
        setLayout(new BorderLayout());
        
        // إنشاء الشجرة
        testTree = new JTree();
        testTree.setFont(arabicFont);
        testTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        testTree.setRowHeight(30); // ارتفاع أكبر لإظهار الأيقونات بوضوح
        testTree.setRootVisible(true);
        testTree.setShowsRootHandles(true);
        
        // إعداد عارض الخلايا المخصص
        testTree.setCellRenderer(new IconTreeCellRenderer());
        
        // إضافة الشجرة إلى scroll pane
        JScrollPane scrollPane = new JScrollPane(testTree);
        scrollPane.setPreferredSize(new Dimension(780, 580));
        
        add(scrollPane, BorderLayout.CENTER);
    }
    
    private void loadTreeData() {
        try {
            if (systemTreeManager != null) {
                DefaultTreeModel treeModel = systemTreeManager.getSystemTreeModel();
                testTree.setModel(treeModel);
                
                // توسيع جميع العقد لإظهار الأيقونات
                for (int i = 0; i < testTree.getRowCount(); i++) {
                    testTree.expandRow(i);
                }
                
                System.out.println("✅ تم تحميل بيانات الشجرة مع الأيقونات");
            } else {
                // إنشاء شجرة اختبار بسيطة
                createTestTree();
            }
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحميل بيانات الشجرة: " + e.getMessage());
            createTestTree();
        }
    }
    
    private void createTestTree() {
        DefaultMutableTreeNode root = new DefaultMutableTreeNode("🏢 نظام إدارة الشحنات");
        
        // إضافة عقد اختبار مع أيقونات
        DefaultMutableTreeNode itemsNode = new DefaultMutableTreeNode("📦 إدارة الأصناف");
        itemsNode.add(new DefaultMutableTreeNode("🪟 بيانات الأصناف"));
        itemsNode.add(new DefaultMutableTreeNode("🪟 مجموعات الأصناف"));
        
        DefaultMutableTreeNode usersNode = new DefaultMutableTreeNode("👥 إدارة المستخدمين");
        usersNode.add(new DefaultMutableTreeNode("🪟 إدارة المستخدمين"));
        
        DefaultMutableTreeNode settingsNode = new DefaultMutableTreeNode("⚙️ الإعدادات العامة");
        settingsNode.add(new DefaultMutableTreeNode("🪟 الإعدادات العامة"));
        
        root.add(itemsNode);
        root.add(usersNode);
        root.add(settingsNode);
        
        DefaultTreeModel model = new DefaultTreeModel(root);
        testTree.setModel(model);
        
        // توسيع جميع العقد
        for (int i = 0; i < testTree.getRowCount(); i++) {
            testTree.expandRow(i);
        }
        
        System.out.println("✅ تم إنشاء شجرة اختبار افتراضية");
    }
    
    /**
     * عارض خلايا الشجرة المخصص مع دعم الأيقونات
     */
    private class IconTreeCellRenderer extends DefaultTreeCellRenderer {
        
        public IconTreeCellRenderer() {
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        }
        
        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value, boolean selected,
                boolean expanded, boolean leaf, int row, boolean hasFocus) {
            
            super.getTreeCellRendererComponent(tree, value, selected, expanded, leaf, row, hasFocus);
            
            // إعداد الخط العربي
            setFont(arabicFont);
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            
            // محاولة تحميل الأيقونة من قاعدة البيانات
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
            Icon nodeIcon = getNodeIcon(node);
            
            if (nodeIcon != null) {
                setIcon(nodeIcon);
                System.out.println("✅ تم تحميل أيقونة للعقدة: " + node.toString());
            } else {
                // استخدام أيقونات افتراضية
                setDefaultIcon(node, leaf);
            }
            
            return this;
        }
        
        /**
         * الحصول على أيقونة العقدة من قاعدة البيانات
         */
        private Icon getNodeIcon(DefaultMutableTreeNode node) {
            try {
                Object userObject = node.getUserObject();
                if (userObject instanceof SystemTreeManager.SystemTreeNode) {
                    SystemTreeManager.SystemTreeNode treeNode = (SystemTreeManager.SystemTreeNode) userObject;
                    if (treeNode.iconPath != null && !treeNode.iconPath.trim().isEmpty()) {
                        return loadIconFromPath(treeNode.iconPath);
                    }
                }
            } catch (Exception e) {
                System.err.println("خطأ في تحميل أيقونة العقدة: " + e.getMessage());
            }
            return null;
        }
        
        /**
         * تحميل الأيقونة من المسار
         */
        private Icon loadIconFromPath(String iconPath) {
            try {
                File iconFile = new File(iconPath);
                if (iconFile.exists()) {
                    ImageIcon icon = new ImageIcon(iconPath);
                    if (icon.getIconWidth() > 0) {
                        // تغيير حجم الأيقونة
                        java.awt.Image scaledImage = icon.getImage().getScaledInstance(20, 20, java.awt.Image.SCALE_SMOOTH);
                        return new ImageIcon(scaledImage);
                    }
                }
                System.out.println("⚠️ لم يتم العثور على الأيقونة: " + iconPath);
            } catch (Exception e) {
                System.err.println("خطأ في تحميل الأيقونة من " + iconPath + ": " + e.getMessage());
            }
            return null;
        }
        
        /**
         * تعيين أيقونة افتراضية
         */
        private void setDefaultIcon(DefaultMutableTreeNode node, boolean leaf) {
            if (node.isRoot()) {
                // أيقونة الجذر
                setIcon(new ImageIcon("resources/icons/folder.png"));
            } else if (!leaf) {
                // أيقونة المجلد
                setIcon(new ImageIcon("resources/icons/folder.png"));
            } else {
                // أيقونة النافذة
                setIcon(new ImageIcon("resources/icons/window.png"));
            }
        }
        
        @Override
        protected void paintComponent(Graphics g) {
            if (g instanceof Graphics2D) {
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
                        RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING,
                        RenderingHints.VALUE_RENDER_QUALITY);
            }
            super.paintComponent(g);
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                FinalThemeManager.initializeDefaultTheme();
            } catch (Exception e) {
                System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
            }
            
            System.out.println("🎨 تشغيل عارض اختبار الأيقونات");
            System.out.println("Icon Test Viewer Starting...");
            System.out.println("==========================================");
            
            IconTestViewer viewer = new IconTestViewer();
            viewer.setVisible(true);
            
            System.out.println("✅ تم تشغيل عارض اختبار الأيقونات");
            System.out.println("📋 تحقق من ظهور الأيقونات في الشجرة");
        });
    }
}
