import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridLayout;
import java.awt.Toolkit;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.JToolBar;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة طلبات الشراء المتقدمة - نظام رئيسي فرعي (Master-Detail)
 * 
 * التصميم:
 * 1. شريط أدوات متقدم
 * 2. جدول رئيسي (طلبات الشراء)
 * 3. جدول فرعي (تفاصيل الطلب المختار)
 * 4. لوحة معلومات الطلب
 * 5. شريط حالة
 */
public class PurchaseRequestMasterDetailWindow extends JFrame {
    
    // الخطوط والألوان
    private final Font arabicFont = new Font("Tahoma", Font.PLAIN, 11);
    private final Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 11);
    private final Font titleFont = new Font("Tahoma", Font.BOLD, 14);
    
    // ألوان النظام
    private final Color headerColor = new Color(70, 130, 180);
    private final Color selectedColor = new Color(173, 216, 230);
    private final Color alternateColor = new Color(245, 245, 245);
    private final Color detailHeaderColor = new Color(60, 179, 113);
    
    // مكونات تفاصيل الطلب الجديدة مرتبطة بجدول ERP_P_REQUEST_DETAIL
    private JTable detailTable;
    private DefaultTableModel detailTableModel;

    // حقول الإدخال مرتبطة بجدول ERP_P_REQUEST
    private JComboBox<String> branchNoCombo;        // BRN_NO - رقم الفرع
    private JComboBox<String> approvedCombo;        // APPROVED - حالة الاعتماد
    private JComboBox<String> prTypeCombo;          // PR_TYPE - النوع
    private JTextField prNoField;                   // PR_NO - رقم الطلب
    private JTextField prDateField;                 // PR_DATE - تاريخ الطلب
    private JTextField sideReqField;                // SIDE_REQ - جهة الطلب
    private JComboBox<String> wCodeCombo;           // W_CODE - رقم المخزن
    private JTextField prDescField;                 // PR_DESC - البيان
    private JTextField refNoField;                  // REF_NO - رقم المرجع
    private JTextField vCodeField;                  // V_CODE - رقم المورد
    private JTextField vNameField;                  // V_NAME - اسم المورد
    private JComboBox<String> aCyCombo;             // A_CY - العملة
    private JTextField reqAvlDateField;             // REQ_AVL_DATE - تاريخ توفير الطلب
    
    // مكونات واجهة المستخدم
    private JLabel statusLabel;
    private JLabel totalRequestsLabel;
    private JLabel selectedRequestLabel;
    private JTextField searchField;
    private JComboBox<String> statusFilter;
    private JProgressBar progressBar;
    
    // بيانات الطلبات الحقيقية
    private List<ERPPurchaseRequest> masterDataList;
    private Connection dbConnection;
    
    public PurchaseRequestMasterDetailWindow() {
        initializeData();
        initializeWindow();
        createComponents();
        setupLayout();
        setupEventHandlers();
        loadInitialData();
        setVisible(true);
    }

    private void loadInitialData() {
        // تهيئة قائمة فارغة بدون اتصال أولي
        masterDataList = new ArrayList<>();
        clearForm();
        statusLabel.setText("جاهز - اضغط 'تحديث' لتحميل البيانات أو 'استيراد البيانات' للاستيراد من ias20251");

        System.out.println("تم تهيئة النافذة بنجاح - جاهز للاستخدام");
    }

    // تم حذف دالة createSampleData نهائياً

    private void loadRealDetailData(String prNo) {
        // تحميل تفاصيل الطلب الحقيقية من قاعدة البيانات
        DatabaseManager dbManager = new DatabaseManager();
        List<ERPPurchaseRequestDetail> details = dbManager.loadPurchaseRequestDetails(prNo);

        if (!details.isEmpty()) {
            loadDetailDataFromERP(details);
        } else {
            // في حالة عدم وجود تفاصيل، عرض جدول فارغ
            detailTableModel.setRowCount(0);
            statusLabel.setText("لا توجد تفاصيل للطلب: " + prNo);
        }
    }
    
    private void initializeWindow() {
        setTitle("نظام إدارة طلبات الشراء المتقدم - رئيسي/فرعي");
        setSize(1400, 800);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // أيقونة النافذة
        try {
            setIconImage(Toolkit.getDefaultToolkit().getImage("icons/purchase_system.png"));
        } catch (Exception e) {
            // تجاهل خطأ الأيقونة
        }
    }
    
    private void initializeData() {
        // سيتم استبدالها بـ loadInitialData()
    }
    
    private void createComponents() {
        // إنشاء شريط الأدوات
        createToolbar();
        
        // إنشاء حقول الإدخال الرئيسية
        createMasterInputFields();
        
        // إنشاء الجدول الفرعي
        createDetailTable();
        
        // إنشاء لوحة المعلومات
        createInfoPanel();
        
        // إنشاء شريط الحالة
        createStatusBar();
    }
    
    private void createToolbar() {
        JToolBar toolbar = new JToolBar();
        toolbar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        toolbar.setFloatable(false);
        toolbar.setBackground(headerColor);

        // تحسين شريط الأدوات بنفس تأثيرات رؤوس الأعمدة
        toolbar.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createRaisedSoftBevelBorder(),
            BorderFactory.createEmptyBorder(8, 10, 8, 10)
        ));
        toolbar.setPreferredSize(new Dimension(0, 55)); // ارتفاع أكبر مثل رؤوس الأعمدة
        
        // أزرار العمليات الرئيسية
        String[] buttonNames = {"جديد", "تعديل", "حذف", "حفظ", "إلغاء", "|",
                               "طباعة", "معاينة", "تصدير Excel", "|",
                               "بحث", "فلتر", "تحديث", "|",
                               "اعتماد", "رفض", "تعليق", "|",
                               "استيراد البيانات", "|",
                               "إعدادات", "مساعدة", "خروج"};
        
        for (String name : buttonNames) {
            if (name.equals("|")) {
                toolbar.addSeparator();
                continue;
            }
            
            JButton button = createEnhancedToolbarButton(name);
            button.setFont(new Font("Tahoma", Font.BOLD, 10)); // نفس خط رؤوس الأعمدة
            button.setPreferredSize(new Dimension(80, 40)); // حجم أكبر قليلاً
            
            // إضافة أيقونات (اختيارية)
            button.addActionListener(e -> handleToolbarAction(name));
            toolbar.add(button);
        }
        
        // إضافة مربع البحث محسن
        toolbar.addSeparator();
        JLabel searchLabel = new JLabel("بحث:");
        searchLabel.setFont(new Font("Tahoma", Font.BOLD, 10));
        searchLabel.setForeground(Color.WHITE);
        toolbar.add(searchLabel);

        searchField = new JTextField(15);
        searchField.setFont(new Font("Tahoma", Font.PLAIN, 10));
        searchField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLoweredSoftBevelBorder(),
            BorderFactory.createEmptyBorder(3, 5, 3, 5)
        ));
        searchField.setPreferredSize(new Dimension(150, 30));
        toolbar.add(searchField);

        // إضافة فلتر الحالة محسن
        toolbar.addSeparator();
        JLabel statusLabel = new JLabel("الحالة:");
        statusLabel.setFont(new Font("Tahoma", Font.BOLD, 10));
        statusLabel.setForeground(Color.WHITE);
        toolbar.add(statusLabel);

        statusFilter = new JComboBox<>(new String[]{"الكل", "معتمد", "قيد المراجعة", "مرفوض"});
        statusFilter.setFont(new Font("Tahoma", Font.PLAIN, 10));
        statusFilter.setBorder(BorderFactory.createLoweredSoftBevelBorder());
        statusFilter.setPreferredSize(new Dimension(120, 30));
        toolbar.add(statusFilter);
        
        add(toolbar, BorderLayout.NORTH);
    }

    // دالة إنشاء أزرار محسنة لشريط الأدوات
    private JButton createEnhancedToolbarButton(String name) {
        JButton button = new JButton(name);

        // تطبيق نفس تأثيرات رؤوس الأعمدة
        button.setForeground(Color.WHITE);
        button.setBackground(headerColor);
        button.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createRaisedSoftBevelBorder(),
            BorderFactory.createEmptyBorder(5, 8, 5, 8)
        ));
        button.setFocusPainted(false);
        button.setOpaque(true);

        // تأثيرات التفاعل
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button.setBackground(headerColor.brighter()); // لون أفتح عند التمرير
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button.setBackground(headerColor); // العودة للون الأصلي
            }

            @Override
            public void mousePressed(java.awt.event.MouseEvent e) {
                button.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLoweredSoftBevelBorder(), // حدود منخفضة عند الضغط
                    BorderFactory.createEmptyBorder(5, 8, 5, 8)
                ));
            }

            @Override
            public void mouseReleased(java.awt.event.MouseEvent e) {
                button.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createRaisedSoftBevelBorder(), // العودة للحدود المرفوعة
                    BorderFactory.createEmptyBorder(5, 8, 5, 8)
                ));
            }
        });

        return button;
    }

    private void createMasterInputFields() {
        // لا حاجة لإنشاء مكونات هنا، سيتم إنشاؤها في setupLayout
    }

    private void createDetailTable() {
        // سيتم إعادة إنشاء قسم تفاصيل الطلب حسب التصميم الجديد
    }

    private JPanel createNewDetailPanel() {
        // إنشاء الجدول حسب التصميم المرفق مع تسميات واضحة ومختصرة
        String[] detailColumns = {
            "<html><center><b>م</b></center></html>",
            "<html><center><b>رقم<br>الصنف</b></center></html>",
            "<html><center><b>اسم الصنف</b></center></html>",
            "<html><center><b>الوحدة</b></center></html>",
            "<html><center><b>العبوة</b></center></html>",
            "<html><center><b>تاريخ<br>الانتهاء</b></center></html>",
            "<html><center><b>الكمية<br>المطلوبة</b></center></html>",
            "<html><center><b>تاريخ التسليم<br>المتوقع</b></center></html>",
            "<html><center><b>سعر<br>المورد</b></center></html>",
            "<html><center><b>الكمية<br>المتوفرة</b></center></html>",
            "<html><center><b>الكمية في<br>الطريق</b></center></html>",
            "<html><center><b>كمية الطلب<br>المنفذة</b></center></html>",
            "<html><center><b>كمية الطلب<br>غير المنفذة</b></center></html>",
            "<html><center><b>الكمية السابقة<br>المنفذة</b></center></html>",
            "<html><center><b>الكمية السابقة<br>غير المنفذة</b></center></html>"
        };

        detailTableModel = new DefaultTableModel(detailColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        detailTable = new JTable(detailTableModel);
        detailTable.setFont(arabicFont);
        detailTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تحسين رؤوس الأعمدة
        detailTable.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 10)); // خط أصغر لتناسب النصوص الطويلة
        detailTable.getTableHeader().setBackground(new Color(41, 98, 255)); // أزرق أكثر تمييزاً
        detailTable.getTableHeader().setForeground(Color.WHITE);
        detailTable.getTableHeader().setPreferredSize(new Dimension(0, 50)); // ارتفاع أكبر لإظهار النصوص كاملة
        detailTable.getTableHeader().setBorder(BorderFactory.createRaisedSoftBevelBorder()); // حدود مرفوعة
        detailTable.getTableHeader().setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        detailTable.setRowHeight(28); // ارتفاع أكبر للصفوف
        detailTable.setGridColor(new Color(180, 180, 180)); // لون رمادي أفتح للشبكة
        detailTable.setShowGrid(true);
        detailTable.setIntercellSpacing(new Dimension(1, 1)); // مسافة بين الخلايا

        // تطبيق ألوان متناوبة كما في الصورة
        detailTable.setDefaultRenderer(Object.class, new DetailTableCellRenderer());

        // تحسين رؤوس الأعمدة بمُحسِّن خاص
        detailTable.getTableHeader().setDefaultRenderer(new DetailHeaderRenderer());

        // تعيين عرض الأعمدة
        int[] detailColumnWidths = {40, 80, 150, 60, 60, 100, 80, 120, 80, 80, 80, 80, 80, 80, 80};
        for (int i = 0; i < detailColumnWidths.length && i < detailTable.getColumnCount(); i++) {
            detailTable.getColumnModel().getColumn(i).setPreferredWidth(detailColumnWidths[i]);
        }

        // سيتم تحميل البيانات الحقيقية من قاعدة البيانات

        JScrollPane scrollPane = new JScrollPane(detailTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel panel = new JPanel(new BorderLayout());
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    private void createInfoPanel() {
        JPanel infoPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        infoPanel.setBackground(alternateColor);
        infoPanel.setBorder(BorderFactory.createTitledBorder("معلومات سريعة"));

        totalRequestsLabel = new JLabel("إجمالي الطلبات: 0");
        totalRequestsLabel.setFont(arabicBoldFont);

        selectedRequestLabel = new JLabel("الطلب المختار: غير محدد");
        selectedRequestLabel.setFont(arabicBoldFont);

        infoPanel.add(totalRequestsLabel);
        infoPanel.add(Box.createHorizontalStrut(20));
        infoPanel.add(selectedRequestLabel);

        add(infoPanel, BorderLayout.SOUTH);
    }

    private void createStatusBar() {
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createEtchedBorder());
        statusPanel.setBackground(Color.LIGHT_GRAY);

        statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);
        statusLabel.setBorder(BorderFactory.createEmptyBorder(2, 5, 2, 5));

        progressBar = new JProgressBar();
        progressBar.setVisible(false);
        progressBar.setPreferredSize(new Dimension(150, 20));

        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.add(progressBar, BorderLayout.EAST);

        add(statusPanel, BorderLayout.PAGE_END);
    }

    private void setupLayout() {
        // إنشاء لوحة مقسمة أفقياً
        JSplitPane mainSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        mainSplitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainSplitPane.setDividerLocation(200);
        mainSplitPane.setResizeWeight(0.25);

        // حقول الإدخال في الأعلى
        JPanel inputPanel = createInputPanel();

        // قسم تفاصيل الطلب الجديد
        JPanel detailPanel = createNewDetailPanel();
        detailPanel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "تفاصيل الطلب",
            TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));
        detailPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        mainSplitPane.setTopComponent(inputPanel);
        mainSplitPane.setBottomComponent(detailPanel);

        add(mainSplitPane, BorderLayout.CENTER);
    }

    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "بيانات طلب الشراء",
            TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(alternateColor);

        // إنشاء لوحة رئيسية بعمودين
        JPanel mainPanel = new JPanel(new GridLayout(1, 2, 10, 0));
        mainPanel.setBackground(alternateColor);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // العمود الأيمن
        JPanel rightColumn = createRightColumn();

        // العمود الأيسر
        JPanel leftColumn = createLeftColumn();

        mainPanel.add(rightColumn);
        mainPanel.add(leftColumn);

        panel.add(mainPanel, BorderLayout.CENTER);
        return panel;
    }

    private JPanel createRightColumn() {
        JPanel column = new JPanel();
        column.setLayout(new BoxLayout(column, BoxLayout.Y_AXIS));
        column.setBackground(alternateColor);

        // رقم الفرع
        JPanel row1 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row1.setBackground(alternateColor);
        branchNoCombo = new JComboBox<>(new String[]{"", "001", "002", "003", "004"});
        branchNoCombo.setFont(arabicFont);
        branchNoCombo.setPreferredSize(new Dimension(120, 22));
        row1.add(branchNoCombo);
        row1.add(new JLabel("رقم الفرع:"));
        column.add(row1);

        // تاريخ الطلب
        JPanel row2 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row2.setBackground(alternateColor);
        prDateField = new JTextField();
        prDateField.setFont(arabicFont);
        prDateField.setPreferredSize(new Dimension(120, 22));
        row2.add(prDateField);
        row2.add(new JLabel("تاريخ الطلب:"));
        column.add(row2);

        // رقم المخزن
        JPanel row3 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row3.setBackground(alternateColor);
        wCodeCombo = new JComboBox<>(new String[]{"", "W001", "W002", "W003"});
        wCodeCombo.setFont(arabicFont);
        wCodeCombo.setPreferredSize(new Dimension(120, 22));
        row3.add(wCodeCombo);
        row3.add(new JLabel("رقم المخزن:"));
        column.add(row3);

        // رقم المرجع
        JPanel row4 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row4.setBackground(alternateColor);
        refNoField = new JTextField();
        refNoField.setFont(arabicFont);
        refNoField.setPreferredSize(new Dimension(120, 22));
        row4.add(refNoField);
        row4.add(new JLabel("رقم المرجع:"));
        column.add(row4);

        // العملة
        JPanel row5 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row5.setBackground(alternateColor);
        aCyCombo = new JComboBox<>(new String[]{"", "ريال", "دولار", "يورو"});
        aCyCombo.setFont(arabicFont);
        aCyCombo.setPreferredSize(new Dimension(120, 22));
        row5.add(aCyCombo);
        row5.add(new JLabel("العملة:"));
        column.add(row5);

        // تاريخ توفير الطلب
        JPanel row6 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row6.setBackground(alternateColor);
        reqAvlDateField = new JTextField();
        reqAvlDateField.setFont(arabicFont);
        reqAvlDateField.setPreferredSize(new Dimension(120, 22));
        row6.add(reqAvlDateField);
        row6.add(new JLabel("تاريخ توفير الطلب:"));
        column.add(row6);

        return column;
    }

    private JPanel createLeftColumn() {
        JPanel column = new JPanel();
        column.setLayout(new BoxLayout(column, BoxLayout.Y_AXIS));
        column.setBackground(alternateColor);

        // حالة الاعتماد
        JPanel row1 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row1.setBackground(alternateColor);
        approvedCombo = new JComboBox<>(new String[]{"", "معتمد", "غير معتمد", "قيد المراجعة"});
        approvedCombo.setFont(arabicFont);
        approvedCombo.setPreferredSize(new Dimension(120, 22));
        row1.add(approvedCombo);
        row1.add(new JLabel("حالة الاعتماد:"));
        column.add(row1);

        // النوع + رقم الطلب
        JPanel row2 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row2.setBackground(alternateColor);
        prNoField = new JTextField();
        prNoField.setFont(arabicFont);
        prNoField.setPreferredSize(new Dimension(100, 22));
        row2.add(prNoField);
        row2.add(new JLabel("رقم الطلب:"));
        prTypeCombo = new JComboBox<>(new String[]{"", "عادي", "عاجل", "طارئ"});
        prTypeCombo.setFont(arabicFont);
        prTypeCombo.setPreferredSize(new Dimension(80, 22));
        row2.add(prTypeCombo);
        row2.add(new JLabel("النوع:"));
        column.add(row2);

        // جهة الطلب
        JPanel row3 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row3.setBackground(alternateColor);
        sideReqField = new JTextField();
        sideReqField.setFont(arabicFont);
        sideReqField.setPreferredSize(new Dimension(200, 22));
        row3.add(sideReqField);
        row3.add(new JLabel("جهة الطلب:"));
        column.add(row3);

        // البيان
        JPanel row4 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row4.setBackground(alternateColor);
        prDescField = new JTextField();
        prDescField.setFont(arabicFont);
        prDescField.setPreferredSize(new Dimension(200, 22));
        row4.add(prDescField);
        row4.add(new JLabel("البيان:"));
        column.add(row4);

        // رقم المورد + اسم المورد
        JPanel row5 = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 2));
        row5.setBackground(alternateColor);
        vNameField = new JTextField();
        vNameField.setFont(arabicFont);
        vNameField.setPreferredSize(new Dimension(120, 22));
        row5.add(vNameField);
        row5.add(new JLabel("اسم المورد:"));
        vCodeField = new JTextField();
        vCodeField.setFont(arabicFont);
        vCodeField.setPreferredSize(new Dimension(80, 22));
        row5.add(vCodeField);
        row5.add(new JLabel("رقم المورد:"));
        column.add(row5);

        return column;
    }

    private void setupEventHandlers() {
        // معالج البحث
        searchField.addActionListener(e -> performSearch());

        // معالج فلتر الحالة
        statusFilter.addActionListener(e -> applyStatusFilter());

        // معالج تغيير الحالة
        approvedCombo.addActionListener(e -> updateStatusDisplay());
    }

    private void loadSelectedRequest() {
        // تحميل بيانات طلب محدد
        statusLabel.setText("تم تحميل الطلب: " + prNoField.getText());
        loadDetailData(0); // تحميل التفاصيل الأولى كمثال
        updateCalculatedFields();
    }

    private void saveCurrentRequest() {
        // حفظ التغييرات
        statusLabel.setText("تم حفظ الطلب: " + prNoField.getText());
        showMessage("تم حفظ التغييرات بنجاح");
    }

    private void updateStatusDisplay() {
        String status = (String) approvedCombo.getSelectedItem();
        statusLabel.setText("تم تغيير الحالة إلى: " + status);
    }

    private void updateCalculatedFields() {
        // تحديث الحقول المحسوبة - مبسط
        statusLabel.setText("تم تحديث الحقول المحسوبة");
    }

    // تم نقل هذه الدالة إلى أعلى الملف

    private void loadDetailData(int masterRowIndex) {
        // سيتم إعادة تطبيق تحميل البيانات للتصميم الجديد
        statusLabel.setText("تم تحميل تفاصيل الطلب المختار");
    }

    private void updateSelectedRequestInfo(int selectedRow) {
        if (selectedRow >= 0 && selectedRow < masterDataList.size()) {
            ERPPurchaseRequest request = masterDataList.get(selectedRow);
            selectedRequestLabel.setText("الطلب المختار: " + request.getPrNo() + " - " + request.getSideReq());
        }
    }

    private void performSearch() {
        String searchText = searchField.getText().trim();
        if (searchText.isEmpty()) {
            statusLabel.setText("يرجى إدخال نص للبحث");
            return;
        }

        // البحث في البيانات وتحديث الحقول
        boolean found = false;
        for (int i = 0; i < masterDataList.size(); i++) {
            ERPPurchaseRequest request = masterDataList.get(i);
            if (request.getPrNo().contains(searchText) ||
                request.getSideReq().contains(searchText) ||
                request.getPrDesc().contains(searchText)) {

                // تحديث حقول الإدخال بالبيانات المطابقة
                loadDataFromERP(request);
                loadRealDetailData(request.getPrNo());
                found = true;
                break;
            }
        }

        if (found) {
            statusLabel.setText("تم العثور على: " + searchText);
        } else {
            statusLabel.setText("لم يتم العثور على: " + searchText);
        }
    }

    private void applyStatusFilter() {
        String selectedStatus = (String) statusFilter.getSelectedItem();
        if ("الكل".equals(selectedStatus)) {
            loadInitialData();
            return;
        }

        // البحث عن أول طلب بالحالة المطلوبة وعرضه
        boolean found = false;
        for (ERPPurchaseRequest request : masterDataList) {
            if (selectedStatus.equals(request.getApproved())) {
                // تحديث حقول الإدخال
                loadDataFromERP(request);
                loadRealDetailData(request.getPrNo());
                found = true;
                break;
            }
        }

        if (found) {
            statusLabel.setText("فلتر الحالة: " + selectedStatus);
        } else {
            statusLabel.setText("لا توجد طلبات بحالة: " + selectedStatus);
        }
    }

    private void handleToolbarAction(String action) {
        statusLabel.setText("تم النقر على: " + action);

        switch (action) {
            case "جديد":
                showMessage("إنشاء طلب شراء جديد");
                break;
            case "تعديل":
                showMessage("تعديل الطلب المختار");
                break;
            case "حذف":
                showMessage("حذف الطلب المختار");
                break;
            case "حفظ":
                saveCurrentRequestToDatabase();
                break;
            case "اعتماد":
                showMessage("اعتماد الطلب المختار");
                break;
            case "رفض":
                showMessage("رفض الطلب المختار");
                break;
            case "طباعة":
                showMessage("طباعة التقرير");
                break;
            case "تصدير Excel":
                showMessage("تصدير إلى Excel");
                break;

            case "تحديث":
                refreshDataFromDatabase();
                break;
            case "استيراد البيانات":
                importDataFromIAS20251();
                break;
            case "خروج":
                System.exit(0);
                break;
            default:
                showMessage("الوظيفة قيد التطوير: " + action);
        }
    }

    private void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "رسالة النظام", JOptionPane.INFORMATION_MESSAGE);
    }



    // تم حذف دالة loadSampleDetailData - لا نحتاج بيانات تجريبية

    // فئة مُحسِّن رؤوس الأعمدة
    private class DetailHeaderRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value,
                boolean isSelected, boolean hasFocus, int row, int column) {

            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

            // تطبيق التصميم المميز للرؤوس
            setBackground(new Color(41, 98, 255)); // أزرق مميز
            setForeground(Color.WHITE);
            setFont(new Font("Tahoma", Font.BOLD, 10)); // خط أصغر لتناسب النصوص الطويلة
            setHorizontalAlignment(SwingConstants.CENTER);
            setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createRaisedSoftBevelBorder(),
                BorderFactory.createEmptyBorder(8, 4, 8, 4) // حشو أكبر عمودياً وأصغر أفقياً
            ));

            // إضافة تأثير تدرج للخلفية
            setOpaque(true);

            return c;
        }

        @Override
        protected void paintComponent(java.awt.Graphics g) {
            // رسم خلفية متدرجة
            java.awt.Graphics2D g2d = (java.awt.Graphics2D) g.create();
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_RENDERING, java.awt.RenderingHints.VALUE_RENDER_QUALITY);

            java.awt.GradientPaint gradient = new java.awt.GradientPaint(
                0, 0, new Color(41, 98, 255),
                0, getHeight(), new Color(25, 60, 200)
            );
            g2d.setPaint(gradient);
            g2d.fillRect(0, 0, getWidth(), getHeight());
            g2d.dispose();

            super.paintComponent(g);
        }
    }

    // فئة تلوين الجدول حسب التصميم المرفق
    private class DetailTableCellRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value,
                boolean isSelected, boolean hasFocus, int row, int column) {

            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

            if (!isSelected) {
                // ألوان متناوبة كما في الصورة
                if (row % 2 == 0) {
                    c.setBackground(Color.WHITE);
                } else {
                    c.setBackground(new Color(230, 240, 255)); // أزرق فاتح
                }

                // تلوين خاص لبعض الخلايا (أصفر فاتح كما في الصورة)
                if (column >= 9 && column <= 11) { // أعمدة الكميات
                    c.setBackground(new Color(255, 255, 200)); // أصفر فاتح
                }
            }

            setHorizontalAlignment(SwingConstants.CENTER);
            setFont(arabicFont);
            return c;
        }
    }

    // فئة لربط البيانات مع جدول ERP_P_REQUEST_DETAIL
    public class ERPPurchaseRequestDetail {
        private int rcrdNo;             // RCRD_NO - تسلسل الي
        private String iCode;           // I_CODE - رقم الصنف
        private String itemName;        // اسم الصنف (مشتق من I_CODE)
        private String itmUnt;          // ITM_UNT - الوحدة
        private String pSize;           // P_SIZE - العبوة
        private String expireDate;      // EXPIRE_DATE - تاريخ الانتهاء
        private double iQty;            // I_QTY - الكمية المطلوبة
        private String dlvryDate;       // DLVRY_DATE - تاريخ التسليم المتوقع
        private double iPrice;          // I_PRICE - سعر المورد
        private double avlQty;          // AVL_QTY - الكمية المتوفرة
        private double poQty;           // PO_QTY - الكمية في الطريق
        private double arivQty;         // ARIV_QTY - كمية الطلب المنفذة
        private double notPoPrvQty;     // NOT_PO_PRV_QTY - كمية الطلب غير المنفذة
        private double poPrvQty;        // PO_PRV_QTY - الكمية السابقة المنفذة
        private double notPoPrvQtyPrev; // NOT_PO_PRV_QTY - الكمية السابقة غير المنفذة

        // Constructor
        public ERPPurchaseRequestDetail() {}

        // Getters and Setters
        public int getRcrdNo() { return rcrdNo; }
        public void setRcrdNo(int rcrdNo) { this.rcrdNo = rcrdNo; }

        public String getICode() { return iCode; }
        public void setICode(String iCode) { this.iCode = iCode; }

        public String getItemName() { return itemName; }
        public void setItemName(String itemName) { this.itemName = itemName; }

        public String getItmUnt() { return itmUnt; }
        public void setItmUnt(String itmUnt) { this.itmUnt = itmUnt; }

        public String getPSize() { return pSize; }
        public void setPSize(String pSize) { this.pSize = pSize; }

        public String getExpireDate() { return expireDate; }
        public void setExpireDate(String expireDate) { this.expireDate = expireDate; }

        public double getIQty() { return iQty; }
        public void setIQty(double iQty) { this.iQty = iQty; }

        public String getDlvryDate() { return dlvryDate; }
        public void setDlvryDate(String dlvryDate) { this.dlvryDate = dlvryDate; }

        public double getIPrice() { return iPrice; }
        public void setIPrice(double iPrice) { this.iPrice = iPrice; }

        public double getAvlQty() { return avlQty; }
        public void setAvlQty(double avlQty) { this.avlQty = avlQty; }

        public double getPoQty() { return poQty; }
        public void setPoQty(double poQty) { this.poQty = poQty; }

        public double getArivQty() { return arivQty; }
        public void setArivQty(double arivQty) { this.arivQty = arivQty; }

        public double getNotPoPrvQty() { return notPoPrvQty; }
        public void setNotPoPrvQty(double notPoPrvQty) { this.notPoPrvQty = notPoPrvQty; }

        public double getPoPrvQty() { return poPrvQty; }
        public void setPoPrvQty(double poPrvQty) { this.poPrvQty = poPrvQty; }

        public double getNotPoPrvQtyPrev() { return notPoPrvQtyPrev; }
        public void setNotPoPrvQtyPrev(double notPoPrvQtyPrev) { this.notPoPrvQtyPrev = notPoPrvQtyPrev; }
    }

    // فئة لربط البيانات مع جدول ERP_P_REQUEST
    public class ERPPurchaseRequest {
        private String brnNo;           // BRN_NO - رقم الفرع
        private String approved;        // APPROVED - حالة الاعتماد
        private String prType;          // PR_TYPE - النوع
        private String prNo;            // PR_NO - رقم الطلب
        private String prDate;          // PR_DATE - تاريخ الطلب
        private String sideReq;         // SIDE_REQ - جهة الطلب
        private String wCode;           // W_CODE - رقم المخزن
        private String prDesc;          // PR_DESC - البيان
        private String refNo;           // REF_NO - رقم المرجع
        private String vCode;           // V_CODE - رقم المورد
        private String vName;           // V_NAME - اسم المورد
        private String aCy;             // A_CY - العملة
        private String reqAvlDate;      // REQ_AVL_DATE - تاريخ توفير الطلب

        // Constructor
        public ERPPurchaseRequest() {}

        // Getters and Setters
        public String getBrnNo() { return brnNo; }
        public void setBrnNo(String brnNo) { this.brnNo = brnNo; }

        public String getApproved() { return approved; }
        public void setApproved(String approved) { this.approved = approved; }

        public String getPrType() { return prType; }
        public void setPrType(String prType) { this.prType = prType; }

        public String getPrNo() { return prNo; }
        public void setPrNo(String prNo) { this.prNo = prNo; }

        public String getPrDate() { return prDate; }
        public void setPrDate(String prDate) { this.prDate = prDate; }

        public String getSideReq() { return sideReq; }
        public void setSideReq(String sideReq) { this.sideReq = sideReq; }

        public String getWCode() { return wCode; }
        public void setWCode(String wCode) { this.wCode = wCode; }

        public String getPrDesc() { return prDesc; }
        public void setPrDesc(String prDesc) { this.prDesc = prDesc; }

        public String getRefNo() { return refNo; }
        public void setRefNo(String refNo) { this.refNo = refNo; }

        public String getVCode() { return vCode; }
        public void setVCode(String vCode) { this.vCode = vCode; }

        public String getVName() { return vName; }
        public void setVName(String vName) { this.vName = vName; }

        public String getACy() { return aCy; }
        public void setACy(String aCy) { this.aCy = aCy; }

        public String getReqAvlDate() { return reqAvlDate; }
        public void setReqAvlDate(String reqAvlDate) { this.reqAvlDate = reqAvlDate; }
    }

    // دوال ربط البيانات بين النموذج وجدول ERP_P_REQUEST
    public void loadDataFromERP(ERPPurchaseRequest erpData) {
        if (erpData != null) {
            // ربط البيانات من الجدول إلى النموذج
            if (erpData.getBrnNo() != null) branchNoCombo.setSelectedItem(erpData.getBrnNo());
            if (erpData.getApproved() != null) approvedCombo.setSelectedItem(erpData.getApproved());
            if (erpData.getPrType() != null) prTypeCombo.setSelectedItem(erpData.getPrType());
            if (erpData.getPrNo() != null) prNoField.setText(erpData.getPrNo());
            if (erpData.getPrDate() != null) prDateField.setText(erpData.getPrDate());
            if (erpData.getSideReq() != null) sideReqField.setText(erpData.getSideReq());
            if (erpData.getWCode() != null) wCodeCombo.setSelectedItem(erpData.getWCode());
            if (erpData.getPrDesc() != null) prDescField.setText(erpData.getPrDesc());
            if (erpData.getRefNo() != null) refNoField.setText(erpData.getRefNo());
            if (erpData.getVCode() != null) vCodeField.setText(erpData.getVCode());
            if (erpData.getVName() != null) vNameField.setText(erpData.getVName());
            if (erpData.getACy() != null) aCyCombo.setSelectedItem(erpData.getACy());
            if (erpData.getReqAvlDate() != null) reqAvlDateField.setText(erpData.getReqAvlDate());

            statusLabel.setText("تم تحميل البيانات من جدول ERP_P_REQUEST");
        }
    }

    public ERPPurchaseRequest saveDataToERP() {
        // ربط البيانات من النموذج إلى الجدول
        ERPPurchaseRequest erpData = new ERPPurchaseRequest();

        erpData.setBrnNo((String) branchNoCombo.getSelectedItem());
        erpData.setApproved((String) approvedCombo.getSelectedItem());
        erpData.setPrType((String) prTypeCombo.getSelectedItem());
        erpData.setPrNo(prNoField.getText());
        erpData.setPrDate(prDateField.getText());
        erpData.setSideReq(sideReqField.getText());
        erpData.setWCode((String) wCodeCombo.getSelectedItem());
        erpData.setPrDesc(prDescField.getText());
        erpData.setRefNo(refNoField.getText());
        erpData.setVCode(vCodeField.getText());
        erpData.setVName(vNameField.getText());
        erpData.setACy((String) aCyCombo.getSelectedItem());
        erpData.setReqAvlDate(reqAvlDateField.getText());

        statusLabel.setText("تم حفظ البيانات إلى جدول ERP_P_REQUEST");
        return erpData;
    }

    // تم حذف دالة testERPConnection

    // دوال ربط البيانات مع جدول ERP_P_REQUEST_DETAIL
    public void loadDetailDataFromERP(java.util.List<ERPPurchaseRequestDetail> erpDetailList) {
        if (erpDetailList != null && !erpDetailList.isEmpty()) {
            // مسح البيانات السابقة
            detailTableModel.setRowCount(0);

            // تحميل البيانات من قائمة ERP
            for (ERPPurchaseRequestDetail detail : erpDetailList) {
                Object[] row = {
                    detail.getRcrdNo(),
                    detail.getICode(),
                    detail.getItemName(),
                    detail.getItmUnt(),
                    detail.getPSize(),
                    detail.getExpireDate(),
                    detail.getIQty(),
                    detail.getDlvryDate(),
                    detail.getIPrice(),
                    detail.getAvlQty(),
                    detail.getPoQty(),
                    detail.getArivQty(),
                    detail.getNotPoPrvQty(),
                    detail.getPoPrvQty(),
                    detail.getNotPoPrvQtyPrev()
                };
                detailTableModel.addRow(row);
            }

            statusLabel.setText("تم تحميل " + erpDetailList.size() + " صنف من جدول ERP_P_REQUEST_DETAIL");
        }
    }

    public java.util.List<ERPPurchaseRequestDetail> saveDetailDataToERP() {
        java.util.List<ERPPurchaseRequestDetail> erpDetailList = new java.util.ArrayList<>();

        // تحويل بيانات الجدول إلى قائمة ERP
        for (int i = 0; i < detailTableModel.getRowCount(); i++) {
            ERPPurchaseRequestDetail detail = new ERPPurchaseRequestDetail();

            detail.setRcrdNo((Integer) detailTableModel.getValueAt(i, 0));
            detail.setICode((String) detailTableModel.getValueAt(i, 1));
            detail.setItemName((String) detailTableModel.getValueAt(i, 2));
            detail.setItmUnt((String) detailTableModel.getValueAt(i, 3));
            detail.setPSize((String) detailTableModel.getValueAt(i, 4));
            detail.setExpireDate((String) detailTableModel.getValueAt(i, 5));
            detail.setIQty((Double) detailTableModel.getValueAt(i, 6));
            detail.setDlvryDate((String) detailTableModel.getValueAt(i, 7));
            detail.setIPrice((Double) detailTableModel.getValueAt(i, 8));
            detail.setAvlQty((Double) detailTableModel.getValueAt(i, 9));
            detail.setPoQty((Double) detailTableModel.getValueAt(i, 10));
            detail.setArivQty((Double) detailTableModel.getValueAt(i, 11));
            detail.setNotPoPrvQty((Double) detailTableModel.getValueAt(i, 12));
            detail.setPoPrvQty((Double) detailTableModel.getValueAt(i, 13));
            detail.setNotPoPrvQtyPrev((Double) detailTableModel.getValueAt(i, 14));

            erpDetailList.add(detail);
        }

        statusLabel.setText("تم حفظ " + erpDetailList.size() + " صنف إلى جدول ERP_P_REQUEST_DETAIL");
        return erpDetailList;
    }

    // دالة لاختبار ربط تفاصيل الطلب
    public void testDetailERPConnection() {
        java.util.List<ERPPurchaseRequestDetail> testDetailList = new java.util.ArrayList<>();

        // إنشاء بيانات تجريبية
        for (int i = 1; i <= 3; i++) {
            ERPPurchaseRequestDetail detail = new ERPPurchaseRequestDetail();
            detail.setRcrdNo(i);
            detail.setICode("TEST" + String.format("%03d", i));
            detail.setItemName("صنف تجريبي " + i);
            detail.setItmUnt("قطعة");
            detail.setPSize("1 قطعة");
            detail.setExpireDate("2025-12-31");
            detail.setIQty(10.0 * i);
            detail.setDlvryDate("2025-02-15");
            detail.setIPrice(5.0 * i);
            detail.setAvlQty(5.0 * i);
            detail.setPoQty(2.0 * i);
            detail.setArivQty(3.0 * i);
            detail.setNotPoPrvQty(7.0 * i);
            detail.setPoPrvQty(2.0 * i);
            detail.setNotPoPrvQtyPrev(1.0 * i);

            testDetailList.add(detail);
        }

        // تحميل البيانات التجريبية
        loadDetailDataFromERP(testDetailList);

        showMessage("تم اختبار الربط مع جدول ERP_P_REQUEST_DETAIL بنجاح!\n" +
                   "تم تحميل " + testDetailList.size() + " أصناف تجريبية");
    }

    // دالة تحديث البيانات من قاعدة البيانات
    public void refreshDataFromDatabase() {
        DatabaseManager dbManager = new DatabaseManager();

        try {
            // تحديث بيانات الطلبات الرئيسية
            List<ERPPurchaseRequest> newData = dbManager.loadPurchaseRequests();

            if (!newData.isEmpty()) {
                masterDataList = newData;
                statusLabel.setText("تم تحديث " + masterDataList.size() + " طلب من قاعدة البيانات");

                // تحديث النموذج بأول طلب
                loadDataFromERP(masterDataList.get(0));
                loadRealDetailData(masterDataList.get(0).getPrNo());

                showMessage("تم تحديث البيانات بنجاح من قاعدة البيانات");
            } else {
                statusLabel.setText("لا توجد بيانات في قاعدة البيانات");
                showMessage("لا توجد بيانات في قاعدة البيانات");
            }

        } catch (Exception e) {
            statusLabel.setText("فشل في تحديث البيانات من قاعدة البيانات");
            showMessage("خطأ في تحديث البيانات: " + e.getMessage());
        }
    }

    // دالة حفظ البيانات الحالية في النموذج إلى قاعدة البيانات
    public void saveCurrentRequestToDatabase() {
        DatabaseManager dbManager = new DatabaseManager();

        try (Connection conn = dbManager.getConnection()) {
            // بدء معاملة قاعدة البيانات
            conn.setAutoCommit(false);

            try {
                // جمع البيانات من النموذج
                ERPPurchaseRequest request = saveDataToERP();

                System.out.println("=== بدء حفظ الطلب ===");
                System.out.println("رقم الطلب: " + request.getPrNo());

                // التحقق من وجود الطلب في قاعدة البيانات
                if (isRequestExists(request.getPrNo())) {
                    System.out.println("الطلب موجود - سيتم التحديث");

                    // حذف التفاصيل القديمة أولاً
                    deleteRequestDetails(conn, request.getPrNo());

                    // تحديث الطلب الرئيسي
                    if (updateRequestInDatabase(conn, request)) {
                        // إدراج التفاصيل الجديدة
                        saveCurrentDetailsToDatabase(conn, request.getPrNo());

                        conn.commit();
                        statusLabel.setText("تم تحديث الطلب " + request.getPrNo() + " في قاعدة البيانات");
                        showMessage("تم تحديث الطلب بنجاح في قاعدة البيانات");
                    } else {
                        conn.rollback();
                        statusLabel.setText("فشل في تحديث الطلب في قاعدة البيانات");
                        showMessage("خطأ في تحديث الطلب في قاعدة البيانات");
                    }
                } else {
                    System.out.println("طلب جديد - سيتم الإدراج");

                    // إدراج الطلب الرئيسي أولاً
                    if (savePurchaseRequestWithConnection(conn, request)) {
                        // ثم إدراج التفاصيل
                        saveCurrentDetailsToDatabase(conn, request.getPrNo());

                        conn.commit();
                        statusLabel.setText("تم حفظ الطلب " + request.getPrNo() + " في قاعدة البيانات");
                        showMessage("تم حفظ الطلب بنجاح في قاعدة البيانات");

                        // تحديث قائمة الطلبات
                        refreshDataFromDatabase();
                    } else {
                        conn.rollback();
                        statusLabel.setText("فشل في حفظ الطلب في قاعدة البيانات");
                        showMessage("خطأ في حفظ الطلب في قاعدة البيانات");
                    }
                }

            } catch (Exception e) {
                conn.rollback();
                throw e;
            } finally {
                conn.setAutoCommit(true);
            }

        } catch (Exception e) {
            statusLabel.setText("خطأ في حفظ البيانات: " + e.getMessage());
            showMessage("خطأ في حفظ البيانات: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // دالة للتحقق من وجود الطلب في قاعدة البيانات
    private boolean isRequestExists(String prNo) {
        DatabaseManager dbManager = new DatabaseManager();
        String sql = "SELECT COUNT(*) FROM ERP_P_REQUEST WHERE PR_NO = ?";

        try (Connection conn = dbManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, prNo);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                return rs.getInt(1) > 0;
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return false;
    }

    // دالة تحديث الطلب الموجود في قاعدة البيانات
    private boolean updateRequestInDatabase(ERPPurchaseRequest request) {
        DatabaseManager dbManager = new DatabaseManager();
        String sql = "UPDATE ERP_P_REQUEST SET BRN_NO=?, APPROVED=?, PR_TYPE=?, PR_DATE=?, " +
                    "SIDE_REQ=?, W_CODE=?, PR_DESC=?, REF_NO=?, V_CODE=?, V_NAME=?, A_CY=?, REQ_AVL_DATE=? " +
                    "WHERE PR_NO=?";

        try (Connection conn = dbManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, request.getBrnNo());
            stmt.setString(2, request.getApproved());
            stmt.setString(3, request.getPrType());
            stmt.setString(4, request.getPrDate());
            stmt.setString(5, request.getSideReq());
            stmt.setString(6, request.getWCode());
            stmt.setString(7, request.getPrDesc());
            stmt.setString(8, request.getRefNo());
            stmt.setString(9, request.getVCode());
            stmt.setString(10, request.getVName());
            stmt.setString(11, request.getACy());
            stmt.setString(12, request.getReqAvlDate());
            stmt.setString(13, request.getPrNo());

            int result = stmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    // دالة حفظ تفاصيل الطلب الحالية مع اتصال محدد
    private void saveCurrentDetailsToDatabase(Connection conn, String prNo) throws SQLException {
        // إدراج التفاصيل الجديدة
        String insertSQL = "INSERT INTO ERP_P_REQUEST_DETAIL (PR_NO, RCRD_NO, I_CODE, ITM_UNT, P_SIZE, " +
                          "EXPIRE_DATE, I_QTY, DLVRY_DATE, I_PRICE, AVL_QTY, PO_QTY, ARIV_QTY, " +
                          "NOT_PO_PRV_QTY, PO_PRV_QTY) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement insertStmt = conn.prepareStatement(insertSQL)) {
            int insertedRows = 0;

            // حفظ كل صف من جدول التفاصيل
            for (int i = 0; i < detailTableModel.getRowCount(); i++) {
                try {
                    insertStmt.setString(1, prNo);

                    // تحويل آمن للأرقام
                    Object rcrdNoObj = detailTableModel.getValueAt(i, 0);
                    int rcrdNo = (rcrdNoObj instanceof Integer) ? (Integer) rcrdNoObj :
                                (rcrdNoObj instanceof String) ? Integer.parseInt((String) rcrdNoObj) : i + 1;
                    insertStmt.setInt(2, rcrdNo);

                    insertStmt.setString(3, String.valueOf(detailTableModel.getValueAt(i, 1))); // I_CODE
                    insertStmt.setString(4, String.valueOf(detailTableModel.getValueAt(i, 3))); // ITM_UNT
                    insertStmt.setString(5, String.valueOf(detailTableModel.getValueAt(i, 4))); // P_SIZE
                    insertStmt.setString(6, String.valueOf(detailTableModel.getValueAt(i, 5))); // EXPIRE_DATE

                    // تحويل آمن للأرقام العشرية
                    insertStmt.setDouble(7, parseDoubleValue(detailTableModel.getValueAt(i, 6))); // I_QTY
                    insertStmt.setString(8, String.valueOf(detailTableModel.getValueAt(i, 7))); // DLVRY_DATE
                    insertStmt.setDouble(9, parseDoubleValue(detailTableModel.getValueAt(i, 8))); // I_PRICE
                    insertStmt.setDouble(10, parseDoubleValue(detailTableModel.getValueAt(i, 9))); // AVL_QTY
                    insertStmt.setDouble(11, parseDoubleValue(detailTableModel.getValueAt(i, 10))); // PO_QTY
                    insertStmt.setDouble(12, parseDoubleValue(detailTableModel.getValueAt(i, 11))); // ARIV_QTY
                    insertStmt.setDouble(13, parseDoubleValue(detailTableModel.getValueAt(i, 12))); // NOT_PO_PRV_QTY
                    insertStmt.setDouble(14, parseDoubleValue(detailTableModel.getValueAt(i, 13))); // PO_PRV_QTY

                    insertStmt.executeUpdate();
                    insertedRows++;
                } catch (Exception e) {
                    System.err.println("خطأ في حفظ الصف " + i + ": " + e.getMessage());
                    e.printStackTrace();
                }
            }

            System.out.println("تم إدراج " + insertedRows + " صف في جدول التفاصيل");
        }
    }

    // دالة مساعدة لتحويل القيم إلى double بشكل آمن
    private double parseDoubleValue(Object value) {
        if (value == null) return 0.0;

        if (value instanceof Double) {
            return (Double) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).doubleValue();
        } else if (value instanceof String) {
            try {
                String strValue = ((String) value).trim();
                if (strValue.isEmpty()) return 0.0;
                // إزالة الفواصل والرموز غير الرقمية
                strValue = strValue.replaceAll("[^0-9.-]", "");
                return Double.parseDouble(strValue);
            } catch (NumberFormatException e) {
                return 0.0;
            }
        }

        return 0.0;
    }

    // دالة حذف تفاصيل الطلب
    private void deleteRequestDetails(Connection conn, String prNo) throws SQLException {
        String sql = "DELETE FROM ERP_P_REQUEST_DETAIL WHERE PR_NO = ?";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, prNo);
            int deleted = stmt.executeUpdate();
            System.out.println("تم حذف " + deleted + " صف من تفاصيل الطلب");
        }
    }

    // دالة تحديث الطلب مع اتصال محدد
    private boolean updateRequestInDatabase(Connection conn, ERPPurchaseRequest request) throws SQLException {
        String sql = "UPDATE ERP_P_REQUEST SET BRN_NO=?, APPROVED=?, PR_TYPE=?, PR_DATE=?, " +
                    "SIDE_REQ=?, W_CODE=?, PR_DESC=?, REF_NO=?, V_CODE=?, V_NAME=?, A_CY=?, REQ_AVL_DATE=? " +
                    "WHERE PR_NO=?";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, request.getBrnNo());
            stmt.setString(2, request.getApproved());
            stmt.setString(3, request.getPrType());
            stmt.setString(4, request.getPrDate());
            stmt.setString(5, request.getSideReq());
            stmt.setString(6, request.getWCode());
            stmt.setString(7, request.getPrDesc());
            stmt.setString(8, request.getRefNo());
            stmt.setString(9, request.getVCode());
            stmt.setString(10, request.getVName());
            stmt.setString(11, request.getACy());
            stmt.setString(12, request.getReqAvlDate());
            stmt.setString(13, request.getPrNo());

            int result = stmt.executeUpdate();
            System.out.println("تم تحديث " + result + " صف في الجدول الرئيسي");
            return result > 0;
        }
    }

    // دالة حفظ الطلب مع اتصال محدد - محسنة لتجنب ORA-01722
    private boolean savePurchaseRequestWithConnection(Connection conn, ERPPurchaseRequest request) throws SQLException {
        // استخدام أسماء الأعمدة الصحيحة وتنظيف البيانات
        String sql = "INSERT INTO ERP_P_REQUEST (BRN_NO, APPROVED, PR_TYPE, PR_NO, PR_DATE, " +
                    "SIDE_REQ, W_CODE, PR_DESC, REF_NO, V_CODE, V_NAME, A_CY, REQ_AVL_DATE) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            // تنظيف وتحقق من البيانات قبل الإدراج
            String brnNo = cleanStringValue(request.getBrnNo(), "001");
            String approved = cleanStringValue(request.getApproved(), "قيد المراجعة");
            String prType = cleanStringValue(request.getPrType(), "عادي");
            String prNo = cleanStringValue(request.getPrNo(), "PR001");
            String prDate = cleanStringValue(request.getPrDate(), "2025-01-21");
            String sideReq = cleanStringValue(request.getSideReq(), "قسم المشتريات");
            String wCode = cleanStringValue(request.getWCode(), "W001");
            String prDesc = cleanStringValue(request.getPrDesc(), "طلب شراء");
            String refNo = cleanStringValue(request.getRefNo(), "REF001");
            String vCode = cleanStringValue(request.getVCode(), "V001");
            String vName = cleanStringValue(request.getVName(), "مورد");
            String aCy = cleanStringValue(request.getACy(), "ريال");
            String reqAvlDate = cleanStringValue(request.getReqAvlDate(), "2025-02-01");

            // طباعة القيم المنظفة للتشخيص
            System.out.println("=== القيم المنظفة للإدراج ===");
            System.out.println("BRN_NO: '" + brnNo + "'");
            System.out.println("APPROVED: '" + approved + "'");
            System.out.println("PR_TYPE: '" + prType + "'");
            System.out.println("PR_NO: '" + prNo + "'");
            System.out.println("PR_DATE: '" + prDate + "'");
            System.out.println("SIDE_REQ: '" + sideReq + "'");
            System.out.println("W_CODE: '" + wCode + "'");
            System.out.println("PR_DESC: '" + prDesc + "'");
            System.out.println("REF_NO: '" + refNo + "'");
            System.out.println("V_CODE: '" + vCode + "'");
            System.out.println("V_NAME: '" + vName + "'");
            System.out.println("A_CY: '" + aCy + "'");
            System.out.println("REQ_AVL_DATE: '" + reqAvlDate + "'");

            stmt.setString(1, brnNo);
            stmt.setString(2, approved);
            stmt.setString(3, prType);
            stmt.setString(4, prNo);
            stmt.setString(5, prDate);
            stmt.setString(6, sideReq);
            stmt.setString(7, wCode);
            stmt.setString(8, prDesc);
            stmt.setString(9, refNo);
            stmt.setString(10, vCode);
            stmt.setString(11, vName);
            stmt.setString(12, aCy);
            stmt.setString(13, reqAvlDate);

            int result = stmt.executeUpdate();
            System.out.println("تم إدراج " + result + " صف في الجدول الرئيسي");
            return result > 0;
        }
    }

    // دالة تنظيف القيم النصية
    private String cleanStringValue(String value, String defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }

        // إزالة الأحرف غير المرغوب فيها التي قد تسبب مشاكل
        String cleaned = value.trim();

        // إزالة الأحرف الخاصة التي قد تسبب مشاكل في Oracle
        cleaned = cleaned.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

        // التأكد من أن القيمة ليست فارغة بعد التنظيف
        if (cleaned.isEmpty()) {
            return defaultValue;
        }

        return cleaned;
    }

    // دالة مسح النموذج وعرضه فارغاً
    private void clearForm() {
        // مسح حقول الطلب الرئيسي
        if (prNoField != null) prNoField.setText("");
        if (prDateField != null) prDateField.setText("");
        if (sideReqField != null) sideReqField.setText("");
        if (prDescField != null) prDescField.setText("");
        if (refNoField != null) refNoField.setText("");
        if (vCodeField != null) vCodeField.setText("");
        if (vNameField != null) vNameField.setText("");
        if (reqAvlDateField != null) reqAvlDateField.setText("");

        // إعادة تعيين القوائم المنسدلة للقيم الافتراضية
        if (approvedCombo != null) approvedCombo.setSelectedIndex(0);
        if (prTypeCombo != null) prTypeCombo.setSelectedIndex(0);
        if (branchNoCombo != null) branchNoCombo.setSelectedIndex(0);
        if (wCodeCombo != null) wCodeCombo.setSelectedIndex(0);
        if (aCyCombo != null) aCyCombo.setSelectedIndex(0);

        // مسح جدول التفاصيل
        if (detailTableModel != null) {
            detailTableModel.setRowCount(0);
        }

        // تحديث شريط الحالة
        if (statusLabel != null) {
            statusLabel.setText("لا توجد بيانات في قاعدة البيانات - النموذج فارغ");
        }

        if (selectedRequestLabel != null) {
            selectedRequestLabel.setText("لا يوجد طلب مختار");
        }

        System.out.println("تم مسح النموذج - لا توجد بيانات في قاعدة البيانات");
    }

    // تم حذف دالة resetAndCreateTestData نهائياً
    public void resetAndCreateTestDataDELETED() {
        DatabaseManager dbManager = new DatabaseManager();

        try (Connection conn = dbManager.getConnection()) {
            System.out.println("=== حذف البيانات الحالية وإضافة بيانات تجريبية ===");

            // بدء معاملة
            conn.setAutoCommit(false);

            try {
                // 1. حذف جميع البيانات الحالية (الفرعي أولاً ثم الرئيسي)
                System.out.println("1. حذف البيانات الحالية...");

                PreparedStatement deleteDetails = conn.prepareStatement("DELETE FROM ERP_P_REQUEST_DETAIL");
                int deletedDetails = deleteDetails.executeUpdate();
                System.out.println("تم حذف " + deletedDetails + " صف من جدول التفاصيل");
                deleteDetails.close();

                PreparedStatement deleteRequests = conn.prepareStatement("DELETE FROM ERP_P_REQUEST");
                int deletedRequests = deleteRequests.executeUpdate();
                System.out.println("تم حذف " + deletedRequests + " صف من جدول الطلبات");
                deleteRequests.close();

                // 2. إضافة بيانات تجريبية جديدة للجدول الرئيسي
                System.out.println("2. إضافة بيانات تجريبية للجدول الرئيسي...");

                String insertRequestSQL = "INSERT INTO ERP_P_REQUEST (BRN_NO, APPROVED, PR_TYPE, PR_NO, PR_DATE, " +
                                         "SIDE_REQ, W_CODE, PR_DESC, REF_NO, V_CODE, V_NAME, A_CY, REQ_AVL_DATE) " +
                                         "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                PreparedStatement insertStmt = conn.prepareStatement(insertRequestSQL);

                // طلب تجريبي 1
                insertStmt.setString(1, "001");
                insertStmt.setString(2, "معتمد");
                insertStmt.setString(3, "عادي");
                insertStmt.setString(4, "PR001");
                insertStmt.setString(5, "2025-01-21");
                insertStmt.setString(6, "قسم المشتريات");
                insertStmt.setString(7, "W001");
                insertStmt.setString(8, "طلب شراء مواد مكتبية");
                insertStmt.setString(9, "REF001");
                insertStmt.setString(10, "V001");
                insertStmt.setString(11, "شركة التوريدات المتقدمة");
                insertStmt.setString(12, "ريال");
                insertStmt.setString(13, "2025-02-01");
                insertStmt.executeUpdate();
                System.out.println("تم إدراج الطلب PR001");

                // طلب تجريبي 2
                insertStmt.setString(1, "002");
                insertStmt.setString(2, "قيد المراجعة");
                insertStmt.setString(3, "عاجل");
                insertStmt.setString(4, "PR002");
                insertStmt.setString(5, "2025-01-22");
                insertStmt.setString(6, "قسم الصيانة");
                insertStmt.setString(7, "W002");
                insertStmt.setString(8, "طلب شراء قطع غيار");
                insertStmt.setString(9, "REF002");
                insertStmt.setString(10, "V002");
                insertStmt.setString(11, "شركة قطع الغيار الحديثة");
                insertStmt.setString(12, "ريال");
                insertStmt.setString(13, "2025-02-05");
                insertStmt.executeUpdate();
                System.out.println("تم إدراج الطلب PR002");

                insertStmt.close();

                // 3. إضافة تفاصيل تجريبية
                System.out.println("3. إضافة تفاصيل تجريبية...");

                String insertDetailSQL = "INSERT INTO ERP_P_REQUEST_DETAIL (PR_NO, RCRD_NO, I_CODE, ITM_UNT, P_SIZE, " +
                                        "EXPIRE_DATE, I_QTY, DLVRY_DATE, I_PRICE, AVL_QTY, PO_QTY, ARIV_QTY, " +
                                        "NOT_PO_PRV_QTY, PO_PRV_QTY) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                PreparedStatement detailStmt = conn.prepareStatement(insertDetailSQL);

                // تفاصيل الطلب PR001
                detailStmt.setString(1, "PR001");
                detailStmt.setInt(2, 1);
                detailStmt.setString(3, "ITM001");
                detailStmt.setString(4, "قطعة");
                detailStmt.setString(5, "12 قطعة");
                detailStmt.setString(6, "2025-12-31");
                detailStmt.setDouble(7, 100.0);
                detailStmt.setString(8, "2025-02-01");
                detailStmt.setDouble(9, 2.50);
                detailStmt.setDouble(10, 50.0);
                detailStmt.setDouble(11, 20.0);
                detailStmt.setDouble(12, 30.0);
                detailStmt.setDouble(13, 70.0);
                detailStmt.setDouble(14, 25.0);
                detailStmt.executeUpdate();
                System.out.println("تم إدراج تفصيل 1 للطلب PR001");

                detailStmt.setString(1, "PR001");
                detailStmt.setInt(2, 2);
                detailStmt.setString(3, "ITM002");
                detailStmt.setString(4, "علبة");
                detailStmt.setString(5, "500 ورقة");
                detailStmt.setString(6, "2026-06-30");
                detailStmt.setDouble(7, 50.0);
                detailStmt.setString(8, "2025-01-28");
                detailStmt.setDouble(9, 25.00);
                detailStmt.setDouble(10, 10.0);
                detailStmt.setDouble(11, 5.0);
                detailStmt.setDouble(12, 15.0);
                detailStmt.setDouble(13, 35.0);
                detailStmt.setDouble(14, 10.0);
                detailStmt.executeUpdate();
                System.out.println("تم إدراج تفصيل 2 للطلب PR001");

                // تفاصيل الطلب PR002
                detailStmt.setString(1, "PR002");
                detailStmt.setInt(2, 1);
                detailStmt.setString(3, "ITM003");
                detailStmt.setString(4, "قطعة");
                detailStmt.setString(5, "1 قطعة");
                detailStmt.setString(6, "2027-01-01");
                detailStmt.setDouble(7, 25.0);
                detailStmt.setString(8, "2025-02-05");
                detailStmt.setDouble(9, 8.75);
                detailStmt.setDouble(10, 15.0);
                detailStmt.setDouble(11, 10.0);
                detailStmt.setDouble(12, 20.0);
                detailStmt.setDouble(13, 5.0);
                detailStmt.setDouble(14, 12.0);
                detailStmt.executeUpdate();
                System.out.println("تم إدراج تفصيل 1 للطلب PR002");

                detailStmt.close();

                // تأكيد المعاملة
                conn.commit();
                System.out.println("4. تم تأكيد جميع العمليات بنجاح");

                // 4. تحديث النافذة بالبيانات الجديدة
                refreshDataFromDatabase();

                showMessage("تم حذف البيانات القديمة وإضافة بيانات تجريبية جديدة بنجاح!\n" +
                           "تم إضافة طلبين (PR001, PR002) مع تفاصيلهما");

            } catch (SQLException e) {
                conn.rollback();
                System.err.println("خطأ في العملية - تم التراجع: " + e.getMessage());
                throw e;
            } finally {
                conn.setAutoCommit(true);
            }

        } catch (SQLException e) {
            System.err.println("خطأ في إعادة تعيين البيانات: " + e.getMessage());
            e.printStackTrace();
            showMessage("خطأ في إعادة تعيين البيانات: " + e.getMessage());
        }
    }

    // دالة استيراد البيانات مع نافذة إدخال بيانات الاتصال
    public void importDataFromIAS20251() {
        // إظهار نافذة إدخال بيانات الاتصال
        ConnectionDialog dialog = new ConnectionDialog(this);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            String server = dialog.getServer();
            String port = dialog.getPort();
            String sid = dialog.getSid();
            String sourceUsername = dialog.getSourceUsername();
            String sourcePassword = dialog.getSourcePassword();
            String targetUsername = dialog.getTargetUsername();
            String targetPassword = dialog.getTargetPassword();

            performImport(server, port, sid, sourceUsername, sourcePassword, targetUsername, targetPassword);
        }
    }

    // دالة تنفيذ الاستيراد بالبيانات المدخلة
    private void performImport(String server, String port, String sid, String sourceUsername, String sourcePassword, String targetUsername, String targetPassword) {
        System.out.println("=== بدء استيراد البيانات ===");
        System.out.println("الخادم: " + server + ":" + port);
        System.out.println("SID: " + sid);
        System.out.println("المستخدم المصدر: " + sourceUsername);
        System.out.println("المستخدم الهدف: " + targetUsername);

        String dbUrl = "jdbc:oracle:thin:@" + server + ":" + port + ":" + sid;

        // اتصال بقاعدة البيانات الهدف للكتابة
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Connection targetConn = DriverManager.getConnection(dbUrl, targetUsername, targetPassword);
            System.out.println("تم الاتصال بقاعدة البيانات الهدف بنجاح");

            // اتصال بقاعدة البيانات المصدر للقراءة
            try {
                Connection sourceConn = DriverManager.getConnection(dbUrl, sourceUsername, sourcePassword);
                System.out.println("تم الاتصال بقاعدة البيانات المصدر بنجاح");

                // مقارنة بنية الجداول وإضافة الحقول المفقودة
                System.out.println("=== مقارنة بنية الجداول ===");

                try {
                    compareAndUpdateTableStructures(sourceConn, targetConn);
                } catch (SQLException e) {
                    System.err.println("✗ خطأ في مقارنة الجداول: " + e.getMessage());
                    showMessage("خطأ في مقارنة بنية الجداول:\n" + e.getMessage());
                    return;
                }

                targetConn.setAutoCommit(false);

                try {
                    // 1. حذف البيانات الحالية
                    System.out.println("1. حذف البيانات الحالية...");
                    targetConn.createStatement().executeUpdate("DELETE FROM ERP_P_REQUEST_DETAIL");
                    targetConn.createStatement().executeUpdate("DELETE FROM ERP_P_REQUEST");

                    // 2. استيراد الطلبات
                    System.out.println("2. استيراد الطلبات...");

                    // استعلام محسن مع معالجة التواريخ
                    String selectSQL = "SELECT BRN_NO, APPROVED, PR_TYPE, PR_NO, " +
                                      "TO_CHAR(NVL(PR_DATE, SYSDATE), 'DD/MM/YYYY') as PR_DATE_STR, " +
                                      "SIDE_REQ, W_CODE, PR_DESC, REF_NO, V_CODE, V_NAME, A_CY, " +
                                      "TO_CHAR(NVL(REQ_AVL_DATE, SYSDATE), 'DD/MM/YYYY') as REQ_AVL_DATE_STR " +
                                      "FROM P_REQUEST";

                    PreparedStatement selectStmt = sourceConn.prepareStatement(selectSQL);
                    ResultSet rs = selectStmt.executeQuery();

                    String insertSQL = "INSERT INTO ERP_P_REQUEST (BRN_NO, APPROVED, PR_TYPE, PR_NO, PR_DATE, SIDE_REQ, W_CODE, PR_DESC, REF_NO, V_CODE, V_NAME, A_CY, REQ_AVL_DATE) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    PreparedStatement insertStmt = targetConn.prepareStatement(insertSQL);

                    int requestCount = 0;
                    while (rs.next()) {
                        try {
                            insertStmt.setString(1, rs.getString("BRN_NO"));
                            insertStmt.setString(2, rs.getString("APPROVED"));
                            insertStmt.setString(3, rs.getString("PR_TYPE"));
                            insertStmt.setString(4, rs.getString("PR_NO"));
                            insertStmt.setString(5, rs.getString("PR_DATE_STR"));
                            insertStmt.setString(6, rs.getString("SIDE_REQ"));
                            insertStmt.setString(7, rs.getString("W_CODE"));
                            insertStmt.setString(8, rs.getString("PR_DESC"));
                            insertStmt.setString(9, rs.getString("REF_NO"));
                            insertStmt.setString(10, rs.getString("V_CODE"));
                            insertStmt.setString(11, rs.getString("V_NAME"));
                            insertStmt.setString(12, rs.getString("A_CY"));
                            insertStmt.setString(13, rs.getString("REQ_AVL_DATE_STR"));
                            insertStmt.executeUpdate();
                            requestCount++;
                        } catch (SQLException e) {
                            System.err.println("خطأ في إدراج الطلب " + rs.getString("PR_NO") + ": " + e.getMessage());
                            // تجاهل هذا السجل والمتابعة
                        }
                    }
                    rs.close();
                    selectStmt.close();
                    insertStmt.close();

                    System.out.println("تم استيراد " + requestCount + " طلب");

                    // 3. استيراد التفاصيل
                    System.out.println("3. استيراد التفاصيل...");

                    // استعلام محسن مع معالجة آمنة للأرقام والتواريخ
                    String selectDetailSQL = "SELECT d.PR_NO, " +
                                            "NVL(d.RCRD_NO, 1) as RCRD_NO, " +
                                            "d.I_CODE, d.ITM_UNT, d.P_SIZE, " +
                                            "TO_CHAR(NVL(d.EXPIRE_DATE, SYSDATE), 'DD/MM/YYYY') as EXPIRE_DATE_STR, " +
                                            "CASE WHEN REGEXP_LIKE(TO_CHAR(d.I_QTY), '^[0-9]+(\\.[0-9]+)?$') THEN NVL(d.I_QTY, 0) ELSE 0 END as I_QTY, " +
                                            "TO_CHAR(NVL(d.DLVRY_DATE, SYSDATE), 'DD/MM/YYYY') as DLVRY_DATE_STR, " +
                                            "CASE WHEN REGEXP_LIKE(TO_CHAR(d.I_PRICE), '^[0-9]+(\\.[0-9]+)?$') THEN NVL(d.I_PRICE, 0) ELSE 0 END as I_PRICE, " +
                                            "CASE WHEN REGEXP_LIKE(TO_CHAR(d.AVL_QTY), '^[0-9]+(\\.[0-9]+)?$') THEN NVL(d.AVL_QTY, 0) ELSE 0 END as AVL_QTY, " +
                                            "CASE WHEN REGEXP_LIKE(TO_CHAR(d.PO_QTY), '^[0-9]+(\\.[0-9]+)?$') THEN NVL(d.PO_QTY, 0) ELSE 0 END as PO_QTY, " +
                                            "CASE WHEN REGEXP_LIKE(TO_CHAR(d.ARIV_QTY), '^[0-9]+(\\.[0-9]+)?$') THEN NVL(d.ARIV_QTY, 0) ELSE 0 END as ARIV_QTY, " +
                                            "CASE WHEN REGEXP_LIKE(TO_CHAR(d.NOT_PO_PRV_QTY), '^[0-9]+(\\.[0-9]+)?$') THEN NVL(d.NOT_PO_PRV_QTY, 0) ELSE 0 END as NOT_PO_PRV_QTY, " +
                                            "CASE WHEN REGEXP_LIKE(TO_CHAR(d.PO_PRV_QTY), '^[0-9]+(\\.[0-9]+)?$') THEN NVL(d.PO_PRV_QTY, 0) ELSE 0 END as PO_PRV_QTY, " +
                                            "NVL(r.PR_TYPE, 'عادي') as PR_TYPE " +
                                            "FROM P_REQUEST_DETAIL d " +
                                            "LEFT JOIN P_REQUEST r ON d.PR_NO = r.PR_NO";

                    PreparedStatement selectDetailStmt = sourceConn.prepareStatement(selectDetailSQL);
                    ResultSet detailRs = selectDetailStmt.executeQuery();

                    String insertDetailSQL = "INSERT INTO ERP_P_REQUEST_DETAIL (PR_NO, RCRD_NO, I_CODE, ITM_UNT, P_SIZE, EXPIRE_DATE, I_QTY, DLVRY_DATE, I_PRICE, AVL_QTY, PO_QTY, ARIV_QTY, NOT_PO_PRV_QTY, PO_PRV_QTY, PR_TYPE) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    PreparedStatement insertDetailStmt = targetConn.prepareStatement(insertDetailSQL);

                    int detailCount = 0;
                    while (detailRs.next()) {
                        try {
                            insertDetailStmt.setString(1, detailRs.getString("PR_NO"));
                            insertDetailStmt.setInt(2, detailRs.getInt("RCRD_NO"));
                            insertDetailStmt.setString(3, detailRs.getString("I_CODE"));
                            insertDetailStmt.setString(4, detailRs.getString("ITM_UNT"));
                            insertDetailStmt.setString(5, detailRs.getString("P_SIZE"));
                            insertDetailStmt.setString(6, detailRs.getString("EXPIRE_DATE_STR"));
                            insertDetailStmt.setDouble(7, detailRs.getDouble("I_QTY"));
                            insertDetailStmt.setString(8, detailRs.getString("DLVRY_DATE_STR"));
                            insertDetailStmt.setDouble(9, detailRs.getDouble("I_PRICE"));
                            insertDetailStmt.setDouble(10, detailRs.getDouble("AVL_QTY"));
                            insertDetailStmt.setDouble(11, detailRs.getDouble("PO_QTY"));
                            insertDetailStmt.setDouble(12, detailRs.getDouble("ARIV_QTY"));
                            insertDetailStmt.setDouble(13, detailRs.getDouble("NOT_PO_PRV_QTY"));
                            insertDetailStmt.setDouble(14, detailRs.getDouble("PO_PRV_QTY"));
                            insertDetailStmt.setString(15, detailRs.getString("PR_TYPE")); // إضافة PR_TYPE
                            insertDetailStmt.executeUpdate();
                            detailCount++;
                        } catch (SQLException e) {
                            System.err.println("خطأ في إدراج التفصيل " + detailRs.getString("PR_NO") + "-" + detailRs.getInt("RCRD_NO") + ": " + e.getMessage());
                            // تجاهل هذا السجل والمتابعة
                        }
                    }
                    detailRs.close();
                    selectDetailStmt.close();
                    insertDetailStmt.close();

                    System.out.println("تم استيراد " + detailCount + " تفصيل");

                    sourceConn.close();
                    targetConn.commit();
                    System.out.println("4. تم تأكيد جميع العمليات بنجاح");

                    refreshDataFromDatabase();
                    showMessage("تم استيراد البيانات بنجاح!\n" + requestCount + " طلب و " + detailCount + " تفصيل");
                    statusLabel.setText("تم استيراد " + requestCount + " طلب");

                } catch (SQLException e) {
                    targetConn.rollback();
                    throw e;
                } finally {
                    targetConn.setAutoCommit(true);
                }

                targetConn.close();

            } catch (Exception e) {
                System.err.println("خطأ في الاتصال بقاعدة البيانات المصدر: " + e.getMessage());
                if (e.getMessage().contains("ORA-01017")) {
                    showMessage("خطأ في بيانات الاتصال بقاعدة البيانات المصدر!\n\n" +
                               "اسم المستخدم أو كلمة المرور غير صحيحة.\n" +
                               "يرجى التحقق من:\n" +
                               "• اسم المستخدم (جرب: IAS20251 بدلاً من ias20251)\n" +
                               "• كلمة المرور\n" +
                               "• أن المستخدم مفعل ومتاح");
                } else {
                    showMessage("خطأ في الاتصال بقاعدة البيانات المصدر:\n" + e.getMessage());
                }
            }

        } catch (Exception e) {
            System.err.println("خطأ في الاتصال بقاعدة البيانات الهدف: " + e.getMessage());
            if (e.getMessage().contains("ORA-01017")) {
                showMessage("خطأ في بيانات الاتصال بقاعدة البيانات الهدف!\n\n" +
                           "اسم المستخدم أو كلمة المرور غير صحيحة.\n" +
                           "يرجى التحقق من:\n" +
                           "• اسم المستخدم (جرب: SHIP_ERP بدلاً من ship_erp)\n" +
                           "• كلمة المرور\n" +
                           "• أن المستخدم مفعل ومتاح");
            } else {
                showMessage("خطأ في الاتصال بقاعدة البيانات الهدف:\n" + e.getMessage());
            }
        }
    }

    // دالة مقارنة وتحديث بنية الجداول
    private void compareAndUpdateTableStructures(Connection sourceConn, Connection targetConn) throws SQLException {
        System.out.println("1. مقارنة جدول P_REQUEST مع ERP_P_REQUEST...");
        compareAndUpdateTable(sourceConn, targetConn, "P_REQUEST", "ERP_P_REQUEST");

        System.out.println("2. مقارنة جدول P_REQUEST_DETAIL مع ERP_P_REQUEST_DETAIL...");
        compareAndUpdateTable(sourceConn, targetConn, "P_REQUEST_DETAIL", "ERP_P_REQUEST_DETAIL");

        System.out.println("✓ تم الانتهاء من مقارنة وتحديث بنية الجداول");
    }

    // دالة مقارنة وتحديث جدول واحد
    private void compareAndUpdateTable(Connection sourceConn, Connection targetConn, String sourceTable, String targetTable) throws SQLException {
        System.out.println("   فحص جدول " + sourceTable + " -> " + targetTable);

        // الحصول على حقول الجدول المصدر
        Map<String, ColumnInfo> sourceColumns = getTableColumns(sourceConn, sourceTable);
        System.out.println("   الجدول المصدر " + sourceTable + " يحتوي على " + sourceColumns.size() + " حقل");

        // الحصول على حقول الجدول الهدف
        Map<String, ColumnInfo> targetColumns = getTableColumns(targetConn, targetTable);
        System.out.println("   الجدول الهدف " + targetTable + " يحتوي على " + targetColumns.size() + " حقل");

        // العثور على الحقول المفقودة
        List<String> missingColumns = new ArrayList<>();
        for (String columnName : sourceColumns.keySet()) {
            if (!targetColumns.containsKey(columnName)) {
                missingColumns.add(columnName);
            }
        }

        if (missingColumns.isEmpty()) {
            System.out.println("   ✓ جميع الحقول موجودة في الجدول الهدف");
        } else {
            System.out.println("   ⚠ وجد " + missingColumns.size() + " حقل مفقود:");
            for (String columnName : missingColumns) {
                ColumnInfo columnInfo = sourceColumns.get(columnName);
                System.out.println("     - " + columnName + " (" + columnInfo.dataType + ")");

                // إضافة الحقل المفقود
                try {
                    addColumnToTable(targetConn, targetTable, columnName, columnInfo);
                    System.out.println("     ✓ تم إضافة الحقل " + columnName);
                } catch (SQLException e) {
                    System.err.println("     ✗ فشل في إضافة الحقل " + columnName + ": " + e.getMessage());
                }
            }
        }
    }

    // دالة الحصول على معلومات أعمدة الجدول
    private Map<String, ColumnInfo> getTableColumns(Connection conn, String tableName) throws SQLException {
        Map<String, ColumnInfo> columns = new HashMap<>();

        String sql = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE " +
                    "FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ? ORDER BY COLUMN_ID";

        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setString(1, tableName.toUpperCase());
        ResultSet rs = stmt.executeQuery();

        while (rs.next()) {
            String columnName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            int dataLength = rs.getInt("DATA_LENGTH");
            int dataPrecision = rs.getInt("DATA_PRECISION");
            int dataScale = rs.getInt("DATA_SCALE");
            String nullable = rs.getString("NULLABLE");

            ColumnInfo columnInfo = new ColumnInfo(columnName, dataType, dataLength, dataPrecision, dataScale, nullable);
            columns.put(columnName, columnInfo);
        }

        rs.close();
        stmt.close();
        return columns;
    }

    // دالة إضافة حقل إلى جدول
    private void addColumnToTable(Connection conn, String tableName, String columnName, ColumnInfo columnInfo) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("ALTER TABLE ").append(tableName);
        sql.append(" ADD ").append(columnName).append(" ");

        // تحديد نوع البيانات
        switch (columnInfo.dataType) {
            case "VARCHAR2":
                sql.append("VARCHAR2(").append(columnInfo.dataLength).append(")");
                break;
            case "NUMBER":
                if (columnInfo.dataPrecision > 0) {
                    sql.append("NUMBER(").append(columnInfo.dataPrecision);
                    if (columnInfo.dataScale > 0) {
                        sql.append(",").append(columnInfo.dataScale);
                    }
                    sql.append(")");
                } else {
                    sql.append("NUMBER");
                }
                break;
            case "DATE":
                sql.append("DATE");
                break;
            case "CLOB":
                sql.append("CLOB");
                break;
            default:
                sql.append(columnInfo.dataType);
                if (columnInfo.dataLength > 0) {
                    sql.append("(").append(columnInfo.dataLength).append(")");
                }
                break;
        }

        // إضافة NULL/NOT NULL
        if ("N".equals(columnInfo.nullable)) {
            sql.append(" NOT NULL");
        }

        System.out.println("     تنفيذ: " + sql.toString());
        PreparedStatement stmt = conn.prepareStatement(sql.toString());
        stmt.executeUpdate();
        stmt.close();
    }

    // فئة معلومات العمود
    private static class ColumnInfo {
        String columnName;
        String dataType;
        int dataLength;
        int dataPrecision;
        int dataScale;
        String nullable;

        public ColumnInfo(String columnName, String dataType, int dataLength, int dataPrecision, int dataScale, String nullable) {
            this.columnName = columnName;
            this.dataType = dataType;
            this.dataLength = dataLength;
            this.dataPrecision = dataPrecision;
            this.dataScale = dataScale;
            this.nullable = nullable;
        }
    }

    // دالة فحص بنية الجداول للاستيراد
    private void checkTableStructures(Connection conn) throws SQLException {
        System.out.println("=== فحص بنية جدول ERP_P_REQUEST ===");

        String checkERP = "SELECT COLUMN_NAME, DATA_TYPE, NULLABLE FROM USER_TAB_COLUMNS " +
                         "WHERE TABLE_NAME = 'ERP_P_REQUEST' ORDER BY COLUMN_ID";

        PreparedStatement stmt = conn.prepareStatement(checkERP);
        ResultSet rs = stmt.executeQuery();

        while (rs.next()) {
            String colName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            String nullable = rs.getString("NULLABLE");
            System.out.println("ERP_P_REQUEST." + colName + " - " + dataType + " - Nullable: " + nullable);
        }
        rs.close();
        stmt.close();

        System.out.println("\n=== فحص بنية جدول ias20251.P_REQUEST ===");

        String checkIAS = "SELECT COLUMN_NAME, DATA_TYPE, NULLABLE FROM ALL_TAB_COLUMNS " +
                         "WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'P_REQUEST' ORDER BY COLUMN_ID";

        stmt = conn.prepareStatement(checkIAS);
        rs = stmt.executeQuery();

        while (rs.next()) {
            String colName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            String nullable = rs.getString("NULLABLE");
            System.out.println("ias20251.P_REQUEST." + colName + " - " + dataType + " - Nullable: " + nullable);
        }
        rs.close();
        stmt.close();

        System.out.println("\n=== فحص بنية جدول ERP_P_REQUEST_DETAIL ===");

        String checkERPDetail = "SELECT COLUMN_NAME, DATA_TYPE, NULLABLE FROM USER_TAB_COLUMNS " +
                               "WHERE TABLE_NAME = 'ERP_P_REQUEST_DETAIL' ORDER BY COLUMN_ID";

        stmt = conn.prepareStatement(checkERPDetail);
        rs = stmt.executeQuery();

        while (rs.next()) {
            String colName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            String nullable = rs.getString("NULLABLE");
            System.out.println("ERP_P_REQUEST_DETAIL." + colName + " - " + dataType + " - Nullable: " + nullable);
        }
        rs.close();
        stmt.close();

        System.out.println("\n=== فحص بنية جدول ias20251.P_REQUEST_DETAIL ===");

        String checkIASDetail = "SELECT COLUMN_NAME, DATA_TYPE, NULLABLE FROM ALL_TAB_COLUMNS " +
                               "WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'P_REQUEST_DETAIL' ORDER BY COLUMN_ID";

        stmt = conn.prepareStatement(checkIASDetail);
        rs = stmt.executeQuery();

        while (rs.next()) {
            String colName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            String nullable = rs.getString("NULLABLE");
            System.out.println("ias20251.P_REQUEST_DETAIL." + colName + " - " + dataType + " - Nullable: " + nullable);
        }
        rs.close();
        stmt.close();
    }

    // دالة فحص مقارن بين بنية الجدولين
    private void checkDetailTableStructure(Connection conn) throws SQLException {
        System.out.println("\n=== مقارنة بنية الجدولين ===");

        // فحص جدول ship_erp.ERP_P_REQUEST_DETAIL
        System.out.println("\n--- جدول ship_erp.ERP_P_REQUEST_DETAIL ---");
        String checkERP = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE " +
                         "FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'ERP_P_REQUEST_DETAIL' ORDER BY COLUMN_ID";

        PreparedStatement stmt = conn.prepareStatement(checkERP);
        ResultSet rs = stmt.executeQuery();

        while (rs.next()) {
            String colName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            int dataLength = rs.getInt("DATA_LENGTH");
            String nullable = rs.getString("NULLABLE");

            System.out.println(String.format("ERP: %-20s | %-12s | Length: %-3d | Nullable: %s",
                colName, dataType, dataLength, nullable));
        }
        rs.close();
        stmt.close();

        // فحص جدول ias20251.P_REQUEST_DETAIL
        System.out.println("\n--- جدول ias20251.P_REQUEST_DETAIL ---");
        String checkIAS = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE " +
                         "FROM ALL_TAB_COLUMNS WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'P_REQUEST_DETAIL' ORDER BY COLUMN_ID";

        stmt = conn.prepareStatement(checkIAS);
        rs = stmt.executeQuery();

        while (rs.next()) {
            String colName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            int dataLength = rs.getInt("DATA_LENGTH");
            String nullable = rs.getString("NULLABLE");

            System.out.println(String.format("IAS: %-20s | %-12s | Length: %-3d | Nullable: %s",
                colName, dataType, dataLength, nullable));
        }
        rs.close();
        stmt.close();

        // فحص عينة من البيانات الحقيقية
        System.out.println("\n--- عينة من البيانات الحقيقية في ias20251.P_REQUEST_DETAIL ---");
        String sampleSQL = "SELECT PR_NO, RCRD_NO, I_CODE, ITM_UNT, I_QTY, I_PRICE " +
                          "FROM ias20251.P_REQUEST_DETAIL WHERE ROWNUM <= 5";

        stmt = conn.prepareStatement(sampleSQL);
        rs = stmt.executeQuery();

        while (rs.next()) {
            System.out.println(String.format("PR_NO: %s | RCRD_NO: %s | I_CODE: %s | ITM_UNT: %s | I_QTY: %s | I_PRICE: %s",
                rs.getString("PR_NO"), rs.getString("RCRD_NO"), rs.getString("I_CODE"),
                rs.getString("ITM_UNT"), rs.getString("I_QTY"), rs.getString("I_PRICE")));
        }
        rs.close();
        stmt.close();
    }

    // دالة اختبار بنية الجداول
    public void testTableStructure() {
        DatabaseManager dbManager = new DatabaseManager();

        try (Connection conn = dbManager.getConnection()) {
            // اختبار بنية جدول ERP_P_REQUEST
            System.out.println("=== بنية جدول ERP_P_REQUEST ===");
            java.sql.DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, null, "ERP_P_REQUEST", null);

            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String dataType = columns.getString("TYPE_NAME");
                int columnSize = columns.getInt("COLUMN_SIZE");
                String nullable = columns.getString("IS_NULLABLE");

                System.out.println(columnName + " - " + dataType + "(" + columnSize + ") - Nullable: " + nullable);
            }
            columns.close();

            // اختبار بنية جدول ERP_P_REQUEST_DETAIL
            System.out.println("\n=== بنية جدول ERP_P_REQUEST_DETAIL ===");
            columns = metaData.getColumns(null, null, "ERP_P_REQUEST_DETAIL", null);

            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String dataType = columns.getString("TYPE_NAME");
                int columnSize = columns.getInt("COLUMN_SIZE");
                String nullable = columns.getString("IS_NULLABLE");

                System.out.println(columnName + " - " + dataType + "(" + columnSize + ") - Nullable: " + nullable);
            }
            columns.close();

            showMessage("تم عرض بنية الجداول في وحدة التحكم");

        } catch (SQLException e) {
            showMessage("خطأ في اختبار بنية الجداول: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // دالة تشخيص شاملة لمشكلة ORA-01722
    public void diagnoseORA01722Issue() {
        DatabaseManager dbManager = new DatabaseManager();

        try (Connection conn = dbManager.getConnection()) {
            System.out.println("=== تشخيص شامل لمشكلة ORA-01722 ===");

            // 1. فحص بنية الجدول بالتفصيل
            System.out.println("\n1. فحص بنية جدول ERP_P_REQUEST:");
            String tableStructureSQL = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE " +
                                      "FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'ERP_P_REQUEST' ORDER BY COLUMN_ID";

            PreparedStatement stmt = conn.prepareStatement(tableStructureSQL);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                String colName = rs.getString("COLUMN_NAME");
                String dataType = rs.getString("DATA_TYPE");
                int dataLength = rs.getInt("DATA_LENGTH");
                int precision = rs.getInt("DATA_PRECISION");
                int scale = rs.getInt("DATA_SCALE");
                String nullable = rs.getString("NULLABLE");

                System.out.println(String.format("%-20s | %-12s | Length: %-3d | Precision: %-2d | Scale: %-2d | Nullable: %s",
                    colName, dataType, dataLength, precision, scale, nullable));
            }
            rs.close();
            stmt.close();

            // 2. اختبار إدراج بسيط مع قيم ثابتة
            System.out.println("\n2. اختبار إدراج بسيط:");
            try {
                String testSQL = "INSERT INTO ERP_P_REQUEST (BRN_NO, APPROVED, PR_TYPE, PR_NO, PR_DATE, " +
                               "SIDE_REQ, W_CODE, PR_DESC, REF_NO, V_CODE, V_NAME, A_CY, REQ_AVL_DATE) " +
                               "VALUES ('001', 'معتمد', 'عادي', 'TEST001', '2025-01-21', " +
                               "'قسم اختبار', 'W001', 'طلب اختبار', 'REF001', 'V001', 'مورد اختبار', 'ريال', '2025-02-01')";

                PreparedStatement testStmt = conn.prepareStatement(testSQL);
                int result = testStmt.executeUpdate();
                System.out.println("نجح الإدراج البسيط: " + result + " صف");
                testStmt.close();

                // حذف البيانات التجريبية
                conn.prepareStatement("DELETE FROM ERP_P_REQUEST WHERE PR_NO = 'TEST001'").executeUpdate();
                System.out.println("تم حذف البيانات التجريبية");

            } catch (SQLException e) {
                System.err.println("فشل الإدراج البسيط: " + e.getMessage());
                System.err.println("رمز الخطأ: " + e.getErrorCode());
            }

            // 3. فحص القيود والفهارس
            System.out.println("\n3. فحص القيود على الجدول:");
            String constraintsSQL = "SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE, SEARCH_CONDITION " +
                                   "FROM USER_CONSTRAINTS WHERE TABLE_NAME = 'ERP_P_REQUEST'";

            stmt = conn.prepareStatement(constraintsSQL);
            rs = stmt.executeQuery();

            while (rs.next()) {
                String constraintName = rs.getString("CONSTRAINT_NAME");
                String constraintType = rs.getString("CONSTRAINT_TYPE");
                String searchCondition = rs.getString("SEARCH_CONDITION");

                System.out.println("القيد: " + constraintName + " | النوع: " + constraintType +
                                 " | الشرط: " + (searchCondition != null ? searchCondition : "لا يوجد"));
            }
            rs.close();
            stmt.close();

            // 4. اختبار البيانات الحالية من النموذج
            System.out.println("\n4. فحص البيانات الحالية من النموذج:");
            ERPPurchaseRequest currentData = saveDataToERP();

            System.out.println("BRN_NO: '" + currentData.getBrnNo() + "' (طول: " +
                             (currentData.getBrnNo() != null ? currentData.getBrnNo().length() : 0) + ")");
            System.out.println("APPROVED: '" + currentData.getApproved() + "'");
            System.out.println("PR_TYPE: '" + currentData.getPrType() + "'");
            System.out.println("PR_NO: '" + currentData.getPrNo() + "'");
            System.out.println("PR_DATE: '" + currentData.getPrDate() + "'");
            System.out.println("SIDE_REQ: '" + currentData.getSideReq() + "'");
            System.out.println("W_CODE: '" + currentData.getWCode() + "'");
            System.out.println("PR_DESC: '" + currentData.getPrDesc() + "'");
            System.out.println("REF_NO: '" + currentData.getRefNo() + "'");
            System.out.println("V_CODE: '" + currentData.getVCode() + "'");
            System.out.println("V_NAME: '" + currentData.getVName() + "'");
            System.out.println("A_CY: '" + currentData.getACy() + "'");
            System.out.println("REQ_AVL_DATE: '" + currentData.getReqAvlDate() + "'");

            showMessage("تم إكمال التشخيص - راجع وحدة التحكم للتفاصيل");

        } catch (SQLException e) {
            System.err.println("خطأ في التشخيص: " + e.getMessage());
            e.printStackTrace();
            showMessage("خطأ في التشخيص: " + e.getMessage());
        }
    }

    // فئة إدارة قاعدة البيانات Oracle
    private class DatabaseManager {
        private static final String DB_URL = "*************************************";
        private static final String SHIP_ERP_USER = "ship_erp";
        private static final String SHIP_ERP_PASSWORD = "ship_erp_pass";

        // إعدادات ias20251 المؤكدة
        private String IAS_USER = "ias20251";
        private String IAS_PASSWORD = "ys123";

        public Connection getConnection() throws SQLException {
            try {
                Class.forName("oracle.jdbc.driver.OracleDriver");
                return DriverManager.getConnection(DB_URL, SHIP_ERP_USER, SHIP_ERP_PASSWORD);
            } catch (ClassNotFoundException e) {
                throw new SQLException("Oracle JDBC Driver not found", e);
            }
        }

        public Connection getIASConnection() throws SQLException {
            try {
                Class.forName("oracle.jdbc.driver.OracleDriver");

                String dbUrl = "*************************************";
                String user = "IAS20251";  // أحرف كبيرة
                String password = "ys123";

                System.out.println("محاولة الاتصال بـ: " + user + " / " + password);
                Connection conn = DriverManager.getConnection(dbUrl, user, password);
                System.out.println("✓ نجح الاتصال بـ: " + user + " / " + password);
                return conn;

            } catch (ClassNotFoundException e) {
                throw new SQLException("Oracle JDBC Driver not found", e);
            }
        }

        // دالة لاختبار إعدادات اتصال مختلفة
        public boolean testIASConnection(String user, String password) {
            return testIASConnectionWithURL(DB_URL, user, password);
        }

        // دالة لاختبار الاتصال مع URL مختلف
        public boolean testIASConnectionWithURL(String dbUrl, String user, String password) {
            try {
                Class.forName("oracle.jdbc.driver.OracleDriver");
                System.out.print("اختبار: " + user + "/" + password + " على " + dbUrl + " ... ");

                Connection testConn = DriverManager.getConnection(dbUrl, user, password);

                // اختبار بسيط للتأكد من وجود الجداول
                String testSQL = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'P_REQUEST'";
                PreparedStatement testStmt = testConn.prepareStatement(testSQL);
                ResultSet testRs = testStmt.executeQuery();
                testRs.next();
                int tableCount = testRs.getInt(1);

                testRs.close();
                testStmt.close();
                testConn.close();

                if (tableCount > 0) {
                    // إذا نجح الاتصال ووُجد الجدول، احفظ الإعدادات
                    this.IAS_USER = user;
                    this.IAS_PASSWORD = password;
                    System.out.println("✓ نجح (وجد جدول P_REQUEST)");
                    return true;
                } else {
                    System.out.println("✗ فشل (لم يجد جدول P_REQUEST)");
                    return false;
                }

            } catch (Exception e) {
                System.out.println("✗ فشل (" + e.getMessage() + ")");
                return false;
            }
        }

        public List<ERPPurchaseRequest> loadPurchaseRequests() {
            List<ERPPurchaseRequest> requests = new ArrayList<>();
            String sql = "SELECT BRN_NO, APPROVED, PR_TYPE, PR_NO, PR_DATE, SIDE_REQ, " +
                        "W_CODE, PR_DESC, REF_NO, V_CODE, V_NAME, A_CY, REQ_AVL_DATE " +
                        "FROM ERP_P_REQUEST ORDER BY PR_DATE DESC";

            try (Connection conn = getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {

                while (rs.next()) {
                    ERPPurchaseRequest request = new ERPPurchaseRequest();
                    request.setBrnNo(rs.getString("BRN_NO"));
                    request.setApproved(rs.getString("APPROVED"));
                    request.setPrType(rs.getString("PR_TYPE"));
                    request.setPrNo(rs.getString("PR_NO"));
                    request.setPrDate(rs.getString("PR_DATE"));
                    request.setSideReq(rs.getString("SIDE_REQ"));
                    request.setWCode(rs.getString("W_CODE"));
                    request.setPrDesc(rs.getString("PR_DESC"));
                    request.setRefNo(rs.getString("REF_NO"));
                    request.setVCode(rs.getString("V_CODE"));
                    request.setVName(rs.getString("V_NAME"));
                    request.setACy(rs.getString("A_CY"));
                    request.setReqAvlDate(rs.getString("REQ_AVL_DATE"));

                    requests.add(request);
                }

            } catch (SQLException e) {
                showMessage("خطأ في تحميل بيانات الطلبات: " + e.getMessage());
                e.printStackTrace();
            }

            return requests;
        }

        public List<ERPPurchaseRequestDetail> loadPurchaseRequestDetails(String prNo) {
            List<ERPPurchaseRequestDetail> details = new ArrayList<>();
            String sql = "SELECT RCRD_NO, I_CODE, ITM_UNT, P_SIZE, EXPIRE_DATE, I_QTY, " +
                        "DLVRY_DATE, I_PRICE, AVL_QTY, PO_QTY, ARIV_QTY, NOT_PO_PRV_QTY, " +
                        "PO_PRV_QTY FROM ERP_P_REQUEST_DETAIL WHERE PR_NO = ? ORDER BY RCRD_NO";

            try (Connection conn = getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setString(1, prNo);
                ResultSet rs = stmt.executeQuery();

                while (rs.next()) {
                    ERPPurchaseRequestDetail detail = new ERPPurchaseRequestDetail();
                    detail.setRcrdNo(rs.getInt("RCRD_NO"));
                    detail.setICode(rs.getString("I_CODE"));
                    detail.setItemName(getItemName(rs.getString("I_CODE"))); // جلب اسم الصنف
                    detail.setItmUnt(rs.getString("ITM_UNT"));
                    detail.setPSize(rs.getString("P_SIZE"));
                    detail.setExpireDate(rs.getString("EXPIRE_DATE"));
                    detail.setIQty(rs.getDouble("I_QTY"));
                    detail.setDlvryDate(rs.getString("DLVRY_DATE"));
                    detail.setIPrice(rs.getDouble("I_PRICE"));
                    detail.setAvlQty(rs.getDouble("AVL_QTY"));
                    detail.setPoQty(rs.getDouble("PO_QTY"));
                    detail.setArivQty(rs.getDouble("ARIV_QTY"));
                    detail.setNotPoPrvQty(rs.getDouble("NOT_PO_PRV_QTY"));
                    detail.setPoPrvQty(rs.getDouble("PO_PRV_QTY"));
                    detail.setNotPoPrvQtyPrev(rs.getDouble("NOT_PO_PRV_QTY")); // نفس القيمة مؤقتاً

                    details.add(detail);
                }

            } catch (SQLException e) {
                showMessage("خطأ في تحميل تفاصيل الطلب: " + e.getMessage());
                e.printStackTrace();
            }

            return details;
        }

        private String getItemName(String itemCode) {
            // دالة مساعدة لجلب اسم الصنف من جدول الأصناف
            String sql = "SELECT ITEM_NAME FROM ITEMS WHERE ITEM_CODE = ?";
            try (Connection conn = getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setString(1, itemCode);
                ResultSet rs = stmt.executeQuery();

                if (rs.next()) {
                    return rs.getString("ITEM_NAME");
                }

            } catch (SQLException e) {
                // في حالة عدم وجود جدول الأصناف، نعيد رقم الصنف
                return "صنف " + itemCode;
            }

            return "صنف " + itemCode;
        }

        // دالة حفظ طلب شراء في قاعدة البيانات
        public boolean savePurchaseRequest(ERPPurchaseRequest request) {
            // أولاً، دعنا نتحقق من بنية الجدول
            try (Connection conn = getConnection()) {
                // فحص بنية الجدول أولاً
                String checkTableSQL = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'ERP_P_REQUEST' ORDER BY COLUMN_ID";
                PreparedStatement checkStmt = conn.prepareStatement(checkTableSQL);
                ResultSet rs = checkStmt.executeQuery();

                System.out.println("=== بنية جدول ERP_P_REQUEST ===");
                while (rs.next()) {
                    System.out.println(rs.getString("COLUMN_NAME") + " - " + rs.getString("DATA_TYPE") + "(" + rs.getInt("DATA_LENGTH") + ")");
                }
                rs.close();
                checkStmt.close();

                // الآن محاولة الإدراج بناءً على أنواع البيانات الصحيحة
                String sql = "INSERT INTO ERP_P_REQUEST (BRN_NO, APPROVED, PR_TYPE, PR_NO, PR_DATE, " +
                            "SIDE_REQ, W_CODE, PR_DESC, REF_NO, V_CODE, V_NAME, A_CY, REQ_AVL_DATE) " +
                            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                PreparedStatement stmt = conn.prepareStatement(sql);

                System.out.println("محاولة حفظ الطلب: " + request.getPrNo());

                // طباعة القيم قبل الإدراج
                String brnNo = request.getBrnNo() != null ? request.getBrnNo() : "001";
                String approved = request.getApproved() != null ? request.getApproved() : "قيد المراجعة";
                String prType = request.getPrType() != null ? request.getPrType() : "عادي";
                String prNo = request.getPrNo() != null ? request.getPrNo() : "PR001";
                String prDate = request.getPrDate() != null ? request.getPrDate() : "2025-01-21";
                String sideReq = request.getSideReq() != null ? request.getSideReq() : "قسم المشتريات";
                String wCode = request.getWCode() != null ? request.getWCode() : "W001";
                String prDesc = request.getPrDesc() != null ? request.getPrDesc() : "طلب شراء";
                String refNo = request.getRefNo() != null ? request.getRefNo() : "REF001";
                String vCode = request.getVCode() != null ? request.getVCode() : "V001";
                String vName = request.getVName() != null ? request.getVName() : "مورد";
                String aCy = request.getACy() != null ? request.getACy() : "ريال";
                String reqAvlDate = request.getReqAvlDate() != null ? request.getReqAvlDate() : "2025-02-01";

                System.out.println("القيم المراد إدراجها:");
                System.out.println("BRN_NO: " + brnNo);
                System.out.println("APPROVED: " + approved);
                System.out.println("PR_TYPE: " + prType);
                System.out.println("PR_NO: " + prNo);
                System.out.println("PR_DATE: " + prDate);
                System.out.println("SIDE_REQ: " + sideReq);
                System.out.println("W_CODE: " + wCode);
                System.out.println("PR_DESC: " + prDesc);
                System.out.println("REF_NO: " + refNo);
                System.out.println("V_CODE: " + vCode);
                System.out.println("V_NAME: " + vName);
                System.out.println("A_CY: " + aCy);
                System.out.println("REQ_AVL_DATE: " + reqAvlDate);

                stmt.setString(1, brnNo);
                stmt.setString(2, approved);
                stmt.setString(3, prType);
                stmt.setString(4, prNo);
                stmt.setString(5, prDate);
                stmt.setString(6, sideReq);
                stmt.setString(7, wCode);
                stmt.setString(8, prDesc);
                stmt.setString(9, refNo);
                stmt.setString(10, vCode);
                stmt.setString(11, vName);
                stmt.setString(12, aCy);
                stmt.setString(13, reqAvlDate);

                System.out.println("تنفيذ الاستعلام...");
                int result = stmt.executeUpdate();
                System.out.println("نتيجة الحفظ: " + result);
                stmt.close();
                return result > 0;

            } catch (SQLException e) {
                System.err.println("تفاصيل الخطأ: " + e.getMessage());
                System.err.println("رمز الخطأ: " + e.getErrorCode());
                System.err.println("حالة SQL: " + e.getSQLState());
                showMessage("خطأ في حفظ الطلب: " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        }

        // تم حذف دالة createSampleDataInDatabase نهائياً
        public void createSampleDataInDatabaseDELETED() {
            try (Connection conn = getConnection()) {
                // حذف البيانات الموجودة
                conn.createStatement().executeUpdate("DELETE FROM ERP_P_REQUEST_DETAIL");
                conn.createStatement().executeUpdate("DELETE FROM ERP_P_REQUEST");

                // إدراج بيانات تجريبية في ERP_P_REQUEST
                String insertRequest = "INSERT INTO ERP_P_REQUEST (BRN_NO, APPROVED, PR_TYPE, PR_NO, PR_DATE, " +
                                     "SIDE_REQ, W_CODE, PR_DESC, REF_NO, V_CODE, V_NAME, A_CY, REQ_AVL_DATE) " +
                                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                PreparedStatement stmt = conn.prepareStatement(insertRequest);

                // طلب 1
                stmt.setString(1, "001");
                stmt.setString(2, "معتمد");
                stmt.setString(3, "عاجل");
                stmt.setString(4, "PR001");
                stmt.setString(5, "2025-01-21");
                stmt.setString(6, "قسم المشتريات");
                stmt.setString(7, "W001");
                stmt.setString(8, "طلب شراء مواد مكتبية");
                stmt.setString(9, "REF001");
                stmt.setString(10, "V001");
                stmt.setString(11, "شركة التوريدات المتقدمة");
                stmt.setString(12, "ريال");
                stmt.setString(13, "2025-02-01");
                stmt.executeUpdate();

                // طلب 2
                stmt.setString(1, "002");
                stmt.setString(2, "قيد المراجعة");
                stmt.setString(3, "عادي");
                stmt.setString(4, "PR002");
                stmt.setString(5, "2025-01-20");
                stmt.setString(6, "قسم الصيانة");
                stmt.setString(7, "W002");
                stmt.setString(8, "طلب شراء قطع غيار");
                stmt.setString(9, "REF002");
                stmt.setString(10, "V002");
                stmt.setString(11, "شركة قطع الغيار الحديثة");
                stmt.setString(12, "ريال");
                stmt.setString(13, "2025-02-05");
                stmt.executeUpdate();

                // طلب 3
                stmt.setString(1, "001");
                stmt.setString(2, "معتمد");
                stmt.setString(3, "طارئ");
                stmt.setString(4, "PR003");
                stmt.setString(5, "2025-01-19");
                stmt.setString(6, "قسم الإنتاج");
                stmt.setString(7, "W001");
                stmt.setString(8, "طلب شراء مواد خام");
                stmt.setString(9, "REF003");
                stmt.setString(10, "V003");
                stmt.setString(11, "شركة المواد الخام المحدودة");
                stmt.setString(12, "ريال");
                stmt.setString(13, "2025-01-25");
                stmt.executeUpdate();

                stmt.close();

                // إدراج تفاصيل الطلبات
                String insertDetail = "INSERT INTO ERP_P_REQUEST_DETAIL (PR_NO, RCRD_NO, I_CODE, ITM_UNT, P_SIZE, " +
                                    "EXPIRE_DATE, I_QTY, DLVRY_DATE, I_PRICE, AVL_QTY, PO_QTY, ARIV_QTY, " +
                                    "NOT_PO_PRV_QTY, PO_PRV_QTY) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                PreparedStatement detailStmt = conn.prepareStatement(insertDetail);

                // تفاصيل الطلب PR001
                detailStmt.setString(1, "PR001");
                detailStmt.setInt(2, 1);
                detailStmt.setString(3, "ITM001");
                detailStmt.setString(4, "قطعة");
                detailStmt.setString(5, "12 قطعة");
                detailStmt.setString(6, "2025-12-31");
                detailStmt.setDouble(7, 100);
                detailStmt.setString(8, "2025-02-01");
                detailStmt.setDouble(9, 2.50);
                detailStmt.setDouble(10, 50);
                detailStmt.setDouble(11, 20);
                detailStmt.setDouble(12, 30);
                detailStmt.setDouble(13, 70);
                detailStmt.setDouble(14, 25);
                detailStmt.executeUpdate();

                detailStmt.setString(1, "PR001");
                detailStmt.setInt(2, 2);
                detailStmt.setString(3, "ITM002");
                detailStmt.setString(4, "علبة");
                detailStmt.setString(5, "500 ورقة");
                detailStmt.setString(6, "2026-06-30");
                detailStmt.setDouble(7, 50);
                detailStmt.setString(8, "2025-01-28");
                detailStmt.setDouble(9, 25.00);
                detailStmt.setDouble(10, 10);
                detailStmt.setDouble(11, 5);
                detailStmt.setDouble(12, 15);
                detailStmt.setDouble(13, 35);
                detailStmt.setDouble(14, 10);
                detailStmt.executeUpdate();

                // تفاصيل الطلب PR002
                detailStmt.setString(1, "PR002");
                detailStmt.setInt(2, 1);
                detailStmt.setString(3, "ITM003");
                detailStmt.setString(4, "قطعة");
                detailStmt.setString(5, "1 قطعة");
                detailStmt.setString(6, "2027-01-01");
                detailStmt.setDouble(7, 25);
                detailStmt.setString(8, "2025-02-05");
                detailStmt.setDouble(9, 8.75);
                detailStmt.setDouble(10, 15);
                detailStmt.setDouble(11, 10);
                detailStmt.setDouble(12, 20);
                detailStmt.setDouble(13, 5);
                detailStmt.setDouble(14, 12);
                detailStmt.executeUpdate();

                // تفاصيل الطلب PR003
                detailStmt.setString(1, "PR003");
                detailStmt.setInt(2, 1);
                detailStmt.setString(3, "ITM004");
                detailStmt.setString(4, "كيلو");
                detailStmt.setString(5, "25 كيلو");
                detailStmt.setString(6, "2025-06-30");
                detailStmt.setDouble(7, 500);
                detailStmt.setString(8, "2025-01-25");
                detailStmt.setDouble(9, 15.00);
                detailStmt.setDouble(10, 200);
                detailStmt.setDouble(11, 100);
                detailStmt.setDouble(12, 300);
                detailStmt.setDouble(13, 200);
                detailStmt.setDouble(14, 150);
                detailStmt.executeUpdate();

                detailStmt.close();

                System.out.println("تم إنشاء البيانات التجريبية في قاعدة البيانات بنجاح!");

            } catch (SQLException e) {
                System.err.println("خطأ في إنشاء البيانات التجريبية: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel("com.sun.java.swing.plaf.windows.WindowsLookAndFeel");
            } catch (Exception e) {
                // استخدام المظهر الافتراضي
            }

            PurchaseRequestMasterDetailWindow window = new PurchaseRequestMasterDetailWindow();

            // تم حذف اختبار الربط التلقائي
        });
    }
}

// فئة نافذة إدخال بيانات الاتصال
class ConnectionDialog extends JDialog {
    private JTextField serverField;
    private JTextField portField;
    private JTextField sidField;
    private JTextField sourceUsernameField;
    private JPasswordField sourcePasswordField;
    private JTextField targetUsernameField;
    private JPasswordField targetPasswordField;
    private boolean confirmed = false;

    public ConnectionDialog(JFrame parent) {
        super(parent, "بيانات الاتصال بقواعد البيانات", true);
        initializeDialog();
    }

    private void initializeDialog() {
        setLayout(new BorderLayout());
        setSize(500, 400);
        setLocationRelativeTo(getParent());

        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));

        // معلومات الخادم
        JPanel serverPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        serverPanel.setBorder(BorderFactory.createTitledBorder("معلومات الخادم"));

        serverPanel.add(new JLabel("الخادم:"));
        serverField = new JTextField("localhost", 10);
        serverField.setFont(arabicFont);
        serverPanel.add(serverField);

        serverPanel.add(new JLabel("المنفذ:"));
        portField = new JTextField("1521", 5);
        portField.setFont(arabicFont);
        serverPanel.add(portField);

        serverPanel.add(new JLabel("SID:"));
        sidField = new JTextField("orcl", 8);
        sidField.setFont(arabicFont);
        serverPanel.add(sidField);

        mainPanel.add(serverPanel);

        // قاعدة البيانات المصدر
        JPanel sourcePanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        sourcePanel.setBorder(BorderFactory.createTitledBorder("قاعدة البيانات المصدر (للقراءة)"));

        sourcePanel.add(new JLabel("المستخدم:"));
        sourceUsernameField = new JTextField("ias20251", 12);
        sourceUsernameField.setFont(arabicFont);
        sourcePanel.add(sourceUsernameField);

        sourcePanel.add(new JLabel("كلمة المرور:"));
        sourcePasswordField = new JPasswordField("ys123", 12);
        sourcePasswordField.setFont(arabicFont);
        sourcePanel.add(sourcePasswordField);

        mainPanel.add(sourcePanel);

        // قاعدة البيانات الهدف
        JPanel targetPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        targetPanel.setBorder(BorderFactory.createTitledBorder("قاعدة البيانات الهدف (للكتابة)"));

        targetPanel.add(new JLabel("المستخدم:"));
        targetUsernameField = new JTextField("ship_erp", 12);
        targetUsernameField.setFont(arabicFont);
        targetPanel.add(targetUsernameField);

        targetPanel.add(new JLabel("كلمة المرور:"));
        targetPasswordField = new JPasswordField("ship_erp_pass", 12);
        targetPasswordField.setFont(arabicFont);
        targetPanel.add(targetPasswordField);

        mainPanel.add(targetPanel);

        add(mainPanel, BorderLayout.CENTER);

        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton connectButton = new JButton("اتصال");
        connectButton.setFont(arabicFont);
        connectButton.addActionListener(e -> {
            confirmed = true;
            setVisible(false);
        });

        JButton cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.addActionListener(e -> {
            confirmed = false;
            setVisible(false);
        });

        buttonPanel.add(connectButton);
        buttonPanel.add(cancelButton);
        add(buttonPanel, BorderLayout.SOUTH);

        // إعداد الإغلاق
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    }

    public boolean isConfirmed() { return confirmed; }
    public String getServer() { return serverField.getText().trim(); }
    public String getPort() { return portField.getText().trim(); }
    public String getSid() { return sidField.getText().trim(); }
    public String getSourceUsername() { return sourceUsernameField.getText().trim(); }
    public String getSourcePassword() { return new String(sourcePasswordField.getPassword()); }
    public String getTargetUsername() { return targetUsernameField.getText().trim(); }
    public String getTargetPassword() { return new String(targetPasswordField.getPassword()); }
}
