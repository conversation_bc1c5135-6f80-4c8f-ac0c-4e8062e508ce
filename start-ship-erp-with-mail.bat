@echo off
echo Starting Ship ERP with JavaMail support...
cd /d "e:\ship_erp\java"

echo Compiling TreeMenuPanel...
javac -encoding UTF-8 -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\*;." -d . src\main\java\TreeMenuPanel.java

echo Compiling CompleteEmailAccountsWindow...
javac -encoding UTF-8 -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\*;." -d . src\main\java\CompleteEmailAccountsWindow.java

echo Starting Ship ERP...
java -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\*;." ShipERPMain

pause
