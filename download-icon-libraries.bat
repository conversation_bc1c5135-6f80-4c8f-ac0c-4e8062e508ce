@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 📦 تحميل مكتبات الأيقونات
echo    Downloading Icon Libraries
echo ================================================
echo.

cd /d "d:\java\java\lib"

echo 🔍 تحميل مكتبات الأيقونات المطلوبة...
echo.

echo [1] تحميل FontAwesome Icons...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-fontawesome-pack/12.3.1/ikonli-fontawesome-pack-12.3.1.jar' -OutFile 'ikonli-fontawesome-pack-12.3.1.jar'"
if exist "ikonli-fontawesome-pack-12.3.1.jar" (
    echo ✅ تم تحميل FontAwesome بنجاح
) else (
    echo ❌ فشل في تحميل FontAwesome
)

echo.
echo [2] تحميل Material Design Icons...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-material-pack/12.3.1/ikonli-material-pack-12.3.1.jar' -OutFile 'ikonli-material-pack-12.3.1.jar'"
if exist "ikonli-material-pack-12.3.1.jar" (
    echo ✅ تم تحميل Material Design Icons بنجاح
) else (
    echo ❌ فشل في تحميل Material Design Icons
)

echo.
echo [3] تحميل Ikonli Core...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-core/12.3.1/ikonli-core-12.3.1.jar' -OutFile 'ikonli-core-12.3.1.jar'"
if exist "ikonli-core-12.3.1.jar" (
    echo ✅ تم تحميل Ikonli Core بنجاح
) else (
    echo ❌ فشل في تحميل Ikonli Core
)

echo.
echo [4] تحميل Ikonli Swing...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-swing/12.3.1/ikonli-swing-12.3.1.jar' -OutFile 'ikonli-swing-12.3.1.jar'"
if exist "ikonli-swing-12.3.1.jar" (
    echo ✅ تم تحميل Ikonli Swing بنجاح
) else (
    echo ❌ فشل في تحميل Ikonli Swing
)

echo.
echo [5] تحميل Commons CSV...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/apache/commons/commons-csv/1.10.0/commons-csv-1.10.0.jar' -OutFile 'commons-csv-1.10.0.jar'"
if exist "commons-csv-1.10.0.jar" (
    echo ✅ تم تحميل Commons CSV بنجاح
) else (
    echo ❌ فشل في تحميل Commons CSV
)

echo.
echo [6] تحميل Apache Commons Text...
powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/apache/commons/commons-text/1.10.0/commons-text-1.10.0.jar' -OutFile 'commons-text-1.10.0.jar'"
if exist "commons-text-1.10.0.jar" (
    echo ✅ تم تحميل Commons Text بنجاح
) else (
    echo ❌ فشل في تحميل Commons Text
)

echo.
echo ================================================
echo ✅ تم إكمال تحميل مكتبات الأيقونات
echo ================================================
echo.

echo 📋 المكتبات المحملة:
dir /b *.jar | findstr /i "ikonli\|commons-csv\|commons-text"

echo.
echo 💡 الآن يمكن استخدام الأيقونات في التطبيق
echo.

pause
