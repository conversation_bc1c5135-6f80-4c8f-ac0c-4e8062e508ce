@echo off
echo ========================================
echo    SHIP ERP SYSTEM - FINAL VERSION
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Starting Ship ERP System...
echo.

echo Features enabled:
echo - Dynamic system tree from database
echo - Automatic window registration
echo - TNS connection management
echo - Arabic language support
echo - Real-time tree updates
echo - Security management
echo - Configuration management
echo - Global variables management
echo - UI theme and appearance settings
echo - Modern FlatLaf themes
echo - Color customization
echo.

echo Starting application...

java -Doracle.net.tns_admin=e:\ship_erp\java\network\admin -Doracle.jdbc.defaultNChar=true -Dfile.encoding=UTF-8 -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\ojdbc11.jar;lib\orai18n.jar;lib\flatlaf-3.2.5.jar;lib\flatlaf-extras-3.2.5.jar;lib\miglayout-core-11.0.jar;lib\miglayout-swing-11.0.jar;lib\*;." TreeMenuPanel

echo.
echo Application closed.
pause
