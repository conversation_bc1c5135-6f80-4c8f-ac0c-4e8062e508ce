-- فحص وجود جداول الأصناف
SET PAGESIZE 50
SET LINESIZE 100

PROMPT ========================================
PROMPT    فحص جداول الأصناف في قاعدة البيانات
PROMPT ========================================

PROMPT 
PROMPT 1. فحص جدول الأصناف (ITEMS):
SELECT COUNT(*) AS ITEMS_EXISTS FROM user_tables WHERE table_name = 'ITEMS';

PROMPT 
PROMPT 2. فحص جدول مجموعات الأصناف (ITEM_CATEGORIES):
SELECT COUNT(*) AS CATEGORIES_EXISTS FROM user_tables WHERE table_name = 'ITEM_CATEGORIES';

PROMPT 
PROMPT 3. فحص جدول وحدات القياس (UNITS_OF_MEASURE):
SELECT COUNT(*) AS UOM_EXISTS FROM user_tables WHERE table_name = 'UNITS_OF_MEASURE';

PROMPT 
PROMPT 4. جميع الجداول الموجودة:
SELECT table_name FROM user_tables ORDER BY table_name;

PROMPT 
PROMPT 5. عدد الجداول الإجمالي:
SELECT COUNT(*) AS TOTAL_TABLES FROM user_tables;

PROMPT 
PROMPT ========================================
PROMPT    انتهى الفحص
PROMPT ========================================

EXIT;
