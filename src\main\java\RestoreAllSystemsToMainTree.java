import java.sql.*;

/**
 * استعادة جميع الأنظمة للقائمة الرئيسية
 * Restore All Systems to Main Tree
 */
public class RestoreAllSystemsToMainTree {
    
    public static void main(String[] args) {
        System.out.println("🔄 استعادة جميع الأنظمة للقائمة الرئيسية...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            // استعادة جميع الفئات للمستوى الرئيسي
            restoreAllCategoriesToMainLevel(connection);
            
            connection.close();
            System.out.println("🎉 تم استعادة جميع الأنظمة للقائمة الرئيسية بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في استعادة الأنظمة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void restoreAllCategoriesToMainLevel(Connection connection) throws SQLException {
        System.out.println("📋 البحث عن جميع الفئات المخفية...");
        
        // البحث عن جميع الفئات التي لها نوافذ فرعية ولكنها ليست في المستوى الرئيسي
        String findCategoriesSQL = """
            SELECT DISTINCT c.TREE_ID, c.NODE_NAME_AR, c.NODE_NAME_EN, c.NODE_DESCRIPTION
            FROM ERP_SYSTEM_TREE c
            WHERE c.NODE_TYPE = 'CATEGORY' 
            AND c.IS_ACTIVE = 'Y'
            AND c.TREE_ID IN (
                SELECT DISTINCT w.PARENT_ID 
                FROM ERP_SYSTEM_TREE w 
                WHERE w.NODE_TYPE = 'WINDOW' 
                AND w.IS_ACTIVE = 'Y'
                AND w.PARENT_ID IS NOT NULL
            )
            ORDER BY c.NODE_NAME_AR
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(findCategoriesSQL);
             ResultSet rs = stmt.executeQuery()) {
            
            int order = getNextRootOrder(connection);
            
            while (rs.next()) {
                int categoryId = rs.getInt("TREE_ID");
                String nameAr = rs.getString("NODE_NAME_AR");
                String nameEn = rs.getString("NODE_NAME_EN");
                String description = rs.getString("NODE_DESCRIPTION");
                
                // تحديث الفئة لتكون في المستوى الرئيسي
                updateCategoryToMainLevel(connection, categoryId, order);
                System.out.println("✅ تم استعادة الفئة: " + nameAr);
                order++;
            }
        }
    }
    
    private static void updateCategoryToMainLevel(Connection connection, int categoryId, int order) throws SQLException {
        String updateSQL = """
            UPDATE ERP_SYSTEM_TREE 
            SET PARENT_ID = NULL, 
                TREE_LEVEL = 1, 
                DISPLAY_ORDER = ?,
                IS_VISIBLE = 'Y',
                IS_ACTIVE = 'Y',
                LAST_UPDATED = SYSDATE,
                UPDATED_BY = 'SYSTEM'
            WHERE TREE_ID = ?
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(updateSQL)) {
            stmt.setInt(1, order);
            stmt.setInt(2, categoryId);
            stmt.executeUpdate();
        }
    }
    
    private static int getNextRootOrder(Connection connection) throws SQLException {
        String sql = "SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID IS NULL";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        return 1;
    }
}
