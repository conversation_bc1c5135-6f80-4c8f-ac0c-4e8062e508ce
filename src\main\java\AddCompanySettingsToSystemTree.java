import java.sql.*;

/**
 * إدراج نافذة إعدادات الشركات في شجرة الأنظمة
 * Add Company Settings Window to System Tree
 */
public class AddCompanySettingsToSystemTree {
    
    public static void main(String[] args) {
        System.out.println("🏢 إدراج نافذة إعدادات الشركات في شجرة الأنظمة...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            addCompanySettingsToTree(connection);
            
            connection.close();
            System.out.println("✅ تم إدراج نافذة إعدادات الشركات في شجرة الأنظمة بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إدراج نافذة إعدادات الشركات: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void addCompanySettingsToTree(Connection connection) throws SQLException {
        System.out.println("📋 البحث عن فئة الإعدادات العامة...");
        
        // البحث عن فئة الإعدادات العامة
        int settingsId = getCategoryId(connection, "الإعدادات العامة");
        
        if (settingsId == 0) {
            System.err.println("❌ لم يتم العثور على فئة الإعدادات العامة");
            return;
        }
        
        System.out.println("✅ تم العثور على فئة الإعدادات العامة (ID: " + settingsId + ")");
        
        // التحقق من وجود النافذة مسبقاً
        if (isWindowExists(connection, "AdvancedCompanySettingsWindow")) {
            System.out.println("ℹ️ نافذة إعدادات الشركات موجودة مسبقاً، سيتم تحديثها...");
            updateExistingWindow(connection);
            return;
        }
        
        // الحصول على الترتيب التالي
        int nextOrder = getNextOrder(connection, settingsId);
        
        // إدراج النافذة الجديدة
        String insertSQL = """
            INSERT INTO ERP_SYSTEM_TREE 
            (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, 
             WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
             CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
            VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, ?, ?, ?, ?, 'WINDOW', ?, ?, 2, 'Y', 'Y', 
                    SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
            stmt.setInt(1, settingsId);
            stmt.setString(2, "إعدادات الشركات الشاملة");
            stmt.setString(3, "Advanced Company Settings");
            stmt.setString(4, "نظام إعدادات الشركات الشامل والمتقدم - يتضمن إدارة المعلومات الأساسية، العناوين، المعلومات القانونية، البنوك، الفروع، جهات الاتصال، والوثائق");
            stmt.setString(5, "AdvancedCompanySettingsWindow");
            stmt.setInt(6, nextOrder);
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("✅ تم إدراج نافذة إعدادات الشركات الشاملة بنجاح");
                
                // الحصول على معرف النافذة المدرجة
                int newWindowId = getLastInsertedId(connection);
                System.out.println("📋 معرف النافذة الجديدة: " + newWindowId);
                
                // إضافة صلاحيات افتراضية
                addDefaultPermissions(connection, newWindowId);
                
            } else {
                System.err.println("❌ فشل في إدراج نافذة إعدادات الشركات");
            }
        }
        
        // عرض النتيجة النهائية
        displaySystemTreeStructure(connection, settingsId);
    }
    
    private static int getCategoryId(Connection conn, String categoryName) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("TREE_ID");
                }
            }
        }
        
        return 0;
    }
    
    private static boolean isWindowExists(Connection conn, String windowClass) throws SQLException {
        String sql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, windowClass);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        
        return false;
    }
    
    private static void updateExistingWindow(Connection conn) throws SQLException {
        String updateSQL = """
            UPDATE ERP_SYSTEM_TREE 
            SET NODE_NAME_AR = ?, 
                NODE_NAME_EN = ?, 
                NODE_DESCRIPTION = ?, 
                LAST_UPDATED = SYSDATE,
                UPDATED_BY = 'SYSTEM',
                VERSION_NUMBER = VERSION_NUMBER + 1
            WHERE WINDOW_CLASS = ?
            """;
        
        try (PreparedStatement stmt = conn.prepareStatement(updateSQL)) {
            stmt.setString(1, "إعدادات الشركات الشاملة");
            stmt.setString(2, "Advanced Company Settings");
            stmt.setString(3, "نظام إعدادات الشركات الشامل والمتقدم - محدث - يتضمن إدارة المعلومات الأساسية، العناوين، المعلومات القانونية، البنوك، الفروع، جهات الاتصال، والوثائق");
            stmt.setString(4, "AdvancedCompanySettingsWindow");
            
            int updated = stmt.executeUpdate();
            
            if (updated > 0) {
                System.out.println("✅ تم تحديث نافذة إعدادات الشركات الموجودة");
            } else {
                System.err.println("❌ فشل في تحديث نافذة إعدادات الشركات");
            }
        }
    }
    
    private static int getNextOrder(Connection conn, int parentId) throws SQLException {
        String sql = "SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        
        return 1;
    }
    
    private static int getLastInsertedId(Connection conn) throws SQLException {
        String sql = "SELECT ERP_SYSTEM_TREE_SEQ.CURRVAL FROM DUAL";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        
        return 0;
    }
    
    private static void addDefaultPermissions(Connection conn, int windowId) throws SQLException {
        System.out.println("🔐 إضافة صلاحيات افتراضية للنافذة...");
        
        // التحقق من وجود جدول الصلاحيات
        try {
            String checkPermissionsSQL = """
                INSERT INTO ERP_WINDOW_PERMISSIONS 
                (PERMISSION_ID, WINDOW_ID, ROLE_ID, CAN_VIEW, CAN_ADD, CAN_EDIT, CAN_DELETE, 
                 CREATED_DATE, CREATED_BY)
                VALUES (ERP_WINDOW_PERMISSIONS_SEQ.NEXTVAL, ?, 1, 'Y', 'Y', 'Y', 'Y', 
                        SYSDATE, 'SYSTEM')
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(checkPermissionsSQL)) {
                stmt.setInt(1, windowId);
                stmt.executeUpdate();
                System.out.println("✅ تم إضافة صلاحيات افتراضية");
            }
            
        } catch (SQLException e) {
            System.out.println("ℹ️ جدول الصلاحيات غير موجود أو لا يحتاج صلاحيات");
        }
    }
    
    private static void displaySystemTreeStructure(Connection conn, int settingsId) throws SQLException {
        System.out.println("\n📊 هيكل فئة الإعدادات العامة بعد الإضافة:");
        System.out.println("========================================");
        
        String sql = """
            SELECT TREE_ID, NODE_NAME_AR, NODE_NAME_EN, WINDOW_CLASS, DISPLAY_ORDER, IS_ACTIVE
            FROM ERP_SYSTEM_TREE 
            WHERE PARENT_ID = ? 
            ORDER BY DISPLAY_ORDER
            """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, settingsId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                int count = 0;
                while (rs.next()) {
                    count++;
                    int treeId = rs.getInt("TREE_ID");
                    String nameAr = rs.getString("NODE_NAME_AR");
                    String nameEn = rs.getString("NODE_NAME_EN");
                    String windowClass = rs.getString("WINDOW_CLASS");
                    int order = rs.getInt("DISPLAY_ORDER");
                    String isActive = rs.getString("IS_ACTIVE");
                    
                    String status = "Y".equals(isActive) ? "✅" : "❌";
                    
                    System.out.println(String.format("  %s %d. %s (%s)", 
                        status, order, nameAr, nameEn));
                    
                    if (windowClass != null && !windowClass.isEmpty()) {
                        System.out.println(String.format("     🔧 Class: %s", windowClass));
                    }
                }
                
                System.out.println("\n📈 إجمالي العناصر في فئة الإعدادات العامة: " + count);
            }
        }
    }
}
