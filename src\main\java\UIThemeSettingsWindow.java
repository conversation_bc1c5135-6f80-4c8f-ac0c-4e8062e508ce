import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.SwingUtilities;

/**
 * نافذة إعدادات المظاهر - تستدعي النافذة الشاملة الجديدة Theme Settings Window - Redirects to New
 * Comprehensive Window
 */
public class UIThemeSettingsWindow extends JFrame {

    public UIThemeSettingsWindow() {
        // إغلاق هذه النافذة فوراً وفتح النافذة الشاملة
        SwingUtilities.invokeLater(() -> {
            this.dispose(); // إغلاق النافذة القديمة
            openComprehensiveThemeWindow(); // فتح النافذة الجديدة
        });
    }

    /**
     * فتح نافذة المظاهر العملية الجديدة
     */
    private void openComprehensiveThemeWindow() {
        try {
            System.out.println("🎨 فتح نافذة المظاهر العملية الجديدة...");
            WorkingThemeWindow workingWindow = new WorkingThemeWindow();
            workingWindow.setVisible(true);
            System.out.println("✅ تم فتح نافذة المظاهر العملية بنجاح");
        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح نافذة المظاهر العملية: " + e.getMessage());
            e.printStackTrace();

            // في حالة الفشل، أظهر رسالة خطأ
            JOptionPane.showMessageDialog(null,
                    "خطأ في فتح نافذة المظاهر:\n" + e.getMessage()
                            + "\n\nيرجى التأكد من تجميع النظام بشكل صحيح.",
                    "خطأ في المظاهر - Theme Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * الدالة الرئيسية للاختبار
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق مظهر افتراضي
                com.formdev.flatlaf.FlatLightLaf.setup();
            } catch (Exception e) {
                e.printStackTrace();
            }

            new UIThemeSettingsWindow();
        });
    }
}
