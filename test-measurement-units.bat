@echo off
echo ========================================
echo    TEST MEASUREMENT UNITS WINDOW
echo    اختبار نافذة وحدات القياس
echo ========================================

cd /d "e:\ship_erp\java"

set CP=.;lib\ojdbc11.jar;lib\orai18n.jar;lib\commons-logging-1.2.jar

echo [INFO] Testing Measurement Units Window Integration...
echo.

echo [1] Cleaning and compiling...
if exist "*.class" del /q *.class

echo [2] Compiling core components...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\DatabaseConfig.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ItemData.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UIUtils.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\SettingsManager.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\GeneralSettingsWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\User.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserFormDialog.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserPermissionsDialog.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserManagementWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\RealItemDataWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ComprehensiveItemDataWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ItemGroupsManagementWindow.java

echo [3] Compiling Measurement Units Window...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\MeasurementUnitsWindow.java

echo [4] Compiling Tree Menu with Measurement Units support...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\TreeMenuPanel.java

echo [5] Compiling Enhanced Main Window...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\EnhancedMainWindow.java

echo [6] Compiling Complete Oracle System...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\CompleteOracleSystemTest.java

echo.
echo [SUCCESS] All components compiled with Measurement Units support!
echo.

echo ========================================
echo    TESTING MEASUREMENT UNITS INTEGRATION
echo ========================================
echo.
echo Features Available:
echo - Measurement Units Window in Tree Menu
echo - Oracle Database Integration
echo - Arabic RTL Interface
echo - Sample Data Support
echo.

echo [INFO] Starting Complete System with Measurement Units...
java -cp "%CP%" -Dfile.encoding=UTF-8 -Doracle.jdbc.defaultNChar=true CompleteOracleSystemTest

echo.
echo ========================================
echo    MEASUREMENT UNITS TEST COMPLETED
echo ========================================
pause
