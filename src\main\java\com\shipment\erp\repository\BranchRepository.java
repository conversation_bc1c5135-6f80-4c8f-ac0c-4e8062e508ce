package com.shipment.erp.repository;

import com.shipment.erp.model.Branch;
import java.util.List;
import java.util.Optional;

/**
 * مستودع بيانات الفروع
 * Branch Data Repository
 */
public interface BranchRepository extends BaseRepository<Branch> {
    
    /**
     * البحث عن الفروع حسب الشركة
     * Find branches by company
     */
    List<Branch> findByCompanyId(Long companyId);
    
    /**
     * البحث عن الفروع النشطة حسب الشركة
     * Find active branches by company
     */
    List<Branch> findByCompanyIdAndIsActiveTrue(Long companyId);
    
    /**
     * البحث عن فرع بالكود
     * Find branch by code
     */
    Optional<Branch> findByCode(String code);
    
    /**
     * البحث عن الفروع بالاسم (يحتوي على)
     * Find branches by name containing
     */
    List<Branch> findByNameContainingIgnoreCase(String name);
    
    /**
     * التحقق من وجود كود الفرع
     * Check if branch code exists
     */
    boolean existsByCode(String code);
    
    /**
     * عدد الفروع النشطة للشركة
     * Count active branches for company
     */
    long countByCompanyIdAndIsActiveTrue(Long companyId);
    
    /**
     * البحث عن الفروع حسب المدير
     * Find branches by manager name
     */
    List<Branch> findByManagerNameContainingIgnoreCase(String managerName);
    
    /**
     * البحث عن الفروع حسب الهاتف
     * Find branches by phone
     */
    Optional<Branch> findByPhone(String phone);
    
    /**
     * البحث عن الفروع حسب البريد الإلكتروني
     * Find branches by email
     */
    Optional<Branch> findByEmail(String email);
}
