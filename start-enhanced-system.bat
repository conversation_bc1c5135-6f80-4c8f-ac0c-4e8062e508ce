@echo off
echo ========================================
echo    ENHANCED SHIP ERP SYSTEM
echo    نظام إدارة الشحنات المحسن
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Starting Enhanced Ship ERP System...
echo [معلومات] بدء تشغيل نظام إدارة الشحنات المحسن...
echo.

echo ========================================
echo    PHASE 1: SYSTEM INITIALIZATION
echo    المرحلة 1: تهيئة النظام
echo ========================================

echo [1] Initializing enhanced components...

REM تعيين الترميز والمحلية العربية
set JAVA_OPTS=-Dfile.encoding=UTF-8 -Doracle.jdbc.defaultNChar=true -Duser.language=ar -Duser.country=SA

REM إعداد CLASSPATH
set CLASSPATH=.
for %%f in (lib\*.jar) do (
    set CLASSPATH=!CLASSPATH!;%%f
)

echo Java Options: %JAVA_OPTS%
echo Classpath configured with enhanced libraries

echo.
echo ========================================
echo    PHASE 2: SECURITY INITIALIZATION
echo    المرحلة 2: تهيئة الأمان
echo ========================================

echo [2] Testing security components...

java %JAVA_OPTS% -cp "%CLASSPATH%" SecurityManager
if errorlevel 1 (
    echo ERROR: Security system failed to initialize
    pause
    exit /b 1
)

echo.
echo ========================================
echo    PHASE 3: DATABASE PREPARATION
echo    المرحلة 3: تحضير قاعدة البيانات
echo ========================================

echo [3] Initializing connection pool and configuration...

REM إنشاء ملفات التكوين إذا لم تكن موجودة
if not exist "database_config.properties" (
    echo Creating database configuration...
    java %JAVA_OPTS% -cp "%CLASSPATH%" EnhancedConfigManager
)

echo Database configuration ready

echo.
echo ========================================
echo    PHASE 4: PERFORMANCE MONITORING
echo    المرحلة 4: مراقبة الأداء
echo ========================================

echo [4] Starting performance monitoring...

echo Performance monitoring initialized

echo.
echo ========================================
echo    PHASE 5: SYSTEM STARTUP
echo    المرحلة 5: بدء تشغيل النظام
echo ========================================

echo [5] Starting Enhanced Oracle System...

echo System Information:
echo معلومات النظام:
echo - Database: Oracle ORCL (localhost:1521)
echo - SHIP_ERP: Encrypted password
echo - IAS20251: Encrypted password
echo - Connection Pooling: Enabled
echo - Performance Monitoring: Active
echo - Security: Enhanced encryption
echo - Configuration: Dynamic management

echo.
echo Starting Complete Oracle System with Enhancements...

java %JAVA_OPTS% -cp "%CLASSPATH%" CompleteOracleSystemTest

echo.
echo ========================================
echo    POST-STARTUP VERIFICATION
echo    التحقق بعد التشغيل
echo ========================================

echo [6] System verification...

REM فحص الملفات المُنشأة
if exist "database_config.properties" (
    echo ✅ Database configuration file created
) else (
    echo ⚠️ Database configuration file missing
)

if exist "security_config.properties" (
    echo ✅ Security configuration file created
) else (
    echo ⚠️ Security configuration file missing
)

echo.
echo ========================================
echo    ENHANCED SYSTEM FEATURES
echo    ميزات النظام المحسن
echo ========================================

echo.
echo 🚀 Enhanced Features Active:
echo الميزات المحسنة النشطة:
echo.
echo 🔐 Security Enhancements:
echo التحسينات الأمنية:
echo   ✅ Password encryption with AES-256
echo   ✅ Secure configuration management
echo   ✅ Security event logging
echo   ✅ Password strength validation
echo.
echo 🔗 Connection Management:
echo إدارة الاتصالات:
echo   ✅ Connection pooling (2-10 connections)
echo   ✅ Automatic connection validation
echo   ✅ Connection timeout handling
echo   ✅ Resource leak prevention
echo.
echo 📊 Performance Monitoring:
echo مراقبة الأداء:
echo   ✅ Real-time performance metrics
echo   ✅ Query execution monitoring
echo   ✅ Memory usage tracking
echo   ✅ Slow query detection
echo.
echo ⚙️ Configuration Management:
echo إدارة التكوين:
echo   ✅ Dynamic configuration loading
echo   ✅ Configuration backup/restore
echo   ✅ Environment-specific settings
echo   ✅ Hot configuration reload
echo.
echo 🎯 System Reliability:
echo موثوقية النظام:
echo   ✅ Graceful error handling
echo   ✅ Automatic resource cleanup
echo   ✅ System health monitoring
echo   ✅ Performance optimization
echo.

echo ========================================
echo    SYSTEM READY
echo    النظام جاهز
echo ========================================

echo.
echo [SUCCESS] Enhanced Ship ERP System is now running!
echo [نجح] نظام إدارة الشحنات المحسن يعمل الآن!
echo.
echo Available Commands:
echo الأوامر المتاحة:
echo - performance-report.bat (Performance Report)
echo - security-test.bat (Security Testing)
echo - config-backup.bat (Configuration Backup)
echo - system-health-check.bat (System Health Check)
echo.
echo The system is now running with enhanced security,
echo performance monitoring, and connection pooling.
echo.
echo النظام يعمل الآن مع الأمان المحسن،
echo مراقبة الأداء، وتجميع الاتصالات.
echo.

pause
