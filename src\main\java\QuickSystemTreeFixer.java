import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * أداة إصلاح سريعة لجدول شجرة الأنظمة
 * Quick System Tree Fixer - Command Line Tool
 */
public class QuickSystemTreeFixer {
    
    private Connection dbConnection;
    private int totalFixed = 0;
    
    public static void main(String[] args) {
        System.out.println("🔧 أداة الإصلاح السريعة لشجرة الأنظمة");
        System.out.println("Quick System Tree Fixer");
        System.out.println("==========================================");
        
        QuickSystemTreeFixer fixer = new QuickSystemTreeFixer();
        fixer.performAllFixes();
    }
    
    public void performAllFixes() {
        try {
            // الاتصال بقاعدة البيانات
            connectToDatabase();
            
            System.out.println("\n🚀 بدء عملية الإصلاح الشاملة...");
            System.out.println("==========================================");
            
            // إنشاء نسخة احتياطية (تم تعطيلها مؤقتاً)
            // createBackup();
            System.out.println("\n📦 تم تخطي النسخة الاحتياطية (مؤقتاً)");
            
            // إصلاح الأسماء المكررة
            fixDuplicateNames();
            
            // إضافة الأوصاف المفقودة
            addMissingDescriptions();
            
            // إضافة الأيقونات المفقودة
            addMissingIcons();
            
            // تحسين هيكل الشجرة
            optimizeTreeStructure();
            
            // تأكيد التغييرات
            dbConnection.commit();
            
            System.out.println("\n✅ تم إكمال جميع الإصلاحات بنجاح!");
            System.out.println("إجمالي الإصلاحات: " + totalFixed);
            System.out.println("==========================================");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في الإصلاح: " + e.getMessage());
            e.printStackTrace();
            
            try {
                if (dbConnection != null) {
                    dbConnection.rollback();
                    System.out.println("🔄 تم التراجع عن التغييرات");
                }
            } catch (SQLException rollbackEx) {
                System.err.println("خطأ في التراجع: " + rollbackEx.getMessage());
            }
        } finally {
            closeConnection();
        }
    }
    
    private void connectToDatabase() throws Exception {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            dbConnection = tnsManager.getShipErpConnection();
            dbConnection.setAutoCommit(false); // تعطيل التأكيد التلقائي
            System.out.println("✅ تم الاتصال بقاعدة البيانات SHIP_ERP");
        } catch (Exception e) {
            System.err.println("❌ فشل الاتصال بقاعدة البيانات: " + e.getMessage());
            throw e;
        }
    }
    
    private void createBackup() throws SQLException {
        System.out.println("\n📦 إنشاء نسخة احتياطية...");

        // استخدام تنسيق إنجليزي للتاريخ
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.ENGLISH);
        String timestamp = sdf.format(new Date());

        String backupTableName = "ERP_SYSTEM_TREE_BACKUP_" + timestamp;

        String sql = "CREATE TABLE " + backupTableName + " AS SELECT * FROM ERP_SYSTEM_TREE";
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            stmt.executeUpdate();
            System.out.println("✅ تم إنشاء نسخة احتياطية: " + backupTableName);
        }
    }
    
    private void fixDuplicateNames() throws SQLException {
        System.out.println("\n🔄 إصلاح الأسماء المكررة...");
        
        // الحصول على الأسماء المكررة
        Map<String, List<Integer>> duplicates = getDuplicateNames();
        
        int fixedCount = 0;
        for (Map.Entry<String, List<Integer>> entry : duplicates.entrySet()) {
            String nodeName = entry.getKey();
            List<Integer> nodeIds = entry.getValue();
            
            // ترك العقدة الأولى كما هي وتغيير الباقي
            for (int i = 1; i < nodeIds.size(); i++) {
                String newName = nodeName + " (" + (i + 1) + ")";
                
                String sql = "UPDATE ERP_SYSTEM_TREE SET NODE_NAME_AR = ? WHERE TREE_ID = ?";
                try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
                    stmt.setString(1, newName);
                    stmt.setInt(2, nodeIds.get(i));
                    stmt.executeUpdate();
                    
                    System.out.println("  • تم تغيير العقدة " + nodeIds.get(i) + " إلى: " + newName);
                    fixedCount++;
                }
            }
        }
        
        System.out.println("✅ تم إصلاح " + fixedCount + " اسم مكرر");
        totalFixed += fixedCount;
    }
    
    private Map<String, List<Integer>> getDuplicateNames() throws SQLException {
        Map<String, List<Integer>> duplicates = new HashMap<>();
        
        String sql = """
            SELECT NODE_NAME_AR, COUNT(*) as COUNT
            FROM ERP_SYSTEM_TREE
            GROUP BY NODE_NAME_AR
            HAVING COUNT(*) > 1
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String nodeName = rs.getString("NODE_NAME_AR");
                List<Integer> nodeIds = getNodeIds(nodeName);
                duplicates.put(nodeName, nodeIds);
            }
        }
        
        return duplicates;
    }
    
    private List<Integer> getNodeIds(String nodeName) throws SQLException {
        List<Integer> nodeIds = new ArrayList<>();
        
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? ORDER BY TREE_ID";
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            stmt.setString(1, nodeName);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    nodeIds.add(rs.getInt("TREE_ID"));
                }
            }
        }
        
        return nodeIds;
    }
    
    private void addMissingDescriptions() throws SQLException {
        System.out.println("\n📝 إضافة الأوصاف المفقودة...");
        
        String selectSql = """
            SELECT TREE_ID, NODE_NAME_AR, NODE_TYPE
            FROM ERP_SYSTEM_TREE
            WHERE NODE_DESCRIPTION IS NULL OR TRIM(NODE_DESCRIPTION) = ''
        """;
        
        String updateSql = "UPDATE ERP_SYSTEM_TREE SET NODE_DESCRIPTION = ? WHERE TREE_ID = ?";
        
        int addedCount = 0;
        try (PreparedStatement selectStmt = dbConnection.prepareStatement(selectSql);
             PreparedStatement updateStmt = dbConnection.prepareStatement(updateSql);
             ResultSet rs = selectStmt.executeQuery()) {
            
            while (rs.next()) {
                int treeId = rs.getInt("TREE_ID");
                String nodeName = rs.getString("NODE_NAME_AR");
                String nodeType = rs.getString("NODE_TYPE");
                
                String description = generateDescription(nodeName, nodeType);
                
                updateStmt.setString(1, description);
                updateStmt.setInt(2, treeId);
                updateStmt.executeUpdate();
                
                System.out.println("  • [" + treeId + "] " + nodeName + ": " + description);
                addedCount++;
            }
        }
        
        System.out.println("✅ تم إضافة " + addedCount + " وصف");
        totalFixed += addedCount;
    }
    
    private String generateDescription(String nodeName, String nodeType) {
        if (nodeType == null) return "وصف العقدة";
        
        return switch (nodeType) {
            case "CATEGORY" -> "فئة " + nodeName + " - تحتوي على النوافذ والأدوات المتعلقة بـ" + nodeName;
            case "WINDOW" -> "نافذة " + nodeName + " - واجهة لإدارة وعرض بيانات " + nodeName;
            case "TOOL" -> "أداة " + nodeName + " - أداة مساعدة لتنفيذ مهام " + nodeName;
            case "REPORT" -> "تقرير " + nodeName + " - تقرير مفصل حول " + nodeName;
            default -> "عقدة " + nodeName + " من نوع " + nodeType;
        };
    }
    
    private void addMissingIcons() throws SQLException {
        System.out.println("\n🎨 إضافة الأيقونات المفقودة...");
        
        String selectSql = """
            SELECT TREE_ID, NODE_NAME_AR, NODE_TYPE
            FROM ERP_SYSTEM_TREE
            WHERE ICON_PATH IS NULL OR TRIM(ICON_PATH) = ''
        """;
        
        String updateSql = "UPDATE ERP_SYSTEM_TREE SET ICON_PATH = ? WHERE TREE_ID = ?";
        
        int addedCount = 0;
        Map<String, Integer> iconCounts = new HashMap<>();
        
        try (PreparedStatement selectStmt = dbConnection.prepareStatement(selectSql);
             PreparedStatement updateStmt = dbConnection.prepareStatement(updateSql);
             ResultSet rs = selectStmt.executeQuery()) {
            
            while (rs.next()) {
                int treeId = rs.getInt("TREE_ID");
                String nodeType = rs.getString("NODE_TYPE");
                
                String iconPath = generateIconPath(nodeType);
                
                updateStmt.setString(1, iconPath);
                updateStmt.setInt(2, treeId);
                updateStmt.executeUpdate();
                
                iconCounts.put(iconPath, iconCounts.getOrDefault(iconPath, 0) + 1);
                addedCount++;
            }
        }
        
        // عرض ملخص الأيقونات المضافة
        for (Map.Entry<String, Integer> entry : iconCounts.entrySet()) {
            System.out.println("  • " + entry.getKey() + ": " + entry.getValue() + " عقدة");
        }
        
        System.out.println("✅ تم إضافة " + addedCount + " أيقونة");
        totalFixed += addedCount;
    }
    
    private String generateIconPath(String nodeType) {
        if (nodeType == null) return "icons/default.png";
        
        return switch (nodeType) {
            case "CATEGORY" -> "icons/folder.png";
            case "WINDOW" -> "icons/window.png";
            case "TOOL" -> "icons/tool.png";
            case "REPORT" -> "icons/report.png";
            default -> "icons/default.png";
        };
    }

    private void optimizeTreeStructure() throws SQLException {
        System.out.println("\n🔧 تحسين هيكل الشجرة...");

        // إعادة ترقيم ترتيب العرض
        reorderDisplayOrder();

        // تحديث مستويات الشجرة
        updateTreeLevels();

        // تحديث الإحصائيات
        updateStatistics();

        System.out.println("✅ تم تحسين هيكل الشجرة");
    }

    private void reorderDisplayOrder() throws SQLException {
        String sql = """
            UPDATE ERP_SYSTEM_TREE SET DISPLAY_ORDER = (
                SELECT ROW_NUMBER() OVER (
                    PARTITION BY NVL(PARENT_ID, 0)
                    ORDER BY DISPLAY_ORDER, TREE_ID
                ) * 10
                FROM ERP_SYSTEM_TREE t2
                WHERE t2.TREE_ID = ERP_SYSTEM_TREE.TREE_ID
            )
        """;

        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            int updated = stmt.executeUpdate();
            System.out.println("  • تم إعادة ترقيم " + updated + " عقدة");
            totalFixed += updated;
        }
    }

    private void updateTreeLevels() throws SQLException {
        String sql = """
            UPDATE ERP_SYSTEM_TREE SET TREE_LEVEL = (
                SELECT LEVEL - 1
                FROM ERP_SYSTEM_TREE t2
                WHERE t2.TREE_ID = ERP_SYSTEM_TREE.TREE_ID
                START WITH t2.PARENT_ID IS NULL
                CONNECT BY PRIOR t2.TREE_ID = t2.PARENT_ID
            )
        """;

        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            int updated = stmt.executeUpdate();
            System.out.println("  • تم تحديث مستويات " + updated + " عقدة");
        }
    }

    private void updateStatistics() throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET LAST_UPDATED = SYSDATE, UPDATED_BY = 'QUICK_FIXER'";
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
            int updated = stmt.executeUpdate();
            System.out.println("  • تم تحديث إحصائيات " + updated + " عقدة");
        }
    }

    private void closeConnection() {
        try {
            if (dbConnection != null && !dbConnection.isClosed()) {
                dbConnection.close();
                System.out.println("\n🔌 تم إغلاق الاتصال بقاعدة البيانات");
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
        }
    }
}
