package com.shipment.erp.service;

import com.shipment.erp.model.FiscalYear;
import java.util.Date;
import java.util.List;

/**
 * خدمة إدارة السنوات المالية
 * Fiscal Year Management Service
 */
public interface FiscalYearService extends BaseService<FiscalYear> {
    
    /**
     * الحصول على السنة المالية الحالية
     * Get current fiscal year
     */
    FiscalYear getCurrentFiscalYear();
    
    /**
     * تعيين السنة المالية الحالية
     * Set current fiscal year
     */
    void setCurrentFiscalYear(Long fiscalYearId);
    
    /**
     * البحث عن السنة المالية بالاسم
     * Find fiscal year by name
     */
    FiscalYear findByYearName(String yearName);
    
    /**
     * البحث عن السنوات المالية المفتوحة
     * Find open fiscal years
     */
    List<FiscalYear> findOpenFiscalYears();
    
    /**
     * البحث عن السنوات المالية المغلقة
     * Find closed fiscal years
     */
    List<FiscalYear> findClosedFiscalYears();
    
    /**
     * البحث عن السنة المالية التي تحتوي على تاريخ معين
     * Find fiscal year containing specific date
     */
    FiscalYear findByDate(Date date);
    
    /**
     * التحقق من تداخل التواريخ
     * Check date overlap
     */
    boolean hasDateOverlap(Date startDate, Date endDate, Long excludeId);
    
    /**
     * إغلاق السنة المالية
     * Close fiscal year
     */
    void closeFiscalYear(Long fiscalYearId);
    
    /**
     * فتح السنة المالية
     * Open fiscal year
     */
    void openFiscalYear(Long fiscalYearId);
    
    /**
     * التحقق من إمكانية حذف السنة المالية
     * Check if fiscal year can be deleted
     */
    boolean canDelete(Long fiscalYearId);
    
    /**
     * إنشاء سنة مالية جديدة تلقائياً
     * Create new fiscal year automatically
     */
    FiscalYear createNextFiscalYear();
    
    /**
     * الحصول على السنوات المالية حسب النطاق الزمني
     * Get fiscal years by date range
     */
    List<FiscalYear> findByDateRange(Date startDate, Date endDate);
}
