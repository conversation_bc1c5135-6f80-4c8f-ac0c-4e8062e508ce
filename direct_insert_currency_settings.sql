-- إدراج مباشر لنافذة إعدادات العملة في جدول ERP_SYSTEM_TREE
-- Direct Insert for Currency Settings Window in ERP_SYSTEM_TREE Table

-- البحث عن معرف فئة الإعدادات العامة
SELECT TREE_ID, NODE_NAME_AR, PARENT_ID 
FROM ERP_SYSTEM_TREE 
WHERE NODE_NAME_AR = 'الإعدادات العامة';

-- إدراج نافذة إعدادات العملة مباشرة
INSERT INTO ERP_SYSTEM_TREE (
    TREE_ID,
    PARENT_ID,
    NODE_NAME_AR,
    NODE_NAME_EN,
    NODE_DESCRIPTION,
    NODE_TYPE,
    WINDOW_CLASS,
    ICON_PATH,
    DISPLAY_ORDER,
    TREE_LEVEL,
    IS_ACTIVE,
    IS_VISIBLE,
    ACCESS_PERMISSIONS,
    ADDITIONAL_INFO,
    CREATED_DATE,
    CREATED_BY,
    LAST_UPDATED,
    UPDATED_BY,
    VERSION_NUMBER
) VALUES (
    (SELECT NVL(MAX(TREE_ID), 0) + 1 FROM ERP_SYSTEM_TREE),
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'الإعدادات العامة'),
    'إعدادات العملة',
    'Currency Settings',
    'إعدادات وإدارة العملات المستخدمة في النظام - قيد التطوير',
    'WINDOW',
    'CurrencySettingsWindow',
    'icons/currency.png',
    (SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID = (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'الإعدادات العامة')),
    2,
    'Y',
    'Y',
    'ADMIN,SETTINGS_MANAGER',
    'STATUS:UNDER_DEVELOPMENT;PRIORITY:MEDIUM;MODULE:SETTINGS;VERSION:1.0',
    SYSDATE,
    USER,
    SYSDATE,
    USER,
    1
);

-- تأكيد الإدراج
COMMIT;

-- التحقق من الإدراج
SELECT 
    TREE_ID,
    NODE_NAME_AR,
    NODE_NAME_EN,
    WINDOW_CLASS,
    DISPLAY_ORDER,
    ADDITIONAL_INFO
FROM ERP_SYSTEM_TREE 
WHERE NODE_NAME_AR = 'إعدادات العملة';
