import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * إدراج مباشر لنافذة إعدادات العملة
 * Direct Insert for Currency Settings Window
 */
public class DirectInsertCurrency {
    
    private static final String DB_URL = "*************************************";
    private static final String DB_USER = "ship_erp";
    private static final String DB_PASSWORD = "ship_erp_password";
    
    public static void main(String[] args) {
        System.out.println("💰 إدراج نافذة إعدادات العملة مباشرة");
        System.out.println("Direct Insert Currency Settings Window");
        System.out.println("=====================================");
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            
            // 1. البحث عن فئة الإعدادات العامة
            int generalSettingsId = findGeneralSettingsId(conn);
            
            // 2. إدراج النافذة مباشرة
            insertCurrencyWindow(conn, generalSettingsId);
            
            // 3. التحقق من الإدراج
            verifyCurrencyWindow(conn);
            
            System.out.println("\n✅ تم إدراج نافذة إعدادات العملة بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static int findGeneralSettingsId(Connection conn) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'الإعدادات العامة'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                int id = rs.getInt("TREE_ID");
                System.out.println("✅ تم العثور على فئة الإعدادات العامة - ID: " + id);
                return id;
            } else {
                throw new SQLException("❌ لم يتم العثور على فئة الإعدادات العامة");
            }
        }
    }
    
    private static void insertCurrencyWindow(Connection conn, int parentId) throws SQLException {
        // فحص وجود النافذة مسبقاً
        String checkSql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'إعدادات العملة'";
        try (PreparedStatement checkStmt = conn.prepareStatement(checkSql);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next() && rs.getInt(1) > 0) {
                System.out.println("⚠️ نافذة إعدادات العملة موجودة مسبقاً");
                return;
            }
        }
        
        String insertSql = """
            INSERT INTO ERP_SYSTEM_TREE (
                TREE_ID,
                PARENT_ID,
                NODE_NAME_AR,
                NODE_NAME_EN,
                NODE_DESCRIPTION,
                NODE_TYPE,
                WINDOW_CLASS,
                DISPLAY_ORDER,
                TREE_LEVEL,
                IS_ACTIVE,
                IS_VISIBLE,
                ADDITIONAL_INFO,
                CREATED_DATE,
                CREATED_BY,
                LAST_UPDATED,
                UPDATED_BY
            ) VALUES (
                (SELECT NVL(MAX(TREE_ID), 0) + 1 FROM ERP_SYSTEM_TREE),
                ?,
                'إعدادات العملة',
                'Currency Settings',
                'إعدادات وإدارة العملات المستخدمة في النظام - قيد التطوير',
                'WINDOW',
                'CurrencySettingsWindow',
                (SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID = ?),
                2,
                'Y',
                'Y',
                'STATUS:UNDER_DEVELOPMENT;PRIORITY:MEDIUM;MODULE:SETTINGS',
                SYSDATE,
                'SYSTEM',
                SYSDATE,
                'SYSTEM'
            )
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(insertSql)) {
            stmt.setInt(1, parentId);
            stmt.setInt(2, parentId);
            
            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                conn.commit();
                System.out.println("✅ تم إدراج نافذة إعدادات العملة بنجاح");
            } else {
                throw new SQLException("فشل في إدراج النافذة");
            }
        }
    }
    
    private static void verifyCurrencyWindow(Connection conn) throws SQLException {
        String sql = """
            SELECT 
                TREE_ID,
                NODE_NAME_AR,
                NODE_NAME_EN,
                WINDOW_CLASS,
                DISPLAY_ORDER,
                ADDITIONAL_INFO
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR = 'إعدادات العملة'
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                System.out.println("\n📋 تفاصيل النافذة المدرجة:");
                System.out.println("========================");
                System.out.println("🆔 معرف العقدة: " + rs.getInt("TREE_ID"));
                System.out.println("🏷️ الاسم العربي: " + rs.getString("NODE_NAME_AR"));
                System.out.println("🏷️ الاسم الإنجليزي: " + rs.getString("NODE_NAME_EN"));
                System.out.println("💻 كلاس النافذة: " + rs.getString("WINDOW_CLASS"));
                System.out.println("📊 ترتيب العرض: " + rs.getInt("DISPLAY_ORDER"));
                System.out.println("ℹ️ معلومات إضافية: " + rs.getString("ADDITIONAL_INFO"));
            } else {
                throw new SQLException("❌ لم يتم العثور على النافذة المدرجة!");
            }
        }
    }
}
