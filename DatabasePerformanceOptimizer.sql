-- أداة تحسين أداء قاعدة البيانات
-- Database Performance Optimization Tool

PROMPT ========================================
PROMPT   DATABASE PERFORMANCE OPTIMIZER
PROMPT   أداة تحسين أداء قاعدة البيانات
PROMPT ========================================

-- تفعيل عرض النتائج
SET SERVEROUTPUT ON
SET PAGESIZE 1000
SET LINESIZE 200

-- 1. إنشاء فهارس للأداء
PROMPT Creating performance indexes...
PROMPT إنشاء فهارس للأداء...

-- فهارس جدول IAS_ITM_MST
CREATE INDEX IDX_ITM_MST_CODE ON IAS_ITM_MST(ITM_CD);
CREATE INDEX IDX_ITM_MST_NAME ON IAS_ITM_MST(ITM_NM);
CREATE INDEX IDX_ITM_MST_GROUP ON IAS_ITM_MST(ITM_GRP_CD);
CREATE INDEX IDX_ITM_MST_SUBGROUP ON IAS_ITM_MST(ITM_SUB_GRP_CD);
CREATE INDEX IDX_ITM_MST_STATUS ON IAS_ITM_MST(ITM_STATUS);
CREATE INDEX IDX_ITM_MST_CREATED ON IAS_ITM_MST(CREATED_DATE);

-- فهارس جدول IAS_ITM_DTL
CREATE INDEX IDX_ITM_DTL_CODE ON IAS_ITM_DTL(ITM_CD);
CREATE INDEX IDX_ITM_DTL_STATUS ON IAS_ITM_DTL(ITM_DTL_STATUS);
CREATE INDEX IDX_ITM_DTL_CREATED ON IAS_ITM_DTL(CREATED_DATE);

-- فهارس جدول MEASUREMENT
CREATE INDEX IDX_MEASUREMENT_TYPE ON MEASUREMENT(UNIT_TYPE);
CREATE INDEX IDX_MEASUREMENT_STATUS ON MEASUREMENT(STATUS);

-- فهارس مركبة للاستعلامات المعقدة
CREATE INDEX IDX_ITM_MST_COMPOSITE ON IAS_ITM_MST(ITM_GRP_CD, ITM_SUB_GRP_CD, ITM_STATUS);
CREATE INDEX IDX_ITM_DTL_COMPOSITE ON IAS_ITM_DTL(ITM_CD, ITM_DTL_STATUS);

PROMPT Indexes created successfully!
PROMPT تم إنشاء الفهارس بنجاح!

-- 2. تحديث إحصائيات الجداول
PROMPT Updating table statistics...
PROMPT تحديث إحصائيات الجداول...

BEGIN
    -- تحديث إحصائيات الجداول الرئيسية
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'IAS_ITM_MST',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'IAS_ITM_DTL',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'MEASUREMENT',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_OUTPUT.PUT_LINE('Table statistics updated successfully!');
    DBMS_OUTPUT.PUT_LINE('تم تحديث إحصائيات الجداول بنجاح!');
END;
/

-- 3. إنشاء Views محسنة للأداء
PROMPT Creating optimized views...
PROMPT إنشاء Views محسنة للأداء...

-- View للأصناف النشطة
CREATE OR REPLACE VIEW V_ACTIVE_ITEMS AS
SELECT 
    ITM_CD,
    ITM_NM,
    ITM_NM_AR,
    ITM_DESC,
    ITM_GRP_CD,
    ITM_SUB_GRP_CD,
    ITM_UNIT,
    ITM_PRICE,
    ITM_COST,
    CREATED_DATE
FROM IAS_ITM_MST 
WHERE ITM_STATUS = 'A';

-- View لتفاصيل الأصناف النشطة
CREATE OR REPLACE VIEW V_ACTIVE_ITEM_DETAILS AS
SELECT 
    m.ITM_CD,
    m.ITM_NM,
    m.ITM_NM_AR,
    d.ITM_DTL_CD,
    d.ITM_DTL_NM,
    d.ITM_DTL_NM_AR,
    d.ITM_DTL_QTY,
    d.ITM_DTL_UNIT,
    d.ITM_DTL_PRICE
FROM IAS_ITM_MST m
JOIN IAS_ITM_DTL d ON m.ITM_CD = d.ITM_CD
WHERE m.ITM_STATUS = 'A' AND d.ITM_DTL_STATUS = 'A';

-- View للإحصائيات السريعة
CREATE OR REPLACE VIEW V_ITEM_STATISTICS AS
SELECT 
    ITM_GRP_CD,
    COUNT(*) as TOTAL_ITEMS,
    AVG(ITM_PRICE) as AVG_PRICE,
    MIN(ITM_PRICE) as MIN_PRICE,
    MAX(ITM_PRICE) as MAX_PRICE,
    SUM(CASE WHEN ITM_STATUS = 'A' THEN 1 ELSE 0 END) as ACTIVE_ITEMS
FROM IAS_ITM_MST 
GROUP BY ITM_GRP_CD;

PROMPT Views created successfully!
PROMPT تم إنشاء Views بنجاح!

-- 4. إنشاء Stored Procedures محسنة
PROMPT Creating optimized stored procedures...
PROMPT إنشاء Stored Procedures محسنة...

-- إجراء للبحث السريع في الأصناف
CREATE OR REPLACE PROCEDURE SP_SEARCH_ITEMS(
    p_search_term IN VARCHAR2,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_cursor FOR
    SELECT ITM_CD, ITM_NM, ITM_NM_AR, ITM_PRICE, ITM_UNIT
    FROM V_ACTIVE_ITEMS
    WHERE UPPER(ITM_NM) LIKE '%' || UPPER(p_search_term) || '%'
       OR UPPER(ITM_NM_AR) LIKE '%' || UPPER(p_search_term) || '%'
       OR UPPER(ITM_CD) LIKE '%' || UPPER(p_search_term) || '%'
    ORDER BY ITM_CD;
END;
/

-- إجراء لإحصائيات المجموعات
CREATE OR REPLACE PROCEDURE SP_GROUP_STATISTICS(
    p_group_code IN VARCHAR2 DEFAULT NULL,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    IF p_group_code IS NULL THEN
        OPEN p_cursor FOR
        SELECT * FROM V_ITEM_STATISTICS
        ORDER BY TOTAL_ITEMS DESC;
    ELSE
        OPEN p_cursor FOR
        SELECT * FROM V_ITEM_STATISTICS
        WHERE ITM_GRP_CD = p_group_code;
    END IF;
END;
/

-- إجراء لتحديث أسعار المجموعة
CREATE OR REPLACE PROCEDURE SP_UPDATE_GROUP_PRICES(
    p_group_code IN VARCHAR2,
    p_price_factor IN NUMBER,
    p_updated_count OUT NUMBER
) AS
BEGIN
    UPDATE IAS_ITM_MST 
    SET ITM_PRICE = ITM_PRICE * p_price_factor,
        MODIFIED_DATE = SYSDATE,
        MODIFIED_BY = USER
    WHERE ITM_GRP_CD = p_group_code
      AND ITM_STATUS = 'A';
    
    p_updated_count := SQL%ROWCOUNT;
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('Updated ' || p_updated_count || ' items in group ' || p_group_code);
END;
/

PROMPT Stored procedures created successfully!
PROMPT تم إنشاء Stored Procedures بنجاح!

-- 5. إنشاء Triggers للتدقيق
PROMPT Creating audit triggers...
PROMPT إنشاء Triggers للتدقيق...

-- Trigger لتسجيل تعديلات الأصناف
CREATE OR REPLACE TRIGGER TRG_ITM_MST_AUDIT
    BEFORE UPDATE ON IAS_ITM_MST
    FOR EACH ROW
BEGIN
    :NEW.MODIFIED_DATE := SYSDATE;
    :NEW.MODIFIED_BY := USER;
END;
/

-- Trigger لتسجيل تعديلات تفاصيل الأصناف
CREATE OR REPLACE TRIGGER TRG_ITM_DTL_AUDIT
    BEFORE UPDATE ON IAS_ITM_DTL
    FOR EACH ROW
BEGIN
    :NEW.MODIFIED_DATE := SYSDATE;
    :NEW.MODIFIED_BY := USER;
END;
/

PROMPT Audit triggers created successfully!
PROMPT تم إنشاء Triggers التدقيق بنجاح!

-- 6. تحسين إعدادات الجلسة
PROMPT Optimizing session settings...
PROMPT تحسين إعدادات الجلسة...

-- تحسين إعدادات الذاكرة
ALTER SESSION SET SORT_AREA_SIZE = 1048576;
ALTER SESSION SET HASH_AREA_SIZE = 1048576;

-- تحسين إعدادات المعالج
ALTER SESSION SET OPTIMIZER_MODE = ALL_ROWS;
ALTER SESSION SET OPTIMIZER_INDEX_COST_ADJ = 10;

PROMPT Session settings optimized!
PROMPT تم تحسين إعدادات الجلسة!

-- 7. اختبار الأداء
PROMPT Testing performance...
PROMPT اختبار الأداء...

-- اختبار سرعة الاستعلامات
SET TIMING ON

SELECT 'Performance Test 1: Count all items' as test_name FROM DUAL;
SELECT COUNT(*) as total_items FROM IAS_ITM_MST;

SELECT 'Performance Test 2: Active items by group' as test_name FROM DUAL;
SELECT ITM_GRP_CD, COUNT(*) as item_count 
FROM V_ACTIVE_ITEMS 
GROUP BY ITM_GRP_CD 
ORDER BY item_count DESC;

SELECT 'Performance Test 3: Price statistics' as test_name FROM DUAL;
SELECT 
    MIN(ITM_PRICE) as min_price,
    MAX(ITM_PRICE) as max_price,
    AVG(ITM_PRICE) as avg_price,
    COUNT(*) as total_items
FROM V_ACTIVE_ITEMS;

SET TIMING OFF

-- 8. تقرير التحسين
PROMPT ========================================
PROMPT   OPTIMIZATION REPORT
PROMPT   تقرير التحسين
PROMPT ========================================

SELECT 'Database Performance Optimization Completed' as status FROM DUAL;

-- عرض الفهارس المنشأة
SELECT 'Created Indexes:' as info FROM DUAL;
SELECT INDEX_NAME, TABLE_NAME, UNIQUENESS, STATUS 
FROM USER_INDEXES 
WHERE INDEX_NAME LIKE 'IDX_%'
ORDER BY TABLE_NAME, INDEX_NAME;

-- عرض Views المنشأة
SELECT 'Created Views:' as info FROM DUAL;
SELECT VIEW_NAME, TEXT_LENGTH 
FROM USER_VIEWS 
WHERE VIEW_NAME LIKE 'V_%'
ORDER BY VIEW_NAME;

-- عرض Procedures المنشأة
SELECT 'Created Procedures:' as info FROM DUAL;
SELECT OBJECT_NAME, OBJECT_TYPE, STATUS, CREATED 
FROM USER_OBJECTS 
WHERE OBJECT_TYPE IN ('PROCEDURE', 'TRIGGER')
  AND OBJECT_NAME LIKE 'SP_%' OR OBJECT_NAME LIKE 'TRG_%'
ORDER BY OBJECT_TYPE, OBJECT_NAME;

PROMPT ========================================
PROMPT   OPTIMIZATION COMPLETED SUCCESSFULLY
PROMPT   تم الانتهاء من التحسين بنجاح
PROMPT ========================================

PROMPT
PROMPT Next steps:
PROMPT الخطوات التالية:
PROMPT 1. Test application performance
PROMPT    اختبار أداء التطبيق
PROMPT 2. Monitor query execution plans
PROMPT    مراقبة خطط تنفيذ الاستعلامات
PROMPT 3. Regular statistics updates
PROMPT    تحديث الإحصائيات بانتظام
PROMPT

SPOOL OFF
EXIT;
