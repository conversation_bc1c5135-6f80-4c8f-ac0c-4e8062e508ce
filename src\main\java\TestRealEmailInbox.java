import javax.swing.*;

/**
 * اختبار نافذة صندوق البريد الوارد الحقيقية
 * Test Real Email Inbox Window
 */
public class TestRealEmailInbox {
    
    public static void main(String[] args) {
        System.out.println("🚀 بدء اختبار نافذة صندوق البريد الوارد الحقيقية مع JavaMail");
        System.out.println("📧 Real Email Inbox Window Test with JavaMail");
        
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق مظهر حديث
                UIManager.setLookAndFeel("com.formdev.flatlaf.FlatDarkLaf");
                System.out.println("✅ تم تطبيق المظهر الحديث");
                
                // إنشاء وعرض النافذة الحقيقية
                System.out.println("🔄 إنشاء نافذة صندوق البريد الوارد الحقيقية...");
                RealEmailInboxWindow window = new RealEmailInboxWindow();
                window.setVisible(true);
                
                System.out.println("✅ تم فتح نافذة صندوق البريد الوارد الحقيقية بنجاح!");
                System.out.println("📋 الميزات المتاحة:");
                System.out.println("   • قراءة حقيقية من حسابات البريد الإلكتروني باستخدام JavaMail");
                System.out.println("   • دعم Gmail, Outlook, Yahoo وخوادم IMAP أخرى");
                System.out.println("   • واجهة متقدمة مع فلاتر وبحث");
                System.out.println("   • معاينة الرسائل مع دعم HTML");
                System.out.println("   • إدارة المجلدات والتصنيفات");
                System.out.println("   • إحصائيات مفصلة");
                
            } catch (Exception e) {
                System.err.println("❌ خطأ في تشغيل النافذة: " + e.getMessage());
                e.printStackTrace();
                
                // عرض رسالة خطأ
                JOptionPane.showMessageDialog(null, 
                    "خطأ في تشغيل نافذة صندوق البريد الوارد الحقيقية:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
