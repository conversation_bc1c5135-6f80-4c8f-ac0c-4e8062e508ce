import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * مدير تجميع الاتصالات - Connection Pool Manager
 * يدير مجموعة من الاتصالات لتحسين الأداء
 */
public class ConnectionPoolManager {
    
    private static ConnectionPoolManager instance;
    private ConcurrentLinkedQueue<Connection> shipErpPool;
    private ConcurrentLinkedQueue<Connection> ias20251Pool;
    
    private final int MAX_POOL_SIZE = 10;
    private final int MIN_POOL_SIZE = 2;
    
    private AtomicInteger shipErpActiveConnections = new AtomicInteger(0);
    private AtomicInteger ias20251ActiveConnections = new AtomicInteger(0);
    
    // معلومات الاتصال
    private final String ORACLE_URL = "*************************************";
    private final String SHIP_ERP_USER = "SHIP_ERP";
    private final String SHIP_ERP_PASSWORD = "ship_erp_password";
    private final String IAS20251_USER = "ias20251";
    private final String IAS20251_PASSWORD = "ys123";
    
    private ConnectionPoolManager() {
        initializePools();
    }
    
    public static synchronized ConnectionPoolManager getInstance() {
        if (instance == null) {
            instance = new ConnectionPoolManager();
        }
        return instance;
    }
    
    /**
     * تهيئة مجمعات الاتصالات
     */
    private void initializePools() {
        try {
            // تحميل Oracle JDBC Driver
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            // تهيئة مجمع SHIP_ERP
            shipErpPool = new ConcurrentLinkedQueue<>();
            for (int i = 0; i < MIN_POOL_SIZE; i++) {
                Connection conn = createShipErpConnection();
                if (conn != null) {
                    shipErpPool.offer(conn);
                }
            }
            
            // تهيئة مجمع IAS20251
            ias20251Pool = new ConcurrentLinkedQueue<>();
            for (int i = 0; i < MIN_POOL_SIZE; i++) {
                Connection conn = createIas20251Connection();
                if (conn != null) {
                    ias20251Pool.offer(conn);
                }
            }
            
            System.out.println("✅ تم تهيئة مجمعات الاتصالات بنجاح");
            System.out.println("   - SHIP_ERP Pool: " + shipErpPool.size() + " connections");
            System.out.println("   - IAS20251 Pool: " + ias20251Pool.size() + " connections");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة مجمعات الاتصالات: " + e.getMessage());
        }
    }
    
    /**
     * إنشاء اتصال SHIP_ERP جديد
     */
    private Connection createShipErpConnection() throws SQLException {
        Properties props = new Properties();
        props.setProperty("user", SHIP_ERP_USER);
        props.setProperty("password", SHIP_ERP_PASSWORD);
        props.setProperty("oracle.jdbc.defaultNChar", "true");
        props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
        
        return DriverManager.getConnection(ORACLE_URL, props);
    }
    
    /**
     * إنشاء اتصال IAS20251 جديد
     */
    private Connection createIas20251Connection() throws SQLException {
        Properties props = new Properties();
        props.setProperty("user", IAS20251_USER);
        props.setProperty("password", IAS20251_PASSWORD);
        props.setProperty("oracle.jdbc.defaultNChar", "true");
        props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
        
        return DriverManager.getConnection(ORACLE_URL, props);
    }
    
    /**
     * الحصول على اتصال SHIP_ERP من المجمع
     */
    public Connection getShipErpConnection() throws SQLException {
        Connection conn = shipErpPool.poll();
        
        if (conn == null || conn.isClosed()) {
            // إنشاء اتصال جديد إذا لم يوجد في المجمع
            if (shipErpActiveConnections.get() < MAX_POOL_SIZE) {
                conn = createShipErpConnection();
                shipErpActiveConnections.incrementAndGet();
            } else {
                throw new SQLException("تم الوصول للحد الأقصى من الاتصالات لـ SHIP_ERP");
            }
        }
        
        return conn;
    }
    
    /**
     * الحصول على اتصال IAS20251 من المجمع
     */
    public Connection getIas20251Connection() throws SQLException {
        Connection conn = ias20251Pool.poll();
        
        if (conn == null || conn.isClosed()) {
            // إنشاء اتصال جديد إذا لم يوجد في المجمع
            if (ias20251ActiveConnections.get() < MAX_POOL_SIZE) {
                conn = createIas20251Connection();
                ias20251ActiveConnections.incrementAndGet();
            } else {
                throw new SQLException("تم الوصول للحد الأقصى من الاتصالات لـ IAS20251");
            }
        }
        
        return conn;
    }
    
    /**
     * إرجاع اتصال SHIP_ERP إلى المجمع
     */
    public void returnShipErpConnection(Connection conn) {
        if (conn != null) {
            try {
                if (!conn.isClosed()) {
                    shipErpPool.offer(conn);
                } else {
                    shipErpActiveConnections.decrementAndGet();
                }
            } catch (SQLException e) {
                System.err.println("خطأ في إرجاع اتصال SHIP_ERP: " + e.getMessage());
                shipErpActiveConnections.decrementAndGet();
            }
        }
    }
    
    /**
     * إرجاع اتصال IAS20251 إلى المجمع
     */
    public void returnIas20251Connection(Connection conn) {
        if (conn != null) {
            try {
                if (!conn.isClosed()) {
                    ias20251Pool.offer(conn);
                } else {
                    ias20251ActiveConnections.decrementAndGet();
                }
            } catch (SQLException e) {
                System.err.println("خطأ في إرجاع اتصال IAS20251: " + e.getMessage());
                ias20251ActiveConnections.decrementAndGet();
            }
        }
    }
    
    /**
     * إغلاق جميع الاتصالات في المجمعات
     */
    public void closeAllConnections() {
        System.out.println("🔄 إغلاق جميع الاتصالات في المجمعات...");
        
        // إغلاق اتصالات SHIP_ERP
        while (!shipErpPool.isEmpty()) {
            Connection conn = shipErpPool.poll();
            try {
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                }
            } catch (SQLException e) {
                System.err.println("خطأ في إغلاق اتصال SHIP_ERP: " + e.getMessage());
            }
        }
        
        // إغلاق اتصالات IAS20251
        while (!ias20251Pool.isEmpty()) {
            Connection conn = ias20251Pool.poll();
            try {
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                }
            } catch (SQLException e) {
                System.err.println("خطأ في إغلاق اتصال IAS20251: " + e.getMessage());
            }
        }
        
        System.out.println("✅ تم إغلاق جميع الاتصالات بنجاح");
    }
    
    /**
     * الحصول على إحصائيات المجمعات
     */
    public void printPoolStatistics() {
        System.out.println("📊 إحصائيات مجمعات الاتصالات:");
        System.out.println("   SHIP_ERP Pool:");
        System.out.println("     - Available: " + shipErpPool.size());
        System.out.println("     - Active: " + shipErpActiveConnections.get());
        System.out.println("   IAS20251 Pool:");
        System.out.println("     - Available: " + ias20251Pool.size());
        System.out.println("     - Active: " + ias20251ActiveConnections.get());
    }
    
    /**
     * فحص صحة الاتصالات
     */
    public void validateConnections() {
        System.out.println("🔍 فحص صحة الاتصالات...");
        
        // فحص اتصالات SHIP_ERP
        int validShipErp = 0;
        for (Connection conn : shipErpPool) {
            try {
                if (conn != null && !conn.isClosed() && conn.isValid(5)) {
                    validShipErp++;
                }
            } catch (SQLException e) {
                System.err.println("اتصال SHIP_ERP غير صالح: " + e.getMessage());
            }
        }
        
        // فحص اتصالات IAS20251
        int validIas20251 = 0;
        for (Connection conn : ias20251Pool) {
            try {
                if (conn != null && !conn.isClosed() && conn.isValid(5)) {
                    validIas20251++;
                }
            } catch (SQLException e) {
                System.err.println("اتصال IAS20251 غير صالح: " + e.getMessage());
            }
        }
        
        System.out.println("✅ الاتصالات الصالحة:");
        System.out.println("   - SHIP_ERP: " + validShipErp + "/" + shipErpPool.size());
        System.out.println("   - IAS20251: " + validIas20251 + "/" + ias20251Pool.size());
    }
    
    /**
     * إضافة shutdown hook لإغلاق الاتصالات عند إغلاق التطبيق
     */
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            ConnectionPoolManager manager = ConnectionPoolManager.getInstance();
            manager.closeAllConnections();
        }));
    }
}
