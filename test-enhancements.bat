@echo off
echo ========================================
echo    ENHANCEMENTS TESTING SUITE
echo    مجموعة اختبار التحسينات
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Testing all applied enhancements...
echo [معلومات] اختبار جميع التحسينات المطبقة...
echo.

echo ========================================
echo    TEST 1: SECURITY SYSTEM
echo    الاختبار 1: نظام الأمان
echo ========================================

echo [1] Testing SecurityManager...

java -cp . SecurityManager
if errorlevel 1 (
    echo ❌ SecurityManager test failed
) else (
    echo ✅ SecurityManager test passed
)

echo.
echo ========================================
echo    TEST 2: CONNECTION POOLING
echo    الاختبار 2: تجميع الاتصالات
echo ========================================

echo [2] Testing ConnectionPoolManager...

java -cp . -Doracle.jdbc.defaultNChar=true ConnectionPoolManager
if errorlevel 1 (
    echo ❌ ConnectionPoolManager test failed
) else (
    echo ✅ ConnectionPoolManager test passed
)

echo.
echo ========================================
echo    TEST 3: PERFORMANCE MONITORING
echo    الاختبار 3: مراقبة الأداء
echo ========================================

echo [3] Testing PerformanceMonitor...

java -cp . PerformanceMonitor
if errorlevel 1 (
    echo ❌ PerformanceMonitor test failed
) else (
    echo ✅ PerformanceMonitor test passed
)

echo.
echo ========================================
echo    TEST 4: CONFIGURATION MANAGEMENT
echo    الاختبار 4: إدارة التكوين
echo ========================================

echo [4] Testing EnhancedConfigManager...

java -cp . EnhancedConfigManager
if errorlevel 1 (
    echo ❌ EnhancedConfigManager test failed
) else (
    echo ✅ EnhancedConfigManager test passed
)

echo.
echo ========================================
echo    TEST 5: INTEGRATION TEST
echo    الاختبار 5: اختبار التكامل
echo ========================================

echo [5] Testing system integration...

echo Testing if all classes are compiled...
set /a count=0
for %%f in (*.class) do set /a count+=1

echo Total compiled classes: %count%

if %count% GEQ 15 (
    echo ✅ Integration test passed - All classes compiled
) else (
    echo ❌ Integration test failed - Missing classes
)

echo.
echo ========================================
echo    TEST 6: CONFIGURATION FILES
echo    الاختبار 6: ملفات التكوين
echo ========================================

echo [6] Checking configuration files...

if exist "ship_erp_settings.properties" (
    echo ✅ System configuration file exists
) else (
    echo ❌ System configuration file missing
)

if exist "database_config.properties" (
    echo ✅ Database configuration file exists
) else (
    echo ⚠️ Database configuration file will be created on first run
)

if exist "security_config.properties" (
    echo ✅ Security configuration file exists
) else (
    echo ⚠️ Security configuration file will be created on first run
)

echo.
echo ========================================
echo    TEST 7: LIBRARIES CHECK
echo    الاختبار 7: فحص المكتبات
echo ========================================

echo [7] Checking enhanced libraries...

set /a libcount=0
for %%f in (lib\*.jar) do set /a libcount+=1

echo Total libraries: %libcount%

if %libcount% GEQ 12 (
    echo ✅ Enhanced libraries available
) else (
    echo ⚠️ Some enhanced libraries may be missing
)

echo.
echo Key libraries check:
if exist "lib\ojdbc11.jar" (
    echo ✅ Oracle JDBC Driver
) else (
    echo ❌ Oracle JDBC Driver missing
)

if exist "lib\orai18n.jar" (
    echo ✅ Oracle Internationalization
) else (
    echo ❌ Oracle Internationalization missing
)

echo.
echo ========================================
echo    TEST SUMMARY
echo    ملخص الاختبارات
echo ========================================

echo.
echo 📊 Test Results Summary:
echo ملخص نتائج الاختبارات:
echo.
echo Core Enhancements:
echo التحسينات الأساسية:
echo - SecurityManager: Ready for testing
echo - ConnectionPoolManager: Ready for testing  
echo - PerformanceMonitor: Ready for testing
echo - EnhancedConfigManager: Ready for testing
echo.
echo System Status:
echo حالة النظام:
echo - Compiled Classes: %count%
echo - Available Libraries: %libcount%
echo - Configuration Files: Ready
echo.
echo Next Steps:
echo الخطوات التالية:
echo 1. Run: start-enhanced-system.bat
echo 2. Test individual components
echo 3. Monitor performance improvements
echo 4. Verify security enhancements
echo.

echo ========================================
echo    TESTING COMPLETED
echo    اكتمل الاختبار
echo ========================================

echo.
echo [INFO] Enhancement testing completed!
echo [معلومات] اكتمل اختبار التحسينات!
echo.
echo The system is ready to run with enhancements.
echo النظام جاهز للتشغيل مع التحسينات.
echo.
echo Use start-enhanced-system.bat to run the enhanced system.
echo استخدم start-enhanced-system.bat لتشغيل النظام المحسن.
echo.

pause
