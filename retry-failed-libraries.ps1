Write-Host "========================================" -ForegroundColor Red
Write-Host "  RETRYING FAILED LIBRARIES" -ForegroundColor Red
Write-Host "  إعادة تحميل المكتبات الفاشلة" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red

Set-Location "e:\ship_erp\java\lib"

# Function to download with multiple sources
function Download-MultiSource {
    param(
        [string]$Name,
        [array]$Sources,
        [string]$OutputFile
    )
    
    Write-Host "`n[$Name]" -ForegroundColor White
    
    foreach ($source in $Sources) {
        try {
            Write-Host "  Trying source: $($source.Name)" -ForegroundColor Cyan
            Write-Host "  URL: $($source.Url)" -ForegroundColor Gray
            
            Invoke-WebRequest -Uri $source.Url -OutFile $OutputFile -TimeoutSec 120
            
            if (Test-Path $OutputFile) {
                $size = (Get-Item $OutputFile).Length
                if ($size -gt 10000) {
                    Write-Host "    SUCCESS: $OutputFile ($size bytes)" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "    FAILED: File too small ($size bytes)" -ForegroundColor Red
                    Remove-Item $OutputFile -Force -ErrorAction SilentlyContinue
                }
            }
        }
        catch {
            Write-Host "    ERROR: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "    ALL SOURCES FAILED for $Name" -ForegroundColor Red
    return $false
}

# Define failed libraries with multiple sources
$failedLibraries = @(
    @{
        Name = "Substance Look and Feel"
        File = "substance-8.0.02.jar"
        Sources = @(
            @{ Name = "Maven Central"; Url = "https://repo1.maven.org/maven2/org/pushingpixels/substance/8.0.02/substance-8.0.02.jar" },
            @{ Name = "JCenter"; Url = "https://jcenter.bintray.com/org/pushingpixels/substance/8.0.02/substance-8.0.02.jar" },
            @{ Name = "GitHub Release"; Url = "https://github.com/kirill-grouchnikov/substance/releases/download/v8.0.02/substance-8.0.02.jar" }
        )
    },
    @{
        Name = "Trident Animation Library"
        File = "trident-1.5.00.jar"
        Sources = @(
            @{ Name = "Maven Central"; Url = "https://repo1.maven.org/maven2/org/pushingpixels/trident/1.5.00/trident-1.5.00.jar" },
            @{ Name = "JCenter"; Url = "https://jcenter.bintray.com/org/pushingpixels/trident/1.5.00/trident-1.5.00.jar" },
            @{ Name = "GitHub Release"; Url = "https://github.com/kirill-grouchnikov/trident/releases/download/v1.5.00/trident-1.5.00.jar" }
        )
    },
    @{
        Name = "WebLaF Core"
        File = "weblaf-core-2.2.1.jar"
        Sources = @(
            @{ Name = "Maven Central"; Url = "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-core/2.2.1/weblaf-core-2.2.1.jar" },
            @{ Name = "GitHub Release"; Url = "https://github.com/mgarin/weblaf/releases/download/v2.2.1/weblaf-core-2.2.1.jar" },
            @{ Name = "Alternative Maven"; Url = "https://mvnrepository.com/artifact/com.weblookandfeel/weblaf-core/2.2.1" }
        )
    },
    @{
        Name = "WebLaF UI"
        File = "weblaf-ui-2.2.1.jar"
        Sources = @(
            @{ Name = "Maven Central"; Url = "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-ui/2.2.1/weblaf-ui-2.2.1.jar" },
            @{ Name = "GitHub Release"; Url = "https://github.com/mgarin/weblaf/releases/download/v2.2.1/weblaf-ui-2.2.1.jar" },
            @{ Name = "Alternative Maven"; Url = "https://mvnrepository.com/artifact/com.weblookandfeel/weblaf-ui/2.2.1" }
        )
    },
    @{
        Name = "Synthetica Look and Feel"
        File = "synthetica-2.30.0.jar"
        Sources = @(
            @{ Name = "Maven Central"; Url = "https://repo1.maven.org/maven2/de/javasoft/synthetica/2.30.0/synthetica-2.30.0.jar" },
            @{ Name = "Official Site"; Url = "http://www.javasoft.de/synthetica/download/synthetica-2.30.0.jar" },
            @{ Name = "Alternative"; Url = "https://mvnrepository.com/artifact/de.javasoft/synthetica/2.30.0" }
        )
    },
    @{
        Name = "BeautyEye Look and Feel"
        File = "beautyeye-3.7.jar"
        Sources = @(
            @{ Name = "Maven Central"; Url = "https://repo1.maven.org/maven2/org/jb2011/beautyeye/3.7/beautyeye-3.7.jar" },
            @{ Name = "GitHub"; Url = "https://github.com/JackJiang2011/beautyeye/releases/download/v3.7/beautyeye-3.7.jar" },
            @{ Name = "Alternative"; Url = "https://mvnrepository.com/artifact/org.jb2011/beautyeye/3.7" }
        )
    }
)

$successCount = 0
$failCount = 0

foreach ($lib in $failedLibraries) {
    $result = Download-MultiSource -Name $lib.Name -Sources $lib.Sources -OutputFile $lib.File
    if ($result) {
        $successCount++
    } else {
        $failCount++
    }
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "  RETRY RESULTS" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "Libraries Retried: $($failedLibraries.Count)" -ForegroundColor White
Write-Host "Successfully Downloaded: $successCount" -ForegroundColor Green
Write-Host "Still Failed: $failCount" -ForegroundColor Red

# Try alternative downloads for remaining failed libraries
if ($failCount -gt 0) {
    Write-Host "`n[ALTERNATIVE DOWNLOADS]" -ForegroundColor Yellow
    Write-Host "Trying alternative versions..." -ForegroundColor Yellow
    
    # Try Substance 7.3.04 (older stable version)
    if (-not (Test-Path "substance-8.0.02.jar")) {
        Write-Host "`nTrying Substance 7.3.04 (older version)..." -ForegroundColor Cyan
        try {
            Invoke-WebRequest -Uri "https://repo1.maven.org/maven2/org/pushingpixels/substance/7.3.04/substance-7.3.04.jar" -OutFile "substance-7.3.04.jar" -TimeoutSec 60
            if (Test-Path "substance-7.3.04.jar") {
                $size = (Get-Item "substance-7.3.04.jar").Length
                if ($size -gt 10000) {
                    Write-Host "    SUCCESS: substance-7.3.04.jar ($size bytes)" -ForegroundColor Green
                    $successCount++
                    $failCount--
                }
            }
        }
        catch {
            Write-Host "    FAILED: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Try Trident 1.4.00 (older version)
    if (-not (Test-Path "trident-1.5.00.jar")) {
        Write-Host "`nTrying Trident 1.4.00 (older version)..." -ForegroundColor Cyan
        try {
            Invoke-WebRequest -Uri "https://repo1.maven.org/maven2/org/pushingpixels/trident/1.4.00/trident-1.4.00.jar" -OutFile "trident-1.4.00.jar" -TimeoutSec 60
            if (Test-Path "trident-1.4.00.jar") {
                $size = (Get-Item "trident-1.4.00.jar").Length
                if ($size -gt 10000) {
                    Write-Host "    SUCCESS: trident-1.4.00.jar ($size bytes)" -ForegroundColor Green
                    $successCount++
                    $failCount--
                }
            }
        }
        catch {
            Write-Host "    FAILED: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Try WebLaF 2.1.0 (older version)
    if (-not (Test-Path "weblaf-core-2.2.1.jar")) {
        Write-Host "`nTrying WebLaF 2.1.0 (older version)..." -ForegroundColor Cyan
        try {
            Invoke-WebRequest -Uri "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-core/2.1.0/weblaf-core-2.1.0.jar" -OutFile "weblaf-core-2.1.0.jar" -TimeoutSec 60
            if (Test-Path "weblaf-core-2.1.0.jar") {
                $size = (Get-Item "weblaf-core-2.1.0.jar").Length
                if ($size -gt 10000) {
                    Write-Host "    SUCCESS: weblaf-core-2.1.0.jar ($size bytes)" -ForegroundColor Green
                    $successCount++
                    $failCount--
                }
            }
        }
        catch {
            Write-Host "    FAILED: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    if (-not (Test-Path "weblaf-ui-2.2.1.jar")) {
        Write-Host "`nTrying WebLaF UI 2.1.0 (older version)..." -ForegroundColor Cyan
        try {
            Invoke-WebRequest -Uri "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-ui/2.1.0/weblaf-ui-2.1.0.jar" -OutFile "weblaf-ui-2.1.0.jar" -TimeoutSec 60
            if (Test-Path "weblaf-ui-2.1.0.jar") {
                $size = (Get-Item "weblaf-ui-2.1.0.jar").Length
                if ($size -gt 10000) {
                    Write-Host "    SUCCESS: weblaf-ui-2.1.0.jar ($size bytes)" -ForegroundColor Green
                    $successCount++
                    $failCount--
                }
            }
        }
        catch {
            Write-Host "    FAILED: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "  FINAL RESULTS" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "Total Attempts: $($failedLibraries.Count)" -ForegroundColor White
Write-Host "Successfully Downloaded: $successCount" -ForegroundColor Green
Write-Host "Still Failed: $failCount" -ForegroundColor Red

if ($failCount -eq 0) {
    Write-Host "`nALL FAILED LIBRARIES RECOVERED!" -ForegroundColor Green
} else {
    Write-Host "`nSome libraries are still missing, but we have enough for basic functionality." -ForegroundColor Yellow
}

Write-Host "`nPress any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
