import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSpinner;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SpinnerDateModel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إعدادات الشركات الشاملة والمتقدمة Advanced and Comprehensive Company Settings Window
 */
public class AdvancedCompanySettingsWindow extends JFrame {

    private Connection connection;
    private JTabbedPane mainTabbedPane;

    // تبويب المعلومات الأساسية
    private JTextField companyCodeField;
    private JTextField companyNameArField;
    private JTextField companyNameEnField;
    private JTextField shortNameArField;
    private JTextField shortNameEnField;
    private JTextField primaryEmailField;
    private JTextField primaryPhoneField;
    private JTextField websiteField;

    // تبويب العنوان والموقع
    private JTextArea addressArArea;
    private JTextArea addressEnArea;
    private JTextField cityArField;
    private JTextField cityEnField;
    private JTextField countryArField;
    private JTextField countryEnField;
    private JTextField postalCodeField;

    // تبويب المعلومات القانونية
    private JTextField commercialRegisterField;
    private JTextField taxNumberField;
    private JTextField vatNumberField;
    private JTextField chamberNumberField;
    private JSpinner establishmentDateSpinner;
    private JComboBox<String> legalFormCombo;
    private JTextField capitalAmountField;
    private JComboBox<String> capitalCurrencyCombo;

    // تبويب البنك
    private JTextField bankNameArField;
    private JTextField bankNameEnField;
    private JTextField accountNumberField;
    private JTextField ibanField;
    private JTextField swiftCodeField;

    // تبويب الإعدادات
    private JComboBox<String> defaultCurrencyCombo;
    private JComboBox<String> defaultLanguageCombo;
    private JComboBox<String> timeZoneCombo;
    private JComboBox<String> companyStatusCombo;
    private JComboBox<String> companyTypeCombo;

    // جداول البيانات
    private JTable branchesTable;
    private DefaultTableModel branchesModel;
    private JTable contactsTable;
    private DefaultTableModel contactsModel;
    private JTable documentsTable;
    private DefaultTableModel documentsModel;

    // أزرار التحكم
    private JButton saveButton;
    private JButton cancelButton;
    private JButton newCompanyButton;
    private JButton deleteCompanyButton;
    private JComboBox<String> companySelector;

    private int currentCompanyId = -1;

    public AdvancedCompanySettingsWindow() {
        initializeConnection();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadCompanies();

        setTitle("🏢 إعدادات الشركات الشاملة - Advanced Company Settings");
        setSize(1200, 800);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        // تطبيق اتجاه اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        applyComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تطبيق الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        setUIFont(arabicFont);
    }

    private void initializeConnection() {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            connection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات لنافذة إعدادات الشركات");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                    "خطأ في الاتصال بقاعدة البيانات:\n" + e.getMessage(),
                    "خطأ في الاتصال - Connection Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void initializeComponents() {
        // إنشاء التبويبات الرئيسية مع اتجاه عربي
        mainTabbedPane = new JTabbedPane(JTabbedPane.TOP);
        mainTabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainTabbedPane.applyComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط التحكم العلوي
        companySelector = new JComboBox<>();
        companySelector.setFont(new Font("Tahoma", Font.PLAIN, 12));
        companySelector.setPreferredSize(new Dimension(300, 30));
        companySelector.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        newCompanyButton = new JButton("شركة جديدة - New Company");
        newCompanyButton.setFont(new Font("Tahoma", Font.BOLD, 12));
        newCompanyButton.setBackground(new Color(40, 167, 69));
        newCompanyButton.setForeground(Color.WHITE);

        deleteCompanyButton = new JButton("حذف الشركة - Delete Company");
        deleteCompanyButton.setFont(new Font("Tahoma", Font.PLAIN, 12));
        deleteCompanyButton.setBackground(new Color(220, 53, 69));
        deleteCompanyButton.setForeground(Color.WHITE);

        // تبويب المعلومات الأساسية
        createBasicInfoTab();

        // تبويب العنوان والموقع
        createAddressTab();

        // تبويب المعلومات القانونية
        createLegalInfoTab();

        // تبويب البنك
        createBankInfoTab();

        // تبويب الفروع
        createBranchesTab();

        // تبويب جهات الاتصال
        createContactsTab();

        // تبويب الوثائق
        createDocumentsTab();

        // تبويب الإعدادات
        createSettingsTab();

        // أزرار التحكم السفلية
        saveButton = new JButton("حفظ التغييرات - Save Changes");
        saveButton.setFont(new Font("Tahoma", Font.BOLD, 14));
        saveButton.setBackground(new Color(0, 123, 255));
        saveButton.setForeground(Color.WHITE);
        saveButton.setPreferredSize(new Dimension(200, 40));

        cancelButton = new JButton("إلغاء - Cancel");
        cancelButton.setFont(new Font("Tahoma", Font.PLAIN, 12));
        cancelButton.setBackground(new Color(108, 117, 125));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setPreferredSize(new Dimension(120, 40));
    }

    private void createBasicInfoTab() {
        JPanel basicInfoPanel = new JPanel(new GridBagLayout());
        basicInfoPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        basicInfoPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST; // تغيير من EAST إلى WEST للعربية

        // الصف الأول
        gbc.gridx = 0;
        gbc.gridy = 0;
        JLabel companyCodeLabel = new JLabel("رمز الشركة - Company Code:");
        companyCodeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(companyCodeLabel, gbc);
        gbc.gridx = 1;
        companyCodeField = new JTextField(20);
        companyCodeField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(companyCodeField, gbc);

        gbc.gridx = 2;
        JLabel companyNameArLabel = new JLabel("الاسم بالعربية - Arabic Name:");
        companyNameArLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(companyNameArLabel, gbc);
        gbc.gridx = 3;
        companyNameArField = new JTextField(30);
        companyNameArField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(companyNameArField, gbc);

        // الصف الثاني
        gbc.gridx = 0;
        gbc.gridy = 1;
        JLabel companyNameEnLabel = new JLabel("الاسم بالإنجليزية - English Name:");
        companyNameEnLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(companyNameEnLabel, gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        companyNameEnField = new JTextField(50);
        companyNameEnField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(companyNameEnField, gbc);
        gbc.gridwidth = 1;

        // الصف الثالث
        gbc.gridx = 0;
        gbc.gridy = 2;
        JLabel shortNameArLabel = new JLabel("الاسم المختصر عربي - Short Name AR:");
        shortNameArLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(shortNameArLabel, gbc);
        gbc.gridx = 1;
        shortNameArField = new JTextField(20);
        shortNameArField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(shortNameArField, gbc);

        gbc.gridx = 2;
        JLabel shortNameEnLabel = new JLabel("الاسم المختصر إنجليزي - Short Name EN:");
        shortNameEnLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(shortNameEnLabel, gbc);
        gbc.gridx = 3;
        shortNameEnField = new JTextField(20);
        shortNameEnField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(shortNameEnField, gbc);

        // الصف الرابع
        gbc.gridx = 0;
        gbc.gridy = 3;
        JLabel emailLabel = new JLabel("البريد الإلكتروني - Email:");
        emailLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(emailLabel, gbc);
        gbc.gridx = 1;
        primaryEmailField = new JTextField(25);
        primaryEmailField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(primaryEmailField, gbc);

        gbc.gridx = 2;
        JLabel phoneLabel = new JLabel("الهاتف - Phone:");
        phoneLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(phoneLabel, gbc);
        gbc.gridx = 3;
        primaryPhoneField = new JTextField(20);
        primaryPhoneField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(primaryPhoneField, gbc);

        // الصف الخامس
        gbc.gridx = 0;
        gbc.gridy = 4;
        JLabel websiteLabel = new JLabel("الموقع الإلكتروني - Website:");
        websiteLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(websiteLabel, gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        websiteField = new JTextField(50);
        websiteField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        basicInfoPanel.add(websiteField, gbc);

        mainTabbedPane.addTab("المعلومات الأساسية - Basic Info", basicInfoPanel);
    }

    private void createAddressTab() {
        JPanel addressPanel = new JPanel(new GridBagLayout());
        addressPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        addressPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // العنوان بالعربية
        gbc.gridx = 0;
        gbc.gridy = 0;
        addressPanel.add(new JLabel("العنوان بالعربية - Arabic Address:"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.fill = GridBagConstraints.BOTH;
        addressArArea = new JTextArea(3, 50);
        addressArArea.setLineWrap(true);
        addressArArea.setWrapStyleWord(true);
        JScrollPane addressArScroll = new JScrollPane(addressArArea);
        addressPanel.add(addressArScroll, gbc);

        // العنوان بالإنجليزية
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        addressPanel.add(new JLabel("العنوان بالإنجليزية - English Address:"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.fill = GridBagConstraints.BOTH;
        addressEnArea = new JTextArea(3, 50);
        addressEnArea.setLineWrap(true);
        addressEnArea.setWrapStyleWord(true);
        JScrollPane addressEnScroll = new JScrollPane(addressEnArea);
        addressPanel.add(addressEnScroll, gbc);

        // المدينة والدولة
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        addressPanel.add(new JLabel("المدينة عربي - City AR:"), gbc);
        gbc.gridx = 1;
        cityArField = new JTextField(20);
        addressPanel.add(cityArField, gbc);

        gbc.gridx = 2;
        addressPanel.add(new JLabel("المدينة إنجليزي - City EN:"), gbc);
        gbc.gridx = 3;
        cityEnField = new JTextField(20);
        addressPanel.add(cityEnField, gbc);

        // الدولة والرمز البريدي
        gbc.gridx = 0;
        gbc.gridy = 3;
        addressPanel.add(new JLabel("الدولة عربي - Country AR:"), gbc);
        gbc.gridx = 1;
        countryArField = new JTextField(20);
        addressPanel.add(countryArField, gbc);

        gbc.gridx = 2;
        addressPanel.add(new JLabel("الدولة إنجليزي - Country EN:"), gbc);
        gbc.gridx = 3;
        countryEnField = new JTextField(20);
        addressPanel.add(countryEnField, gbc);

        // الرمز البريدي
        gbc.gridx = 0;
        gbc.gridy = 4;
        addressPanel.add(new JLabel("الرمز البريدي - Postal Code:"), gbc);
        gbc.gridx = 1;
        postalCodeField = new JTextField(15);
        addressPanel.add(postalCodeField, gbc);

        mainTabbedPane.addTab("العنوان والموقع - Address & Location", addressPanel);
    }

    private void createLegalInfoTab() {
        JPanel legalPanel = new JPanel(new GridBagLayout());
        legalPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // السجل التجاري
        gbc.gridx = 0;
        gbc.gridy = 0;
        legalPanel.add(new JLabel("السجل التجاري - Commercial Register:"), gbc);
        gbc.gridx = 1;
        commercialRegisterField = new JTextField(20);
        legalPanel.add(commercialRegisterField, gbc);

        // الرقم الضريبي
        gbc.gridx = 2;
        legalPanel.add(new JLabel("الرقم الضريبي - Tax Number:"), gbc);
        gbc.gridx = 3;
        taxNumberField = new JTextField(20);
        legalPanel.add(taxNumberField, gbc);

        // رقم ضريبة القيمة المضافة
        gbc.gridx = 0;
        gbc.gridy = 1;
        legalPanel.add(new JLabel("رقم ضريبة القيمة المضافة - VAT Number:"), gbc);
        gbc.gridx = 1;
        vatNumberField = new JTextField(20);
        legalPanel.add(vatNumberField, gbc);

        // رقم الغرفة التجارية
        gbc.gridx = 2;
        legalPanel.add(new JLabel("رقم الغرفة التجارية - Chamber Number:"), gbc);
        gbc.gridx = 3;
        chamberNumberField = new JTextField(20);
        legalPanel.add(chamberNumberField, gbc);

        // تاريخ التأسيس
        gbc.gridx = 0;
        gbc.gridy = 2;
        legalPanel.add(new JLabel("تاريخ التأسيس - Establishment Date:"), gbc);
        gbc.gridx = 1;
        establishmentDateSpinner = new JSpinner(new SpinnerDateModel());
        JSpinner.DateEditor dateEditor =
                new JSpinner.DateEditor(establishmentDateSpinner, "dd/MM/yyyy");
        establishmentDateSpinner.setEditor(dateEditor);
        legalPanel.add(establishmentDateSpinner, gbc);

        // الشكل القانوني
        gbc.gridx = 2;
        legalPanel.add(new JLabel("الشكل القانوني - Legal Form:"), gbc);
        gbc.gridx = 3;
        String[] legalForms = {"شركة مساهمة", "شركة ذات مسؤولية محدودة", "مؤسسة فردية", "شراكة"};
        legalFormCombo = new JComboBox<>(legalForms);
        legalPanel.add(legalFormCombo, gbc);

        // رأس المال
        gbc.gridx = 0;
        gbc.gridy = 3;
        legalPanel.add(new JLabel("رأس المال - Capital Amount:"), gbc);
        gbc.gridx = 1;
        capitalAmountField = new JTextField(15);
        legalPanel.add(capitalAmountField, gbc);

        // عملة رأس المال
        gbc.gridx = 2;
        legalPanel.add(new JLabel("عملة رأس المال - Capital Currency:"), gbc);
        gbc.gridx = 3;
        String[] currencies = {"SAR", "USD", "EUR", "AED"};
        capitalCurrencyCombo = new JComboBox<>(currencies);
        legalPanel.add(capitalCurrencyCombo, gbc);

        mainTabbedPane.addTab("المعلومات القانونية - Legal Info", legalPanel);
    }

    private void createBankInfoTab() {
        JPanel bankPanel = new JPanel(new GridBagLayout());
        bankPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // اسم البنك
        gbc.gridx = 0;
        gbc.gridy = 0;
        bankPanel.add(new JLabel("اسم البنك عربي - Bank Name AR:"), gbc);
        gbc.gridx = 1;
        bankNameArField = new JTextField(25);
        bankPanel.add(bankNameArField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 1;
        bankPanel.add(new JLabel("اسم البنك إنجليزي - Bank Name EN:"), gbc);
        gbc.gridx = 1;
        bankNameEnField = new JTextField(25);
        bankPanel.add(bankNameEnField, gbc);

        // رقم الحساب
        gbc.gridx = 0;
        gbc.gridy = 2;
        bankPanel.add(new JLabel("رقم الحساب - Account Number:"), gbc);
        gbc.gridx = 1;
        accountNumberField = new JTextField(25);
        bankPanel.add(accountNumberField, gbc);

        // رقم الآيبان
        gbc.gridx = 0;
        gbc.gridy = 3;
        bankPanel.add(new JLabel("رقم الآيبان - IBAN:"), gbc);
        gbc.gridx = 1;
        ibanField = new JTextField(30);
        bankPanel.add(ibanField, gbc);

        // رمز السويفت
        gbc.gridx = 0;
        gbc.gridy = 4;
        bankPanel.add(new JLabel("رمز السويفت - SWIFT Code:"), gbc);
        gbc.gridx = 1;
        swiftCodeField = new JTextField(15);
        bankPanel.add(swiftCodeField, gbc);

        mainTabbedPane.addTab("معلومات البنك - Bank Info", bankPanel);
    }

    private void createBranchesTab() {
        JPanel branchesPanel = new JPanel(new BorderLayout());
        branchesPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // جدول الفروع
        String[] branchColumns =
                {"رقم الفرع", "رمز الفرع", "اسم الفرع", "المدينة", "الهاتف", "فرع رئيسي", "نشط"};
        branchesModel = new DefaultTableModel(branchColumns, 0);
        branchesTable = new JTable(branchesModel);
        branchesTable.setFont(new Font("Tahoma", Font.PLAIN, 11));
        branchesTable.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 12));

        JScrollPane branchesScroll = new JScrollPane(branchesTable);
        branchesScroll.setPreferredSize(new Dimension(800, 300));
        branchesPanel.add(branchesScroll, BorderLayout.CENTER);

        // أزرار إدارة الفروع
        JPanel branchButtonsPanel = new JPanel(new FlowLayout());
        JButton addBranchBtn = new JButton("إضافة فرع - Add Branch");
        JButton editBranchBtn = new JButton("تعديل فرع - Edit Branch");
        JButton deleteBranchBtn = new JButton("حذف فرع - Delete Branch");

        addBranchBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));
        editBranchBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));
        deleteBranchBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));

        branchButtonsPanel.add(addBranchBtn);
        branchButtonsPanel.add(editBranchBtn);
        branchButtonsPanel.add(deleteBranchBtn);
        branchesPanel.add(branchButtonsPanel, BorderLayout.SOUTH);

        mainTabbedPane.addTab("الفروع - Branches", branchesPanel);
    }

    private void createContactsTab() {
        JPanel contactsPanel = new JPanel(new BorderLayout());
        contactsPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // جدول جهات الاتصال
        String[] contactColumns = {"رقم الاتصال", "النوع", "الاسم", "المنصب", "الهاتف",
                "البريد الإلكتروني", "رئيسي", "نشط"};
        contactsModel = new DefaultTableModel(contactColumns, 0);
        contactsTable = new JTable(contactsModel);
        contactsTable.setFont(new Font("Tahoma", Font.PLAIN, 11));
        contactsTable.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 12));

        JScrollPane contactsScroll = new JScrollPane(contactsTable);
        contactsScroll.setPreferredSize(new Dimension(800, 300));
        contactsPanel.add(contactsScroll, BorderLayout.CENTER);

        // أزرار إدارة جهات الاتصال
        JPanel contactButtonsPanel = new JPanel(new FlowLayout());
        JButton addContactBtn = new JButton("إضافة جهة اتصال - Add Contact");
        JButton editContactBtn = new JButton("تعديل جهة اتصال - Edit Contact");
        JButton deleteContactBtn = new JButton("حذف جهة اتصال - Delete Contact");

        addContactBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));
        editContactBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));
        deleteContactBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));

        contactButtonsPanel.add(addContactBtn);
        contactButtonsPanel.add(editContactBtn);
        contactButtonsPanel.add(deleteContactBtn);
        contactsPanel.add(contactButtonsPanel, BorderLayout.SOUTH);

        mainTabbedPane.addTab("جهات الاتصال - Contacts", contactsPanel);
    }

    private void createDocumentsTab() {
        JPanel documentsPanel = new JPanel(new BorderLayout());
        documentsPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // جدول الوثائق
        String[] documentColumns = {"رقم الوثيقة", "النوع", "اسم الوثيقة", "رقم الوثيقة",
                "تاريخ الإصدار", "تاريخ الانتهاء", "الحالة"};
        documentsModel = new DefaultTableModel(documentColumns, 0);
        documentsTable = new JTable(documentsModel);
        documentsTable.setFont(new Font("Tahoma", Font.PLAIN, 11));
        documentsTable.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 12));

        JScrollPane documentsScroll = new JScrollPane(documentsTable);
        documentsScroll.setPreferredSize(new Dimension(800, 300));
        documentsPanel.add(documentsScroll, BorderLayout.CENTER);

        // أزرار إدارة الوثائق
        JPanel documentButtonsPanel = new JPanel(new FlowLayout());
        JButton addDocumentBtn = new JButton("إضافة وثيقة - Add Document");
        JButton editDocumentBtn = new JButton("تعديل وثيقة - Edit Document");
        JButton deleteDocumentBtn = new JButton("حذف وثيقة - Delete Document");
        JButton viewDocumentBtn = new JButton("عرض الوثيقة - View Document");

        addDocumentBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));
        editDocumentBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));
        deleteDocumentBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));
        viewDocumentBtn.setFont(new Font("Tahoma", Font.PLAIN, 11));

        documentButtonsPanel.add(addDocumentBtn);
        documentButtonsPanel.add(editDocumentBtn);
        documentButtonsPanel.add(deleteDocumentBtn);
        documentButtonsPanel.add(viewDocumentBtn);
        documentsPanel.add(documentButtonsPanel, BorderLayout.SOUTH);

        mainTabbedPane.addTab("الوثائق - Documents", documentsPanel);
    }

    private void createSettingsTab() {
        JPanel settingsPanel = new JPanel(new GridBagLayout());
        settingsPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // العملة الافتراضية
        gbc.gridx = 0;
        gbc.gridy = 0;
        settingsPanel.add(new JLabel("العملة الافتراضية - Default Currency:"), gbc);
        gbc.gridx = 1;
        String[] currencies = {"SAR", "USD", "EUR", "AED"};
        defaultCurrencyCombo = new JComboBox<>(currencies);
        settingsPanel.add(defaultCurrencyCombo, gbc);

        // اللغة الافتراضية
        gbc.gridx = 2;
        settingsPanel.add(new JLabel("اللغة الافتراضية - Default Language:"), gbc);
        gbc.gridx = 3;
        String[] languages = {"العربية - Arabic", "الإنجليزية - English"};
        defaultLanguageCombo = new JComboBox<>(languages);
        settingsPanel.add(defaultLanguageCombo, gbc);

        // المنطقة الزمنية
        gbc.gridx = 0;
        gbc.gridy = 1;
        settingsPanel.add(new JLabel("المنطقة الزمنية - Time Zone:"), gbc);
        gbc.gridx = 1;
        String[] timeZones = {"Asia/Riyadh", "UTC", "Asia/Dubai", "Europe/London"};
        timeZoneCombo = new JComboBox<>(timeZones);
        settingsPanel.add(timeZoneCombo, gbc);

        // حالة الشركة
        gbc.gridx = 2;
        settingsPanel.add(new JLabel("حالة الشركة - Company Status:"), gbc);
        gbc.gridx = 3;
        String[] statuses = {"ACTIVE", "INACTIVE", "SUSPENDED"};
        companyStatusCombo = new JComboBox<>(statuses);
        settingsPanel.add(companyStatusCombo, gbc);

        // نوع الشركة
        gbc.gridx = 0;
        gbc.gridy = 2;
        settingsPanel.add(new JLabel("نوع الشركة - Company Type:"), gbc);
        gbc.gridx = 1;
        String[] types = {"HEAD_OFFICE", "BRANCH", "SUBSIDIARY"};
        companyTypeCombo = new JComboBox<>(types);
        settingsPanel.add(companyTypeCombo, gbc);

        mainTabbedPane.addTab("الإعدادات - Settings", settingsPanel);
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // الشريط العلوي
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        topPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel selectLabel = new JLabel("اختر الشركة - Select Company:");
        selectLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        topPanel.add(selectLabel);
        topPanel.add(companySelector);
        topPanel.add(newCompanyButton);
        topPanel.add(deleteCompanyButton);

        add(topPanel, BorderLayout.NORTH);
        add(mainTabbedPane, BorderLayout.CENTER);

        // الشريط السفلي
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        bottomPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        bottomPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        bottomPanel.add(cancelButton);
        bottomPanel.add(saveButton);

        add(bottomPanel, BorderLayout.SOUTH);
    }

    private void setupEventHandlers() {
        // معالج اختيار الشركة
        companySelector.addActionListener(e -> loadSelectedCompany());

        // معالج زر الشركة الجديدة
        newCompanyButton.addActionListener(e -> createNewCompany());

        // معالج زر حذف الشركة
        deleteCompanyButton.addActionListener(e -> deleteSelectedCompany());

        // معالج زر الحفظ
        saveButton.addActionListener(e -> saveCompanyData());

        // معالج زر الإلغاء
        cancelButton.addActionListener(e -> dispose());
    }

    private void loadCompanies() {
        if (connection == null)
            return;

        try {
            companySelector.removeAllItems();
            companySelector.addItem("-- اختر الشركة - Select Company --");

            String sql =
                    "SELECT COMPANY_ID, COMPANY_CODE, COMPANY_NAME_AR FROM ERP_ADVANCED_COMPANIES WHERE IS_ACTIVE = 'Y' ORDER BY COMPANY_NAME_AR";

            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                while (rs.next()) {
                    int companyId = rs.getInt("COMPANY_ID");
                    String companyCode = rs.getString("COMPANY_CODE");
                    String companyName = rs.getString("COMPANY_NAME_AR");

                    String displayText = companyCode + " - " + companyName;
                    CompanyItem item = new CompanyItem(companyId, displayText);
                    companySelector.addItem(item.toString());
                }
            }

        } catch (SQLException e) {
            System.err.println("خطأ في تحميل الشركات: " + e.getMessage());
        }
    }

    private void loadSelectedCompany() {
        // تنفيذ تحميل بيانات الشركة المختارة
        System.out.println("تحميل بيانات الشركة المختارة...");
    }

    private void createNewCompany() {
        // تنفيذ إنشاء شركة جديدة
        clearAllFields();
        currentCompanyId = -1;
        System.out.println("إنشاء شركة جديدة...");
    }

    private void deleteSelectedCompany() {
        // تنفيذ حذف الشركة المختارة
        System.out.println("حذف الشركة المختارة...");
    }

    private void saveCompanyData() {
        // تنفيذ حفظ بيانات الشركة
        System.out.println("حفظ بيانات الشركة...");
    }

    private void clearAllFields() {
        // مسح جميع الحقول
        companyCodeField.setText("");
        companyNameArField.setText("");
        companyNameEnField.setText("");
        // ... باقي الحقول
    }

    private void setUIFont(Font font) {
        java.util.Enumeration<Object> keys = UIManager.getDefaults().keys();
        while (keys.hasMoreElements()) {
            Object key = keys.nextElement();
            Object value = UIManager.get(key);
            if (value instanceof javax.swing.plaf.FontUIResource) {
                UIManager.put(key, font);
            }
        }
    }

    // فئة مساعدة لعناصر الشركة
    private static class CompanyItem {
        int id;
        String displayText;

        CompanyItem(int id, String displayText) {
            this.id = id;
            this.displayText = displayText;
        }

        @Override
        public String toString() {
            return displayText;
        }
    }

    /**
     * الدالة الرئيسية للاختبار
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getCrossPlatformLookAndFeelClassName());

                AdvancedCompanySettingsWindow window = new AdvancedCompanySettingsWindow();
                window.setVisible(true);

                System.out.println("✅ تم تشغيل نافذة إعدادات الشركات الشاملة");

            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null,
                        "خطأ في تشغيل نافذة إعدادات الشركات:\n" + e.getMessage(), "خطأ - Error",
                        JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
