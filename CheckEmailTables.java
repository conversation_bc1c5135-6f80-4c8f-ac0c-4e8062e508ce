import java.sql.*;

/**
 * فحص وجود جداول البريد الإلكتروني في قاعدة البيانات
 */
public class CheckEmailTables {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  CHECKING EMAIL TABLES");
        System.out.println("  فحص جداول البريد الإلكتروني");
        System.out.println("========================================");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح");
            
            // فحص الجداول المطلوبة
            checkTable(connection, "EMAIL_ACCOUNTS");
            checkTable(connection, "ERP_EMAIL_ACCOUNTS");
            checkTable(connection, "EMAIL_MESSAGES");
            checkTable(connection, "EMAIL_TEMPLATES");
            checkTable(connection, "EMAIL_ATTACHMENTS");
            checkTable(connection, "EMAIL_ADDRESS_BOOK");
            
            // فحص المتسلسلات
            checkSequence(connection, "EMAIL_ACCOUNTS_SEQ");
            checkSequence(connection, "ERP_EMAIL_ACCOUNTS_SEQ");
            
            // فحص البيانات الموجودة
            checkData(connection, "EMAIL_ACCOUNTS");
            checkData(connection, "ERP_EMAIL_ACCOUNTS");
            
            connection.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فحص الجداول: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkTable(Connection connection, String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, tableName.toUpperCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        System.out.println("✅ جدول " + tableName + " موجود");
                        
                        // فحص عدد الأعمدة
                        String columnsSql = "SELECT COUNT(*) FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ?";
                        try (PreparedStatement colStmt = connection.prepareStatement(columnsSql)) {
                            colStmt.setString(1, tableName.toUpperCase());
                            try (ResultSet colRs = colStmt.executeQuery()) {
                                if (colRs.next()) {
                                    System.out.println("   📊 عدد الأعمدة: " + colRs.getInt(1));
                                }
                            }
                        }
                        
                    } else {
                        System.out.println("❌ جدول " + tableName + " غير موجود");
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في فحص جدول " + tableName + ": " + e.getMessage());
        }
    }
    
    private static void checkSequence(Connection connection, String sequenceName) {
        try {
            String sql = "SELECT COUNT(*) FROM USER_SEQUENCES WHERE SEQUENCE_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, sequenceName.toUpperCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        System.out.println("✅ متسلسل " + sequenceName + " موجود");
                    } else {
                        System.out.println("❌ متسلسل " + sequenceName + " غير موجود");
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في فحص متسلسل " + sequenceName + ": " + e.getMessage());
        }
    }
    
    private static void checkData(Connection connection, String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM " + tableName;
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int count = rs.getInt(1);
                        System.out.println("📊 جدول " + tableName + " يحتوي على " + count + " سجل");
                    }
                }
            }
        } catch (SQLException e) {
            System.out.println("⚠️ لا يمكن قراءة البيانات من جدول " + tableName + ": " + e.getMessage());
        }
    }
}
