import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * تقرير سريع لجدول شجرة الأنظمة
 * Quick System Tree Report
 */
public class QuickSystemTreeReport {
    
    private Connection dbConnection;
    
    public static void main(String[] args) {
        System.out.println("🚀 بدء فحص جدول شجرة الأنظمة...");
        System.out.println("==========================================");
        
        QuickSystemTreeReport report = new QuickSystemTreeReport();
        report.generateReport();
    }
    
    public void generateReport() {
        try {
            // الاتصال بقاعدة البيانات
            connectToDatabase();
            
            // طباعة رأس التقرير
            printReportHeader();
            
            // فحص وجود الجدول
            checkTableExists();
            
            // الإحصائيات العامة
            printGeneralStatistics();
            
            // توزيع أنواع العقد
            printNodeTypeDistribution();
            
            // توزيع المستويات
            printLevelDistribution();
            
            // فحص المشاكل
            checkForIssues();
            
            // الهيكل الهرمي
            printTreeStructure();
            
            // معلومات الإنشاء
            printCreationInfo();
            
            // التوصيات
            printRecommendations();
            
            System.out.println("\n✅ تم إكمال فحص جدول شجرة الأنظمة بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء التقرير: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeConnection();
        }
    }
    
    private void connectToDatabase() throws Exception {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            dbConnection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات SHIP_ERP");
        } catch (Exception e) {
            System.err.println("❌ فشل الاتصال بقاعدة البيانات: " + e.getMessage());
            throw e;
        }
    }
    
    private void printReportHeader() {
        System.out.println("\n📊 تقرير فحص جدول شجرة الأنظمة");
        System.out.println("System Tree Analysis Report");
        System.out.println("==========================================");
        System.out.println("التاريخ: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        System.out.println("قاعدة البيانات: Oracle SHIP_ERP");
        System.out.println("الجدول: ERP_SYSTEM_TREE");
        System.out.println("==========================================");
    }
    
    private void checkTableExists() throws SQLException {
        System.out.println("\n🔍 [1] فحص وجود الجدول:");
        System.out.println("========================");
        
        String sql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'ERP_SYSTEM_TREE'";
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next() && rs.getInt(1) > 0) {
                System.out.println("✅ الجدول ERP_SYSTEM_TREE موجود");
            } else {
                System.out.println("❌ الجدول ERP_SYSTEM_TREE غير موجود!");
                return;
            }
        }
    }
    
    private void printGeneralStatistics() throws SQLException {
        System.out.println("\n📊 [2] الإحصائيات العامة:");
        System.out.println("========================");
        
        // إجمالي العقد
        String totalSql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE";
        try (PreparedStatement stmt = dbConnection.prepareStatement(totalSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                System.out.println("📋 إجمالي العقد: " + rs.getInt(1));
            }
        }
        
        // العقد النشطة
        String activeSql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE IS_ACTIVE = 'Y'";
        try (PreparedStatement stmt = dbConnection.prepareStatement(activeSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                System.out.println("✅ العقد النشطة: " + rs.getInt(1));
            }
        }
        
        // العقد المرئية
        String visibleSql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE IS_VISIBLE = 'Y'";
        try (PreparedStatement stmt = dbConnection.prepareStatement(visibleSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                System.out.println("👁️ العقد المرئية: " + rs.getInt(1));
            }
        }
        
        // العقد الجذرية
        String rootSql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE PARENT_ID IS NULL";
        try (PreparedStatement stmt = dbConnection.prepareStatement(rootSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                System.out.println("🌳 العقد الجذرية: " + rs.getInt(1));
            }
        }
    }
    
    private void printNodeTypeDistribution() throws SQLException {
        System.out.println("\n📋 [3] توزيع أنواع العقد:");
        System.out.println("=========================");
        
        String sql = """
            SELECT NODE_TYPE, COUNT(*) as COUNT,
                   ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ERP_SYSTEM_TREE)), 1) as PERCENTAGE
            FROM ERP_SYSTEM_TREE
            GROUP BY NODE_TYPE
            ORDER BY COUNT(*) DESC
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String nodeType = rs.getString("NODE_TYPE");
                int count = rs.getInt("COUNT");
                double percentage = rs.getDouble("PERCENTAGE");
                
                String icon = getNodeTypeIcon(nodeType);
                System.out.printf("%s %-12s: %3d عقدة (%.1f%%)\n", 
                    icon, nodeType, count, percentage);
            }
        }
    }
    
    private void printLevelDistribution() throws SQLException {
        System.out.println("\n📏 [4] توزيع المستويات:");
        System.out.println("======================");
        
        String sql = """
            SELECT TREE_LEVEL, COUNT(*) as COUNT,
                   ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ERP_SYSTEM_TREE)), 1) as PERCENTAGE
            FROM ERP_SYSTEM_TREE
            GROUP BY TREE_LEVEL
            ORDER BY TREE_LEVEL
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                int level = rs.getInt("TREE_LEVEL");
                int count = rs.getInt("COUNT");
                double percentage = rs.getDouble("PERCENTAGE");
                
                System.out.printf("📊 المستوى %d: %3d عقدة (%.1f%%)\n", 
                    level, count, percentage);
            }
        }
    }
    
    private void checkForIssues() throws SQLException {
        System.out.println("\n⚠️ [5] فحص المشاكل:");
        System.out.println("==================");
        
        int issuesFound = 0;
        
        // العقد اليتيمة
        String orphanSql = """
            SELECT COUNT(*) FROM ERP_SYSTEM_TREE
            WHERE PARENT_ID IS NOT NULL 
            AND PARENT_ID NOT IN (SELECT TREE_ID FROM ERP_SYSTEM_TREE)
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(orphanSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                int orphans = rs.getInt(1);
                if (orphans > 0) {
                    System.out.println("❌ عقد يتيمة: " + orphans);
                    issuesFound += orphans;
                }
            }
        }
        
        // الأسماء المكررة
        String duplicateSql = """
            SELECT COUNT(*) FROM (
                SELECT NODE_NAME_AR FROM ERP_SYSTEM_TREE
                GROUP BY NODE_NAME_AR HAVING COUNT(*) > 1
            )
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(duplicateSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                int duplicates = rs.getInt(1);
                if (duplicates > 0) {
                    System.out.println("⚠️ أسماء مكررة: " + duplicates);
                    issuesFound += duplicates;
                }
            }
        }
        
        // النوافذ بدون كلاس
        String noClassSql = """
            SELECT COUNT(*) FROM ERP_SYSTEM_TREE
            WHERE NODE_TYPE = 'WINDOW' 
            AND (WINDOW_CLASS IS NULL OR TRIM(WINDOW_CLASS) = '')
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(noClassSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                int noClass = rs.getInt(1);
                if (noClass > 0) {
                    System.out.println("❌ نوافذ بدون كلاس: " + noClass);
                    issuesFound += noClass;
                }
            }
        }
        
        if (issuesFound == 0) {
            System.out.println("✅ لم يتم العثور على مشاكل");
        } else {
            System.out.println("⚠️ إجمالي المشاكل: " + issuesFound);
        }
    }
    
    private void printTreeStructure() throws SQLException {
        System.out.println("\n🌳 [6] الهيكل الهرمي (أول 10 عقد):");
        System.out.println("===================================");
        
        String sql = """
            SELECT TREE_ID, LPAD(' ', (TREE_LEVEL * 2)) || NODE_NAME_AR AS TREE_DISPLAY,
                   NODE_TYPE, 
                   CASE WHEN IS_ACTIVE = 'Y' THEN '✅' ELSE '❌' END AS ACTIVE_STATUS,
                   DISPLAY_ORDER
            FROM ERP_SYSTEM_TREE
            WHERE ROWNUM <= 10
            START WITH PARENT_ID IS NULL
            CONNECT BY PRIOR TREE_ID = PARENT_ID
            ORDER SIBLINGS BY DISPLAY_ORDER
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String treeDisplay = rs.getString("TREE_DISPLAY");
                String nodeType = rs.getString("NODE_TYPE");
                String activeStatus = rs.getString("ACTIVE_STATUS");
                
                System.out.printf("%s %s (%s) %s\n", 
                    activeStatus, treeDisplay, nodeType, getNodeTypeIcon(nodeType));
            }
        }
    }
    
    private void printCreationInfo() throws SQLException {
        System.out.println("\n📅 [7] معلومات الإنشاء:");
        System.out.println("=====================");
        
        // أقدم عقدة
        String oldestSql = "SELECT MIN(CREATED_DATE) FROM ERP_SYSTEM_TREE WHERE CREATED_DATE IS NOT NULL";
        try (PreparedStatement stmt = dbConnection.prepareStatement(oldestSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next() && rs.getTimestamp(1) != null) {
                System.out.println("📅 أقدم عقدة: " + 
                    new SimpleDateFormat("yyyy-MM-dd HH:mm").format(rs.getTimestamp(1)));
            }
        }
        
        // أحدث عقدة
        String newestSql = "SELECT MAX(CREATED_DATE) FROM ERP_SYSTEM_TREE WHERE CREATED_DATE IS NOT NULL";
        try (PreparedStatement stmt = dbConnection.prepareStatement(newestSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next() && rs.getTimestamp(1) != null) {
                System.out.println("📅 أحدث عقدة: " + 
                    new SimpleDateFormat("yyyy-MM-dd HH:mm").format(rs.getTimestamp(1)));
            }
        }
    }
    
    private void printRecommendations() {
        System.out.println("\n💡 [8] التوصيات:");
        System.out.println("================");
        System.out.println("• إجراء نسخ احتياطي دوري للجدول");
        System.out.println("• مراجعة العقد غير النشطة وحذف غير المستخدمة");
        System.out.println("• إضافة أوصاف للعقد المفقودة");
        System.out.println("• إضافة أيقونات للعقد");
        System.out.println("• مراجعة الأسماء المكررة");
        System.out.println("• التأكد من صحة كلاسات النوافذ");
        System.out.println("• استخدام SystemTreeAnalyzer للتحليل المتقدم");
    }
    
    private String getNodeTypeIcon(String nodeType) {
        return switch (nodeType != null ? nodeType : "") {
            case "CATEGORY" -> "📂";
            case "WINDOW" -> "🪟";
            case "TOOL" -> "🛠️";
            case "REPORT" -> "📊";
            default -> "📄";
        };
    }
    
    private void closeConnection() {
        try {
            if (dbConnection != null && !dbConnection.isClosed()) {
                dbConnection.close();
                System.out.println("\n🔌 تم إغلاق الاتصال بقاعدة البيانات");
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
        }
    }
}
