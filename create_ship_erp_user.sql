-- إنشاء مستخدم SHIP_ERP في قاعدة البيانات Oracle
-- Create SHIP_ERP User in Oracle Database

-- الاتصال كـ SYSTEM أو DBA
-- Connect as SYSTEM or DBA user

PROMPT ========================================
PROMPT   CREATE SHIP_ERP USER SCRIPT
PROMPT   سكريبت إنشاء مستخدم SHIP_ERP
PROMPT ========================================

-- التحقق من وجود المستخدم
PROMPT Checking if user SHIP_ERP exists...
PROMPT فحص وجود المستخدم SHIP_ERP...

SELECT 'User SHIP_ERP exists' AS status 
FROM DBA_USERS 
WHERE USERNAME = 'SHIP_ERP'
UNION ALL
SELECT 'User SHIP_ERP does not exist' AS status 
FROM DUAL 
WHERE NOT EXISTS (SELECT 1 FROM DBA_USERS WHERE USERNAME = 'SHIP_ERP');

-- حذف المستخدم إذا كان موجوداً (اختياري)
-- Drop user if exists (optional)
PROMPT Dropping existing user if exists...
PROMPT حذف المستخدم الموجود إن وجد...

BEGIN
    EXECUTE IMMEDIATE 'DROP USER SHIP_ERP CASCADE';
    DBMS_OUTPUT.PUT_LINE('User SHIP_ERP dropped successfully');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1918 THEN
            DBMS_OUTPUT.PUT_LINE('User SHIP_ERP does not exist');
        ELSE
            DBMS_OUTPUT.PUT_LINE('Error dropping user: ' || SQLERRM);
        END IF;
END;
/

-- إنشاء المستخدم الجديد
-- Create new user
PROMPT Creating new user SHIP_ERP...
PROMPT إنشاء مستخدم جديد SHIP_ERP...

CREATE USER SHIP_ERP 
IDENTIFIED BY ship_erp_password
DEFAULT TABLESPACE USERS
TEMPORARY TABLESPACE TEMP
QUOTA UNLIMITED ON USERS;

PROMPT User SHIP_ERP created successfully!
PROMPT تم إنشاء المستخدم SHIP_ERP بنجاح!

-- منح الصلاحيات الأساسية
-- Grant basic privileges
PROMPT Granting basic privileges...
PROMPT منح الصلاحيات الأساسية...

GRANT CONNECT TO SHIP_ERP;
GRANT RESOURCE TO SHIP_ERP;
GRANT CREATE SESSION TO SHIP_ERP;
GRANT CREATE TABLE TO SHIP_ERP;
GRANT CREATE VIEW TO SHIP_ERP;
GRANT CREATE SEQUENCE TO SHIP_ERP;
GRANT CREATE PROCEDURE TO SHIP_ERP;
GRANT CREATE TRIGGER TO SHIP_ERP;
GRANT CREATE SYNONYM TO SHIP_ERP;
GRANT CREATE INDEX TO SHIP_ERP;

PROMPT Basic privileges granted successfully!
PROMPT تم منح الصلاحيات الأساسية بنجاح!

-- منح صلاحيات إضافية للتطوير
-- Grant additional development privileges
PROMPT Granting additional privileges...
PROMPT منح صلاحيات إضافية...

GRANT CREATE ANY TABLE TO SHIP_ERP;
GRANT ALTER ANY TABLE TO SHIP_ERP;
GRANT DROP ANY TABLE TO SHIP_ERP;
GRANT SELECT ANY TABLE TO SHIP_ERP;
GRANT INSERT ANY TABLE TO SHIP_ERP;
GRANT UPDATE ANY TABLE TO SHIP_ERP;
GRANT DELETE ANY TABLE TO SHIP_ERP;

PROMPT Additional privileges granted!
PROMPT تم منح الصلاحيات الإضافية!

-- إنشاء الجداول الأساسية
-- Create basic tables
PROMPT Creating basic tables...
PROMPT إنشاء الجداول الأساسية...

-- الاتصال كـ SHIP_ERP
CONNECT SHIP_ERP/ship_erp_password@localhost:1521/ORCL;

-- إنشاء جدول الأصناف الرئيسي
CREATE TABLE IAS_ITM_MST (
    ITM_CD VARCHAR2(20) PRIMARY KEY,
    ITM_NM VARCHAR2(200) NOT NULL,
    ITM_NM_AR NVARCHAR2(200),
    ITM_DESC VARCHAR2(500),
    ITM_DESC_AR NVARCHAR2(500),
    ITM_GRP_CD VARCHAR2(10),
    ITM_SUB_GRP_CD VARCHAR2(10),
    ITM_UNIT VARCHAR2(10),
    ITM_PRICE NUMBER(15,3) DEFAULT 0,
    ITM_COST NUMBER(15,3) DEFAULT 0,
    ITM_STATUS VARCHAR2(1) DEFAULT 'A',
    CREATED_BY VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MODIFIED_BY VARCHAR2(50),
    MODIFIED_DATE DATE
);

-- إنشاء جدول تفاصيل الأصناف
CREATE TABLE IAS_ITM_DTL (
    ITM_CD VARCHAR2(20),
    ITM_DTL_CD VARCHAR2(20),
    ITM_DTL_NM VARCHAR2(200),
    ITM_DTL_NM_AR NVARCHAR2(200),
    ITM_DTL_DESC VARCHAR2(500),
    ITM_DTL_QTY NUMBER(15,3) DEFAULT 0,
    ITM_DTL_UNIT VARCHAR2(10),
    ITM_DTL_PRICE NUMBER(15,3) DEFAULT 0,
    ITM_DTL_STATUS VARCHAR2(1) DEFAULT 'A',
    CREATED_BY VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE,
    PRIMARY KEY (ITM_CD, ITM_DTL_CD),
    FOREIGN KEY (ITM_CD) REFERENCES IAS_ITM_MST(ITM_CD)
);

-- إنشاء جدول وحدات القياس
CREATE TABLE ERP_MEASUREMENT (
    UNIT_CD VARCHAR2(10) PRIMARY KEY,
    UNIT_NM VARCHAR2(50) NOT NULL,
    UNIT_NM_AR NVARCHAR2(50),
    UNIT_DESC VARCHAR2(200),
    UNIT_TYPE VARCHAR2(20),
    CONVERSION_FACTOR NUMBER(10,6) DEFAULT 1,
    BASE_UNIT VARCHAR2(10),
    STATUS VARCHAR2(1) DEFAULT 'A',
    CREATED_BY VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE
);

-- إنشاء جدول المجموعات الفرعية
CREATE TABLE ERP_SUB_GRP_DTL (
    GRP_CD VARCHAR2(10),
    SUB_GRP_CD VARCHAR2(10),
    SUB_GRP_NM VARCHAR2(100),
    SUB_GRP_NM_AR NVARCHAR2(100),
    SUB_GRP_DESC VARCHAR2(300),
    STATUS VARCHAR2(1) DEFAULT 'A',
    CREATED_BY VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE,
    PRIMARY KEY (GRP_CD, SUB_GRP_CD)
);

-- إنشاء جدول المجموعات المساعدة
CREATE TABLE ERP_ASSISTANT_GROUP (
    ASST_GRP_CD VARCHAR2(10) PRIMARY KEY,
    ASST_GRP_NM VARCHAR2(100) NOT NULL,
    ASST_GRP_NM_AR NVARCHAR2(100),
    ASST_GRP_DESC VARCHAR2(300),
    PARENT_GRP_CD VARCHAR2(10),
    STATUS VARCHAR2(1) DEFAULT 'A',
    CREATED_BY VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE
);

-- إنشاء جدول مجموعات التفاصيل
CREATE TABLE ERP_DETAIL_GROUP (
    DTL_GRP_CD VARCHAR2(10) PRIMARY KEY,
    DTL_GRP_NM VARCHAR2(100) NOT NULL,
    DTL_GRP_NM_AR NVARCHAR2(100),
    DTL_GRP_DESC VARCHAR2(300),
    PARENT_GRP_CD VARCHAR2(10),
    STATUS VARCHAR2(1) DEFAULT 'A',
    CREATED_BY VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE
);

-- إنشاء جدول تفاصيل المجموعات
CREATE TABLE ERP_GROUP_DETAILS (
    GRP_DTL_ID NUMBER PRIMARY KEY,
    GRP_CD VARCHAR2(10),
    DTL_TYPE VARCHAR2(20),
    DTL_VALUE VARCHAR2(200),
    DTL_VALUE_AR NVARCHAR2(200),
    DTL_ORDER NUMBER(3) DEFAULT 0,
    STATUS VARCHAR2(1) DEFAULT 'A',
    CREATED_BY VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE
);

-- إنشاء جدول المجموعات الرئيسية الفرعية
CREATE TABLE ERP_MAINSUB_GRP_DTL (
    MAIN_GRP_CD VARCHAR2(10),
    SUB_GRP_CD VARCHAR2(10),
    GRP_RELATION VARCHAR2(50),
    GRP_HIERARCHY NUMBER(2) DEFAULT 1,
    STATUS VARCHAR2(1) DEFAULT 'A',
    CREATED_BY VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE,
    PRIMARY KEY (MAIN_GRP_CD, SUB_GRP_CD)
);

-- إنشاء sequence للمعرفات
CREATE SEQUENCE ERP_GROUP_DETAILS_SEQ
START WITH 1
INCREMENT BY 1
NOCACHE;

-- إنشاء فهارس للأداء
CREATE INDEX IDX_ITM_MST_GRP ON IAS_ITM_MST(ITM_GRP_CD);
CREATE INDEX IDX_ITM_MST_STATUS ON IAS_ITM_MST(ITM_STATUS);
CREATE INDEX IDX_ITM_DTL_STATUS ON IAS_ITM_DTL(ITM_DTL_STATUS);
CREATE INDEX IDX_MEASUREMENT_TYPE ON ERP_MEASUREMENT(UNIT_TYPE);

PROMPT Tables created successfully!
PROMPT تم إنشاء الجداول بنجاح!

-- إدراج بيانات تجريبية
-- Insert sample data
PROMPT Inserting sample data...
PROMPT إدراج بيانات تجريبية...

-- وحدات القياس
INSERT INTO ERP_MEASUREMENT VALUES ('KG', 'Kilogram', 'كيلوجرام', 'Weight unit', 'WEIGHT', 1, 'KG', 'A', 'SYSTEM', SYSDATE);
INSERT INTO ERP_MEASUREMENT VALUES ('G', 'Gram', 'جرام', 'Weight unit', 'WEIGHT', 0.001, 'KG', 'A', 'SYSTEM', SYSDATE);
INSERT INTO ERP_MEASUREMENT VALUES ('M', 'Meter', 'متر', 'Length unit', 'LENGTH', 1, 'M', 'A', 'SYSTEM', SYSDATE);
INSERT INTO ERP_MEASUREMENT VALUES ('CM', 'Centimeter', 'سنتيمتر', 'Length unit', 'LENGTH', 0.01, 'M', 'A', 'SYSTEM', SYSDATE);
INSERT INTO ERP_MEASUREMENT VALUES ('L', 'Liter', 'لتر', 'Volume unit', 'VOLUME', 1, 'L', 'A', 'SYSTEM', SYSDATE);
INSERT INTO ERP_MEASUREMENT VALUES ('ML', 'Milliliter', 'مليلتر', 'Volume unit', 'VOLUME', 0.001, 'L', 'A', 'SYSTEM', SYSDATE);
INSERT INTO ERP_MEASUREMENT VALUES ('PCS', 'Pieces', 'قطعة', 'Count unit', 'COUNT', 1, 'PCS', 'A', 'SYSTEM', SYSDATE);
INSERT INTO ERP_MEASUREMENT VALUES ('BOX', 'Box', 'صندوق', 'Package unit', 'PACKAGE', 1, 'BOX', 'A', 'SYSTEM', SYSDATE);

-- مجموعات مساعدة
INSERT INTO ERP_ASSISTANT_GROUP VALUES ('ELEC', 'Electronics', 'إلكترونيات', 'Electronic items', NULL, 'A', 'SYSTEM', SYSDATE);
INSERT INTO ERP_ASSISTANT_GROUP VALUES ('FOOD', 'Food Items', 'مواد غذائية', 'Food and beverages', NULL, 'A', 'SYSTEM', SYSDATE);
INSERT INTO ERP_ASSISTANT_GROUP VALUES ('CLOTH', 'Clothing', 'ملابس', 'Clothing and textiles', NULL, 'A', 'SYSTEM', SYSDATE);

-- أصناف تجريبية
INSERT INTO IAS_ITM_MST VALUES ('ITM001', 'Sample Item 1', 'صنف تجريبي 1', 'Sample description', 'وصف تجريبي', 'ELEC', 'SUB01', 'PCS', 100.000, 80.000, 'A', 'SYSTEM', SYSDATE, NULL, NULL);
INSERT INTO IAS_ITM_MST VALUES ('ITM002', 'Sample Item 2', 'صنف تجريبي 2', 'Sample description', 'وصف تجريبي', 'FOOD', 'SUB02', 'KG', 50.000, 40.000, 'A', 'SYSTEM', SYSDATE, NULL, NULL);

COMMIT;

PROMPT Sample data inserted successfully!
PROMPT تم إدراج البيانات التجريبية بنجاح!

-- عرض ملخص
PROMPT ========================================
PROMPT   SHIP_ERP USER CREATION COMPLETED
PROMPT   تم الانتهاء من إنشاء مستخدم SHIP_ERP
PROMPT ========================================

SELECT 'User: SHIP_ERP' AS info FROM DUAL
UNION ALL
SELECT 'Password: ship_erp_password' FROM DUAL
UNION ALL
SELECT 'Status: Created Successfully' FROM DUAL;

PROMPT
PROMPT Connection string: SHIP_ERP/ship_erp_password@localhost:1521/ORCL
PROMPT سلسلة الاتصال: SHIP_ERP/ship_erp_password@localhost:1521/ORCL
PROMPT

EXIT;
