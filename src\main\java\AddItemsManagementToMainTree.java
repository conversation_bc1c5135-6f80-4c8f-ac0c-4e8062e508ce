import java.sql.*;

/**
 * إضافة نظام إدارة الأصناف للقائمة الرئيسية
 * Add Items Management System to Main Tree
 */
public class AddItemsManagementToMainTree {
    
    public static void main(String[] args) {
        System.out.println("🔧 إضافة نظام إدارة الأصناف للقائمة الرئيسية...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            // البحث عن فئة إدارة الأصناف
            int itemsCategoryId = findItemsCategory(connection);
            
            if (itemsCategoryId > 0) {
                // تحديث الفئة لتظهر في القائمة الرئيسية
                updateCategoryToMainLevel(connection, itemsCategoryId);
                System.out.println("✅ تم تحديث فئة إدارة الأصناف لتظهر في القائمة الرئيسية");
            } else {
                // إنشاء فئة إدارة الأصناف في القائمة الرئيسية
                createItemsManagementCategory(connection);
                System.out.println("✅ تم إنشاء فئة إدارة الأصناف في القائمة الرئيسية");
            }
            
            connection.close();
            System.out.println("🎉 تم إضافة نظام إدارة الأصناف بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إضافة نظام إدارة الأصناف: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static int findItemsCategory(Connection connection) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'إدارة الأصناف' AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt("TREE_ID");
            }
        }
        return 0;
    }
    
    private static void updateCategoryToMainLevel(Connection connection, int categoryId) throws SQLException {
        // تحديث الفئة لتكون في المستوى الرئيسي
        String updateSQL = """
            UPDATE ERP_SYSTEM_TREE 
            SET PARENT_ID = NULL, 
                TREE_LEVEL = 1, 
                DISPLAY_ORDER = (SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID IS NULL),
                IS_VISIBLE = 'Y',
                IS_ACTIVE = 'Y',
                LAST_UPDATED = SYSDATE,
                UPDATED_BY = 'SYSTEM'
            WHERE TREE_ID = ?
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(updateSQL)) {
            stmt.setInt(1, categoryId);
            stmt.executeUpdate();
        }
    }
    
    private static void createItemsManagementCategory(Connection connection) throws SQLException {
        // إنشاء فئة إدارة الأصناف الرئيسية
        int nextOrder = getNextRootOrder(connection);
        
        String insertCategorySQL = """
            INSERT INTO ERP_SYSTEM_TREE 
            (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, 
             DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
             CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
            VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, NULL, 'إدارة الأصناف', 'Items Management', 
                    'نظام شامل لإدارة الأصناف والمنتجات والمخزون', 'CATEGORY', 
                    ?, 1, 'Y', 'Y', 
                    SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(insertCategorySQL)) {
            stmt.setInt(1, nextOrder);
            stmt.executeUpdate();
        }
        
        // الحصول على معرف الفئة الجديدة
        int categoryId = getLastInsertedId(connection);
        
        // إضافة النوافذ الفرعية
        addItemsWindows(connection, categoryId);
    }
    
    private static int getNextRootOrder(Connection connection) throws SQLException {
        String sql = "SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID IS NULL";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        return 1;
    }
    
    private static int getLastInsertedId(Connection connection) throws SQLException {
        String sql = "SELECT ERP_SYSTEM_TREE_SEQ.CURRVAL FROM DUAL";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        return 0;
    }
    
    private static void addItemsWindows(Connection connection, int parentId) throws SQLException {
        System.out.println("📦 إضافة نوافذ إدارة الأصناف...");
        
        String[][] windows = {
            {"إدارة الأصناف", "Items Management", "ItemsManagementWindow", "إدارة شاملة للأصناف والمنتجات"},
            {"تصنيفات الأصناف", "Item Categories", "ItemCategoriesWindow", "إدارة تصنيفات ومجموعات الأصناف"},
            {"وحدات القياس", "Units of Measure", "UnitsOfMeasureWindow", "إدارة وحدات القياس والتحويلات"},
            {"أسعار الأصناف", "Item Prices", "ItemPricesWindow", "إدارة أسعار الأصناف وقوائم الأسعار"},
            {"مخزون الأصناف", "Item Inventory", "ItemInventoryWindow", "متابعة مخزون الأصناف والكميات"},
            {"حركة المخزون", "Inventory Transactions", "InventoryTransactionsWindow", "تتبع حركة دخول وخروج المخزون"},
            {"تقييم المخزون", "Inventory Valuation", "InventoryValuationWindow", "تقييم المخزون والتكلفة"},
            {"إعدادات المخزون", "Inventory Settings", "InventorySettingsWindow", "إعدادات نظام المخزون"},
            {"تقارير الأصناف", "Items Reports", "ItemsReportsWindow", "تقارير شاملة للأصناف"},
            {"استيراد الأصناف", "Import Items", "ImportItemsWindow", "استيراد الأصناف من ملفات خارجية"},
            {"تصدير الأصناف", "Export Items", "ExportItemsWindow", "تصدير بيانات الأصناف"}
        };
        
        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2], windows[i][3], i + 1);
        }
    }
    
    private static void addWindow(Connection connection, int parentId, String nameAr, String nameEn, 
                                 String windowClass, String description, int order) throws SQLException {
        
        // التحقق من وجود النافذة مسبقاً
        String checkSQL = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = ? OR (NODE_NAME_AR = ? AND PARENT_ID = ?)";
        
        try (PreparedStatement stmt = connection.prepareStatement(checkSQL)) {
            stmt.setString(1, windowClass);
            stmt.setString(2, nameAr);
            stmt.setInt(3, parentId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next() && rs.getInt(1) > 0) {
                    System.out.println("ℹ️ النافذة موجودة مسبقاً: " + nameAr);
                    return;
                }
            }
        }
        
        String sql = """
            INSERT INTO ERP_SYSTEM_TREE 
            (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, 
             WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
             CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
            VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, ?, ?, ?, ?, 'WINDOW', 
                    ?, ?, 2, 'Y', 'Y', 
                    SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            stmt.setString(2, nameAr);
            stmt.setString(3, nameEn);
            stmt.setString(4, description);
            stmt.setString(5, windowClass);
            stmt.setInt(6, order);
            
            int result = stmt.executeUpdate();
            if (result > 0) {
                System.out.println("✅ تم إضافة النافذة: " + nameAr);
            }
        }
    }
}
