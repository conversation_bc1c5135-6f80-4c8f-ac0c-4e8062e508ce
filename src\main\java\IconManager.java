import java.awt.*;
import java.util.HashMap;
import java.util.Map;
import javax.swing.*;

/**
 * مدير الرموز والأيقونات المحسن
 * Enhanced Icons and Symbols Manager
 */
public class IconManager {
    
    // خريطة الرموز النصية
    private static final Map<String, String> TEXT_ICONS = new HashMap<>();
    
    // الخطوط المدعومة للرموز
    private static Font symbolFont;
    private static Font emojiFont;
    
    static {
        initializeTextIcons();
        initializeFonts();
    }
    
    private static void initializeTextIcons() {
        // رموز الملفات والمجلدات
        TEXT_ICONS.put("file", "ملف");
        TEXT_ICONS.put("folder", "مجلد");
        TEXT_ICONS.put("refresh", "تحديث");
        TEXT_ICONS.put("sync", "مزامنة");
        
        // رموز البريد الإلكتروني
        TEXT_ICONS.put("email", "بريد");
        TEXT_ICONS.put("inbox", "وارد");
        TEXT_ICONS.put("outbox", "صادر");
        TEXT_ICONS.put("compose", "إنشاء");
        TEXT_ICONS.put("reply", "رد");
        TEXT_ICONS.put("forward", "تحويل");
        TEXT_ICONS.put("attachment", "مرفق");
        
        // رموز العمليات
        TEXT_ICONS.put("new", "جديد");
        TEXT_ICONS.put("edit", "تحرير");
        TEXT_ICONS.put("delete", "حذف");
        TEXT_ICONS.put("copy", "نسخ");
        TEXT_ICONS.put("cut", "قص");
        TEXT_ICONS.put("paste", "لصق");
        TEXT_ICONS.put("save", "حفظ");
        TEXT_ICONS.put("open", "فتح");
        TEXT_ICONS.put("close", "إغلاق");
        
        // رموز البحث والفلترة
        TEXT_ICONS.put("search", "بحث");
        TEXT_ICONS.put("filter", "فلتر");
        TEXT_ICONS.put("sort", "ترتيب");
        TEXT_ICONS.put("view", "عرض");
        
        // رموز الإعدادات والأدوات
        TEXT_ICONS.put("settings", "إعدادات");
        TEXT_ICONS.put("tools", "أدوات");
        TEXT_ICONS.put("preferences", "تفضيلات");
        TEXT_ICONS.put("options", "خيارات");
        
        // رموز المساعدة والمعلومات
        TEXT_ICONS.put("help", "مساعدة");
        TEXT_ICONS.put("info", "معلومات");
        TEXT_ICONS.put("about", "حول");
        TEXT_ICONS.put("manual", "دليل");
        
        // رموز الحالة
        TEXT_ICONS.put("read", "مقروء");
        TEXT_ICONS.put("unread", "غير مقروء");
        TEXT_ICONS.put("important", "مهم");
        TEXT_ICONS.put("urgent", "عاجل");
        TEXT_ICONS.put("spam", "مزعج");
        TEXT_ICONS.put("archive", "أرشيف");
        
        // رموز الطباعة والتصدير
        TEXT_ICONS.put("print", "طباعة");
        TEXT_ICONS.put("export", "تصدير");
        TEXT_ICONS.put("import", "استيراد");
        TEXT_ICONS.put("backup", "نسخ احتياطي");
        
        // رموز الشبكة والاتصال
        TEXT_ICONS.put("connect", "اتصال");
        TEXT_ICONS.put("disconnect", "قطع");
        TEXT_ICONS.put("online", "متصل");
        TEXT_ICONS.put("offline", "غير متصل");
        
        // رموز التنقل
        TEXT_ICONS.put("next", "التالي");
        TEXT_ICONS.put("previous", "السابق");
        TEXT_ICONS.put("first", "الأول");
        TEXT_ICONS.put("last", "الأخير");
        TEXT_ICONS.put("up", "أعلى");
        TEXT_ICONS.put("down", "أسفل");
        
        // رموز الوقت والتاريخ
        TEXT_ICONS.put("today", "اليوم");
        TEXT_ICONS.put("yesterday", "أمس");
        TEXT_ICONS.put("week", "أسبوع");
        TEXT_ICONS.put("month", "شهر");
        TEXT_ICONS.put("year", "سنة");
        
        // رموز الأمان
        TEXT_ICONS.put("secure", "آمن");
        TEXT_ICONS.put("lock", "قفل");
        TEXT_ICONS.put("unlock", "فتح القفل");
        TEXT_ICONS.put("encrypt", "تشفير");
        TEXT_ICONS.put("decrypt", "فك التشفير");
    }
    
    private static void initializeFonts() {
        try {
            // محاولة استخدام خطوط تدعم الرموز
            symbolFont = new Font("Segoe UI Symbol", Font.PLAIN, 12);
            emojiFont = new Font("Segoe UI Emoji", Font.PLAIN, 12);
            
            // التحقق من توفر الخطوط
            GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
            String[] fontNames = ge.getAvailableFontFamilyNames();
            
            boolean hasSymbolFont = false;
            boolean hasEmojiFont = false;
            
            for (String fontName : fontNames) {
                if (fontName.contains("Symbol")) hasSymbolFont = true;
                if (fontName.contains("Emoji")) hasEmojiFont = true;
            }
            
            if (!hasSymbolFont) {
                symbolFont = new Font("Arial Unicode MS", Font.PLAIN, 12);
            }
            if (!hasEmojiFont) {
                emojiFont = new Font("Arial Unicode MS", Font.PLAIN, 12);
            }
            
        } catch (Exception e) {
            // استخدام الخط الافتراضي
            symbolFont = new Font(Font.SANS_SERIF, Font.PLAIN, 12);
            emojiFont = new Font(Font.SANS_SERIF, Font.PLAIN, 12);
        }
    }
    
    /**
     * الحصول على رمز نصي
     */
    public static String getTextIcon(String iconName) {
        return TEXT_ICONS.getOrDefault(iconName, iconName);
    }
    
    /**
     * إنشاء زر مع رمز نصي
     */
    public static JButton createIconButton(String iconName, String tooltip) {
        String iconText = getTextIcon(iconName);
        JButton button = new JButton(iconText);
        button.setToolTipText(tooltip);
        button.setFont(new Font("Tahoma", Font.BOLD, 11));
        button.setPreferredSize(new Dimension(80, 30));
        button.setFocusPainted(false);
        
        // تحسين المظهر
        button.setBackground(new Color(240, 240, 240));
        button.setBorder(BorderFactory.createRaisedBevelBorder());
        
        // تأثير hover
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(220, 220, 220));
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(240, 240, 240));
            }
        });
        
        return button;
    }
    
    /**
     * إنشاء عنصر قائمة مع رمز
     */
    public static JMenuItem createIconMenuItem(String iconName, String text, String tooltip) {
        String iconText = getTextIcon(iconName);
        JMenuItem menuItem = new JMenuItem(iconText + " " + text);
        menuItem.setToolTipText(tooltip);
        menuItem.setFont(new Font("Tahoma", Font.PLAIN, 12));
        return menuItem;
    }
    
    /**
     * إنشاء تسمية مع رمز
     */
    public static JLabel createIconLabel(String iconName, String text) {
        String iconText = getTextIcon(iconName);
        JLabel label = new JLabel(iconText + " " + text);
        label.setFont(new Font("Tahoma", Font.PLAIN, 12));
        return label;
    }
    
    /**
     * الحصول على خط الرموز
     */
    public static Font getSymbolFont() {
        return symbolFont;
    }
    
    /**
     * الحصول على خط الرموز التعبيرية
     */
    public static Font getEmojiFont() {
        return emojiFont;
    }
    
    /**
     * تطبيق رمز على مكون موجود
     */
    public static void applyIcon(AbstractButton component, String iconName) {
        String iconText = getTextIcon(iconName);
        String currentText = component.getText();
        
        if (currentText != null && !currentText.isEmpty()) {
            component.setText(iconText + " " + currentText);
        } else {
            component.setText(iconText);
        }
    }
    
    /**
     * إنشاء أيقونة ملونة
     */
    public static JLabel createColoredIcon(String iconName, Color color) {
        String iconText = getTextIcon(iconName);
        JLabel icon = new JLabel(iconText);
        icon.setForeground(color);
        icon.setFont(symbolFont);
        return icon;
    }
}
