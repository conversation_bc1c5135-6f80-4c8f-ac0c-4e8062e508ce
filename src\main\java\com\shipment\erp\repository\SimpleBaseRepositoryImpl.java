package com.shipment.erp.repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javax.sql.DataSource;
import com.shipment.erp.model.BaseEntity;

/**
 * تنفيذ مبسط للمستودع الأساسي Simple Base Repository Implementation
 */
public abstract class SimpleBaseRepositoryImpl<T extends BaseEntity>
        implements SimpleBaseRepository<T> {

    protected final DataSource dataSource;

    public SimpleBaseRepositoryImpl(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    // Abstract methods to be implemented by subclasses
    protected abstract String getTableName();

    protected abstract String getSequenceName();

    protected abstract T mapResultSetToEntity(ResultSet rs) throws SQLException;

    protected abstract void setEntityParameters(PreparedStatement ps, T entity, boolean isUpdate)
            throws SQLException;

    protected abstract String getInsertSql();

    protected abstract String getUpdateSql();

    @Override
    public T save(T entity) {
        if (entity.getId() == null) {
            return insert(entity);
        } else {
            return update(entity);
        }
    }

    private T insert(T entity) {
        try (Connection conn = dataSource.getConnection()) {
            // Get next ID from sequence
            String seqSql = "SELECT " + getSequenceName() + ".NEXTVAL FROM DUAL";
            try (PreparedStatement seqPs = conn.prepareStatement(seqSql);
                    ResultSet rs = seqPs.executeQuery()) {
                if (rs.next()) {
                    entity.setId(rs.getLong(1));
                }
            }

            // Insert entity
            try (PreparedStatement ps = conn.prepareStatement(getInsertSql())) {
                ps.setLong(1, entity.getId());
                setEntityParameters(ps, entity, false);
                ps.executeUpdate();
            }

            return entity;
        } catch (SQLException e) {
            throw new RuntimeException("خطأ في إدراج البيانات", e);
        }
    }

    private T update(T entity) {
        try (Connection conn = dataSource.getConnection();
                PreparedStatement ps = conn.prepareStatement(getUpdateSql())) {

            setEntityParameters(ps, entity, true);
            ps.executeUpdate();
            return entity;

        } catch (SQLException e) {
            throw new RuntimeException("خطأ في تحديث البيانات", e);
        }
    }

    @Override
    public Optional<T> findById(Long id) {
        String sql = "SELECT * FROM " + getTableName() + " WHERE ID = ?";
        List<T> results = executeQuery(sql, id);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<T> findAll() {
        String sql = "SELECT * FROM " + getTableName() + " ORDER BY ID";
        return executeQuery(sql);
    }

    @Override
    public void deleteById(Long id) {
        String sql = "DELETE FROM " + getTableName() + " WHERE ID = ?";
        try (Connection conn = dataSource.getConnection();
                PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setLong(1, id);
            ps.executeUpdate();

        } catch (SQLException e) {
            throw new RuntimeException("خطأ في حذف البيانات", e);
        }
    }

    @Override
    public void delete(T entity) {
        deleteById(entity.getId());
    }

    @Override
    public boolean existsById(Long id) {
        String sql = "SELECT COUNT(*) FROM " + getTableName() + " WHERE ID = ?";
        try (Connection conn = dataSource.getConnection();
                PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setLong(1, id);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() && rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            throw new RuntimeException("خطأ في التحقق من وجود البيانات", e);
        }
    }

    @Override
    public long count() {
        String sql = "SELECT COUNT(*) FROM " + getTableName();
        try (Connection conn = dataSource.getConnection();
                PreparedStatement ps = conn.prepareStatement(sql);
                ResultSet rs = ps.executeQuery()) {

            return rs.next() ? rs.getLong(1) : 0;

        } catch (SQLException e) {
            throw new RuntimeException("خطأ في عد البيانات", e);
        }
    }

    @Override
    public List<T> findAllById(Iterable<Long> ids) {
        List<T> results = new ArrayList<>();
        for (Long id : ids) {
            findById(id).ifPresent(results::add);
        }
        return results;
    }

    @Override
    public List<T> saveAll(Iterable<T> entities) {
        List<T> results = new ArrayList<>();
        for (T entity : entities) {
            results.add(save(entity));
        }
        return results;
    }

    @Override
    public void deleteAll() {
        String sql = "DELETE FROM " + getTableName();
        try (Connection conn = dataSource.getConnection();
                PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.executeUpdate();

        } catch (SQLException e) {
            throw new RuntimeException("خطأ في حذف جميع البيانات", e);
        }
    }

    @Override
    public void deleteAll(Iterable<T> entities) {
        for (T entity : entities) {
            delete(entity);
        }
    }

    @Override
    public void deleteAllById(Iterable<Long> ids) {
        for (Long id : ids) {
            deleteById(id);
        }
    }

    // Helper method for executing queries
    protected List<T> executeQuery(String sql, Object... params) {
        List<T> results = new ArrayList<>();

        try (Connection conn = dataSource.getConnection();
                PreparedStatement ps = conn.prepareStatement(sql)) {

            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }

            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    results.add(mapResultSetToEntity(rs));
                }
            }

        } catch (SQLException e) {
            throw new RuntimeException("خطأ في تنفيذ الاستعلام: " + sql, e);
        }

        return results;
    }
}
