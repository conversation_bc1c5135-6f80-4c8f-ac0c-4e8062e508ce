import java.sql.*;
import java.util.Properties;

/**
 * فحص جداول البريد الإلكتروني في قاعدة بيانات ship_erp
 */
public class CheckShipErpEmailTables {
    
    private static final String DB_URL = "*************************************";
    private static final String DB_USER = "ship_erp";
    private static final String DB_PASSWORD = "ship_erp";
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  CHECKING SHIP_ERP EMAIL TABLES");
        System.out.println("  فحص جداول البريد الإلكتروني في ship_erp");
        System.out.println("========================================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", DB_USER);
            props.setProperty("password", DB_PASSWORD);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            Connection connection = DriverManager.getConnection(DB_URL, props);
            System.out.println("✅ تم الاتصال بقاعدة بيانات ship_erp بنجاح");
            
            // فحص الجداول المرتبطة بالبريد الإلكتروني
            System.out.println("\n📧 البحث عن جداول البريد الإلكتروني:");
            searchEmailTables(connection);
            
            // فحص الجداول المطلوبة تحديداً
            System.out.println("\n📋 فحص الجداول المطلوبة:");
            checkTable(connection, "EMAIL_ACCOUNTS");
            checkTable(connection, "ERP_EMAIL_ACCOUNTS");
            checkTable(connection, "EMAIL_MESSAGES");
            checkTable(connection, "EMAIL_TEMPLATES");
            checkTable(connection, "EMAIL_ATTACHMENTS");
            checkTable(connection, "EMAIL_ADDRESS_BOOK");
            
            // فحص المتسلسلات
            System.out.println("\n🔢 فحص المتسلسلات:");
            searchEmailSequences(connection);
            
            // محاولة تشغيل الاستعلام الذي تستخدمه النافذة
            System.out.println("\n🔍 اختبار استعلام النافذة:");
            testWindowQuery(connection);
            
            // فحص جدول شجرة النظام
            System.out.println("\n🌳 فحص جدول شجرة النظام:");
            checkSystemTree(connection);
            
            connection.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فحص الجداول: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void searchEmailTables(Connection connection) {
        try {
            String sql = "SELECT TABLE_NAME FROM USER_TABLES WHERE TABLE_NAME LIKE '%EMAIL%' ORDER BY TABLE_NAME";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                boolean found = false;
                while (rs.next()) {
                    String tableName = rs.getString("TABLE_NAME");
                    System.out.println("   📧 " + tableName);
                    found = true;
                    
                    // فحص عدد السجلات
                    try {
                        String countSql = "SELECT COUNT(*) FROM " + tableName;
                        try (PreparedStatement countStmt = connection.prepareStatement(countSql);
                             ResultSet countRs = countStmt.executeQuery()) {
                            if (countRs.next()) {
                                System.out.println("      📊 عدد السجلات: " + countRs.getInt(1));
                            }
                        }
                    } catch (SQLException e) {
                        System.out.println("      ⚠️ لا يمكن قراءة السجلات: " + e.getMessage());
                    }
                }
                
                if (!found) {
                    System.out.println("   ⚠️ لم يتم العثور على أي جداول تحتوي على كلمة EMAIL");
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في البحث عن جداول البريد الإلكتروني: " + e.getMessage());
        }
    }
    
    private static void checkTable(Connection connection, String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, tableName.toUpperCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        System.out.println("✅ جدول " + tableName + " موجود");
                        
                        // فحص الأعمدة
                        checkTableColumns(connection, tableName);
                        
                    } else {
                        System.out.println("❌ جدول " + tableName + " غير موجود");
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في فحص جدول " + tableName + ": " + e.getMessage());
        }
    }
    
    private static void checkTableColumns(Connection connection, String tableName) {
        try {
            String sql = "SELECT COLUMN_NAME, DATA_TYPE FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ? ORDER BY COLUMN_ID";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, tableName.toUpperCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    
                    System.out.println("      📋 أعمدة الجدول:");
                    int count = 0;
                    while (rs.next() && count < 10) { // عرض أول 10 أعمدة فقط
                        String columnName = rs.getString("COLUMN_NAME");
                        String dataType = rs.getString("DATA_TYPE");
                        System.out.println("         🔹 " + columnName + " (" + dataType + ")");
                        count++;
                    }
                    
                    if (count == 0) {
                        System.out.println("         ⚠️ لا توجد أعمدة");
                    } else if (count == 10) {
                        System.out.println("         ... (والمزيد)");
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في فحص أعمدة جدول " + tableName + ": " + e.getMessage());
        }
    }
    
    private static void searchEmailSequences(Connection connection) {
        try {
            String sql = "SELECT SEQUENCE_NAME FROM USER_SEQUENCES WHERE SEQUENCE_NAME LIKE '%EMAIL%' ORDER BY SEQUENCE_NAME";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                boolean found = false;
                while (rs.next()) {
                    String sequenceName = rs.getString("SEQUENCE_NAME");
                    System.out.println("   🔢 " + sequenceName);
                    found = true;
                }
                
                if (!found) {
                    System.out.println("   ⚠️ لم يتم العثور على أي متسلسلات تحتوي على كلمة EMAIL");
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في البحث عن المتسلسلات: " + e.getMessage());
        }
    }
    
    private static void testWindowQuery(Connection connection) {
        try {
            // محاولة تشغيل الاستعلام الأساسي
            String sql = "SELECT * FROM EMAIL_ACCOUNTS WHERE ROWNUM <= 5";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                System.out.println("✅ استعلام EMAIL_ACCOUNTS يعمل بنجاح");
                
                int count = 0;
                while (rs.next()) {
                    System.out.println("   📧 حساب موجود: " + rs.getString("ACCOUNT_NAME"));
                    count++;
                }
                
                if (count == 0) {
                    System.out.println("   ⚠️ لا توجد حسابات بريد إلكتروني في قاعدة البيانات");
                }
                
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في استعلام EMAIL_ACCOUNTS: " + e.getMessage());
            System.err.println("   السبب المحتمل: جدول EMAIL_ACCOUNTS غير موجود أو لا يحتوي على الأعمدة المطلوبة");
        }
    }
    
    private static void checkSystemTree(Connection connection) {
        try {
            String sql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'SYSTEM_TREE'";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next() && rs.getInt(1) > 0) {
                    System.out.println("✅ جدول SYSTEM_TREE موجود");
                    
                    // البحث عن نافذة البريد الإلكتروني في الشجرة
                    String searchSql = "SELECT NODE_NAME_AR, NODE_NAME_EN, WINDOW_CLASS FROM SYSTEM_TREE WHERE WINDOW_CLASS LIKE '%Email%' OR NODE_NAME_EN LIKE '%Email%'";
                    try (PreparedStatement searchStmt = connection.prepareStatement(searchSql);
                         ResultSet searchRs = searchStmt.executeQuery()) {
                        
                        boolean found = false;
                        while (searchRs.next()) {
                            System.out.println("   📧 عقدة البريد الإلكتروني: " + searchRs.getString("NODE_NAME_AR") + 
                                             " (" + searchRs.getString("WINDOW_CLASS") + ")");
                            found = true;
                        }
                        
                        if (!found) {
                            System.out.println("   ⚠️ لم يتم العثور على نافذة البريد الإلكتروني في شجرة النظام");
                        }
                    }
                    
                } else {
                    System.out.println("❌ جدول SYSTEM_TREE غير موجود");
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في فحص جدول SYSTEM_TREE: " + e.getMessage());
        }
    }
}
