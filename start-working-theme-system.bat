@echo off
echo ========================================
echo   Working Theme System
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Compiling working theme manager...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\WorkingThemeManager.java
if %errorlevel% neq 0 (
    echo Failed to compile WorkingThemeManager
    pause
    exit /b 1
)

echo.
echo [2] Compiling working theme window...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\WorkingThemeWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile WorkingThemeWindow
    pause
    exit /b 1
)

echo.
echo [3] Compiling updated TreeMenuPanel...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo Failed to compile TreeMenuPanel
    pause
    exit /b 1
)

echo.
echo [4] Starting working theme system...
java -cp "lib\*;." WorkingThemeWindow

echo.
echo Working theme system finished
pause
