-- فحص حالة قاعدة البيانات ship_erp
-- Check ship_erp Database Status

SET PAGESIZE 50
SET LINESIZE 100

PROMPT ========================================
PROMPT    فحص حالة قاعدة البيانات ship_erp
PROMPT    Check ship_erp Database Status
PROMPT ========================================

PROMPT 
PROMPT 1. معلومات المستخدم الحالي:
SELECT USER AS CURRENT_USER, 
       SYS_CONTEXT('USERENV', 'SESSION_USER') AS SESSION_USER,
       SYS_CONTEXT('USERENV', 'DB_NAME') AS DATABASE_NAME
FROM DUAL;

PROMPT 
PROMPT 2. الجداول الموجودة:
SELECT COUNT(*) AS TOTAL_TABLES FROM user_tables;

PROMPT 
PROMPT 3. قائمة الجداول:
SELECT table_name FROM user_tables ORDER BY table_name;

PROMPT 
PROMPT 4. المتسلسلات الموجودة:
SELECT COUNT(*) AS TOTAL_SEQUENCES FROM user_sequences;

PROMPT 
PROMPT 5. قائمة المتسلسلات:
SELECT sequence_name FROM user_sequences ORDER BY sequence_name;

PROMPT 
PROMPT 6. الفهارس الموجودة:
SELECT COUNT(*) AS TOTAL_INDEXES FROM user_indexes;

PROMPT 
PROMPT 7. حالة الاتصال:
SELECT 'تم الاتصال بنجاح!' AS CONNECTION_STATUS FROM DUAL;

PROMPT 
PROMPT ========================================
PROMPT    انتهى الفحص
PROMPT ========================================

EXIT;
