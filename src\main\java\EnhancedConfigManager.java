import java.io.*;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * مدير التكوين المحسن - Enhanced Configuration Manager
 * يدير إعدادات النظام بشكل آمن ومحسن
 */
public class EnhancedConfigManager {
    
    private static EnhancedConfigManager instance;
    private Properties systemConfig;
    private Properties databaseConfig;
    private Properties securityConfig;
    
    private final String SYSTEM_CONFIG_FILE = "ship_erp_settings.properties";
    private final String DATABASE_CONFIG_FILE = "database_config.properties";
    private final String SECURITY_CONFIG_FILE = "security_config.properties";
    
    // Cache للإعدادات
    private Map<String, String> configCache = new ConcurrentHashMap<>();
    
    private EnhancedConfigManager() {
        loadAllConfigurations();
    }
    
    public static synchronized EnhancedConfigManager getInstance() {
        if (instance == null) {
            instance = new EnhancedConfigManager();
        }
        return instance;
    }
    
    /**
     * تحميل جميع ملفات التكوين
     */
    private void loadAllConfigurations() {
        try {
            // تحميل إعدادات النظام
            systemConfig = loadProperties(SYSTEM_CONFIG_FILE);
            
            // تحميل إعدادات قاعدة البيانات
            databaseConfig = loadProperties(DATABASE_CONFIG_FILE);
            
            // تحميل إعدادات الأمان
            securityConfig = loadProperties(SECURITY_CONFIG_FILE);
            
            // إنشاء ملفات افتراضية إذا لم تكن موجودة
            createDefaultConfigurations();
            
            System.out.println("✅ تم تحميل جميع ملفات التكوين بنجاح");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحميل ملفات التكوين: " + e.getMessage());
        }
    }
    
    /**
     * تحميل ملف properties
     */
    private Properties loadProperties(String filename) {
        Properties props = new Properties();
        try (InputStream input = new FileInputStream(filename)) {
            props.load(input);
        } catch (FileNotFoundException e) {
            System.out.println("⚠️ ملف التكوين غير موجود: " + filename + " - سيتم إنشاؤه");
        } catch (IOException e) {
            System.err.println("❌ خطأ في قراءة ملف التكوين: " + filename);
        }
        return props;
    }
    
    /**
     * إنشاء ملفات التكوين الافتراضية
     */
    private void createDefaultConfigurations() {
        try {
            // إنشاء إعدادات قاعدة البيانات الافتراضية
            if (databaseConfig.isEmpty()) {
                createDefaultDatabaseConfig();
            }
            
            // إنشاء إعدادات الأمان الافتراضية
            if (securityConfig.isEmpty()) {
                createDefaultSecurityConfig();
            }
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء ملفات التكوين الافتراضية: " + e.getMessage());
        }
    }
    
    /**
     * إنشاء إعدادات قاعدة البيانات الافتراضية
     */
    private void createDefaultDatabaseConfig() throws IOException {
        SecurityManager securityManager = SecurityManager.getInstance();
        
        databaseConfig.setProperty("# Database Configuration", "");
        databaseConfig.setProperty("db.host", "localhost");
        databaseConfig.setProperty("db.port", "1521");
        databaseConfig.setProperty("db.sid", "ORCL");
        
        // تشفير كلمات المرور
        String encryptedShipErpPassword = securityManager.encryptPassword("ship_erp_password");
        String encryptedIasPassword = securityManager.encryptPassword("ys123");
        
        databaseConfig.setProperty("db.ship_erp.username", "SHIP_ERP");
        databaseConfig.setProperty("db.ship_erp.password.encrypted", encryptedShipErpPassword);
        databaseConfig.setProperty("db.ias20251.username", "ias20251");
        databaseConfig.setProperty("db.ias20251.password.encrypted", encryptedIasPassword);
        
        // إعدادات الاتصال
        databaseConfig.setProperty("db.connection.pool.min", "2");
        databaseConfig.setProperty("db.connection.pool.max", "10");
        databaseConfig.setProperty("db.connection.timeout", "30000");
        databaseConfig.setProperty("db.connection.validation.query", "SELECT 1 FROM DUAL");
        
        saveProperties(databaseConfig, DATABASE_CONFIG_FILE, "Database Configuration");
        System.out.println("✅ تم إنشاء ملف إعدادات قاعدة البيانات");
    }
    
    /**
     * إنشاء إعدادات الأمان الافتراضية
     */
    private void createDefaultSecurityConfig() throws IOException {
        securityConfig.setProperty("# Security Configuration", "");
        securityConfig.setProperty("security.encryption.enabled", "true");
        securityConfig.setProperty("security.password.min.length", "8");
        securityConfig.setProperty("security.password.require.uppercase", "true");
        securityConfig.setProperty("security.password.require.lowercase", "true");
        securityConfig.setProperty("security.password.require.numbers", "true");
        securityConfig.setProperty("security.password.require.special", "false");
        securityConfig.setProperty("security.session.timeout", "3600000"); // 1 hour
        securityConfig.setProperty("security.max.login.attempts", "3");
        securityConfig.setProperty("security.lockout.duration", "300000"); // 5 minutes
        
        saveProperties(securityConfig, SECURITY_CONFIG_FILE, "Security Configuration");
        System.out.println("✅ تم إنشاء ملف إعدادات الأمان");
    }
    
    /**
     * حفظ ملف properties
     */
    private void saveProperties(Properties props, String filename, String comments) throws IOException {
        try (OutputStream output = new FileOutputStream(filename)) {
            props.store(output, comments);
        }
    }
    
    /**
     * الحصول على قيمة من التكوين
     */
    public String getConfigValue(String key) {
        // البحث في الكاش أولاً
        if (configCache.containsKey(key)) {
            return configCache.get(key);
        }
        
        String value = null;
        
        // البحث في إعدادات النظام
        if (systemConfig.containsKey(key)) {
            value = systemConfig.getProperty(key);
        }
        // البحث في إعدادات قاعدة البيانات
        else if (databaseConfig.containsKey(key)) {
            value = databaseConfig.getProperty(key);
        }
        // البحث في إعدادات الأمان
        else if (securityConfig.containsKey(key)) {
            value = securityConfig.getProperty(key);
        }
        
        // إضافة إلى الكاش
        if (value != null) {
            configCache.put(key, value);
        }
        
        return value;
    }
    
    /**
     * الحصول على قيمة مع قيمة افتراضية
     */
    public String getConfigValue(String key, String defaultValue) {
        String value = getConfigValue(key);
        return value != null ? value : defaultValue;
    }
    
    /**
     * الحصول على قيمة رقمية
     */
    public int getIntConfigValue(String key, int defaultValue) {
        try {
            String value = getConfigValue(key);
            return value != null ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * الحصول على قيمة boolean
     */
    public boolean getBooleanConfigValue(String key, boolean defaultValue) {
        String value = getConfigValue(key);
        return value != null ? Boolean.parseBoolean(value) : defaultValue;
    }
    
    /**
     * تعيين قيمة في التكوين
     */
    public void setConfigValue(String key, String value) {
        // تحديث الكاش
        configCache.put(key, value);
        
        // تحديث الملف المناسب
        if (key.startsWith("db.")) {
            databaseConfig.setProperty(key, value);
            try {
                saveProperties(databaseConfig, DATABASE_CONFIG_FILE, "Database Configuration");
            } catch (IOException e) {
                System.err.println("❌ خطأ في حفظ إعدادات قاعدة البيانات: " + e.getMessage());
            }
        } else if (key.startsWith("security.")) {
            securityConfig.setProperty(key, value);
            try {
                saveProperties(securityConfig, SECURITY_CONFIG_FILE, "Security Configuration");
            } catch (IOException e) {
                System.err.println("❌ خطأ في حفظ إعدادات الأمان: " + e.getMessage());
            }
        } else {
            systemConfig.setProperty(key, value);
            try {
                saveProperties(systemConfig, SYSTEM_CONFIG_FILE, "System Configuration");
            } catch (IOException e) {
                System.err.println("❌ خطأ في حفظ إعدادات النظام: " + e.getMessage());
            }
        }
    }
    
    /**
     * الحصول على كلمة مرور مفكوكة التشفير
     */
    public String getDecryptedPassword(String key) {
        String encryptedPassword = getConfigValue(key);
        if (encryptedPassword != null) {
            SecurityManager securityManager = SecurityManager.getInstance();
            return securityManager.decryptPassword(encryptedPassword);
        }
        return null;
    }
    
    /**
     * تعيين كلمة مرور مشفرة
     */
    public void setEncryptedPassword(String key, String password) {
        SecurityManager securityManager = SecurityManager.getInstance();
        String encryptedPassword = securityManager.encryptPassword(password);
        setConfigValue(key, encryptedPassword);
    }
    
    /**
     * إنشاء نسخة احتياطية من ملفات التكوين
     */
    public void backupConfigurations() {
        try {
            String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            
            String backupDir = "config_backup_" + timestamp;
            Files.createDirectories(Paths.get(backupDir));
            
            // نسخ ملفات التكوين
            Files.copy(Paths.get(SYSTEM_CONFIG_FILE), 
                      Paths.get(backupDir, SYSTEM_CONFIG_FILE), 
                      StandardCopyOption.REPLACE_EXISTING);
            
            if (Files.exists(Paths.get(DATABASE_CONFIG_FILE))) {
                Files.copy(Paths.get(DATABASE_CONFIG_FILE), 
                          Paths.get(backupDir, DATABASE_CONFIG_FILE), 
                          StandardCopyOption.REPLACE_EXISTING);
            }
            
            if (Files.exists(Paths.get(SECURITY_CONFIG_FILE))) {
                Files.copy(Paths.get(SECURITY_CONFIG_FILE), 
                          Paths.get(backupDir, SECURITY_CONFIG_FILE), 
                          StandardCopyOption.REPLACE_EXISTING);
            }
            
            System.out.println("✅ تم إنشاء نسخة احتياطية من ملفات التكوين في: " + backupDir);
            
        } catch (IOException e) {
            System.err.println("❌ خطأ في إنشاء نسخة احتياطية: " + e.getMessage());
        }
    }
    
    /**
     * إعادة تحميل ملفات التكوين
     */
    public void reloadConfigurations() {
        configCache.clear();
        loadAllConfigurations();
        System.out.println("✅ تم إعادة تحميل ملفات التكوين");
    }
    
    /**
     * طباعة ملخص التكوين
     */
    public void printConfigurationSummary() {
        System.out.println("\n⚙️ ملخص التكوين:");
        System.out.println("=" + "=".repeat(40));
        
        System.out.println("📊 إعدادات قاعدة البيانات:");
        System.out.println("   المضيف: " + getConfigValue("db.host", "localhost"));
        System.out.println("   المنفذ: " + getConfigValue("db.port", "1521"));
        System.out.println("   SID: " + getConfigValue("db.sid", "ORCL"));
        System.out.println("   حد أدنى للاتصالات: " + getConfigValue("db.connection.pool.min", "2"));
        System.out.println("   حد أقصى للاتصالات: " + getConfigValue("db.connection.pool.max", "10"));
        
        System.out.println("\n🔐 إعدادات الأمان:");
        System.out.println("   التشفير مفعل: " + getConfigValue("security.encryption.enabled", "true"));
        System.out.println("   الحد الأدنى لطول كلمة المرور: " + getConfigValue("security.password.min.length", "8"));
        System.out.println("   مهلة الجلسة: " + getConfigValue("security.session.timeout", "3600000") + " ms");
        
        System.out.println("=" + "=".repeat(40));
    }
}
