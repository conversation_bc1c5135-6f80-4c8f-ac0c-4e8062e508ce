import java.sql.*;

/**
 * إضافة نافذة صندوق البريد الوارد إلى جدول ERP_SYSTEM_TREE
 */
public class AddInboxWindowToErpSystemTree {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  ADDING INBOX WINDOW TO ERP_SYSTEM_TREE");
        System.out.println("  إضافة نافذة صندوق البريد الوارد إلى ERP_SYSTEM_TREE");
        System.out.println("========================================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";
            
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة بيانات ship_erp بنجاح");
            
            // البحث عن نوافذ البريد الإلكتروني الموجودة
            searchExistingEmailWindows(connection);
            
            // إضافة نظام البريد الإلكتروني إذا لم يكن موجوداً
            int emailSystemId = addEmailSystemIfMissing(connection);
            
            // إضافة نافذة صندوق البريد الوارد
            addInboxWindow(connection, emailSystemId);
            
            // إضافة نوافذ البريد الإلكتروني الأخرى
            addOtherEmailWindows(connection, emailSystemId);
            
            connection.close();
            System.out.println("\n🎉 تم إضافة نوافذ البريد الإلكتروني بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إضافة النوافذ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void searchExistingEmailWindows(Connection connection) {
        try {
            System.out.println("\n🔍 البحث عن نوافذ البريد الإلكتروني الموجودة...");
            
            String sql = """
                SELECT TREE_ID, NODE_NAME_AR, NODE_NAME_EN, WINDOW_CLASS, NODE_DESCRIPTION
                FROM ERP_SYSTEM_TREE 
                WHERE NODE_NAME_AR LIKE '%بريد%' 
                OR NODE_NAME_EN LIKE '%Email%'
                OR NODE_NAME_EN LIKE '%Mail%'
                OR WINDOW_CLASS LIKE '%Email%'
                ORDER BY NODE_NAME_AR
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                System.out.println("📧 نوافذ البريد الإلكتروني الموجودة:");
                System.out.println("=" .repeat(80));
                
                boolean found = false;
                while (rs.next()) {
                    found = true;
                    System.out.println("🔹 المعرف: " + rs.getInt("TREE_ID"));
                    System.out.println("   الاسم العربي: " + rs.getString("NODE_NAME_AR"));
                    System.out.println("   الاسم الإنجليزي: " + rs.getString("NODE_NAME_EN"));
                    System.out.println("   كلاس النافذة: " + rs.getString("WINDOW_CLASS"));
                    System.out.println("   الوصف: " + rs.getString("NODE_DESCRIPTION"));
                    System.out.println("-" .repeat(60));
                }
                
                if (!found) {
                    System.out.println("❌ لم يتم العثور على نوافذ البريد الإلكتروني");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن نوافذ البريد الإلكتروني: " + e.getMessage());
        }
    }
    
    private static int addEmailSystemIfMissing(Connection connection) {
        try {
            System.out.println("\n📧 إضافة نظام البريد الإلكتروني إذا لم يكن موجوداً...");
            
            // التحقق من وجود نظام البريد الإلكتروني
            String checkSql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'Email Management System'";
            try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
                 ResultSet rs = checkStmt.executeQuery()) {
                
                if (rs.next()) {
                    int existingId = rs.getInt("TREE_ID");
                    System.out.println("⚠️ نظام البريد الإلكتروني موجود مسبقاً (ID: " + existingId + ")");
                    return existingId;
                }
            }
            
            // الحصول على أكبر TREE_ID موجود
            String maxIdSql = "SELECT NVL(MAX(TREE_ID), 0) + 1 FROM ERP_SYSTEM_TREE";
            int newTreeId = 1;
            try (PreparedStatement stmt = connection.prepareStatement(maxIdSql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    newTreeId = rs.getInt(1);
                }
            }
            
            // إدراج نظام البريد الإلكتروني
            String insertSql = """
                INSERT INTO ERP_SYSTEM_TREE (
                    TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, 
                    NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
                    CREATED_DATE, CREATED_BY
                ) VALUES (
                    ?, 1, 'نظام إدارة البريد الإلكتروني', 'Email Management System', 
                    'نظام شامل لإدارة البريد الإلكتروني والرسائل مع دعم IMAP وPOP3 وSMTP', 
                    'CATEGORY', 15, 2, 'Y', 'Y', SYSDATE, USER
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(insertSql)) {
                stmt.setInt(1, newTreeId);
                stmt.executeUpdate();
                System.out.println("✅ تم إدراج نظام البريد الإلكتروني (ID: " + newTreeId + ")");
                return newTreeId;
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إضافة نظام البريد الإلكتروني: " + e.getMessage());
            return -1;
        }
    }
    
    private static void addInboxWindow(Connection connection, int parentId) {
        try {
            System.out.println("\n📥 إضافة نافذة صندوق البريد الوارد...");
            
            if (parentId == -1) {
                System.err.println("❌ لا يمكن إضافة النافذة بدون عقدة أب صحيحة");
                return;
            }
            
            // التحقق من عدم وجود النافذة مسبقاً
            String checkSql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = 'EmailInboxWindow'";
            try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
                 ResultSet rs = checkStmt.executeQuery()) {
                
                if (rs.next() && rs.getInt(1) > 0) {
                    System.out.println("⚠️ نافذة صندوق البريد الوارد موجودة مسبقاً");
                    return;
                }
            }
            
            // الحصول على أكبر TREE_ID موجود
            String maxIdSql = "SELECT NVL(MAX(TREE_ID), 0) + 1 FROM ERP_SYSTEM_TREE";
            int newTreeId = 1;
            try (PreparedStatement stmt = connection.prepareStatement(maxIdSql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    newTreeId = rs.getInt(1);
                }
            }
            
            // إدراج نافذة صندوق البريد الوارد
            String insertSql = """
                INSERT INTO ERP_SYSTEM_TREE (
                    TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, 
                    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
                    CREATED_DATE, CREATED_BY
                ) VALUES (
                    ?, ?, 'صندوق البريد الوارد الشامل', 'Comprehensive Email Inbox', 
                    'صندوق البريد الوارد الشامل لعرض وإدارة جميع الرسائل الواردة من جميع الحسابات', 
                    'WINDOW', 'EmailInboxWindow', 1, 3, 'Y', 'Y', SYSDATE, USER
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(insertSql)) {
                stmt.setInt(1, newTreeId);
                stmt.setInt(2, parentId);
                stmt.executeUpdate();
                System.out.println("✅ تم إدراج نافذة صندوق البريد الوارد الشامل (ID: " + newTreeId + ")");
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إضافة نافذة صندوق البريد الوارد: " + e.getMessage());
        }
    }
    
    private static void addOtherEmailWindows(Connection connection, int parentId) {
        try {
            System.out.println("\n📧 إضافة نوافذ البريد الإلكتروني الأخرى...");
            
            if (parentId == -1) {
                System.err.println("❌ لا يمكن إضافة النوافذ بدون عقدة أب صحيحة");
                return;
            }
            
            // قائمة النوافذ المطلوبة
            String[][] windows = {
                {"إدارة حسابات البريد", "Email Accounts Management", "CompleteEmailAccountsWindow", 
                 "إدارة وتكوين حسابات البريد الإلكتروني - IMAP, POP3, SMTP"},
                
                {"إنشاء رسالة جديدة", "Compose New Message", "EmailComposeWindow", 
                 "إنشاء وإرسال رسائل بريد إلكتروني جديدة"},
                
                {"إدارة القوالب", "Templates Management", "EmailTemplatesWindow", 
                 "إنشاء وإدارة قوالب البريد الإلكتروني"},
                
                {"دفتر العناوين", "Address Book", "EmailAddressBookWindow", 
                 "إدارة جهات الاتصال ومجموعات البريد الإلكتروني"},
                
                {"الإحصائيات والتقارير", "Statistics & Reports", "EmailReportsWindow", 
                 "عرض إحصائيات وتقارير البريد الإلكتروني"}
            };
            
            for (int i = 0; i < windows.length; i++) {
                String nameAr = windows[i][0];
                String nameEn = windows[i][1];
                String windowClass = windows[i][2];
                String description = windows[i][3];
                
                // التحقق من عدم وجود النافذة مسبقاً
                String checkSql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = ?";
                try (PreparedStatement checkStmt = connection.prepareStatement(checkSql)) {
                    checkStmt.setString(1, windowClass);
                    try (ResultSet rs = checkStmt.executeQuery()) {
                        if (rs.next() && rs.getInt(1) > 0) {
                            System.out.println("⚠️ نافذة " + nameAr + " موجودة مسبقاً");
                            continue;
                        }
                    }
                }
                
                // الحصول على أكبر TREE_ID موجود
                String maxIdSql = "SELECT NVL(MAX(TREE_ID), 0) + 1 FROM ERP_SYSTEM_TREE";
                int newTreeId = 1;
                try (PreparedStatement stmt = connection.prepareStatement(maxIdSql);
                     ResultSet rs = stmt.executeQuery()) {
                    
                    if (rs.next()) {
                        newTreeId = rs.getInt(1);
                    }
                }
                
                // إدراج النافذة
                String insertSql = """
                    INSERT INTO ERP_SYSTEM_TREE (
                        TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, 
                        NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
                        CREATED_DATE, CREATED_BY
                    ) VALUES (
                        ?, ?, ?, ?, ?, 'WINDOW', ?, ?, 3, 'Y', 'Y', SYSDATE, USER
                    )
                    """;
                
                try (PreparedStatement stmt = connection.prepareStatement(insertSql)) {
                    stmt.setInt(1, newTreeId);
                    stmt.setInt(2, parentId);
                    stmt.setString(3, nameAr);
                    stmt.setString(4, nameEn);
                    stmt.setString(5, description);
                    stmt.setString(6, windowClass);
                    stmt.setInt(7, i + 2); // ترتيب العرض
                    stmt.executeUpdate();
                    System.out.println("✅ تم إدراج نافذة " + nameAr + " (ID: " + newTreeId + ")");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إضافة النوافذ الأخرى: " + e.getMessage());
        }
    }
}
