-- إصلاح هيكل شجرة النظام
-- Fix System Tree Structure
-- Ship ERP System - Correct Tree Organization

PROMPT 'إصلاح هيكل شجرة النظام...'
PROMPT 'Fixing System Tree Structure...'

-- تعطيل التثبيت التلقائي مؤقتاً
SET AUTOCOMMIT OFF;

-- الحصول على معرفات الفئات
VARIABLE items_id NUMBER;
VARIABLE users_id NUMBER;
VARIABLE settings_id NUMBER;
VARIABLE reports_id NUMBER;
VARIABLE tools_id NUMBER;

BEGIN
    SELECT TREE_ID INTO :items_id FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'إدارة الأصناف';
    SELECT TREE_ID INTO :users_id FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'إدارة المستخدمين';
    SELECT TREE_ID INTO :settings_id FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'الإعدادات العامة';
    SELECT TREE_ID INTO :reports_id FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'التقارير';
    SELECT TREE_ID INTO :tools_id FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'أدوات النظام';
    
    DBMS_OUTPUT.PUT_LINE('معرفات الفئات:');
    DBMS_OUTPUT.PUT_LINE('إدارة الأصناف: ' || :items_id);
    DBMS_OUTPUT.PUT_LINE('إدارة المستخدمين: ' || :users_id);
    DBMS_OUTPUT.PUT_LINE('الإعدادات العامة: ' || :settings_id);
    DBMS_OUTPUT.PUT_LINE('التقارير: ' || :reports_id);
    DBMS_OUTPUT.PUT_LINE('أدوات النظام: ' || :tools_id);
END;
/

PROMPT 'نقل نوافذ إدارة الأصناف...'

-- نقل نوافذ إدارة الأصناف
UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :items_id, TREE_LEVEL = 2, DISPLAY_ORDER = 1
WHERE NODE_NAME_AR = 'بيانات الأصناف الحقيقية';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :items_id, TREE_LEVEL = 2, DISPLAY_ORDER = 2
WHERE NODE_NAME_AR = 'بيانات الأصناف الشاملة';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :items_id, TREE_LEVEL = 2, DISPLAY_ORDER = 3
WHERE NODE_NAME_AR = 'مجموعات الأصناف';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :items_id, TREE_LEVEL = 2, DISPLAY_ORDER = 4
WHERE NODE_NAME_AR = 'وحدات القياس';

PROMPT 'نقل نوافذ إدارة المستخدمين...'

-- نقل نوافذ إدارة المستخدمين
UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :users_id, TREE_LEVEL = 2, DISPLAY_ORDER = 1
WHERE NODE_NAME_AR = 'إدارة المستخدمين';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :users_id, TREE_LEVEL = 2, DISPLAY_ORDER = 2
WHERE NODE_NAME_AR = 'صلاحيات المستخدمين';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :users_id, TREE_LEVEL = 2, DISPLAY_ORDER = 3
WHERE NODE_NAME_AR = 'مجموعات المستخدمين';

PROMPT 'نقل نوافذ الإعدادات العامة...'

-- نقل نوافذ الإعدادات العامة
UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :settings_id, TREE_LEVEL = 2, DISPLAY_ORDER = 1
WHERE NODE_NAME_AR = 'الإعدادات العامة';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :settings_id, TREE_LEVEL = 2, DISPLAY_ORDER = 2
WHERE NODE_NAME_AR = 'إعدادات قاعدة البيانات';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :settings_id, TREE_LEVEL = 2, DISPLAY_ORDER = 3
WHERE NODE_NAME_AR = 'إعدادات الأمان';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :settings_id, TREE_LEVEL = 2, DISPLAY_ORDER = 4
WHERE NODE_NAME_AR = 'تكوين النظام';

PROMPT 'نقل نوافذ التقارير...'

-- نقل نوافذ التقارير
UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :reports_id, TREE_LEVEL = 2, DISPLAY_ORDER = 1
WHERE NODE_NAME_AR = 'تقرير الأصناف';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :reports_id, TREE_LEVEL = 2, DISPLAY_ORDER = 2
WHERE NODE_NAME_AR = 'تقرير المستخدمين';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :reports_id, TREE_LEVEL = 2, DISPLAY_ORDER = 3
WHERE NODE_NAME_AR = 'تقرير النظام';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :reports_id, TREE_LEVEL = 2, DISPLAY_ORDER = 4
WHERE NODE_NAME_AR = 'التقارير المخصصة';

PROMPT 'نقل أدوات النظام...'

-- نقل أدوات النظام (الاحتفاظ بها في فئة أدوات النظام)
UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :tools_id, TREE_LEVEL = 2, DISPLAY_ORDER = 1
WHERE NODE_NAME_AR = 'فحص النظام الشامل';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :tools_id, TREE_LEVEL = 2, DISPLAY_ORDER = 2
WHERE NODE_NAME_AR = 'مراقب الأداء';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :tools_id, TREE_LEVEL = 2, DISPLAY_ORDER = 3
WHERE NODE_NAME_AR = 'إدارة الاتصالات';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :tools_id, TREE_LEVEL = 2, DISPLAY_ORDER = 4
WHERE NODE_NAME_AR = 'مدير الأمان';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :tools_id, TREE_LEVEL = 2, DISPLAY_ORDER = 5
WHERE NODE_NAME_AR = 'مدير التكوين';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :tools_id, TREE_LEVEL = 2, DISPLAY_ORDER = 6
WHERE NODE_NAME_AR = 'البحث المتقدم';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :tools_id, TREE_LEVEL = 2, DISPLAY_ORDER = 7
WHERE NODE_NAME_AR = 'النسخ الاحتياطي والاستعادة';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :tools_id, TREE_LEVEL = 2, DISPLAY_ORDER = 8
WHERE NODE_NAME_AR = 'سجل النظام';

UPDATE ERP_SYSTEM_TREE 
SET PARENT_ID = :tools_id, TREE_LEVEL = 2, DISPLAY_ORDER = 9
WHERE NODE_NAME_AR = 'مركز الإشعارات';

PROMPT 'ترتيب الفئات الرئيسية...'

-- ترتيب الفئات الرئيسية
UPDATE ERP_SYSTEM_TREE 
SET DISPLAY_ORDER = 1, TREE_LEVEL = 1
WHERE NODE_NAME_AR = 'إدارة الأصناف';

UPDATE ERP_SYSTEM_TREE 
SET DISPLAY_ORDER = 2, TREE_LEVEL = 1
WHERE NODE_NAME_AR = 'إدارة المستخدمين';

UPDATE ERP_SYSTEM_TREE 
SET DISPLAY_ORDER = 3, TREE_LEVEL = 1
WHERE NODE_NAME_AR = 'الإعدادات العامة';

UPDATE ERP_SYSTEM_TREE 
SET DISPLAY_ORDER = 4, TREE_LEVEL = 1
WHERE NODE_NAME_AR = 'التقارير';

UPDATE ERP_SYSTEM_TREE 
SET DISPLAY_ORDER = 5, TREE_LEVEL = 1
WHERE NODE_NAME_AR = 'أدوات النظام';

-- تحديث العقدة الجذرية
UPDATE ERP_SYSTEM_TREE 
SET DISPLAY_ORDER = 1, TREE_LEVEL = 0
WHERE NODE_NAME_AR = 'نظام إدارة الشحنات';

PROMPT 'حفظ التغييرات...'

-- حفظ التغييرات
COMMIT;

PROMPT 'عرض النتيجة النهائية...'

-- عرض النتيجة النهائية
SELECT 
    TREE_ID,
    PARENT_ID,
    LPAD(' ', (TREE_LEVEL * 4)) || NODE_NAME_AR AS TREE_STRUCTURE,
    NODE_TYPE,
    WINDOW_CLASS,
    DISPLAY_ORDER,
    TREE_LEVEL
FROM ERP_SYSTEM_TREE
WHERE IS_ACTIVE = 'Y' AND IS_VISIBLE = 'Y'
START WITH PARENT_ID IS NULL
CONNECT BY PRIOR TREE_ID = PARENT_ID
ORDER SIBLINGS BY DISPLAY_ORDER;

PROMPT 'إحصائيات الشجرة:'

-- إحصائيات الشجرة
SELECT 
    NODE_TYPE,
    COUNT(*) AS COUNT
FROM ERP_SYSTEM_TREE
WHERE IS_ACTIVE = 'Y' AND IS_VISIBLE = 'Y'
GROUP BY NODE_TYPE
ORDER BY NODE_TYPE;

PROMPT 'تم إصلاح هيكل شجرة النظام بنجاح!'
PROMPT 'System Tree Structure Fixed Successfully!'
