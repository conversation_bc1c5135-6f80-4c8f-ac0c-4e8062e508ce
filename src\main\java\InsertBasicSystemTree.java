import java.sql.*;
import java.util.Properties;

/**
 * إدراج البيانات الأساسية لشجرة النظام
 * Insert Basic System Tree Data
 */
public class InsertBasicSystemTree {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔄 إدراج البيانات الأساسية لشجرة النظام...");
            
            Connection connection = getConnection();
            
            // تنظيف البيانات الموجودة (اختياري)
            // clearExistingData(connection);
            
            // إدراج الفئات الرئيسية
            insertMainCategories(connection);
            
            // إدراج النوافذ الأساسية
            insertBasicWindows(connection);
            
            // إدراج أدوات النظام
            insertSystemTools(connection);
            
            connection.close();
            
            System.out.println("✅ تم إدراج البيانات الأساسية بنجاح!");
            
            // اختبار النتيجة
            testResult();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إدراج البيانات الأساسية: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    private static void insertMainCategories(Connection conn) throws SQLException {
        System.out.println("📁 إدراج الفئات الرئيسية...");
        
        // التحقق من وجود العقدة الجذرية
        if (!nodeExists(conn, "نظام إدارة الشحنات")) {
            String insertRoot = """
                INSERT INTO ERP_SYSTEM_TREE (
                    TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
                    NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL
                ) VALUES (
                    1, NULL, 'نظام إدارة الشحنات', 'Ship ERP System', 'النظام الرئيسي لإدارة الشحنات',
                    'CATEGORY', 1, 0
                )
            """;
            conn.createStatement().execute(insertRoot);
            System.out.println("  ✅ العقدة الجذرية: نظام إدارة الشحنات");
        } else {
            System.out.println("  ⚠️ العقدة الجذرية موجودة مسبقاً");
        }
        
        // إدراج الفئات الرئيسية
        String[] categories = {
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) " +
            "SELECT 1, 'إدارة الأصناف', 'Items Management', 'إدارة وتصنيف الأصناف والمنتجات', 'CATEGORY', 1, 1 " +
            "FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'إدارة الأصناف')",
            
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) " +
            "SELECT 1, 'إدارة المستخدمين', 'User Management', 'إدارة المستخدمين والصلاحيات', 'CATEGORY', 2, 1 " +
            "FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'إدارة المستخدمين')",
            
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) " +
            "SELECT 1, 'الإعدادات العامة', 'General Settings', 'الإعدادات العامة للنظام', 'CATEGORY', 3, 1 " +
            "FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'الإعدادات العامة')",
            
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) " +
            "SELECT 1, 'التقارير', 'Reports', 'التقارير والإحصائيات', 'CATEGORY', 4, 1 " +
            "FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'التقارير')",
            
            "INSERT INTO ERP_SYSTEM_TREE (PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL) " +
            "SELECT 1, 'أدوات النظام', 'System Tools', 'أدوات مساعدة ومراقبة النظام', 'CATEGORY', 5, 1 " +
            "FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'أدوات النظام')"
        };
        
        for (String sql : categories) {
            try {
                int result = conn.createStatement().executeUpdate(sql);
                if (result > 0) {
                    System.out.println("  ✅ تم إدراج فئة جديدة");
                }
            } catch (SQLException e) {
                System.err.println("  ⚠️ تحذير في إدراج فئة: " + e.getMessage());
            }
        }
        
        conn.commit();
        System.out.println("✅ تم إدراج الفئات الرئيسية");
    }
    
    private static void insertBasicWindows(Connection conn) throws SQLException {
        System.out.println("🪟 إدراج النوافذ الأساسية...");
        
        // الحصول على معرفات الفئات
        int itemsId = getCategoryId(conn, "إدارة الأصناف");
        int usersId = getCategoryId(conn, "إدارة المستخدمين");
        int settingsId = getCategoryId(conn, "الإعدادات العامة");
        
        if (itemsId > 0) {
            // نوافذ إدارة الأصناف
            insertWindow(conn, itemsId, "بيانات الأصناف الحقيقية", "Real Item Data", 
                        "إدارة بيانات الأصناف الحقيقية", "RealItemDataWindow", 1);
            insertWindow(conn, itemsId, "بيانات الأصناف الشاملة", "Comprehensive Item Data", 
                        "إدارة بيانات الأصناف الشاملة", "ComprehensiveItemDataWindow", 2);
            insertWindow(conn, itemsId, "مجموعات الأصناف", "Item Groups", 
                        "إدارة مجموعات وتصنيفات الأصناف", "ItemGroupsManagementWindow", 3);
            insertWindow(conn, itemsId, "وحدات القياس", "Measurement Units", 
                        "إدارة وحدات القياس والتحويلات", "MeasurementUnitsWindow", 4);
        }
        
        if (usersId > 0) {
            // نوافذ إدارة المستخدمين
            insertWindow(conn, usersId, "إدارة المستخدمين", "User Management", 
                        "إضافة وتعديل وحذف المستخدمين", "UserManagementWindow", 1);
        }
        
        if (settingsId > 0) {
            // نوافذ الإعدادات
            insertWindow(conn, settingsId, "الإعدادات العامة", "General Settings", 
                        "إعدادات النظام العامة", "GeneralSettingsWindow", 1);
        }
        
        conn.commit();
        System.out.println("✅ تم إدراج النوافذ الأساسية");
    }
    
    private static void insertSystemTools(Connection conn) throws SQLException {
        System.out.println("🔧 إدراج أدوات النظام...");
        
        int toolsId = getCategoryId(conn, "أدوات النظام");
        
        if (toolsId > 0) {
            insertTool(conn, toolsId, "فحص النظام الشامل", "System Audit", 
                      "فحص شامل لحالة النظام", "CompleteOracleSystemTest", 1);
            insertTool(conn, toolsId, "مراقب الأداء", "Performance Monitor", 
                      "مراقبة أداء النظام والاتصالات", "PerformanceMonitor", 2);
            insertTool(conn, toolsId, "إدارة الاتصالات", "Connection Manager", 
                      "إدارة اتصالات قواعد البيانات", "TNSConnectionManager", 3);
        }
        
        conn.commit();
        System.out.println("✅ تم إدراج أدوات النظام");
    }
    
    private static boolean nodeExists(Connection conn, String nodeName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, nodeName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        return false;
    }
    
    private static int getCategoryId(Connection conn, String categoryName) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }
    
    private static void insertWindow(Connection conn, int parentId, String nameAr, String nameEn, 
                                   String description, String windowClass, int order) throws SQLException {
        if (nodeExists(conn, nameAr)) {
            System.out.println("  ⚠️ " + nameAr + " (موجود مسبقاً)");
            return;
        }
        
        String sql = """
            INSERT INTO ERP_SYSTEM_TREE (
                PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
                NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
            ) VALUES (?, ?, ?, ?, 'WINDOW', ?, ?, 2)
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            stmt.setString(2, nameAr);
            stmt.setString(3, nameEn);
            stmt.setString(4, description);
            stmt.setString(5, windowClass);
            stmt.setInt(6, order);
            
            stmt.executeUpdate();
            System.out.println("  ✅ " + nameAr + " (" + windowClass + ")");
        }
    }
    
    private static void insertTool(Connection conn, int parentId, String nameAr, String nameEn, 
                                 String description, String toolClass, int order) throws SQLException {
        if (nodeExists(conn, nameAr)) {
            System.out.println("  ⚠️ " + nameAr + " (موجود مسبقاً)");
            return;
        }
        
        String sql = """
            INSERT INTO ERP_SYSTEM_TREE (
                PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
                NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
            ) VALUES (?, ?, ?, ?, 'TOOL', ?, ?, 2)
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            stmt.setString(2, nameAr);
            stmt.setString(3, nameEn);
            stmt.setString(4, description);
            stmt.setString(5, toolClass);
            stmt.setInt(6, order);
            
            stmt.executeUpdate();
            System.out.println("  ✅ " + nameAr + " (" + toolClass + ")");
        }
    }
    
    private static void clearExistingData(Connection conn) throws SQLException {
        System.out.println("🗑️ تنظيف البيانات الموجودة...");
        
        try {
            // حذف جميع البيانات ما عدا العقدة الجذرية
            String sql = "DELETE FROM ERP_SYSTEM_TREE WHERE TREE_ID != 1";
            
            int deleted = conn.createStatement().executeUpdate(sql);
            conn.commit();
            System.out.println("✅ تم حذف " + deleted + " عنصر من البيانات السابقة");
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تنظيف البيانات: " + e.getMessage());
        }
    }
    
    private static void testResult() {
        System.out.println("\n🔍 اختبار النتيجة...");
        
        try {
            SystemTreeManager manager = SystemTreeManager.getInstance();
            manager.printSystemTree();
            manager.close();
        } catch (Exception e) {
            System.err.println("❌ خطأ في اختبار النتيجة: " + e.getMessage());
        }
    }
}
