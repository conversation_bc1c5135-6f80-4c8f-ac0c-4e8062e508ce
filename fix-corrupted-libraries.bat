@echo off
echo Fixing Corrupted Libraries
echo =========================

cd /d "e:\ship_erp\java\lib"

echo Removing corrupted files...
del beautyeye-3.7.jar 2>nul
del colorchooser-1.0.jar 2>nul
del expressly-5.0.0.jar 2>nul
del material-theme-ui-6.2.0.jar 2>nul
del substance-8.0.02.jar 2>nul
del synthetica-2.30.0.jar 2>nul
del trident-1.5.00.jar 2>nul
del weblaf-core-2.2.1.jar 2>nul
del weblaf-ui-2.2.1.jar 2>nul

echo.
echo Re-downloading corrupted libraries...

echo [1/9] BeautyEye Look and Feel...
curl -L -o beautyeye-3.7.jar "https://repo1.maven.org/maven2/org/jb2011/beautyeye/3.7/beautyeye-3.7.jar"

echo [2/9] Color Chooser Library...
curl -L -o colorchooser-1.0.jar "https://repo1.maven.org/maven2/com/bric/colorchooser/1.0/colorchooser-1.0.jar"

echo [3/9] Expressly...
curl -L -o expressly-5.0.0.jar "https://repo1.maven.org/maven2/org/glassfish/expressly/5.0.0/expressly-5.0.0.jar"

echo [4/9] Material Theme UI...
curl -L -o material-theme-ui-6.2.0.jar "https://repo1.maven.org/maven2/com/github/vincenzopalazzo/material-ui-swing/6.2.0/material-ui-swing-6.2.0.jar"

echo [5/9] Substance Look and Feel...
curl -L -o substance-8.0.02.jar "https://repo1.maven.org/maven2/org/pushingpixels/substance/8.0.02/substance-8.0.02.jar"

echo [6/9] Synthetica Look and Feel...
curl -L -o synthetica-2.30.0.jar "https://repo1.maven.org/maven2/de/javasoft/synthetica/2.30.0/synthetica-2.30.0.jar"

echo [7/9] Trident Animation Library...
curl -L -o trident-1.5.00.jar "https://repo1.maven.org/maven2/org/pushingpixels/trident/1.5.00/trident-1.5.00.jar"

echo [8/9] WebLaF Core...
curl -L -o weblaf-core-2.2.1.jar "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-core/2.2.1/weblaf-core-2.2.1.jar"

echo [9/9] WebLaF UI...
curl -L -o weblaf-ui-2.2.1.jar "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-ui/2.2.1/weblaf-ui-2.2.1.jar"

echo.
echo Verifying downloaded files...
for %%f in (beautyeye-3.7.jar colorchooser-1.0.jar expressly-5.0.0.jar material-theme-ui-6.2.0.jar substance-8.0.02.jar synthetica-2.30.0.jar trident-1.5.00.jar weblaf-core-2.2.1.jar weblaf-ui-2.2.1.jar) do (
    if exist "%%f" (
        echo   OK: %%f
    ) else (
        echo   MISSING: %%f
    )
)

echo.
echo Fix completed!
pause
