package com.shipment.erp.view;

import com.shipment.erp.model.Currency;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;

/**
 * نموذج إدخال/تعديل بيانات العملة
 * Currency Form Dialog
 */
public class CurrencyFormDialog extends JDialog {
    
    private Font arabicFont;
    private Currency currency;
    private boolean confirmed = false;
    
    // حقول الإدخال
    private JTextField codeField;
    private JTextField nameField;
    private JTextField nameEnField;
    private JTextField symbolField;
    private JTextField exchangeRateField;
    private JCheckBox defaultCheckBox;
    private JCheckBox activeCheckBox;
    
    public CurrencyFormDialog(Dialog parent, String title, Currency currency) {
        super(parent, title, true);
        
        this.currency = currency;
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        initializeComponents();
        setupLayout();
        loadData();
        
        setSize(500, 450);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }
    
    private void initializeComponents() {
        // كود العملة
        codeField = new JTextField(10);
        codeField.setFont(arabicFont);
        codeField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        
        // اسم العملة بالعربية
        nameField = new JTextField(30);
        nameField.setFont(arabicFont);
        nameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // اسم العملة بالإنجليزية
        nameEnField = new JTextField(30);
        nameEnField.setFont(arabicFont);
        nameEnField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        
        // رمز العملة
        symbolField = new JTextField(10);
        symbolField.setFont(arabicFont);
        symbolField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        
        // سعر الصرف
        exchangeRateField = new JTextField(15);
        exchangeRateField.setFont(arabicFont);
        exchangeRateField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        
        // العملة الافتراضية
        defaultCheckBox = new JCheckBox("عملة افتراضية");
        defaultCheckBox.setFont(arabicFont);
        defaultCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // الحالة
        activeCheckBox = new JCheckBox("نشط");
        activeCheckBox.setFont(arabicFont);
        activeCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        activeCheckBox.setSelected(true);
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;
        
        int row = 0;
        
        // كود العملة
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("كود العملة:*"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(codeField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;
        
        // اسم العملة بالعربية
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("اسم العملة (عربي):*"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(nameField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;
        
        // اسم العملة بالإنجليزية
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("اسم العملة (إنجليزي):"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(nameEnField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;
        
        // رمز العملة
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("رمز العملة:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(symbolField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;
        
        // سعر الصرف
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("سعر الصرف:*"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(exchangeRateField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;
        
        // ملاحظة سعر الصرف
        gbc.gridx = 0; gbc.gridy = row; gbc.gridwidth = 2;
        JLabel noteLabel = new JLabel("ملاحظة: سعر الصرف مقابل العملة الافتراضية");
        noteLabel.setFont(new Font("Tahoma", Font.ITALIC, 10));
        noteLabel.setForeground(Color.GRAY);
        noteLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.add(noteLabel, gbc);
        gbc.gridwidth = 1;
        row++;
        
        // العملة الافتراضية
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel(""), gbc);
        gbc.gridx = 0;
        mainPanel.add(defaultCheckBox, gbc);
        row++;
        
        // الحالة
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("الحالة:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(activeCheckBox, gbc);
        row++;
        
        // أزرار التحكم
        JPanel buttonsPanel = createButtonsPanel();
        
        add(mainPanel, BorderLayout.CENTER);
        add(buttonsPanel, BorderLayout.SOUTH);
    }
    
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }
    
    private JPanel createButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JButton saveButton = new JButton("حفظ");
        saveButton.setFont(arabicFont);
        saveButton.setPreferredSize(new Dimension(100, 30));
        saveButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (validateInput()) {
                    saveData();
                    confirmed = true;
                    dispose();
                }
            }
        });
        
        JButton cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.setPreferredSize(new Dimension(100, 30));
        cancelButton.addActionListener(e -> dispose());
        
        panel.add(saveButton);
        panel.add(cancelButton);
        
        return panel;
    }
    
    private void loadData() {
        if (currency != null) {
            codeField.setText(currency.getCode());
            nameField.setText(currency.getName());
            nameEnField.setText(currency.getNameEn());
            symbolField.setText(currency.getSymbol());
            if (currency.getExchangeRate() != null) {
                exchangeRateField.setText(currency.getExchangeRate().toString());
            }
            defaultCheckBox.setSelected(currency.isDefault());
            activeCheckBox.setSelected(currency.isActive());
        }
    }
    
    private boolean validateInput() {
        if (codeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كود العملة", "خطأ", JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }
        
        if (codeField.getText().trim().length() != 3) {
            JOptionPane.showMessageDialog(this, "كود العملة يجب أن يكون 3 أحرف", "خطأ", JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }
        
        if (nameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم العملة", "خطأ", JOptionPane.ERROR_MESSAGE);
            nameField.requestFocus();
            return false;
        }
        
        if (exchangeRateField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال سعر الصرف", "خطأ", JOptionPane.ERROR_MESSAGE);
            exchangeRateField.requestFocus();
            return false;
        }
        
        try {
            BigDecimal rate = new BigDecimal(exchangeRateField.getText().trim());
            if (rate.compareTo(BigDecimal.ZERO) <= 0) {
                JOptionPane.showMessageDialog(this, "سعر الصرف يجب أن يكون أكبر من الصفر", "خطأ", JOptionPane.ERROR_MESSAGE);
                exchangeRateField.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "سعر الصرف غير صحيح", "خطأ", JOptionPane.ERROR_MESSAGE);
            exchangeRateField.requestFocus();
            return false;
        }
        
        return true;
    }
    
    private void saveData() {
        if (currency == null) {
            currency = new Currency();
        }
        
        currency.setCode(codeField.getText().trim().toUpperCase());
        currency.setName(nameField.getText().trim());
        currency.setNameEn(nameEnField.getText().trim());
        currency.setSymbol(symbolField.getText().trim());
        currency.setExchangeRate(new BigDecimal(exchangeRateField.getText().trim()));
        currency.setDefault(defaultCheckBox.isSelected());
        currency.setActive(activeCheckBox.isSelected());
    }
    
    public Currency getCurrency() {
        return currency;
    }
    
    public boolean isConfirmed() {
        return confirmed;
    }
}
