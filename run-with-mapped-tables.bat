@echo off
echo ========================================
echo    SYSTEM WITH MAPPED TABLES
echo    النظام مع الجداول المقابلة
echo ========================================

cd /d "e:\ship_erp\java"

set CP=.;lib\ojdbc11.jar;lib\orai18n.jar;lib\commons-logging-1.2.jar

echo [INFO] Table Mapping Configuration:
echo.
echo SHIP_ERP Tables          ^<--^>  IAS20251 Tables
echo =====================================
echo ERP_MEASUREMENT          ^<--^>  MEASUREMENT
echo ERP_SUB_GRP_DTL          ^<--^>  IAS_SUB_GRP_DTL
echo ERP_ASSISTANT_GROUP      ^<--^>  IAS_ASSISTANT_GROUP
echo ERP_DETAIL_GROUP         ^<--^>  IAS_DETAIL_GROUP
echo ERP_GROUP_DETAILS        ^<--^>  GROUP_DETAILS
echo ERP_MAINSUB_GRP_DTL      ^<--^>  IAS_MAINSUB_GRP_DTL
echo.
echo Database Credentials:
echo - SHIP_ERP: ship_erp_password
echo - IAS20251: ys123
echo - Oracle SID: ORCL
echo.

echo [1] Cleaning and recompiling with mapped tables...
if exist "*.class" del /q *.class

echo [2] Compiling with table mapping support...
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\DatabaseConfig.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ItemData.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UIUtils.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\SettingsManager.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\GeneralSettingsWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\User.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserFormDialog.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserPermissionsDialog.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\UserManagementWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\RealItemDataWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ComprehensiveItemDataWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\ItemGroupsManagementWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\TreeMenuPanel.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\EnhancedMainWindow.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\CompleteSystemTest.java
javac -encoding UTF-8 -cp "%CP%" -d . src\main\java\CompleteOracleSystemTest.java

echo.
echo [SUCCESS] All components compiled with table mapping support!
echo.

echo ========================================
echo    STARTING WITH MAPPED TABLES
echo ========================================
echo.
echo The system will now check the correct tables for each user:
echo - SHIP_ERP: ERP_* tables
echo - IAS20251: Original IAS tables and MEASUREMENT, GROUP_DETAILS
echo.

echo [INFO] Starting Complete Oracle System with table mapping...
java -cp "%CP%" -Dfile.encoding=UTF-8 -Doracle.jdbc.defaultNChar=true CompleteOracleSystemTest

echo.
echo ========================================
echo    MAPPED TABLES SYSTEM COMPLETED
echo ========================================
pause
