import java.sql.*;

/**
 * فحص بنية جدول ERP_SYSTEM_TREE
 */
public class CheckErpSystemTreeStructure {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  CHECKING ERP_SYSTEM_TREE STRUCTURE");
        System.out.println("  فحص بنية جدول ERP_SYSTEM_TREE");
        System.out.println("========================================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";
            
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة بيانات ship_erp بنجاح");
            
            // فحص وجود الجدول
            checkTableExists(connection);
            
            // فحص بنية الجدول
            checkTableStructure(connection);
            
            // عرض عينة من البيانات
            showSampleData(connection);
            
            connection.close();
            System.out.println("\n🎉 تم فحص بنية الجدول بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فحص الجدول: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkTableExists(Connection connection) {
        try {
            System.out.println("\n🔍 فحص وجود جدول ERP_SYSTEM_TREE...");
            
            String sql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'ERP_SYSTEM_TREE'";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next() && rs.getInt(1) > 0) {
                    System.out.println("✅ جدول ERP_SYSTEM_TREE موجود");
                } else {
                    System.out.println("❌ جدول ERP_SYSTEM_TREE غير موجود");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("خطأ في فحص وجود الجدول: " + e.getMessage());
        }
    }
    
    private static void checkTableStructure(Connection connection) {
        try {
            System.out.println("\n📋 فحص بنية جدول ERP_SYSTEM_TREE...");
            
            String sql = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'ERP_SYSTEM_TREE' ORDER BY COLUMN_ID";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                System.out.println("🔹 أعمدة الجدول:");
                System.out.println("=" .repeat(80));
                System.out.printf("%-25s %-15s %-10s %-10s%n", "اسم العمود", "نوع البيانات", "الطول", "يقبل NULL");
                System.out.println("-" .repeat(80));
                
                while (rs.next()) {
                    String columnName = rs.getString("COLUMN_NAME");
                    String dataType = rs.getString("DATA_TYPE");
                    String dataLength = rs.getString("DATA_LENGTH");
                    String nullable = rs.getString("NULLABLE");
                    
                    System.out.printf("%-25s %-15s %-10s %-10s%n", columnName, dataType, dataLength, nullable);
                }
            }
            
        } catch (SQLException e) {
            System.err.println("خطأ في فحص بنية الجدول: " + e.getMessage());
        }
    }
    
    private static void showSampleData(Connection connection) {
        try {
            System.out.println("\n📊 عينة من البيانات الموجودة...");
            
            // أولاً نحصل على أسماء الأعمدة الفعلية
            String columnsSql = "SELECT COLUMN_NAME FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'ERP_SYSTEM_TREE' ORDER BY COLUMN_ID";
            StringBuilder columns = new StringBuilder();
            
            try (PreparedStatement stmt = connection.prepareStatement(columnsSql);
                 ResultSet rs = stmt.executeQuery()) {
                
                while (rs.next()) {
                    if (columns.length() > 0) {
                        columns.append(", ");
                    }
                    columns.append(rs.getString("COLUMN_NAME"));
                }
            }
            
            if (columns.length() == 0) {
                System.out.println("❌ لم يتم العثور على أعمدة في الجدول");
                return;
            }
            
            // عرض البيانات
            String dataSql = "SELECT " + columns.toString() + " FROM ERP_SYSTEM_TREE WHERE ROWNUM <= 5 ORDER BY 1";
            try (PreparedStatement stmt = connection.prepareStatement(dataSql);
                 ResultSet rs = stmt.executeQuery()) {
                
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                System.out.println("🔹 أول 5 سجلات:");
                System.out.println("=" .repeat(120));
                
                // طباعة أسماء الأعمدة
                for (int i = 1; i <= columnCount; i++) {
                    System.out.printf("%-20s ", metaData.getColumnName(i));
                }
                System.out.println();
                System.out.println("-" .repeat(120));
                
                // طباعة البيانات
                while (rs.next()) {
                    for (int i = 1; i <= columnCount; i++) {
                        String value = rs.getString(i);
                        if (value == null) value = "NULL";
                        if (value.length() > 18) value = value.substring(0, 15) + "...";
                        System.out.printf("%-20s ", value);
                    }
                    System.out.println();
                }
                
                if (!rs.isBeforeFirst()) {
                    System.out.println("⚠️ لا توجد بيانات في الجدول");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("خطأ في عرض البيانات: " + e.getMessage());
        }
    }
}
