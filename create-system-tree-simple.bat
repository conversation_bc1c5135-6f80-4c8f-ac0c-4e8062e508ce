@echo off
echo ========================================
echo    CREATE SYSTEM TREE TABLE - SIMPLE
echo    انشاء جدول شجرة النظام - مبسط
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Creating System Tree Table...
echo.

REM اعداد البيئة
call set-tns-env.bat

echo [1] Compiling CreateSystemTreeTable...
javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;." -d . src\main\java\CreateSystemTreeTable.java
if errorlevel 1 (
    echo ERROR: Failed to compile CreateSystemTreeTable
    pause
    exit /b 1
)

echo OK: CreateSystemTreeTable compiled

echo.
echo [2] Creating system tree table...
java %JAVA_TNS_OPTS% -cp "lib\ojdbc11.jar;lib\orai18n.jar;." CreateSystemTreeTable
if errorlevel 1 (
    echo ERROR: Failed to create system tree table
    pause
    exit /b 1
)

echo OK: System tree table created

echo.
echo [3] Compiling SystemTreeManager...
javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;." -d . src\main\java\SystemTreeManager.java
if errorlevel 1 (
    echo ERROR: Failed to compile SystemTreeManager
    pause
    exit /b 1
)

echo OK: SystemTreeManager compiled

echo.
echo [4] Testing SystemTreeManager...
java %JAVA_TNS_OPTS% -cp "lib\ojdbc11.jar;lib\orai18n.jar;." SystemTreeManager
if errorlevel 1 (
    echo WARNING: SystemTreeManager test failed
) else (
    echo OK: SystemTreeManager test passed
)

echo.
echo [5] Compiling TreeMenuPanel...
javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;." -d . src\main\java\TreeMenuPanel.java
if errorlevel 1 (
    echo ERROR: Failed to compile TreeMenuPanel
    pause
    exit /b 1
)

echo OK: TreeMenuPanel compiled

echo.
echo ========================================
echo    SETUP COMPLETED
echo    اكتمل الاعداد
echo ========================================

echo.
echo [SUCCESS] System Tree setup completed!
echo [نجح] اكتمل اعداد شجرة النظام!
echo.
echo Features:
echo - Dynamic system tree from database
echo - Automatic window registration  
echo - Tree structure management
echo.
echo Database table: ERP_SYSTEM_TREE
echo Manager class: SystemTreeManager
echo.

pause
