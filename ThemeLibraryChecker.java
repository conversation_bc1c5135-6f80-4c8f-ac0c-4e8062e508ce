import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * أداة فحص مكتبات المظهر والثيمات
 * Theme and UI Library Checker Tool
 */
public class ThemeLibraryChecker {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  THEME LIBRARY CHECKER");
        System.out.println("  فاحص مكتبات المظهر والثيمات");
        System.out.println("========================================");
        
        ThemeLibraryChecker checker = new ThemeLibraryChecker();
        checker.checkThemeLibraries();
    }
    
    /**
     * فحص مكتبات المظهر والثيمات
     */
    public void checkThemeLibraries() {
        try {
            // إنشاء ملف التقرير
            String reportFile = "THEME_LIBRARIES_REPORT.md";
            PrintWriter reportWriter = new PrintWriter(new FileWriter(reportFile), true);
            
            writeReportHeader(reportWriter);
            
            // فحص مجلد lib
            File libDir = new File("lib");
            if (!libDir.exists() || !libDir.isDirectory()) {
                System.out.println("❌ lib directory not found!");
                return;
            }
            
            // الحصول على جميع ملفات JAR
            File[] jarFiles = libDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".jar"));
            
            if (jarFiles == null || jarFiles.length == 0) {
                System.out.println("❌ No JAR files found in lib directory!");
                return;
            }
            
            // تصنيف المكتبات
            List<LibraryInfo> themeLibraries = new ArrayList<>();
            List<LibraryInfo> uiLibraries = new ArrayList<>();
            List<LibraryInfo> fontLibraries = new ArrayList<>();
            List<LibraryInfo> otherLibraries = new ArrayList<>();
            
            for (File jarFile : jarFiles) {
                String fileName = jarFile.getName().toLowerCase();
                LibraryInfo libInfo = new LibraryInfo(jarFile.getName(), jarFile.length());
                
                if (isThemeLibrary(fileName)) {
                    themeLibraries.add(libInfo);
                } else if (isUILibrary(fileName)) {
                    uiLibraries.add(libInfo);
                } else if (isFontLibrary(fileName)) {
                    fontLibraries.add(libInfo);
                } else {
                    otherLibraries.add(libInfo);
                }
            }
            
            // كتابة التقرير
            writeThemeLibrariesSection(reportWriter, themeLibraries);
            writeUILibrariesSection(reportWriter, uiLibraries);
            writeFontLibrariesSection(reportWriter, fontLibraries);
            writeLibraryTestSection(reportWriter, themeLibraries);
            writeSummarySection(reportWriter, themeLibraries, uiLibraries, fontLibraries, otherLibraries);
            
            reportWriter.close();
            
            System.out.println("📄 Report saved: " + reportFile);
            System.out.println("📄 تم حفظ التقرير: " + reportFile);
            
            // عرض ملخص في وحدة التحكم
            displayConsoleSummary(themeLibraries, uiLibraries, fontLibraries);
            
        } catch (Exception e) {
            System.out.println("❌ Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * التحقق من كون المكتبة مكتبة ثيمات
     */
    private boolean isThemeLibrary(String fileName) {
        return fileName.contains("flatlaf") || 
               fileName.contains("darklaf") || 
               fileName.contains("jtattoo") || 
               fileName.contains("seaglass") || 
               fileName.contains("nimbus") ||
               fileName.contains("theme") ||
               fileName.contains("laf");
    }
    
    /**
     * التحقق من كون المكتبة مكتبة واجهة مستخدم
     */
    private boolean isUILibrary(String fileName) {
        return fileName.contains("material") || 
               fileName.contains("swing") || 
               fileName.contains("ui") ||
               fileName.contains("miglayout") ||
               fileName.contains("layout");
    }
    
    /**
     * التحقق من كون المكتبة مكتبة خطوط
     */
    private boolean isFontLibrary(String fileName) {
        return fileName.contains("font") || 
               fileName.contains("roboto") || 
               fileName.contains("jetbrains") ||
               fileName.contains("mono");
    }
    
    /**
     * كتابة رأس التقرير
     */
    private void writeReportHeader(PrintWriter writer) {
        writer.println("# تقرير فحص مكتبات المظهر والثيمات");
        writer.println("## Theme and UI Libraries Report");
        writer.println();
        writer.println("**التاريخ:** " + new java.util.Date());
        writer.println("**المولد:** ThemeLibraryChecker");
        writer.println();
        writer.println("---");
        writer.println();
    }
    
    /**
     * كتابة قسم مكتبات الثيمات
     */
    private void writeThemeLibrariesSection(PrintWriter writer, List<LibraryInfo> libraries) {
        writer.println("## 🎨 مكتبات الثيمات - Theme Libraries");
        writer.println();
        
        if (libraries.isEmpty()) {
            writer.println("❌ **لم يتم العثور على مكتبات ثيمات**");
            writer.println();
            return;
        }
        
        writer.println("| اسم المكتبة | الحجم | الوصف |");
        writer.println("|-------------|-------|--------|");
        
        for (LibraryInfo lib : libraries) {
            String description = getLibraryDescription(lib.name);
            writer.printf("| %s | %s | %s |\n", 
                lib.name, 
                formatFileSize(lib.size), 
                description);
        }
        
        writer.println();
        writer.printf("**إجمالي مكتبات الثيمات:** %d مكتبة\n", libraries.size());
        writer.println();
    }
    
    /**
     * كتابة قسم مكتبات الواجهة
     */
    private void writeUILibrariesSection(PrintWriter writer, List<LibraryInfo> libraries) {
        writer.println("## 🖥️ مكتبات الواجهة - UI Libraries");
        writer.println();
        
        if (libraries.isEmpty()) {
            writer.println("❌ **لم يتم العثور على مكتبات واجهة**");
            writer.println();
            return;
        }
        
        writer.println("| اسم المكتبة | الحجم | الوصف |");
        writer.println("|-------------|-------|--------|");
        
        for (LibraryInfo lib : libraries) {
            String description = getLibraryDescription(lib.name);
            writer.printf("| %s | %s | %s |\n", 
                lib.name, 
                formatFileSize(lib.size), 
                description);
        }
        
        writer.println();
        writer.printf("**إجمالي مكتبات الواجهة:** %d مكتبة\n", libraries.size());
        writer.println();
    }
    
    /**
     * كتابة قسم مكتبات الخطوط
     */
    private void writeFontLibrariesSection(PrintWriter writer, List<LibraryInfo> libraries) {
        writer.println("## 🔤 مكتبات الخطوط - Font Libraries");
        writer.println();
        
        if (libraries.isEmpty()) {
            writer.println("⚠️ **لم يتم العثور على مكتبات خطوط مخصصة**");
            writer.println();
            return;
        }
        
        writer.println("| اسم المكتبة | الحجم | الوصف |");
        writer.println("|-------------|-------|--------|");
        
        for (LibraryInfo lib : libraries) {
            String description = getLibraryDescription(lib.name);
            writer.printf("| %s | %s | %s |\n", 
                lib.name, 
                formatFileSize(lib.size), 
                description);
        }
        
        writer.println();
        writer.printf("**إجمالي مكتبات الخطوط:** %d مكتبة\n", libraries.size());
        writer.println();
    }
    
    /**
     * كتابة قسم اختبار المكتبات
     */
    private void writeLibraryTestSection(PrintWriter writer, List<LibraryInfo> themeLibraries) {
        writer.println("## 🧪 اختبار المكتبات - Library Testing");
        writer.println();
        
        writer.println("### الثيمات المتاحة للاختبار:");
        writer.println();
        
        for (LibraryInfo lib : themeLibraries) {
            String testCommand = getTestCommand(lib.name);
            if (!testCommand.isEmpty()) {
                writer.println("#### " + lib.name);
                writer.println("```bash");
                writer.println(testCommand);
                writer.println("```");
                writer.println();
            }
        }
    }
    
    /**
     * كتابة قسم الملخص
     */
    private void writeSummarySection(PrintWriter writer, List<LibraryInfo> themeLibs, 
                                   List<LibraryInfo> uiLibs, List<LibraryInfo> fontLibs, 
                                   List<LibraryInfo> otherLibs) {
        writer.println("## 📊 الملخص - Summary");
        writer.println();
        
        writer.println("| النوع | العدد | الحجم الإجمالي |");
        writer.println("|-------|------|---------------|");
        writer.printf("| مكتبات الثيمات | %d | %s |\n", 
            themeLibs.size(), formatFileSize(getTotalSize(themeLibs)));
        writer.printf("| مكتبات الواجهة | %d | %s |\n", 
            uiLibs.size(), formatFileSize(getTotalSize(uiLibs)));
        writer.printf("| مكتبات الخطوط | %d | %s |\n", 
            fontLibs.size(), formatFileSize(getTotalSize(fontLibs)));
        writer.printf("| مكتبات أخرى | %d | %s |\n", 
            otherLibs.size(), formatFileSize(getTotalSize(otherLibs)));
        
        writer.println();
        
        // تقييم الحالة
        if (themeLibs.size() >= 3) {
            writer.println("✅ **حالة ممتازة:** تتوفر مكتبات ثيمات متنوعة");
        } else if (themeLibs.size() >= 1) {
            writer.println("⚠️ **حالة جيدة:** تتوفر بعض مكتبات الثيمات");
        } else {
            writer.println("❌ **حالة ضعيفة:** لا تتوفر مكتبات ثيمات");
        }
        
        writer.println();
        writer.println("---");
        writer.println("**تم إنشاء التقرير:** " + new java.util.Date());
    }
    
    /**
     * عرض ملخص في وحدة التحكم
     */
    private void displayConsoleSummary(List<LibraryInfo> themeLibs, 
                                     List<LibraryInfo> uiLibs, 
                                     List<LibraryInfo> fontLibs) {
        System.out.println();
        System.out.println("📊 ملخص مكتبات المظهر:");
        System.out.println("📊 Theme Libraries Summary:");
        System.out.println();
        
        System.out.printf("🎨 مكتبات الثيمات: %d\n", themeLibs.size());
        for (LibraryInfo lib : themeLibs) {
            System.out.printf("   ✅ %s (%s)\n", lib.name, formatFileSize(lib.size));
        }
        
        System.out.printf("\n🖥️ مكتبات الواجهة: %d\n", uiLibs.size());
        for (LibraryInfo lib : uiLibs) {
            System.out.printf("   ✅ %s (%s)\n", lib.name, formatFileSize(lib.size));
        }
        
        System.out.printf("\n🔤 مكتبات الخطوط: %d\n", fontLibs.size());
        for (LibraryInfo lib : fontLibs) {
            System.out.printf("   ✅ %s (%s)\n", lib.name, formatFileSize(lib.size));
        }
        
        System.out.println();
        System.out.println("🎉 فحص المكتبات مكتمل!");
    }
    
    /**
     * الحصول على وصف المكتبة
     */
    private String getLibraryDescription(String fileName) {
        if (fileName.contains("flatlaf")) {
            if (fileName.contains("extras")) return "إضافات FlatLaf";
            if (fileName.contains("intellij")) return "ثيمات IntelliJ";
            if (fileName.contains("fonts")) return "خطوط FlatLaf";
            return "مظهر FlatLaf الحديث";
        }
        if (fileName.contains("material")) return "تصميم Material Design";
        if (fileName.contains("jtattoo")) return "مجموعة ثيمات JTattoo";
        if (fileName.contains("darklaf")) return "مظهر داكن متقدم";
        if (fileName.contains("seaglass")) return "مظهر SeaGlass";
        if (fileName.contains("miglayout")) return "نظام تخطيط MigLayout";
        return "مكتبة واجهة مستخدم";
    }
    
    /**
     * الحصول على أمر الاختبار
     */
    private String getTestCommand(String fileName) {
        if (fileName.contains("flatlaf")) {
            return "java -cp \"lib\\*;.\" -Djava.awt.headless=false TestFlatLaf";
        }
        if (fileName.contains("material")) {
            return "java -cp \"lib\\*;.\" -Djava.awt.headless=false TestMaterialUI";
        }
        if (fileName.contains("jtattoo")) {
            return "java -cp \"lib\\*;.\" -Djava.awt.headless=false TestJTattoo";
        }
        return "";
    }
    
    /**
     * تنسيق حجم الملف
     */
    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        return String.format("%.1f MB", size / (1024.0 * 1024.0));
    }
    
    /**
     * حساب الحجم الإجمالي
     */
    private long getTotalSize(List<LibraryInfo> libraries) {
        return libraries.stream().mapToLong(lib -> lib.size).sum();
    }
    
    /**
     * فئة معلومات المكتبة
     */
    private static class LibraryInfo {
        String name;
        long size;
        
        LibraryInfo(String name, long size) {
            this.name = name;
            this.size = size;
        }
    }
}
