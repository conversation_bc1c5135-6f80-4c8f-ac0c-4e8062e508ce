# دليل تشغيل نظام Ship ERP
## How to Run Ship ERP System

---

## 🚀 طرق التشغيل المتاحة

### 1. التشغيل الكامل (موصى به)
```bash
start-system.bat
```
**الميزات:**
- ✅ فحص المكتبات قبل التشغيل
- ✅ تجميع جميع المكونات
- ✅ عرض معلومات النظام
- ✅ تشغيل النظام الكامل

### 2. التشغيل السريع
```bash
quick-start.bat
```
**الميزات:**
- ✅ تشغيل مباشر بدون فحص
- ✅ سريع ومباشر
- ✅ مناسب للاستخدام اليومي

### 3. وضع التطوير
```bash
dev-start.bat
```
**الميزات:**
- ✅ حذف الملفات المجمعة السابقة
- ✅ تجميع جميع الملفات
- ✅ مناسب للتطوير والاختبار

---

## 📋 متطلبات التشغيل

### المكتبات المطلوبة:
- ✅ **ojdbc11.jar** - Oracle JDBC Driver
- ✅ **orai18n.jar** - دعم الترميز العربي
- ✅ **commons-logging-1.2.jar** - نظام السجلات

### قواعد البيانات:
- ✅ **Oracle Database** - localhost:1521:ORCL
- ✅ **SHIP_ERP** - المستخدم الرئيسي
- ✅ **IAS20251** - المستخدم المرجعي

---

## 🔧 حل المشاكل الشائعة

### المشكلة: "ojdbc11.jar missing"
**الحل:**
```bash
# تأكد من وجود الملف في مجلد lib
dir lib\ojdbc11.jar
```

### المشكلة: "Compilation failed"
**الحل:**
```bash
# استخدم وضع التطوير لإعادة التجميع
dev-start.bat
```

### المشكلة: "Database connection failed"
**الحل:**
1. تأكد من تشغيل Oracle Database
2. تحقق من صحة كلمات المرور
3. تأكد من إعدادات الشبكة

---

## 📊 ما يحدث عند التشغيل

### 1. فحص النظام:
- ✅ فحص المكتبات المطلوبة
- ✅ فحص ملفات المصدر
- ✅ تجميع المكونات

### 2. الاتصال بقواعد البيانات:
- ✅ اختبار Oracle JDBC Driver
- ✅ الاتصال بـ SHIP_ERP
- ✅ الاتصال بـ IAS20251
- ✅ فحص الجداول المهمة

### 3. تحميل البيانات:
- ✅ **4,647 صنف** من IAS_ITM_MST
- ✅ **9,108 تفصيل** من IAS_ITM_DTL
- ✅ **18 وحدة قياس** من ERP_MEASUREMENT
- ✅ **70+ مجموعة** من جداول المجموعات

### 4. فتح النوافذ:
- ✅ النافذة الرئيسية مع القائمة الشجرية
- ✅ نافذة وحدات القياس
- ✅ نافذة بيانات الأصناف الحقيقية
- ✅ نافذة بيانات الأصناف الشاملة

---

## 🎯 الميزات المتاحة بعد التشغيل

### إدارة المخزون والأصناف:
- ✅ **بيانات الأصناف الحقيقية** - عرض وإدارة 4647 صنف
- ✅ **بيانات الأصناف الشاملة** - واجهة متقدمة
- ✅ **مجموعات الأصناف** - 5 تبويبات منظمة
- ✅ **وحدات القياس** - إدارة 18 وحدة قياس

### إدارة النظام:
- ✅ **إدارة المستخدمين** - إضافة وتعديل المستخدمين
- ✅ **إدارة الصلاحيات** - نظام أمان متدرج
- ✅ **الإعدادات العامة** - تخصيص النظام

### التقارير والتحليل:
- ✅ **تقارير الأصناف** - تقارير مفصلة
- ✅ **إحصائيات المخزون** - تحليل البيانات
- ✅ **تقارير المجموعات** - تصنيف الأصناف

---

## 🔍 للمطورين

### إضافة نافذة جديدة:
1. انسخ من `MeasurementUnitsWindow.java`
2. عدّل `TreeMenuPanel.java` لإضافة القائمة
3. اختبر باستخدام `dev-start.bat`

### إضافة جدول جديد:
1. استخدم `DatabaseConfig.java` كمرجع
2. أضف الجدول في `CompleteOracleSystemTest.java`
3. اختبر الاتصال

### تخصيص الواجهة:
1. عدّل `UIUtils.java` للألوان والخطوط
2. استخدم `SettingsManager.java` للإعدادات
3. اختبر التغييرات

---

## 📞 الدعم الفني

### في حالة المشاكل:
1. **راجع الرسائل في وحدة التحكم**
2. **تأكد من تشغيل Oracle Database**
3. **تحقق من وجود جميع المكتبات**
4. **استخدم `comprehensive-system-audit.bat` للفحص**

### ملفات السجلات:
- **وحدة التحكم** - رسائل مباشرة
- **ملفات النظام** - في مجلد logs (إن وجد)

---

## ✅ التحقق من نجاح التشغيل

### علامات النجاح:
- ✅ رسالة "تم الاتصال بقاعدة البيانات بنجاح"
- ✅ رسالة "تم تحميل X وحدة قياس"
- ✅ ظهور النافذة الرئيسية
- ✅ عمل القائمة الشجرية

### في حالة الفشل:
- ❌ رسائل خطأ في وحدة التحكم
- ❌ عدم ظهور النوافذ
- ❌ رسائل "Connection failed"

---

**🎉 النظام جاهز للاستخدام والتطوير!**

**للبدء السريع:** `start-system.bat`  
**للتطوير:** `dev-start.bat`  
**للاستخدام اليومي:** `quick-start.bat`
