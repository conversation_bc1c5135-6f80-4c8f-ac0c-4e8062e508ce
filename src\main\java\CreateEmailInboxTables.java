import java.sql.*;

/**
 * إنشاء جداول صندوق البريد الوارد
 * Create Email Inbox Tables
 */
public class CreateEmailInboxTables {
    
    public static void main(String[] args) {
        System.out.println("🗄️ إنشاء جداول صندوق البريد الوارد...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            createEmailMessagesTable(connection);
            createEmailAttachmentsTable(connection);
            createEmailFoldersTable(connection);
            createEmailFiltersTable(connection);
            createEmailReadStatusTable(connection);
            
            connection.close();
            System.out.println("✅ تم إنشاء جميع الجداول بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء الجداول: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void createEmailMessagesTable(Connection connection) throws SQLException {
        String sql = """
            CREATE TABLE EMAIL_MESSAGES (
                MESSAGE_ID NUMBER PRIMARY KEY,
                ACCOUNT_ID NUMBER NOT NULL,
                MESSAGE_UID VARCHAR2(100),
                SUBJECT NVARCHAR2(500),
                FROM_ADDRESS NVARCHAR2(200),
                TO_ADDRESSES NCLOB,
                CC_ADDRESSES NCLOB,
                BCC_ADDRESSES NCLOB,
                REPLY_TO NVARCHAR2(200),
                MESSAGE_DATE DATE,
                RECEIVED_DATE DATE DEFAULT SYSDATE,
                MESSAGE_SIZE NUMBER,
                CONTENT_TYPE VARCHAR2(100),
                MESSAGE_BODY NCLOB,
                HTML_BODY NCLOB,
                IS_READ CHAR(1) DEFAULT 'N',
                IS_FLAGGED CHAR(1) DEFAULT 'N',
                IS_DELETED CHAR(1) DEFAULT 'N',
                FOLDER_NAME NVARCHAR2(100) DEFAULT 'INBOX',
                PRIORITY VARCHAR2(20) DEFAULT 'NORMAL',
                HAS_ATTACHMENTS CHAR(1) DEFAULT 'N',
                MESSAGE_ID_HEADER VARCHAR2(200),
                IN_REPLY_TO VARCHAR2(200),
                REFERENCES NCLOB,
                CREATED_DATE DATE DEFAULT SYSDATE,
                LAST_UPDATED DATE DEFAULT SYSDATE,
                CONSTRAINT FK_EMAIL_MSG_ACCOUNT FOREIGN KEY (ACCOUNT_ID) 
                    REFERENCES EMAIL_ACCOUNTS(ACCOUNT_ID) ON DELETE CASCADE
            )
            """;
        
        try {
            executeSQL(connection, sql, "EMAIL_MESSAGES");
            
            // إنشاء sequence
            executeSQL(connection, 
                "CREATE SEQUENCE EMAIL_MESSAGES_SEQ START WITH 1 INCREMENT BY 1", 
                "EMAIL_MESSAGES_SEQ");
            
            // إنشاء فهارس
            executeSQL(connection, 
                "CREATE INDEX IDX_EMAIL_MSG_ACCOUNT ON EMAIL_MESSAGES(ACCOUNT_ID)", 
                "IDX_EMAIL_MSG_ACCOUNT");
            
            executeSQL(connection, 
                "CREATE INDEX IDX_EMAIL_MSG_DATE ON EMAIL_MESSAGES(MESSAGE_DATE)", 
                "IDX_EMAIL_MSG_DATE");
            
            executeSQL(connection, 
                "CREATE INDEX IDX_EMAIL_MSG_READ ON EMAIL_MESSAGES(IS_READ)", 
                "IDX_EMAIL_MSG_READ");
            
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
        }
    }
    
    private static void createEmailAttachmentsTable(Connection connection) throws SQLException {
        String sql = """
            CREATE TABLE EMAIL_ATTACHMENTS (
                ATTACHMENT_ID NUMBER PRIMARY KEY,
                MESSAGE_ID NUMBER NOT NULL,
                FILENAME NVARCHAR2(255),
                CONTENT_TYPE VARCHAR2(100),
                FILE_SIZE NUMBER,
                ATTACHMENT_DATA BLOB,
                IS_INLINE CHAR(1) DEFAULT 'N',
                CONTENT_ID VARCHAR2(100),
                CREATED_DATE DATE DEFAULT SYSDATE,
                CONSTRAINT FK_EMAIL_ATT_MESSAGE FOREIGN KEY (MESSAGE_ID) 
                    REFERENCES EMAIL_MESSAGES(MESSAGE_ID) ON DELETE CASCADE
            )
            """;
        
        try {
            executeSQL(connection, sql, "EMAIL_ATTACHMENTS");
            
            executeSQL(connection, 
                "CREATE SEQUENCE EMAIL_ATTACHMENTS_SEQ START WITH 1 INCREMENT BY 1", 
                "EMAIL_ATTACHMENTS_SEQ");
            
            executeSQL(connection, 
                "CREATE INDEX IDX_EMAIL_ATT_MESSAGE ON EMAIL_ATTACHMENTS(MESSAGE_ID)", 
                "IDX_EMAIL_ATT_MESSAGE");
            
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
        }
    }
    
    private static void createEmailFoldersTable(Connection connection) throws SQLException {
        String sql = """
            CREATE TABLE EMAIL_FOLDERS (
                FOLDER_ID NUMBER PRIMARY KEY,
                ACCOUNT_ID NUMBER NOT NULL,
                FOLDER_NAME NVARCHAR2(100),
                FOLDER_TYPE VARCHAR2(50) DEFAULT 'CUSTOM',
                PARENT_FOLDER_ID NUMBER,
                MESSAGE_COUNT NUMBER DEFAULT 0,
                UNREAD_COUNT NUMBER DEFAULT 0,
                LAST_SYNC_DATE DATE,
                IS_SUBSCRIBED CHAR(1) DEFAULT 'Y',
                CREATED_DATE DATE DEFAULT SYSDATE,
                CONSTRAINT FK_EMAIL_FOLDER_ACCOUNT FOREIGN KEY (ACCOUNT_ID) 
                    REFERENCES EMAIL_ACCOUNTS(ACCOUNT_ID) ON DELETE CASCADE,
                CONSTRAINT FK_EMAIL_FOLDER_PARENT FOREIGN KEY (PARENT_FOLDER_ID) 
                    REFERENCES EMAIL_FOLDERS(FOLDER_ID) ON DELETE CASCADE
            )
            """;
        
        try {
            executeSQL(connection, sql, "EMAIL_FOLDERS");
            
            executeSQL(connection, 
                "CREATE SEQUENCE EMAIL_FOLDERS_SEQ START WITH 1 INCREMENT BY 1", 
                "EMAIL_FOLDERS_SEQ");
            
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
        }
    }
    
    private static void createEmailFiltersTable(Connection connection) throws SQLException {
        String sql = """
            CREATE TABLE EMAIL_FILTERS (
                FILTER_ID NUMBER PRIMARY KEY,
                ACCOUNT_ID NUMBER NOT NULL,
                FILTER_NAME NVARCHAR2(100),
                FILTER_CONDITION NCLOB,
                ACTION_TYPE VARCHAR2(50),
                ACTION_VALUE NVARCHAR2(200),
                IS_ACTIVE CHAR(1) DEFAULT 'Y',
                PRIORITY_ORDER NUMBER DEFAULT 1,
                CREATED_DATE DATE DEFAULT SYSDATE,
                CONSTRAINT FK_EMAIL_FILTER_ACCOUNT FOREIGN KEY (ACCOUNT_ID) 
                    REFERENCES EMAIL_ACCOUNTS(ACCOUNT_ID) ON DELETE CASCADE
            )
            """;
        
        try {
            executeSQL(connection, sql, "EMAIL_FILTERS");
            
            executeSQL(connection, 
                "CREATE SEQUENCE EMAIL_FILTERS_SEQ START WITH 1 INCREMENT BY 1", 
                "EMAIL_FILTERS_SEQ");
            
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
        }
    }
    
    private static void createEmailReadStatusTable(Connection connection) throws SQLException {
        String sql = """
            CREATE TABLE EMAIL_READ_STATUS (
                STATUS_ID NUMBER PRIMARY KEY,
                MESSAGE_ID NUMBER NOT NULL,
                USER_ID NUMBER,
                READ_DATE DATE DEFAULT SYSDATE,
                READ_LOCATION VARCHAR2(100),
                CONSTRAINT FK_EMAIL_STATUS_MESSAGE FOREIGN KEY (MESSAGE_ID) 
                    REFERENCES EMAIL_MESSAGES(MESSAGE_ID) ON DELETE CASCADE
            )
            """;
        
        try {
            executeSQL(connection, sql, "EMAIL_READ_STATUS");
            
            executeSQL(connection, 
                "CREATE SEQUENCE EMAIL_READ_STATUS_SEQ START WITH 1 INCREMENT BY 1", 
                "EMAIL_READ_STATUS_SEQ");
            
        } catch (SQLException e) {
            if (!e.getMessage().contains("name is already used")) {
                throw e;
            }
        }
    }
    
    private static void executeSQL(Connection connection, String sql, String objectName) throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(sql);
            System.out.println("✅ تم إنشاء: " + objectName);
        } catch (SQLException e) {
            if (e.getMessage().contains("name is already used") || 
                e.getMessage().contains("already exists")) {
                System.out.println("ℹ️ موجود مسبقاً: " + objectName);
            } else {
                System.err.println("❌ خطأ في إنشاء " + objectName + ": " + e.getMessage());
                throw e;
            }
        }
    }
}
