import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.Window;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;
import javax.swing.LookAndFeel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * مدير المظاهر الموحد المبسط للتطبيق
 * Simple Unified Theme Manager for the Application
 */
public class SimpleUnifiedThemeManager {
    
    private static SimpleUnifiedThemeManager instance;
    private static final String SETTINGS_FILE = "theme-settings.properties";
    private static final String THEME_KEY = "current.theme";
    
    // المظاهر المتاحة
    public enum Theme {
        FLAT_LIGHT("FlatLaf Light"),
        FLAT_DARK("FlatLaf Dark"),
        FLAT_INTELLIJ("FlatLaf IntelliJ"),
        FLAT_DARCULA("FlatLaf Darcula"),
        SYSTEM_DEFAULT("System Default"),
        WINDOWS("Windows"),
        METAL("Metal"),
        NIMBUS("Nimbus");
        
        private final String displayName;
        
        Theme(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() { return displayName; }
    }
    
    private Theme currentTheme = Theme.FLAT_LIGHT;
    
    private SimpleUnifiedThemeManager() {
        loadSettings();
    }
    
    public static SimpleUnifiedThemeManager getInstance() {
        if (instance == null) {
            instance = new SimpleUnifiedThemeManager();
        }
        return instance;
    }
    
    /**
     * تطبيق المظهر الافتراضي عند بدء التطبيق
     */
    public static void initializeDefaultTheme() {
        try {
            SimpleUnifiedThemeManager manager = getInstance();
            manager.applyStoredTheme();
            System.out.println("✅ تم تطبيق المظهر الافتراضي: " + manager.currentTheme.getDisplayName());
        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر الافتراضي: " + e.getMessage());
            // تطبيق مظهر احتياطي
            try {
                applyFlatLightTheme();
                System.out.println("✅ تم تطبيق المظهر الاحتياطي: FlatLaf Light");
            } catch (Exception ex) {
                System.err.println("❌ فشل في تطبيق المظهر الاحتياطي");
            }
        }
    }
    
    /**
     * تطبيق مظهر محدد
     */
    public boolean applyTheme(Theme theme) {
        try {
            System.out.println("🎨 تطبيق المظهر: " + theme.getDisplayName());
            
            boolean success = false;
            
            switch (theme) {
                case FLAT_LIGHT:
                    success = applyFlatLightTheme();
                    break;
                case FLAT_DARK:
                    success = applyFlatDarkTheme();
                    break;
                case FLAT_INTELLIJ:
                    success = applyFlatIntelliJTheme();
                    break;
                case FLAT_DARCULA:
                    success = applyFlatDarculaTheme();
                    break;
                case SYSTEM_DEFAULT:
                    success = applySystemTheme();
                    break;
                case WINDOWS:
                    success = applyWindowsTheme();
                    break;
                case METAL:
                    success = applyMetalTheme();
                    break;
                case NIMBUS:
                    success = applyNimbusTheme();
                    break;
            }
            
            if (success) {
                currentTheme = theme;
                applyCustomizations();
                updateAllWindows();
                saveSettings();
                System.out.println("✅ تم تطبيق المظهر بنجاح: " + theme.getDisplayName());
                return true;
            } else {
                System.err.println("❌ فشل في تطبيق المظهر: " + theme.getDisplayName());
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyFlatLightTheme() {
        try {
            Class<?> flatLightClass = Class.forName("com.formdev.flatlaf.FlatLightLaf");
            java.lang.reflect.Method setupMethod = flatLightClass.getMethod("setup");
            return (Boolean) setupMethod.invoke(null);
        } catch (Exception e) {
            System.err.println("FlatLaf Light غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyFlatDarkTheme() {
        try {
            Class<?> flatDarkClass = Class.forName("com.formdev.flatlaf.FlatDarkLaf");
            java.lang.reflect.Method setupMethod = flatDarkClass.getMethod("setup");
            return (Boolean) setupMethod.invoke(null);
        } catch (Exception e) {
            System.err.println("FlatLaf Dark غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyFlatIntelliJTheme() {
        try {
            Class<?> flatIntelliJClass = Class.forName("com.formdev.flatlaf.FlatIntelliJLaf");
            java.lang.reflect.Method setupMethod = flatIntelliJClass.getMethod("setup");
            return (Boolean) setupMethod.invoke(null);
        } catch (Exception e) {
            System.err.println("FlatLaf IntelliJ غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyFlatDarculaTheme() {
        try {
            Class<?> flatDarculaClass = Class.forName("com.formdev.flatlaf.FlatDarculaLaf");
            java.lang.reflect.Method setupMethod = flatDarculaClass.getMethod("setup");
            return (Boolean) setupMethod.invoke(null);
        } catch (Exception e) {
            System.err.println("FlatLaf Darcula غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applySystemTheme() {
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            return true;
        } catch (Exception e) {
            System.err.println("System Theme غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyWindowsTheme() {
        try {
            UIManager.setLookAndFeel("com.sun.java.swing.plaf.windows.WindowsLookAndFeel");
            return true;
        } catch (Exception e) {
            System.err.println("Windows Theme غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyMetalTheme() {
        try {
            UIManager.setLookAndFeel("javax.swing.plaf.metal.MetalLookAndFeel");
            return true;
        } catch (Exception e) {
            System.err.println("Metal Theme غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    private static boolean applyNimbusTheme() {
        try {
            UIManager.setLookAndFeel("javax.swing.plaf.nimbus.NimbusLookAndFeel");
            return true;
        } catch (Exception e) {
            System.err.println("Nimbus Theme غير متاح: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * تطبيق التخصيصات الإضافية
     */
    private void applyCustomizations() {
        // إعدادات الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        UIManager.put("defaultFont", arabicFont);
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("TextArea.font", arabicFont);
        UIManager.put("ComboBox.font", arabicFont);
        UIManager.put("Table.font", arabicFont);
        UIManager.put("Tree.font", arabicFont);
        UIManager.put("Menu.font", arabicFont);
        UIManager.put("MenuItem.font", arabicFont);
        
        // إعدادات FlatLaf المتقدمة
        if (currentTheme.name().startsWith("FLAT_")) {
            UIManager.put("Button.arc", 8);
            UIManager.put("Component.arc", 8);
            UIManager.put("ProgressBar.arc", 8);
            UIManager.put("TextComponent.arc", 8);
            UIManager.put("ScrollBar.width", 12);
            UIManager.put("Table.showHorizontalLines", true);
            UIManager.put("Table.showVerticalLines", true);
        }
        
        // إعدادات اللغة العربية
        UIManager.put("ComponentOrientation", ComponentOrientation.RIGHT_TO_LEFT);
        
        System.out.println("✅ تم تطبيق التخصيصات الإضافية");
    }
    
    /**
     * تحديث جميع النوافذ المفتوحة
     */
    private void updateAllWindows() {
        SwingUtilities.invokeLater(() -> {
            for (Window window : Window.getWindows()) {
                if (window.isDisplayable()) {
                    SwingUtilities.updateComponentTreeUI(window);
                    window.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                    window.repaint();
                }
            }
            System.out.println("✅ تم تحديث جميع النوافذ المفتوحة");
        });
    }
    
    /**
     * تطبيق المظهر المحفوظ
     */
    public void applyStoredTheme() {
        applyTheme(currentTheme);
    }
    
    /**
     * تحميل الإعدادات
     */
    private void loadSettings() {
        try {
            Properties props = new Properties();
            props.load(new FileInputStream(SETTINGS_FILE));
            
            String themeName = props.getProperty(THEME_KEY, "FLAT_LIGHT");
            try {
                currentTheme = Theme.valueOf(themeName);
            } catch (IllegalArgumentException e) {
                currentTheme = Theme.FLAT_LIGHT;
            }
            
            System.out.println("✅ تم تحميل الإعدادات: " + currentTheme.getDisplayName());
            
        } catch (IOException e) {
            System.out.println("ℹ️ لم يتم العثور على ملف الإعدادات، سيتم استخدام الإعدادات الافتراضية");
            currentTheme = Theme.FLAT_LIGHT;
        }
    }
    
    /**
     * حفظ الإعدادات
     */
    private void saveSettings() {
        try {
            Properties props = new Properties();
            props.setProperty(THEME_KEY, currentTheme.name());
            
            props.store(new FileOutputStream(SETTINGS_FILE), "Theme Settings");
            System.out.println("✅ تم حفظ الإعدادات");
            
        } catch (IOException e) {
            System.err.println("❌ خطأ في حفظ الإعدادات: " + e.getMessage());
        }
    }
    
    // Getters
    public Theme getCurrentTheme() { return currentTheme; }
    
    /**
     * الحصول على معلومات المظهر الحالي
     */
    public String getCurrentThemeInfo() {
        LookAndFeel laf = UIManager.getLookAndFeel();
        if (laf != null) {
            return String.format("المظهر: %s | الكلاس: %s",
                currentTheme.getDisplayName(),
                laf.getClass().getSimpleName()
            );
        }
        return "لا يوجد مظهر محدد";
    }
    
    /**
     * إصلاح جميع النوافذ لتستخدم المدير الموحد
     */
    public static void fixAllWindowsThemes() {
        System.out.println("🔧 إصلاح مظاهر جميع النوافذ...");
        
        SimpleUnifiedThemeManager manager = getInstance();
        manager.applyStoredTheme();
        
        System.out.println("✅ تم إصلاح مظاهر جميع النوافذ");
    }
}
