import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

/**
 * إنشاء جداول نظام إدارة العملات
 * Create Currency Management System Tables
 */
public class CreateCurrencyTables {
    
    private static final String DB_URL = "*************************************";
    private static final String DB_USER = "ship_erp";
    private static final String DB_PASSWORD = "ship_erp_password";
    
    public static void main(String[] args) {
        System.out.println("💰 إنشاء جداول نظام إدارة العملات الشامل");
        System.out.println("Creating Comprehensive Currency Management System Tables");
        System.out.println("========================================================");
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            
            // 1. إنشاء جدول العملات الرئيسي
            createCurrenciesTable(conn);
            
            // 2. إنشاء جدول أسعار الصرف
            createExchangeRatesTable(conn);
            
            // 3. إنشاء جدول تاريخ أسعار الصرف
            createExchangeRateHistoryTable(conn);
            
            // 4. إنشاء جدول إعدادات النظام
            createCurrencySettingsTable(conn);
            
            // 5. إنشاء جدول مصادر أسعار الصرف
            createExchangeRateSourcesTable(conn);
            
            // 6. إدراج البيانات الافتراضية
            insertDefaultData(conn);
            
            System.out.println("\n🎉 تم إنشاء نظام إدارة العملات الشامل بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void createCurrenciesTable(Connection conn) throws SQLException {
        System.out.println("[1] إنشاء جدول العملات الرئيسي...");
        
        // حذف الجدول إذا كان موجوداً
        try {
            conn.prepareStatement("DROP TABLE ERP_CURRENCIES CASCADE CONSTRAINTS").execute();
            System.out.println("تم حذف الجدول السابق");
        } catch (SQLException e) {
            // الجدول غير موجود
        }
        
        try {
            conn.prepareStatement("DROP SEQUENCE ERP_CURRENCIES_SEQ").execute();
        } catch (SQLException e) {
            // Sequence غير موجود
        }
        
        String createTableSql = """
            CREATE TABLE ERP_CURRENCIES (
                CURRENCY_ID NUMBER(10) PRIMARY KEY,
                CURRENCY_CODE VARCHAR2(10) NOT NULL UNIQUE,
                CURRENCY_NAME_AR VARCHAR2(100) NOT NULL,
                CURRENCY_NAME_EN VARCHAR2(100) NOT NULL,
                CURRENCY_SYMBOL VARCHAR2(10),
                SYMBOL_POSITION VARCHAR2(10) DEFAULT 'BEFORE',
                DECIMAL_PLACES NUMBER(2) DEFAULT 2,
                IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                IS_DEFAULT CHAR(1) DEFAULT 'N' CHECK (IS_DEFAULT IN ('Y', 'N')),
                COUNTRY_CODE VARCHAR2(5),
                DISPLAY_FORMAT VARCHAR2(50) DEFAULT '#,##0.00',
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50) DEFAULT USER,
                LAST_UPDATED DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(50) DEFAULT USER
            )
        """;
        
        conn.prepareStatement(createTableSql).execute();
        conn.prepareStatement("CREATE SEQUENCE ERP_CURRENCIES_SEQ START WITH 1 INCREMENT BY 1").execute();
        
        System.out.println("✅ تم إنشاء جدول العملات الرئيسي");
    }
    
    private static void createExchangeRatesTable(Connection conn) throws SQLException {
        System.out.println("[2] إنشاء جدول أسعار الصرف...");
        
        try {
            conn.prepareStatement("DROP TABLE ERP_EXCHANGE_RATES CASCADE CONSTRAINTS").execute();
        } catch (SQLException e) {
            // الجدول غير موجود
        }
        
        try {
            conn.prepareStatement("DROP SEQUENCE ERP_EXCHANGE_RATES_SEQ").execute();
        } catch (SQLException e) {
            // Sequence غير موجود
        }
        
        String createTableSql = """
            CREATE TABLE ERP_EXCHANGE_RATES (
                RATE_ID NUMBER(10) PRIMARY KEY,
                FROM_CURRENCY_ID NUMBER(10) NOT NULL,
                TO_CURRENCY_ID NUMBER(10) NOT NULL,
                EXCHANGE_RATE NUMBER(15,6) NOT NULL,
                RATE_DATE DATE DEFAULT SYSDATE,
                RATE_SOURCE VARCHAR2(50),
                IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50) DEFAULT USER,
                CONSTRAINT FK_EXCHANGE_FROM_CURRENCY FOREIGN KEY (FROM_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID),
                CONSTRAINT FK_EXCHANGE_TO_CURRENCY FOREIGN KEY (TO_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID)
            )
        """;
        
        conn.prepareStatement(createTableSql).execute();
        conn.prepareStatement("CREATE SEQUENCE ERP_EXCHANGE_RATES_SEQ START WITH 1 INCREMENT BY 1").execute();
        
        System.out.println("✅ تم إنشاء جدول أسعار الصرف");
    }
    
    private static void createExchangeRateHistoryTable(Connection conn) throws SQLException {
        System.out.println("[3] إنشاء جدول تاريخ أسعار الصرف...");
        
        try {
            conn.prepareStatement("DROP TABLE ERP_EXCHANGE_RATE_HISTORY CASCADE CONSTRAINTS").execute();
        } catch (SQLException e) {
            // الجدول غير موجود
        }
        
        try {
            conn.prepareStatement("DROP SEQUENCE ERP_EXCHANGE_RATE_HISTORY_SEQ").execute();
        } catch (SQLException e) {
            // Sequence غير موجود
        }
        
        String createTableSql = """
            CREATE TABLE ERP_EXCHANGE_RATE_HISTORY (
                HISTORY_ID NUMBER(10) PRIMARY KEY,
                FROM_CURRENCY_ID NUMBER(10) NOT NULL,
                TO_CURRENCY_ID NUMBER(10) NOT NULL,
                OLD_RATE NUMBER(15,6),
                NEW_RATE NUMBER(15,6) NOT NULL,
                CHANGE_DATE DATE DEFAULT SYSDATE,
                CHANGE_REASON VARCHAR2(200),
                RATE_SOURCE VARCHAR2(50),
                CHANGED_BY VARCHAR2(50) DEFAULT USER,
                CONSTRAINT FK_HISTORY_FROM_CURRENCY FOREIGN KEY (FROM_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID),
                CONSTRAINT FK_HISTORY_TO_CURRENCY FOREIGN KEY (TO_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID)
            )
        """;
        
        conn.prepareStatement(createTableSql).execute();
        conn.prepareStatement("CREATE SEQUENCE ERP_EXCHANGE_RATE_HISTORY_SEQ START WITH 1 INCREMENT BY 1").execute();
        
        System.out.println("✅ تم إنشاء جدول تاريخ أسعار الصرف");
    }
    
    private static void createCurrencySettingsTable(Connection conn) throws SQLException {
        System.out.println("[4] إنشاء جدول إعدادات النظام...");
        
        try {
            conn.prepareStatement("DROP TABLE ERP_CURRENCY_SETTINGS CASCADE CONSTRAINTS").execute();
        } catch (SQLException e) {
            // الجدول غير موجود
        }
        
        try {
            conn.prepareStatement("DROP SEQUENCE ERP_CURRENCY_SETTINGS_SEQ").execute();
        } catch (SQLException e) {
            // Sequence غير موجود
        }
        
        String createTableSql = """
            CREATE TABLE ERP_CURRENCY_SETTINGS (
                SETTING_ID NUMBER(10) PRIMARY KEY,
                SETTING_KEY VARCHAR2(100) NOT NULL UNIQUE,
                SETTING_VALUE VARCHAR2(500),
                SETTING_DESCRIPTION VARCHAR2(200),
                SETTING_TYPE VARCHAR2(20) DEFAULT 'STRING',
                IS_SYSTEM CHAR(1) DEFAULT 'N' CHECK (IS_SYSTEM IN ('Y', 'N')),
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50) DEFAULT USER,
                LAST_UPDATED DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(50) DEFAULT USER
            )
        """;
        
        conn.prepareStatement(createTableSql).execute();
        conn.prepareStatement("CREATE SEQUENCE ERP_CURRENCY_SETTINGS_SEQ START WITH 1 INCREMENT BY 1").execute();
        
        System.out.println("✅ تم إنشاء جدول إعدادات النظام");
    }
    
    private static void createExchangeRateSourcesTable(Connection conn) throws SQLException {
        System.out.println("[5] إنشاء جدول مصادر أسعار الصرف الخارجية...");
        
        try {
            conn.prepareStatement("DROP TABLE ERP_EXCHANGE_RATE_SOURCES CASCADE CONSTRAINTS").execute();
        } catch (SQLException e) {
            // الجدول غير موجود
        }
        
        try {
            conn.prepareStatement("DROP SEQUENCE ERP_EXCHANGE_RATE_SOURCES_SEQ").execute();
        } catch (SQLException e) {
            // Sequence غير موجود
        }
        
        String createTableSql = """
            CREATE TABLE ERP_EXCHANGE_RATE_SOURCES (
                SOURCE_ID NUMBER(10) PRIMARY KEY,
                SOURCE_NAME VARCHAR2(100) NOT NULL,
                SOURCE_URL VARCHAR2(500),
                API_KEY VARCHAR2(200),
                UPDATE_FREQUENCY NUMBER(5) DEFAULT 60,
                LAST_UPDATE DATE,
                IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                PRIORITY_ORDER NUMBER(3) DEFAULT 1,
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50) DEFAULT USER
            )
        """;
        
        conn.prepareStatement(createTableSql).execute();
        conn.prepareStatement("CREATE SEQUENCE ERP_EXCHANGE_RATE_SOURCES_SEQ START WITH 1 INCREMENT BY 1").execute();
        
        System.out.println("✅ تم إنشاء جدول مصادر أسعار الصرف الخارجية");
    }
    
    private static void insertDefaultData(Connection conn) throws SQLException {
        System.out.println("[6] إدراج البيانات الافتراضية...");
        
        // العملات الافتراضية
        String insertCurrencySql = """
            INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, IS_DEFAULT, COUNTRY_CODE)
            VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(insertCurrencySql)) {
            // الدولار الأمريكي
            stmt.setString(1, "USD");
            stmt.setString(2, "الدولار الأمريكي");
            stmt.setString(3, "US Dollar");
            stmt.setString(4, "$");
            stmt.setString(5, "BEFORE");
            stmt.setInt(6, 2);
            stmt.setString(7, "Y");
            stmt.setString(8, "US");
            stmt.addBatch();
            
            // اليورو
            stmt.setString(1, "EUR");
            stmt.setString(2, "اليورو");
            stmt.setString(3, "Euro");
            stmt.setString(4, "€");
            stmt.setString(5, "BEFORE");
            stmt.setInt(6, 2);
            stmt.setString(7, "N");
            stmt.setString(8, "EU");
            stmt.addBatch();
            
            // الريال السعودي
            stmt.setString(1, "SAR");
            stmt.setString(2, "الريال السعودي");
            stmt.setString(3, "Saudi Riyal");
            stmt.setString(4, "ر.س");
            stmt.setString(5, "AFTER");
            stmt.setInt(6, 2);
            stmt.setString(7, "N");
            stmt.setString(8, "SA");
            stmt.addBatch();
            
            // الدرهم الإماراتي
            stmt.setString(1, "AED");
            stmt.setString(2, "الدرهم الإماراتي");
            stmt.setString(3, "UAE Dirham");
            stmt.setString(4, "د.إ");
            stmt.setString(5, "AFTER");
            stmt.setInt(6, 2);
            stmt.setString(7, "N");
            stmt.setString(8, "AE");
            stmt.addBatch();
            
            // الجنيه المصري
            stmt.setString(1, "EGP");
            stmt.setString(2, "الجنيه المصري");
            stmt.setString(3, "Egyptian Pound");
            stmt.setString(4, "ج.م");
            stmt.setString(5, "AFTER");
            stmt.setInt(6, 2);
            stmt.setString(7, "N");
            stmt.setString(8, "EG");
            stmt.addBatch();
            
            // الدينار الأردني
            stmt.setString(1, "JOD");
            stmt.setString(2, "الدينار الأردني");
            stmt.setString(3, "Jordanian Dinar");
            stmt.setString(4, "د.أ");
            stmt.setString(5, "AFTER");
            stmt.setInt(6, 3);
            stmt.setString(7, "N");
            stmt.setString(8, "JO");
            stmt.addBatch();
            
            stmt.executeBatch();
        }
        
        // أسعار الصرف الافتراضية
        String insertRateSql = """
            INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
            VALUES (ERP_EXCHANGE_RATES_SEQ.NEXTVAL, ?, ?, ?, 'SYSTEM')
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(insertRateSql)) {
            // USD to SAR
            stmt.setInt(1, 1); stmt.setInt(2, 3); stmt.setDouble(3, 3.75); stmt.addBatch();
            // USD to AED
            stmt.setInt(1, 1); stmt.setInt(2, 4); stmt.setDouble(3, 3.67); stmt.addBatch();
            // USD to EGP
            stmt.setInt(1, 1); stmt.setInt(2, 5); stmt.setDouble(3, 30.9); stmt.addBatch();
            // USD to JOD
            stmt.setInt(1, 1); stmt.setInt(2, 6); stmt.setDouble(3, 0.71); stmt.addBatch();
            // EUR to USD
            stmt.setInt(1, 2); stmt.setInt(2, 1); stmt.setDouble(3, 1.18); stmt.addBatch();
            
            stmt.executeBatch();
        }
        
        // الإعدادات الافتراضية
        String insertSettingSql = """
            INSERT INTO ERP_CURRENCY_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESCRIPTION, SETTING_TYPE, IS_SYSTEM)
            VALUES (ERP_CURRENCY_SETTINGS_SEQ.NEXTVAL, ?, ?, ?, ?, 'Y')
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(insertSettingSql)) {
            stmt.setString(1, "DEFAULT_CURRENCY"); stmt.setString(2, "USD"); stmt.setString(3, "العملة الافتراضية للنظام"); stmt.setString(4, "STRING"); stmt.addBatch();
            stmt.setString(1, "AUTO_UPDATE_RATES"); stmt.setString(2, "Y"); stmt.setString(3, "تحديث أسعار الصرف تلقائياً"); stmt.setString(4, "BOOLEAN"); stmt.addBatch();
            stmt.setString(1, "UPDATE_FREQUENCY"); stmt.setString(2, "60"); stmt.setString(3, "تكرار التحديث بالدقائق"); stmt.setString(4, "NUMBER"); stmt.addBatch();
            stmt.setString(1, "DECIMAL_PLACES"); stmt.setString(2, "2"); stmt.setString(3, "عدد الخانات العشرية الافتراضي"); stmt.setString(4, "NUMBER"); stmt.addBatch();
            
            stmt.executeBatch();
        }
        
        // مصادر أسعار الصرف
        String insertSourceSql = """
            INSERT INTO ERP_EXCHANGE_RATE_SOURCES (SOURCE_ID, SOURCE_NAME, SOURCE_URL, UPDATE_FREQUENCY, PRIORITY_ORDER)
            VALUES (ERP_EXCHANGE_RATE_SOURCES_SEQ.NEXTVAL, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(insertSourceSql)) {
            stmt.setString(1, "البنك المركزي السعودي"); stmt.setString(2, "https://www.sama.gov.sa"); stmt.setInt(3, 1440); stmt.setInt(4, 1); stmt.addBatch();
            stmt.setString(1, "European Central Bank"); stmt.setString(2, "https://www.ecb.europa.eu"); stmt.setInt(3, 1440); stmt.setInt(4, 2); stmt.addBatch();
            
            stmt.executeBatch();
        }
        
        conn.commit();
        System.out.println("✅ تم إدراج البيانات الافتراضية");
    }
}
