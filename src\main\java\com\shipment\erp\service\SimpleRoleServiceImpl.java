package com.shipment.erp.service;

import com.shipment.erp.model.Role;
import com.shipment.erp.repository.RoleRepository;

import java.util.List;
import java.util.Optional;

/**
 * تنفيذ مبسط لخدمة إدارة الأدوار
 * Simple Role Management Service Implementation
 */
public class SimpleRoleServiceImpl extends SimpleBaseServiceImpl<Role> implements RoleService {
    
    private final RoleRepository roleRepository;
    
    public SimpleRoleServiceImpl(RoleRepository roleRepository) {
        super(roleRepository);
        this.roleRepository = roleRepository;
    }
    
    @Override
    public List<Role> findActiveRoles() {
        return roleRepository.findByIsActiveTrue();
    }
    
    @Override
    public Role findByName(String name) {
        Optional<Role> role = roleRepository.findByName(name);
        return role.orElse(null);
    }
    
    @Override
    public List<Role> findByNameContaining(String name) {
        return roleRepository.findByNameContainingIgnoreCase(name);
    }
    
    @Override
    public boolean existsByName(String name) {
        return roleRepository.existsByName(name);
    }
    
    @Override
    public void toggleActive(Long roleId) {
        Optional<Role> roleOpt = roleRepository.findById(roleId);
        if (roleOpt.isPresent()) {
            Role role = roleOpt.get();
            role.setActive(!role.isActive());
            roleRepository.save(role);
        }
    }
    
    @Override
    public long getUsersCount(Long roleId) {
        return roleRepository.getUsersCount(roleId);
    }
    
    @Override
    public boolean canDelete(Long roleId) {
        // لا يمكن حذف الدور إذا كان له مستخدمين
        return getUsersCount(roleId) == 0;
    }
    
    @Override
    public List<Role> getDefaultRoles() {
        return roleRepository.findDefaultRoles();
    }
    
    @Override
    public Role save(Role role) {
        // التحقق من عدم تكرار الاسم
        if (role.getId() == null && existsByName(role.getName())) {
            throw new RuntimeException("اسم الدور موجود مسبقاً: " + role.getName());
        }
        
        // التحقق من عدم تكرار الاسم عند التحديث
        if (role.getId() != null) {
            Role existingRole = findByName(role.getName());
            if (existingRole != null && !existingRole.getId().equals(role.getId())) {
                throw new RuntimeException("اسم الدور موجود مسبقاً: " + role.getName());
            }
        }
        
        return super.save(role);
    }
    
    @Override
    public void delete(Long id) {
        if (!canDelete(id)) {
            throw new RuntimeException("لا يمكن حذف الدور لأنه مرتبط بمستخدمين");
        }
        super.delete(id);
    }
}
