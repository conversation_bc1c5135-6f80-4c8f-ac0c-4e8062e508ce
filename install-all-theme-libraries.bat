@echo off
echo Installing ALL Theme Libraries for Ship ERP System
echo ================================================

cd /d "e:\ship_erp\java\lib"

echo.
echo [1/20] FlatLaf Core Library...
curl -L -o flatlaf-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar"

echo [2/20] FlatLaf Extras...
curl -L -o flatlaf-extras-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar"

echo [3/20] FlatLaf IntelliJ Themes...
curl -L -o flatlaf-intellij-themes-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-intellij-themes/3.2.5/flatlaf-intellij-themes-3.2.5.jar"

echo [4/20] FlatLaf Fonts Roboto...
curl -L -o flatlaf-fonts-roboto-2.137.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-fonts-roboto/2.137/flatlaf-fonts-roboto-2.137.jar"

echo [5/20] FlatLaf Fonts JetBrains Mono...
curl -L -o flatlaf-fonts-jetbrains-mono-2.304.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-fonts-jetbrains-mono/2.304/flatlaf-fonts-jetbrains-mono-2.304.jar"

echo [6/20] Substance Look and Feel...
curl -L -o substance-8.0.02.jar "https://repo1.maven.org/maven2/org/pushingpixels/substance/8.0.02/substance-8.0.02.jar"

echo [7/20] Trident Animation Library...
curl -L -o trident-1.5.00.jar "https://repo1.maven.org/maven2/org/pushingpixels/trident/1.5.00/trident-1.5.00.jar"

echo [8/20] JTattoo Look and Feel...
curl -L -o jtattoo-1.6.13.jar "https://repo1.maven.org/maven2/com/jtattoo/JTattoo/1.6.13/JTattoo-1.6.13.jar"

echo [9/20] WebLaF Core...
curl -L -o weblaf-core-2.2.1.jar "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-core/2.2.1/weblaf-core-2.2.1.jar"

echo [10/20] WebLaF UI...
curl -L -o weblaf-ui-2.2.1.jar "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-ui/2.2.1/weblaf-ui-2.2.1.jar"

echo [11/20] Synthetica Look and Feel...
curl -L -o synthetica-2.30.0.jar "https://repo1.maven.org/maven2/de/javasoft/synthetica/2.30.0/synthetica-2.30.0.jar"

echo [12/20] Darklaf Look and Feel...
curl -L -o darklaf-core-3.0.2.jar "https://repo1.maven.org/maven2/com/github/weisj/darklaf-core/3.0.2/darklaf-core-3.0.2.jar"

echo [13/20] Darklaf Theme...
curl -L -o darklaf-theme-3.0.2.jar "https://repo1.maven.org/maven2/com/github/weisj/darklaf-theme/3.0.2/darklaf-theme-3.0.2.jar"

echo [14/20] Material Theme UI...
curl -L -o material-theme-ui-6.2.0.jar "https://repo1.maven.org/maven2/com/github/vincenzopalazzo/material-ui-swing/6.2.0/material-ui-swing-6.2.0.jar"

echo [15/20] BeautyEye Look and Feel...
curl -L -o beautyeye-3.7.jar "https://repo1.maven.org/maven2/org/jb2011/beautyeye/3.7/beautyeye-3.7.jar"

echo [16/20] Seaglass Look and Feel...
curl -L -o seaglass-0.2.1.jar "https://repo1.maven.org/maven2/com/seaglasslookandfeel/seaglasslookandfeel/0.2.1/seaglasslookandfeel-0.2.1.jar"

echo [17/20] Color Chooser Library...
curl -L -o colorchooser-1.0.jar "https://repo1.maven.org/maven2/com/bric/colorchooser/1.0/colorchooser-1.0.jar"

echo [18/20] JSON Processing...
curl -L -o json-20230227.jar "https://repo1.maven.org/maven2/org/json/json/20230227/json-20230227.jar"

echo [19/20] Apache Commons IO...
curl -L -o commons-io-2.11.0.jar "https://repo1.maven.org/maven2/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar"

echo [20/20] Apache Commons Lang...
curl -L -o commons-lang3-3.12.0.jar "https://repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar"

echo.
echo ================================================
echo Installation Complete!
echo ================================================

echo.
echo Installed Theme Libraries:
dir /b *.jar | findstr -i "flat\|substance\|tattoo\|web\|synth\|dark\|material\|beauty\|seaglass\|color\|json\|commons"

echo.
echo Total JAR files in lib directory:
dir /b *.jar | find /c ".jar"

echo.
echo All theme libraries have been successfully installed!
echo You can now use advanced themes in your Ship ERP application.

pause
