import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء جدول الشركات المتقدم والشامل Create Advanced and Comprehensive Companies Table
 */
public class CreateAdvancedCompaniesTable {

    public static void main(String[] args) {
        System.out.println("🏢 إنشاء جدول الشركات المتقدم والشامل...");

        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();

            createAdvancedCompaniesTable(connection);
            createCompanyBranchesTable(connection);
            createCompanyContactsTable(connection);
            createCompanyDocumentsTable(connection);
            createCompanySettingsTable(connection);
            insertSampleData(connection);

            connection.close();
            System.out.println("✅ تم إنشاء نظام الشركات المتقدم بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء جدول الشركات المتقدم: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void createAdvancedCompaniesTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول الشركات المتقدم...");

        // حذف الجدول إذا كان موجوداً
        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_ADVANCED_COMPANIES CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_ADVANCED_COMPANIES_SEQ");
            System.out.println("🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createTableSQL =
                """
                        CREATE TABLE ERP_ADVANCED_COMPANIES (
                            COMPANY_ID NUMBER(10) NOT NULL,
                            COMPANY_CODE VARCHAR2(20) NOT NULL UNIQUE,
                            COMPANY_NAME_AR NVARCHAR2(500) NOT NULL,
                            COMPANY_NAME_EN VARCHAR2(300) NOT NULL,
                            COMPANY_SHORT_NAME_AR NVARCHAR2(100),
                            COMPANY_SHORT_NAME_EN VARCHAR2(50),

                            -- معلومات التواصل الأساسية
                            PRIMARY_EMAIL VARCHAR2(100),
                            SECONDARY_EMAIL VARCHAR2(100),
                            PRIMARY_PHONE VARCHAR2(30),
                            SECONDARY_PHONE VARCHAR2(30),
                            FAX_NUMBER VARCHAR2(30),
                            MOBILE_NUMBER VARCHAR2(30),
                            WEBSITE_URL VARCHAR2(200),

                            -- العنوان التفصيلي
                            STREET_ADDRESS_AR NVARCHAR2(500),
                            STREET_ADDRESS_EN VARCHAR2(300),
                            BUILDING_NUMBER VARCHAR2(20),
                            FLOOR_NUMBER VARCHAR2(10),
                            APARTMENT_NUMBER VARCHAR2(10),
                            DISTRICT_AR NVARCHAR2(100),
                            DISTRICT_EN VARCHAR2(100),
                            CITY_AR NVARCHAR2(100),
                            CITY_EN VARCHAR2(100),
                            STATE_PROVINCE_AR NVARCHAR2(100),
                            STATE_PROVINCE_EN VARCHAR2(100),
                            COUNTRY_AR NVARCHAR2(100),
                            COUNTRY_EN VARCHAR2(100),
                            POSTAL_CODE VARCHAR2(20),
                            PO_BOX VARCHAR2(20),

                            -- المعلومات القانونية والتجارية
                            COMMERCIAL_REGISTER VARCHAR2(50),
                            TAX_NUMBER VARCHAR2(50),
                            VAT_NUMBER VARCHAR2(50),
                            CHAMBER_OF_COMMERCE_NUMBER VARCHAR2(50),
                            INDUSTRIAL_LICENSE_NUMBER VARCHAR2(50),
                            ESTABLISHMENT_DATE DATE,
                            LEGAL_FORM VARCHAR2(50), -- شركة مساهمة، ذات مسؤولية محدودة، إلخ
                            CAPITAL_AMOUNT NUMBER(15,2),
                            CAPITAL_CURRENCY VARCHAR2(3) DEFAULT 'SAR',

                            -- معلومات البنك الرئيسي
                            BANK_NAME_AR NVARCHAR2(200),
                            BANK_NAME_EN VARCHAR2(200),
                            BANK_ACCOUNT_NUMBER VARCHAR2(50),
                            IBAN_NUMBER VARCHAR2(50),
                            SWIFT_CODE VARCHAR2(20),
                            BANK_BRANCH_AR NVARCHAR2(200),
                            BANK_BRANCH_EN VARCHAR2(200),

                            -- الشعار والهوية البصرية
                            LOGO_IMAGE BLOB,
                            LOGO_PATH VARCHAR2(500),
                            COMPANY_COLORS VARCHAR2(100), -- JSON format for brand colors
                            LETTERHEAD_TEMPLATE CLOB,

                            -- إعدادات النظام
                            DEFAULT_CURRENCY VARCHAR2(3) DEFAULT 'SAR',
                            DEFAULT_LANGUAGE VARCHAR2(5) DEFAULT 'ar',
                            TIME_ZONE VARCHAR2(50) DEFAULT 'Asia/Riyadh',
                            DATE_FORMAT VARCHAR2(20) DEFAULT 'DD/MM/YYYY',
                            NUMBER_FORMAT VARCHAR2(20) DEFAULT '#,##0.00',

                            -- معلومات الاتصال الإضافية
                            LINKEDIN_URL VARCHAR2(200),
                            TWITTER_URL VARCHAR2(200),
                            FACEBOOK_URL VARCHAR2(200),
                            INSTAGRAM_URL VARCHAR2(200),

                            -- الحالة والتصنيف
                            COMPANY_STATUS VARCHAR2(20) DEFAULT 'ACTIVE', -- ACTIVE, INACTIVE, SUSPENDED
                            COMPANY_TYPE VARCHAR2(50), -- HEAD_OFFICE, BRANCH, SUBSIDIARY
                            PARENT_COMPANY_ID NUMBER(10),
                            INDUSTRY_SECTOR_AR NVARCHAR2(200),
                            INDUSTRY_SECTOR_EN VARCHAR2(200),
                            BUSINESS_ACTIVITY_AR NCLOB,
                            BUSINESS_ACTIVITY_EN CLOB,

                            -- معلومات الترخيص والشهادات
                            QUALITY_CERTIFICATES CLOB, -- JSON format
                            PROFESSIONAL_LICENSES CLOB, -- JSON format
                            INSURANCE_POLICIES CLOB, -- JSON format

                            -- إعدادات المحاسبة
                            FISCAL_YEAR_START_MONTH NUMBER(2) DEFAULT 1,
                            ACCOUNTING_METHOD VARCHAR2(20) DEFAULT 'ACCRUAL', -- ACCRUAL, CASH
                            BASE_CURRENCY VARCHAR2(3) DEFAULT 'SAR',
                            MULTI_CURRENCY_ENABLED CHAR(1) DEFAULT 'Y',

                            -- معلومات التدقيق والأمان
                            IS_ACTIVE CHAR(1) DEFAULT 'Y',
                            IS_VERIFIED CHAR(1) DEFAULT 'N',
                            VERIFICATION_DATE DATE,
                            VERIFIED_BY VARCHAR2(50),

                            -- معلومات التتبع
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,
                            LAST_UPDATED DATE DEFAULT SYSDATE,
                            UPDATED_BY VARCHAR2(50) DEFAULT USER,
                            VERSION_NUMBER NUMBER(10) DEFAULT 1,

                            -- ملاحظات وتعليقات
                            INTERNAL_NOTES NCLOB,
                            PUBLIC_DESCRIPTION_AR NCLOB,
                            PUBLIC_DESCRIPTION_EN CLOB,

                            -- معلومات إضافية مرنة
                            CUSTOM_FIELD_1 NVARCHAR2(500),
                            CUSTOM_FIELD_2 NVARCHAR2(500),
                            CUSTOM_FIELD_3 NVARCHAR2(500),
                            CUSTOM_FIELD_4 NUMBER(15,2),
                            CUSTOM_FIELD_5 NUMBER(15,2),
                            CUSTOM_FIELD_6 DATE,
                            CUSTOM_FIELD_7 DATE,
                            CUSTOM_FIELD_8 CLOB,

                            CONSTRAINT PK_ERP_ADVANCED_COMPANIES PRIMARY KEY (COMPANY_ID),
                            CONSTRAINT FK_PARENT_COMPANY FOREIGN KEY (PARENT_COMPANY_ID)
                                REFERENCES ERP_ADVANCED_COMPANIES(COMPANY_ID),
                            CONSTRAINT CHK_COMPANY_STATUS CHECK (COMPANY_STATUS IN ('ACTIVE', 'INACTIVE', 'SUSPENDED')),
                            CONSTRAINT CHK_COMPANY_TYPE CHECK (COMPANY_TYPE IN ('HEAD_OFFICE', 'BRANCH', 'SUBSIDIARY')),
                            CONSTRAINT CHK_ACCOUNTING_METHOD CHECK (ACCOUNTING_METHOD IN ('ACCRUAL', 'CASH')),
                            CONSTRAINT CHK_IS_ACTIVE CHECK (IS_ACTIVE IN ('Y', 'N')),
                            CONSTRAINT CHK_IS_VERIFIED CHECK (IS_VERIFIED IN ('Y', 'N')),
                            CONSTRAINT CHK_MULTI_CURRENCY CHECK (MULTI_CURRENCY_ENABLED IN ('Y', 'N'))
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createTableSQL);
            System.out.println("✅ تم إنشاء جدول ERP_ADVANCED_COMPANIES");
        }

        // إنشاء المتسلسل
        String createSequenceSQL = """
                CREATE SEQUENCE ERP_ADVANCED_COMPANIES_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createSequenceSQL);
            System.out.println("✅ تم إنشاء المتسلسل ERP_ADVANCED_COMPANIES_SEQ");
        }

        // إنشاء الفهارس
        createCompanyIndexes(connection);
    }

    private static void createCompanyIndexes(Connection connection) throws SQLException {
        System.out.println("📊 إنشاء فهارس جدول الشركات...");

        String[] indexes = {"CREATE INDEX IDX_COMPANY_CODE ON ERP_ADVANCED_COMPANIES(COMPANY_CODE)",
                "CREATE INDEX IDX_COMPANY_NAME_AR ON ERP_ADVANCED_COMPANIES(COMPANY_NAME_AR)",
                "CREATE INDEX IDX_COMPANY_NAME_EN ON ERP_ADVANCED_COMPANIES(COMPANY_NAME_EN)",
                "CREATE INDEX IDX_COMPANY_STATUS ON ERP_ADVANCED_COMPANIES(COMPANY_STATUS)",
                "CREATE INDEX IDX_COMPANY_TYPE ON ERP_ADVANCED_COMPANIES(COMPANY_TYPE)",
                "CREATE INDEX IDX_PARENT_COMPANY ON ERP_ADVANCED_COMPANIES(PARENT_COMPANY_ID)",
                "CREATE INDEX IDX_TAX_NUMBER ON ERP_ADVANCED_COMPANIES(TAX_NUMBER)",
                "CREATE INDEX IDX_COMMERCIAL_REG ON ERP_ADVANCED_COMPANIES(COMMERCIAL_REGISTER)"};

        try (Statement stmt = connection.createStatement()) {
            for (String indexSQL : indexes) {
                try {
                    stmt.executeUpdate(indexSQL);
                } catch (SQLException e) {
                    // تجاهل إذا كان الفهرس موجود
                }
            }
            System.out.println("✅ تم إنشاء فهارس الجدول");
        }
    }

    private static void createCompanyBranchesTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول فروع الشركات...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_COMPANY_BRANCHES CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_COMPANY_BRANCHES_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createBranchesSQL = """
                CREATE TABLE ERP_COMPANY_BRANCHES (
                    BRANCH_ID NUMBER(10) NOT NULL,
                    COMPANY_ID NUMBER(10) NOT NULL,
                    BRANCH_CODE VARCHAR2(20) NOT NULL,
                    BRANCH_NAME_AR NVARCHAR2(300) NOT NULL,
                    BRANCH_NAME_EN VARCHAR2(200) NOT NULL,
                    ADDRESS_AR NVARCHAR2(500),
                    ADDRESS_EN VARCHAR2(300),
                    CITY_AR NVARCHAR2(100),
                    CITY_EN VARCHAR2(100),
                    PHONE VARCHAR2(30),
                    EMAIL VARCHAR2(100),
                    MANAGER_NAME_AR NVARCHAR2(200),
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    IS_MAIN_BRANCH CHAR(1) DEFAULT 'N',
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,

                    CONSTRAINT PK_ERP_COMPANY_BRANCHES PRIMARY KEY (BRANCH_ID),
                    CONSTRAINT FK_BRANCH_COMPANY FOREIGN KEY (COMPANY_ID)
                        REFERENCES ERP_ADVANCED_COMPANIES(COMPANY_ID) ON DELETE CASCADE
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createBranchesSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_COMPANY_BRANCHES_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_COMPANY_BRANCHES");
        }
    }

    private static void createCompanyContactsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول جهات اتصال الشركات...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_COMPANY_CONTACTS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_COMPANY_CONTACTS_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createContactsSQL = """
                CREATE TABLE ERP_COMPANY_CONTACTS (
                    CONTACT_ID NUMBER(10) NOT NULL,
                    COMPANY_ID NUMBER(10) NOT NULL,
                    CONTACT_TYPE VARCHAR2(50) NOT NULL,
                    FULL_NAME_AR NVARCHAR2(200) NOT NULL,
                    FULL_NAME_EN VARCHAR2(200),
                    JOB_TITLE_AR NVARCHAR2(200),
                    PRIMARY_PHONE VARCHAR2(30),
                    EMAIL VARCHAR2(100),
                    IS_PRIMARY_CONTACT CHAR(1) DEFAULT 'N',
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,

                    CONSTRAINT PK_ERP_COMPANY_CONTACTS PRIMARY KEY (CONTACT_ID),
                    CONSTRAINT FK_CONTACT_COMPANY FOREIGN KEY (COMPANY_ID)
                        REFERENCES ERP_ADVANCED_COMPANIES(COMPANY_ID) ON DELETE CASCADE
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createContactsSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_COMPANY_CONTACTS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_COMPANY_CONTACTS");
        }
    }

    private static void createCompanyDocumentsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول وثائق الشركات...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_COMPANY_DOCUMENTS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_COMPANY_DOCUMENTS_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createDocumentsSQL = """
                CREATE TABLE ERP_COMPANY_DOCUMENTS (
                    DOCUMENT_ID NUMBER(10) NOT NULL,
                    COMPANY_ID NUMBER(10) NOT NULL,
                    DOCUMENT_TYPE VARCHAR2(50) NOT NULL,
                    DOCUMENT_NAME_AR NVARCHAR2(300) NOT NULL,
                    DOCUMENT_NUMBER VARCHAR2(100),
                    ISSUE_DATE DATE,
                    EXPIRY_DATE DATE,
                    FILE_PATH VARCHAR2(500),
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER,

                    CONSTRAINT PK_ERP_COMPANY_DOCUMENTS PRIMARY KEY (DOCUMENT_ID),
                    CONSTRAINT FK_DOCUMENT_COMPANY FOREIGN KEY (COMPANY_ID)
                        REFERENCES ERP_ADVANCED_COMPANIES(COMPANY_ID) ON DELETE CASCADE
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createDocumentsSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_COMPANY_DOCUMENTS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_COMPANY_DOCUMENTS");
        }
    }

    private static void createCompanySettingsTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول إعدادات الشركات...");

        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_COMPANY_SETTINGS CASCADE CONSTRAINTS");
            stmt.executeUpdate("DROP SEQUENCE ERP_COMPANY_SETTINGS_SEQ");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        String createSettingsSQL =
                """
                        CREATE TABLE ERP_COMPANY_SETTINGS (
                            SETTING_ID NUMBER(10) NOT NULL,
                            COMPANY_ID NUMBER(10) NOT NULL,
                            SETTING_CATEGORY VARCHAR2(50) NOT NULL,
                            SETTING_NAME VARCHAR2(100) NOT NULL,
                            SETTING_VALUE NCLOB,
                            SETTING_TYPE VARCHAR2(20) DEFAULT 'STRING',
                            IS_ACTIVE CHAR(1) DEFAULT 'Y',
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50) DEFAULT USER,

                            CONSTRAINT PK_ERP_COMPANY_SETTINGS PRIMARY KEY (SETTING_ID),
                            CONSTRAINT FK_SETTING_COMPANY FOREIGN KEY (COMPANY_ID)
                                REFERENCES ERP_ADVANCED_COMPANIES(COMPANY_ID) ON DELETE CASCADE,
                            CONSTRAINT UK_COMPANY_SETTING UNIQUE (COMPANY_ID, SETTING_CATEGORY, SETTING_NAME)
                        )
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createSettingsSQL);
            stmt.executeUpdate(
                    "CREATE SEQUENCE ERP_COMPANY_SETTINGS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE");
            System.out.println("✅ تم إنشاء جدول ERP_COMPANY_SETTINGS");
        }
    }

    private static void insertSampleData(Connection connection) throws SQLException {
        System.out.println("📊 إدراج بيانات تجريبية...");

        // إدراج شركة تجريبية
        String insertCompanySQL = """
                INSERT INTO ERP_ADVANCED_COMPANIES (
                    COMPANY_ID, COMPANY_CODE, COMPANY_NAME_AR, COMPANY_NAME_EN,
                    COMPANY_SHORT_NAME_AR, COMPANY_SHORT_NAME_EN,
                    PRIMARY_EMAIL, PRIMARY_PHONE, WEBSITE_URL,
                    CITY_AR, CITY_EN, COUNTRY_AR, COUNTRY_EN,
                    COMMERCIAL_REGISTER, TAX_NUMBER, VAT_NUMBER,
                    COMPANY_STATUS, COMPANY_TYPE, DEFAULT_CURRENCY,
                    IS_ACTIVE, CREATED_BY
                ) VALUES (
                    ERP_ADVANCED_COMPANIES_SEQ.NEXTVAL, 'MAIN001',
                    'شركة الشحن المتقدمة للتجارة', 'Advanced Shipping Trading Company',
                    'الشحن المتقدمة', 'Advanced Shipping',
                    '<EMAIL>', '+966-11-1234567', 'www.advancedshipping.com',
                    'الرياض', 'Riyadh', 'المملكة العربية السعودية', 'Saudi Arabia',
                    '1010123456', '300123456789', '310123456789012',
                    'ACTIVE', 'HEAD_OFFICE', 'SAR',
                    'Y', 'SYSTEM'
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(insertCompanySQL);
            System.out.println("✅ تم إدراج شركة تجريبية");
        }

        // الحصول على معرف الشركة المدرجة
        int companyId = 0;
        String getCompanyIdSQL = "SELECT ERP_ADVANCED_COMPANIES_SEQ.CURRVAL FROM DUAL";
        try (Statement stmt = connection.createStatement();
                ResultSet rs = stmt.executeQuery(getCompanyIdSQL)) {
            if (rs.next()) {
                companyId = rs.getInt(1);
                System.out.println("📋 معرف الشركة المدرجة: " + companyId);
            }
        }

        if (companyId > 0) {
            // إدراج فرع تجريبي
            String insertBranchSQL = """
                    INSERT INTO ERP_COMPANY_BRANCHES (
                        BRANCH_ID, COMPANY_ID, BRANCH_CODE, BRANCH_NAME_AR, BRANCH_NAME_EN,
                        ADDRESS_AR, CITY_AR, PHONE, EMAIL, IS_MAIN_BRANCH, CREATED_BY
                    ) VALUES (
                        ERP_COMPANY_BRANCHES_SEQ.NEXTVAL, ?, 'BR001',
                        'الفرع الرئيسي - الرياض', 'Main Branch - Riyadh',
                        'شارع الملك فهد، الرياض', 'الرياض',
                        '+966-11-1234567', '<EMAIL>', 'Y', 'SYSTEM'
                    )
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(insertBranchSQL)) {
                stmt.setInt(1, companyId);
                stmt.executeUpdate();
                System.out.println("✅ تم إدراج فرع تجريبي");
            }

            // إدراج جهة اتصال تجريبية
            String insertContactSQL =
                    """
                            INSERT INTO ERP_COMPANY_CONTACTS (
                                CONTACT_ID, COMPANY_ID, CONTACT_TYPE, FULL_NAME_AR, FULL_NAME_EN,
                                JOB_TITLE_AR, PRIMARY_PHONE, EMAIL, IS_PRIMARY_CONTACT, CREATED_BY
                            ) VALUES (
                                ERP_COMPANY_CONTACTS_SEQ.NEXTVAL, ?, 'CEO',
                                'أحمد محمد العلي', 'Ahmed Mohammed Al-Ali',
                                'المدير التنفيذي', '+966-50-1234567', '<EMAIL>', 'Y', 'SYSTEM'
                            )
                            """;

            try (PreparedStatement stmt = connection.prepareStatement(insertContactSQL)) {
                stmt.setInt(1, companyId);
                stmt.executeUpdate();
                System.out.println("✅ تم إدراج جهة اتصال تجريبية");
            }
        }
    }
}
