package com.shipment.erp.service;

import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.shipment.erp.model.Branch;
import com.shipment.erp.repository.BranchRepository;
import jakarta.validation.Validator;

/**
 * تنفيذ خدمة إدارة الفروع Branch Management Service Implementation
 */
@Service
@Transactional
public class BranchServiceImpl extends BaseServiceImpl<Branch> implements BranchService {

    private final BranchRepository branchRepository;

    @Autowired
    public BranchServiceImpl(BranchRepository branchRepository, Validator validator) {
        super(branchRepository, validator);
        this.branchRepository = branchRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Branch> findByCompanyId(Long companyId) {
        return branchRepository.findByCompanyId(companyId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Branch> findActiveByCompanyId(Long companyId) {
        return branchRepository.findByCompanyIdAndIsActiveTrue(companyId);
    }

    @Override
    @Transactional(readOnly = true)
    public Branch findByCode(String code) {
        Optional<Branch> branch = branchRepository.findByCode(code);
        return branch.orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Branch> findByNameContaining(String name) {
        return branchRepository.findByNameContainingIgnoreCase(name);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByCode(String code) {
        return branchRepository.existsByCode(code);
    }

    @Override
    public void toggleActive(Long branchId) {
        Optional<Branch> branchOpt = branchRepository.findById(branchId);
        if (branchOpt.isPresent()) {
            Branch branch = branchOpt.get();
            branch.setIsActive(!branch.getIsActive());
            branchRepository.save(branch);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public long getActiveBranchesCount(Long companyId) {
        return branchRepository.countByCompanyIdAndIsActiveTrue(companyId);
    }

    @Override
    public Branch save(Branch branch) {
        // التحقق من عدم تكرار الكود
        if (branch.getId() == null && existsByCode(branch.getCode())) {
            throw new RuntimeException("كود الفرع موجود مسبقاً: " + branch.getCode());
        }

        // التحقق من عدم تكرار الكود عند التحديث
        if (branch.getId() != null) {
            Branch existingBranch = findByCode(branch.getCode());
            if (existingBranch != null && !existingBranch.getId().equals(branch.getId())) {
                throw new RuntimeException("كود الفرع موجود مسبقاً: " + branch.getCode());
            }
        }

        return super.save(branch);
    }
}
