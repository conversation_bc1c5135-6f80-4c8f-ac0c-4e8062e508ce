import java.sql.*;

/**
 * إنشاء جدول شجرة النظام
 */
public class CreateSystemTreeTable {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  CREATING SYSTEM_TREE TABLE");
        System.out.println("  إنشاء جدول شجرة النظام");
        System.out.println("========================================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";
            
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة بيانات ship_erp بنجاح");
            
            // إنشاء جدول شجرة النظام
            createSystemTreeTable(connection);
            
            // إنشاء المتسلسل
            createSequence(connection);
            
            // إدراج البيانات الأساسية
            insertBasicData(connection);
            
            connection.close();
            System.out.println("\n🎉 تم إنشاء جدول شجرة النظام بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء جدول شجرة النظام: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void createSystemTreeTable(Connection connection) {
        try {
            System.out.println("\n📋 إنشاء جدول SYSTEM_TREE...");
            
            String sql = """
                CREATE TABLE SYSTEM_TREE (
                    NODE_ID NUMBER PRIMARY KEY,
                    NODE_NAME_AR NVARCHAR2(100) NOT NULL,
                    NODE_NAME_EN VARCHAR2(100) NOT NULL,
                    PARENT_ID NUMBER,
                    NODE_LEVEL NUMBER DEFAULT 0,
                    NODE_ORDER NUMBER DEFAULT 0,
                    WINDOW_CLASS VARCHAR2(200),
                    DESCRIPTION NVARCHAR2(500),
                    ICON_PATH VARCHAR2(200),
                    IS_ACTIVE NUMBER(1) DEFAULT 1,
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول SYSTEM_TREE");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) {
                System.out.println("⚠️ جدول SYSTEM_TREE موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء جدول SYSTEM_TREE: " + e.getMessage());
            }
        }
    }
    
    private static void createSequence(Connection connection) {
        try {
            System.out.println("\n🔢 إنشاء متسلسل SYSTEM_TREE_SEQ...");
            
            String sql = "CREATE SEQUENCE SYSTEM_TREE_SEQ START WITH 1 INCREMENT BY 1";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء متسلسل SYSTEM_TREE_SEQ");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) {
                System.out.println("⚠️ متسلسل SYSTEM_TREE_SEQ موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء متسلسل SYSTEM_TREE_SEQ: " + e.getMessage());
            }
        }
    }
    
    private static void insertBasicData(Connection connection) {
        try {
            System.out.println("\n📝 إدراج البيانات الأساسية...");
            
            // إدراج العقدة الجذر
            insertRootNode(connection);
            
            // إدراج نظام البريد الإلكتروني
            insertEmailSystem(connection);
            
            // إدراج نافذة إدارة حسابات البريد
            insertEmailAccountsWindow(connection);
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إدراج البيانات الأساسية: " + e.getMessage());
        }
    }
    
    private static void insertRootNode(Connection connection) throws SQLException {
        String checkSql = "SELECT COUNT(*) FROM SYSTEM_TREE WHERE NODE_NAME_EN = 'Ship ERP System'";
        try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next() && rs.getInt(1) > 0) {
                System.out.println("⚠️ العقدة الجذر موجودة مسبقاً");
                return;
            }
        }
        
        String sql = """
            INSERT INTO SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, NODE_ORDER, DESCRIPTION, IS_ACTIVE)
            VALUES (SYSTEM_TREE_SEQ.NEXTVAL, 'نظام إدارة الشحنات', 'Ship ERP System', NULL, 0, 1, 'النظام الرئيسي لإدارة الشحنات', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.executeUpdate();
            System.out.println("✅ تم إدراج العقدة الجذر: نظام إدارة الشحنات");
        }
    }
    
    private static void insertEmailSystem(Connection connection) throws SQLException {
        String checkSql = "SELECT COUNT(*) FROM SYSTEM_TREE WHERE NODE_NAME_EN = 'Email Management System'";
        try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next() && rs.getInt(1) > 0) {
                System.out.println("⚠️ عقدة نظام البريد الإلكتروني موجودة مسبقاً");
                return;
            }
        }
        
        // الحصول على معرف العقدة الجذر
        String getRootIdSql = "SELECT NODE_ID FROM SYSTEM_TREE WHERE NODE_NAME_EN = 'Ship ERP System'";
        int rootId = -1;
        try (PreparedStatement stmt = connection.prepareStatement(getRootIdSql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                rootId = rs.getInt("NODE_ID");
            }
        }
        
        if (rootId == -1) {
            System.err.println("❌ لم يتم العثور على العقدة الجذر");
            return;
        }
        
        String sql = """
            INSERT INTO SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, NODE_ORDER, DESCRIPTION, IS_ACTIVE)
            VALUES (SYSTEM_TREE_SEQ.NEXTVAL, 'نظام إدارة البريد الإلكتروني', 'Email Management System', ?, 1, 5, 'نظام شامل لإدارة البريد الإلكتروني والرسائل', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, rootId);
            stmt.executeUpdate();
            System.out.println("✅ تم إدراج عقدة نظام البريد الإلكتروني");
        }
    }
    
    private static void insertEmailAccountsWindow(Connection connection) throws SQLException {
        String checkSql = "SELECT COUNT(*) FROM SYSTEM_TREE WHERE WINDOW_CLASS = 'CompleteEmailAccountsWindow'";
        try (PreparedStatement checkStmt = connection.prepareStatement(checkSql);
             ResultSet rs = checkStmt.executeQuery()) {
            
            if (rs.next() && rs.getInt(1) > 0) {
                System.out.println("⚠️ نافذة إدارة حسابات البريد موجودة مسبقاً");
                return;
            }
        }
        
        // الحصول على معرف عقدة نظام البريد الإلكتروني
        String getEmailSystemIdSql = "SELECT NODE_ID FROM SYSTEM_TREE WHERE NODE_NAME_EN = 'Email Management System'";
        int emailSystemId = -1;
        try (PreparedStatement stmt = connection.prepareStatement(getEmailSystemIdSql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                emailSystemId = rs.getInt("NODE_ID");
            }
        }
        
        if (emailSystemId == -1) {
            System.err.println("❌ لم يتم العثور على عقدة نظام البريد الإلكتروني");
            return;
        }
        
        String sql = """
            INSERT INTO SYSTEM_TREE (NODE_ID, NODE_NAME_AR, NODE_NAME_EN, PARENT_ID, NODE_LEVEL, NODE_ORDER, WINDOW_CLASS, DESCRIPTION, IS_ACTIVE)
            VALUES (SYSTEM_TREE_SEQ.NEXTVAL, 'إدارة حسابات البريد', 'Email Accounts Management', ?, 2, 1, 'CompleteEmailAccountsWindow', 'إدارة شاملة لحسابات البريد الإلكتروني مع دعم IMAP وPOP3 وSMTP', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, emailSystemId);
            stmt.executeUpdate();
            System.out.println("✅ تم إدراج نافذة إدارة حسابات البريد");
        }
    }
}
