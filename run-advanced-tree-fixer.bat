@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 🔧 أداة الإصلاح المتقدمة لشجرة الأنظمة
echo    Advanced System Tree Fixer Tool
echo ================================================
echo.

cd /d "d:\java\java"

echo 🔍 فحص المتطلبات...
echo.

echo [1] فحص المكتبات المطلوبة...
if not exist "lib\ojdbc11.jar" (
    echo ❌ ojdbc11.jar مفقود!
    pause
    exit /b 1
)

if not exist "lib\flatlaf-3.2.5.jar" (
    echo ❌ flatlaf-3.2.5.jar مفقود!
    pause
    exit /b 1
)

echo ✅ جميع المكتبات متوفرة
echo.

echo [2] فحص الملفات المطلوبة...
if not exist "src\main\java\AdvancedSystemTreeFixer.java" (
    echo ❌ AdvancedSystemTreeFixer.java مفقود!
    pause
    exit /b 1
)

if not exist "src\main\java\TNSConnectionManager.java" (
    echo ❌ TNSConnectionManager.java مفقود!
    pause
    exit /b 1
)

echo ✅ جميع الملفات متوفرة
echo.

echo [3] تجميع أداة الإصلاح المتقدمة...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TNSConnectionManager.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع TNSConnectionManager
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\AdvancedSystemTreeFixer.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع AdvancedSystemTreeFixer
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح
echo.

echo [4] معلومات الأداة:
echo • قاعدة البيانات: Oracle ORCL (localhost:1521)
echo • المستخدم: SHIP_ERP
echo • الجدول: ERP_SYSTEM_TREE
echo • الميزات: إصلاح شامل مع واجهة رسومية متقدمة
echo.

echo [5] الإصلاحات المتاحة:
echo • إصلاح الأسماء المكررة (12 مشكلة)
echo • إضافة الأوصاف المفقودة (13 عقدة)
echo • إضافة الأيقونات المفقودة (90 عقدة)
echo • تحسين هيكل الشجرة والترتيب
echo • إنشاء نسخة احتياطية تلقائية
echo.

echo [6] تشغيل أداة الإصلاح المتقدمة...
echo.

java -Doracle.net.tns_admin=d:\java\java\network\admin -Doracle.jdbc.defaultNChar=true -Dfile.encoding=UTF-8 -cp "lib\*;." AdvancedSystemTreeFixer

echo.
echo 🔧 تم إغلاق أداة الإصلاح المتقدمة
echo    Advanced Tree Fixer Closed
echo.

echo ================================================
echo 📋 ملخص الميزات المتقدمة:
echo ================================================
echo.
echo 🔧 الإصلاح التلقائي:
echo • إصلاح الأسماء المكررة بذكاء
echo • إضافة أوصاف مناسبة تلقائياً
echo • إضافة أيقونات حسب نوع العقدة
echo • تحسين ترتيب وهيكل الشجرة
echo.
echo 🛡️ الحماية والأمان:
echo • إنشاء نسخة احتياطية قبل الإصلاح
echo • معاينة التغييرات قبل التطبيق
echo • إمكانية التراجع عن التغييرات
echo • سجل مفصل لجميع العمليات
echo.
echo 🎨 الواجهة المتقدمة:
echo • واجهة رسومية حديثة ومتجاوبة
echo • دعم كامل للغة العربية (RTL)
echo • شريط تقدم وحالة العمليات
echo • تبويبات منظمة للوظائف
echo.
echo 📊 التقارير والتحليل:
echo • تقرير مفصل للمشاكل المكتشفة
echo • سجل زمني لجميع العمليات
echo • إحصائيات شاملة للإصلاحات
echo • إمكانية تصدير التقارير
echo.

pause
