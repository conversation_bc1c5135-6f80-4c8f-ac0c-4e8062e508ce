@echo off
echo ========================================
echo 💰 تشغيل نافذة إعدادات العملة المطورة
echo Running Full Currency Settings Window
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/4] تجميع الملفات...
echo Compiling Files...
echo ========================================

echo تجميع FullCurrencySettingsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\FullCurrencySettingsWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع FullCurrencySettingsWindow بنجاح
) else (
    echo ❌ فشل في تجميع FullCurrencySettingsWindow
    pause
    exit /b 1
)

echo تجميع UpdateCurrencyWindowStatus...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\UpdateCurrencyWindowStatus.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع UpdateCurrencyWindowStatus بنجاح
) else (
    echo ❌ فشل في تجميع UpdateCurrencyWindowStatus
    pause
    exit /b 1
)

echo تجميع TreeMenuPanel المحدث...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع TreeMenuPanel المحدث بنجاح
) else (
    echo ❌ فشل في تجميع TreeMenuPanel المحدث
    pause
    exit /b 1
)

echo.
echo [2/4] اختيار العملية...
echo Choose Operation...
echo ========================================

echo.
echo اختر العملية التي تريد تنفيذها:
echo 1. تشغيل نافذة إعدادات العملة المطورة مباشرة
echo 2. تحديث قاعدة البيانات للنافذة المطورة
echo 3. تشغيل الشجرة الأصلية مع النافذة المطورة
echo 4. تشغيل جميع العمليات
echo.

set /p choice="أدخل اختيارك (1-4): "

echo.
echo [3/4] تنفيذ العملية المختارة...
echo Executing Selected Operation...
echo ========================================

if "%choice%"=="1" (
    echo تشغيل نافذة إعدادات العملة المطورة...
    start "Full Currency Settings Window" java -cp "lib\*;." FullCurrencySettingsWindow
    echo ✅ تم تشغيل نافذة إعدادات العملة المطورة
) else if "%choice%"=="2" (
    echo تحديث قاعدة البيانات للنافذة المطورة...
    java -cp "lib\*;." UpdateCurrencyWindowStatus
    if %errorlevel% equ 0 (
        echo ✅ تم تحديث قاعدة البيانات بنجاح
    ) else (
        echo ⚠️ فشل في الاتصال بقاعدة البيانات
        echo يمكنك تشغيل النافذة مباشرة
    )
) else if "%choice%"=="3" (
    echo تشغيل الشجرة الأصلية مع النافذة المطورة...
    start "Enhanced Ship ERP - Full Currency" java -cp "lib\*;." EnhancedShipERP
    echo ✅ تم تشغيل الشجرة الأصلية المحدثة
) else if "%choice%"=="4" (
    echo تشغيل جميع العمليات...
    
    echo 1. تشغيل نافذة إعدادات العملة المطورة...
    start "Full Currency Settings Window" java -cp "lib\*;." FullCurrencySettingsWindow
    timeout /t 2 /nobreak >nul
    
    echo 2. تشغيل الشجرة الأصلية المحدثة...
    start "Enhanced Ship ERP - Full Currency" java -cp "lib\*;." EnhancedShipERP
    timeout /t 2 /nobreak >nul
    
    echo 3. تحديث قاعدة البيانات...
    java -cp "lib\*;." UpdateCurrencyWindowStatus
    
    echo ✅ تم تشغيل جميع العمليات
) else (
    echo ❌ اختيار غير صحيح، سيتم تشغيل النافذة المطورة افتراضياً
    start "Full Currency Settings Window" java -cp "lib\*;." FullCurrencySettingsWindow
)

echo.
echo [4/4] معلومات النافذة المطورة...
echo Full Currency Window Information...
echo ========================================

echo.
echo 💰 نافذة إعدادات العملة المطورة:
echo ===============================
echo • الاسم: إعدادات العملة (مطورة بالكامل)
echo • الكلاس: FullCurrencySettingsWindow
echo • قاعدة البيانات: ERP_CURRENCIES
echo • الحالة: مطورة ومكتملة

echo.
echo 🎯 الميزات المطورة:
echo ==================
echo ✅ إدارة العملات الكاملة:
echo    • إضافة عملات جديدة
echo    • تعديل العملات الموجودة
echo    • حذف العملات غير المستخدمة
echo    • تفعيل/إلغاء تفعيل العملات

echo.
echo ✅ أسعار الصرف:
echo    • تحديد أسعار الصرف بدقة
echo    • دعم الأرقام العشرية
echo    • تحديث فوري للأسعار

echo.
echo ✅ واجهة المستخدم:
echo    • جدول تفاعلي للعملات
echo    • نموذج إدخال متقدم
echo    • أزرار عمليات واضحة
echo    • دعم كامل للعربية

echo.
echo ✅ قاعدة البيانات:
echo    • جدول ERP_CURRENCIES مخصص
echo    • بيانات افتراضية (USD, EUR, SAR, AED, EGP, JOD)
echo    • تتبع تواريخ الإنشاء والتحديث
echo    • معرفات فريدة تلقائية

echo.
echo 🌳 الموقع في الشجرة:
echo ====================
echo الإعدادات والإدارة
echo └── الإعدادات العامة
echo    ├── المتغيرات العامة
echo    ├── إعدادات العملة ⭐ مطورة بالكامل
echo    ├── إعدادات النظام العامة
echo    └── إعدادات الأمان

echo.
echo 🚀 للاستخدام:
echo =============
echo • تشغيل مباشر: java -cp "lib\*;." FullCurrencySettingsWindow
echo • عبر الشجرة: الإعدادات والإدارة ^> الإعدادات العامة ^> إعدادات العملة
echo • تحديث قاعدة البيانات: java -cp "lib\*;." UpdateCurrencyWindowStatus

echo.
echo 💾 قاعدة البيانات:
echo ==================
echo • الجدول: ERP_CURRENCIES
echo • الحقول: CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME, CURRENCY_SYMBOL, EXCHANGE_RATE, IS_ACTIVE
echo • البيانات الافتراضية: 6 عملات رئيسية
echo • العمليات: CRUD كاملة (إنشاء، قراءة، تحديث، حذف)

echo.
echo 🔧 التطوير:
echo ===========
echo • تم تطوير النافذة بالكامل
echo • تم إزالة حالة "قيد التطوير"
echo • تم تحديث الشجرة الأصلية
echo • تم إنشاء قاعدة بيانات مخصصة

echo.
echo 📊 الإحصائيات:
echo ==============
echo • عدد الملفات المطورة: 3
echo • عدد الميزات المطورة: 15+
echo • عدد العملات الافتراضية: 6
echo • مستوى التطوير: 100%

echo.
echo ========================================
echo ✅ النافذة مطورة بالكامل وجاهزة للاستخدام!
echo Full Currency Window is Complete and Ready!
echo ========================================

echo.
pause
