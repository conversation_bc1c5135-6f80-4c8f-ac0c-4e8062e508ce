import java.awt.Window;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.swing.LookAndFeel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * أداة تشخيص شاملة لمشاكل المظهر في التطبيق
 * Comprehensive Theme Diagnostic Tool
 */
public class ThemeDiagnosticTool {
    
    private Map<String, String> themeIssues = new HashMap<>();
    private List<String> recommendations = new ArrayList<>();
    
    public static void main(String[] args) {
        System.out.println("🔍 أداة تشخيص شاملة لمشاكل المظهر");
        System.out.println("Comprehensive Theme Diagnostic Tool");
        System.out.println("==========================================");
        
        ThemeDiagnosticTool diagnostic = new ThemeDiagnosticTool();
        diagnostic.runComprehensiveDiagnostic();
    }
    
    public void runComprehensiveDiagnostic() {
        System.out.println("🚀 بدء التشخيص الشامل...");
        System.out.println();
        
        // 1. فحص المكتبات المتاحة
        checkAvailableLibraries();
        
        // 2. فحص إعدادات المظهر الحالية
        checkCurrentThemeSettings();
        
        // 3. فحص تضارب المظاهر
        checkThemeConflicts();
        
        // 4. فحص النوافذ المفتوحة
        checkOpenWindows();
        
        // 5. فحص ملفات الإعدادات
        checkSettingsFiles();
        
        // 6. فحص مشاكل الخطوط
        checkFontIssues();
        
        // 7. عرض التقرير النهائي
        showFinalReport();
    }
    
    private void checkAvailableLibraries() {
        System.out.println("📚 فحص المكتبات المتاحة:");
        System.out.println("========================");
        
        // فحص FlatLaf
        checkLibrary("com.formdev.flatlaf.FlatLightLaf", "FlatLaf Light", "lib/flatlaf-*.jar");
        checkLibrary("com.formdev.flatlaf.FlatDarkLaf", "FlatLaf Dark", "lib/flatlaf-*.jar");
        checkLibrary("com.formdev.flatlaf.FlatIntelliJLaf", "FlatLaf IntelliJ", "lib/flatlaf-intellij-themes-*.jar");
        checkLibrary("com.formdev.flatlaf.FlatDarculaLaf", "FlatLaf Darcula", "lib/flatlaf-intellij-themes-*.jar");
        
        // فحص JTattoo
        checkLibrary("com.jtattoo.plaf.acryl.AcrylLookAndFeel", "JTattoo Acryl", "lib/jtattoo-*.jar");
        checkLibrary("com.jtattoo.plaf.graphite.GraphiteLookAndFeel", "JTattoo Graphite", "lib/jtattoo-*.jar");
        
        // فحص System Themes
        checkSystemThemes();
        
        System.out.println();
    }
    
    private void checkLibrary(String className, String displayName, String jarPattern) {
        try {
            Class.forName(className);
            System.out.println("✅ " + displayName + " - متاح");
        } catch (ClassNotFoundException e) {
            System.out.println("❌ " + displayName + " - غير متاح");
            
            // فحص وجود ملف JAR
            File libDir = new File("lib");
            if (libDir.exists()) {
                File[] jarFiles = libDir.listFiles((dir, name) -> 
                    name.toLowerCase().matches(jarPattern.replace("*", ".*").toLowerCase()));
                
                if (jarFiles != null && jarFiles.length > 0) {
                    themeIssues.put(displayName, "ملف JAR موجود ولكن الكلاس غير متاح - مشكلة في classpath");
                } else {
                    themeIssues.put(displayName, "ملف JAR مفقود");
                }
            }
        }
    }
    
    private void checkSystemThemes() {
        System.out.println("\n🖥️ فحص مظاهر النظام:");
        
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            try {
                UIManager.setLookAndFeel(info.getClassName());
                System.out.println("✅ " + info.getName() + " - متاح");
            } catch (Exception e) {
                System.out.println("❌ " + info.getName() + " - غير متاح: " + e.getMessage());
                themeIssues.put(info.getName(), "مظهر النظام غير متاح: " + e.getMessage());
            }
        }
    }
    
    private void checkCurrentThemeSettings() {
        System.out.println("⚙️ فحص إعدادات المظهر الحالية:");
        System.out.println("===============================");
        
        LookAndFeel currentLaf = UIManager.getLookAndFeel();
        if (currentLaf != null) {
            System.out.println("🎨 المظهر الحالي: " + currentLaf.getName());
            System.out.println("📦 الكلاس: " + currentLaf.getClass().getName());
            System.out.println("📝 الوصف: " + currentLaf.getDescription());
        } else {
            System.out.println("❌ لا يوجد مظهر محدد!");
            themeIssues.put("Current Theme", "لا يوجد مظهر محدد");
        }
        
        // فحص إعدادات UIManager
        checkUIManagerSettings();
        
        System.out.println();
    }
    
    private void checkUIManagerSettings() {
        System.out.println("\n🔧 إعدادات UIManager:");
        
        String[] importantKeys = {
            "Panel.background", "Button.background", "TextField.background",
            "Label.foreground", "Button.foreground", "TextField.foreground",
            "defaultFont", "Button.arc", "Component.arc"
        };
        
        for (String key : importantKeys) {
            Object value = UIManager.get(key);
            if (value != null) {
                System.out.println("  " + key + ": " + value);
            } else {
                System.out.println("  " + key + ": غير محدد");
            }
        }
    }
    
    private void checkThemeConflicts() {
        System.out.println("⚠️ فحص تضارب المظاهر:");
        System.out.println("====================");
        
        // فحص تعدد مدراء المظاهر
        checkMultipleThemeManagers();
        
        // فحص تطبيق مظاهر متعددة
        checkMultipleThemeApplications();
        
        System.out.println();
    }
    
    private void checkMultipleThemeManagers() {
        String[] themeManagerClasses = {
            "SettingsManager", "ThemeApplier", "WorkingThemeManager", 
            "AdvancedThemeManager", "CompleteThemeManager"
        };
        
        int foundManagers = 0;
        for (String managerClass : themeManagerClasses) {
            try {
                Class.forName(managerClass);
                System.out.println("🔍 تم العثور على: " + managerClass);
                foundManagers++;
            } catch (ClassNotFoundException e) {
                // لا يوجد
            }
        }
        
        if (foundManagers > 1) {
            themeIssues.put("Multiple Theme Managers", 
                "تم العثور على " + foundManagers + " مدير مظاهر - قد يسبب تضارب");
            recommendations.add("استخدم مدير مظاهر واحد فقط");
        }
    }
    
    private void checkMultipleThemeApplications() {
        // فحص الملفات التي تطبق مظاهر مختلفة
        String[] filesWithThemes = {
            "EnhancedShipERP.java", "SimpleShipERP.java", "AdvancedSystemTreeFixer.java",
            "SystemTreeWithIcons.java", "ItemGroupsDisplayFixer.java"
        };
        
        System.out.println("📄 ملفات تطبق مظاهر:");
        for (String fileName : filesWithThemes) {
            File file = new File("src/main/java/" + fileName);
            if (file.exists()) {
                System.out.println("  ✅ " + fileName);
            }
        }
        
        themeIssues.put("Multiple Theme Applications", 
            "عدة ملفات تطبق مظاهر مختلفة - قد يسبب عدم توحيد");
        recommendations.add("وحد تطبيق المظهر في مكان واحد");
    }
    
    private void checkOpenWindows() {
        System.out.println("🪟 فحص النوافذ المفتوحة:");
        System.out.println("======================");
        
        Window[] windows = Window.getWindows();
        System.out.println("عدد النوافذ المفتوحة: " + windows.length);
        
        for (int i = 0; i < windows.length; i++) {
            Window window = windows[i];
            if (window.isDisplayable()) {
                System.out.println("  " + (i+1) + ". " + window.getClass().getSimpleName() + 
                    " - " + (window.isVisible() ? "مرئية" : "مخفية"));
            }
        }
        
        if (windows.length > 5) {
            themeIssues.put("Too Many Windows", "عدد كبير من النوافذ المفتوحة قد يؤثر على الأداء");
        }
        
        System.out.println();
    }
    
    private void checkSettingsFiles() {
        System.out.println("📁 فحص ملفات الإعدادات:");
        System.out.println("========================");
        
        String[] settingsFiles = {
            "settings.properties", "theme.properties", "app.properties"
        };
        
        for (String fileName : settingsFiles) {
            File file = new File(fileName);
            if (file.exists()) {
                System.out.println("✅ " + fileName + " - موجود (" + file.length() + " bytes)");
            } else {
                System.out.println("❌ " + fileName + " - مفقود");
            }
        }
        
        System.out.println();
    }
    
    private void checkFontIssues() {
        System.out.println("🔤 فحص مشاكل الخطوط:");
        System.out.println("====================");
        
        String[] arabicFonts = {"Tahoma", "Arial Unicode MS", "Segoe UI", "DejaVu Sans"};
        
        for (String fontName : arabicFonts) {
            try {
                java.awt.Font font = new java.awt.Font(fontName, java.awt.Font.PLAIN, 12);
                if (font.getFamily().equals(fontName)) {
                    System.out.println("✅ " + fontName + " - متاح");
                } else {
                    System.out.println("⚠️ " + fontName + " - غير متاح، تم استبداله بـ " + font.getFamily());
                }
            } catch (Exception e) {
                System.out.println("❌ " + fontName + " - خطأ: " + e.getMessage());
            }
        }
        
        System.out.println();
    }
    
    private void showFinalReport() {
        System.out.println("📊 التقرير النهائي:");
        System.out.println("==================");
        System.out.println();
        
        if (themeIssues.isEmpty()) {
            System.out.println("✅ لم يتم العثور على مشاكل في المظهر!");
        } else {
            System.out.println("⚠️ المشاكل المكتشفة:");
            for (Map.Entry<String, String> issue : themeIssues.entrySet()) {
                System.out.println("  • " + issue.getKey() + ": " + issue.getValue());
            }
        }
        
        System.out.println();
        
        if (!recommendations.isEmpty()) {
            System.out.println("💡 التوصيات:");
            for (String recommendation : recommendations) {
                System.out.println("  • " + recommendation);
            }
        }
        
        System.out.println();
        System.out.println("🎯 التوصيات العامة:");
        System.out.println("• استخدم FlatLaf كمظهر أساسي للتطبيق");
        System.out.println("• وحد تطبيق المظهر في مكان واحد");
        System.out.println("• تأكد من تحديث جميع النوافذ عند تغيير المظهر");
        System.out.println("• استخدم مدير مظاهر واحد فقط");
        System.out.println("• تأكد من وجود جميع مكتبات المظاهر المطلوبة");
        
        System.out.println();
        System.out.println("✅ انتهى التشخيص الشامل");
    }
}
