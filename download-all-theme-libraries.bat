@echo off
echo ========================================
echo    DOWNLOAD ALL THEME LIBRARIES
echo    تحميل جميع مكتبات المظاهر
echo ========================================

cd /d "e:\ship_erp\java\lib"

echo [INFO] Downloading comprehensive theme libraries...
echo.

REM FlatLaf - Modern Look and Feel
echo [1] Downloading FlatLaf Core...
curl -L -o flatlaf-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar"

echo [2] Downloading FlatLaf Extras...
curl -L -o flatlaf-extras-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar"

echo [3] Downloading FlatLaf Fonts...
curl -L -o flatlaf-fonts-roboto-2.137.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-fonts-roboto/2.137/flatlaf-fonts-roboto-2.137.jar"

echo [4] Downloading FlatLaf IntelliJ Themes...
curl -L -o flatlaf-intellij-themes-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-intellij-themes/3.2.5/flatlaf-intellij-themes-3.2.5.jar"

REM Substance Look and Feel
echo [5] Downloading Substance Look and Feel...
curl -L -o substance-8.0.02.jar "https://repo1.maven.org/maven2/org/pushingpixels/substance/8.0.02/substance-8.0.02.jar"

echo [6] Downloading Trident Animation Library...
curl -L -o trident-1.5.00.jar "https://repo1.maven.org/maven2/org/pushingpixels/trident/1.5.00/trident-1.5.00.jar"

REM WebLaF - Web Look and Feel
echo [7] Downloading WebLaF...
curl -L -o weblaf-ui-2.2.1.jar "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-ui/2.2.1/weblaf-ui-2.2.1.jar"

curl -L -o weblaf-core-2.2.1.jar "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-core/2.2.1/weblaf-core-2.2.1.jar"

REM JTattoo Look and Feel
echo [8] Downloading JTattoo...
curl -L -o jtattoo-1.6.13.jar "https://repo1.maven.org/maven2/com/jtattoo/JTattoo/1.6.13/JTattoo-1.6.13.jar"

REM Synthetica Look and Feel (Free version)
echo [9] Downloading Synthetica...
curl -L -o synthetica-2.30.0.jar "https://repo1.maven.org/maven2/de/javasoft/synthetica/2.30.0/synthetica-2.30.0.jar"

REM Layout Managers
echo [10] Downloading MigLayout...
curl -L -o miglayout-core-11.0.jar "https://repo1.maven.org/maven2/com/miglayout/miglayout-core/11.0/miglayout-core-11.0.jar"

curl -L -o miglayout-swing-11.0.jar "https://repo1.maven.org/maven2/com/miglayout/miglayout-swing/11.0/miglayout-swing-11.0.jar"

REM Color Chooser Libraries
echo [11] Downloading Color Chooser...
curl -L -o colorchooser-1.0.jar "https://repo1.maven.org/maven2/com/bric/colorchooser/1.0/colorchooser-1.0.jar"

REM JSON Processing
echo [12] Downloading JSON Processing...
curl -L -o json-20230227.jar "https://repo1.maven.org/maven2/org/json/json/20230227/json-20230227.jar"

REM Apache Commons for utilities
echo [13] Downloading Apache Commons...
curl -L -o commons-io-2.11.0.jar "https://repo1.maven.org/maven2/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar"

echo.
echo ========================================
echo    DOWNLOAD COMPLETED
echo    اكتمل التحميل
echo ========================================

echo.
echo Downloaded theme libraries:
dir /b *.jar | findstr -i "flat\|substance\|web\|tattoo\|synth\|mig\|color\|json\|commons"

echo.
echo All theme libraries ready!
echo جميع مكتبات المظاهر جاهزة!

pause
