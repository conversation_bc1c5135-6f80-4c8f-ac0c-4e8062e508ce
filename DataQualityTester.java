import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Date;
import java.util.Properties;

/**
 * أداة اختبار جودة البيانات الشاملة
 * Comprehensive Data Quality Testing Tool
 */
public class DataQualityTester {
    
    private Connection connection;
    private PrintWriter reportWriter;
    private int totalTests = 0;
    private int passedTests = 0;
    private int failedTests = 0;
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  DATA QUALITY TESTING TOOL");
        System.out.println("  أداة اختبار جودة البيانات");
        System.out.println("========================================");
        
        DataQualityTester tester = new DataQualityTester();
        
        // اختبار قاعدة بيانات ias20251
        tester.testDatabase("ias20251", "ys123");
        
        System.out.println("\n✅ تم الانتهاء من اختبار جودة البيانات");
    }
    
    /**
     * اختبار قاعدة بيانات محددة
     */
    public void testDatabase(String username, String password) {
        System.out.println("\n🔍 Testing database: " + username);
        System.out.println("🔍 اختبار قاعدة البيانات: " + username);
        
        try {
            // إنشاء ملف التقرير
            String reportFile = "DATA_QUALITY_REPORT_" + username + "_" + 
                              new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".md";
            reportWriter = new PrintWriter(new FileWriter(reportFile), true);
            
            // الاتصال بقاعدة البيانات
            if (connectToDatabase(username, password)) {
                
                writeReportHeader(username);
                
                // اختبار الجداول الأساسية
                testBasicTables();
                
                // اختبار سلامة البيانات
                testDataIntegrity();
                
                // اختبار الترميز العربي
                testArabicEncoding();
                
                // اختبار الأداء
                testPerformance();
                
                // إحصائيات البيانات
                generateDataStatistics();
                
                writeReportSummary();
                
                System.out.println("📄 Report saved: " + reportFile);
                
            } else {
                System.out.println("❌ Failed to connect to " + username);
            }
            
        } catch (Exception e) {
            System.out.println("❌ Error testing " + username + ": " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeConnections();
        }
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private boolean connectToDatabase(String username, String password) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            String url = "*************************************";
            connection = DriverManager.getConnection(url, props);
            
            return connection != null && !connection.isClosed();
            
        } catch (Exception e) {
            System.out.println("Connection error: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * كتابة رأس التقرير
     */
    private void writeReportHeader(String schema) {
        reportWriter.println("# تقرير اختبار جودة البيانات");
        reportWriter.println("## Data Quality Testing Report");
        reportWriter.println();
        reportWriter.println("**Schema:** " + schema);
        reportWriter.println("**Date:** " + new Date());
        reportWriter.println("**Generated by:** DataQualityTester");
        reportWriter.println();
        reportWriter.println("---");
        reportWriter.println();
    }
    
    /**
     * اختبار الجداول الأساسية
     */
    private void testBasicTables() {
        reportWriter.println("## 📋 اختبار الجداول الأساسية - Basic Tables Test");
        reportWriter.println();
        
        String[] requiredTables = {
            "IAS_ITM_MST", "IAS_ITM_DTL", "MEASUREMENT",
            "IAS_SUB_GRP_DTL", "IAS_ASSISTANT_GROUP", "IAS_DETAIL_GROUP"
        };
        
        reportWriter.println("| Table Name | Exists | Row Count | Status |");
        reportWriter.println("|------------|--------|-----------|--------|");
        
        for (String tableName : requiredTables) {
            testTable(tableName);
        }
        
        reportWriter.println();
    }
    
    /**
     * اختبار جدول محدد
     */
    private void testTable(String tableName) {
        totalTests++;
        
        try {
            // فحص وجود الجدول
            String checkQuery = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = ?";
            boolean tableExists = false;
            int rowCount = 0;
            
            try (PreparedStatement stmt = connection.prepareStatement(checkQuery)) {
                stmt.setString(1, tableName);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        tableExists = true;
                        
                        // عد الصفوف
                        String countQuery = "SELECT COUNT(*) FROM " + tableName;
                        try (PreparedStatement countStmt = connection.prepareStatement(countQuery);
                             ResultSet countRs = countStmt.executeQuery()) {
                            
                            if (countRs.next()) {
                                rowCount = countRs.getInt(1);
                            }
                        }
                    }
                }
            }
            
            String status = tableExists ? (rowCount > 0 ? "✅ PASS" : "⚠️ EMPTY") : "❌ MISSING";
            
            reportWriter.printf("| %s | %s | %d | %s |\n", 
                tableName, 
                tableExists ? "Yes" : "No",
                rowCount,
                status);
            
            if (tableExists && rowCount > 0) {
                passedTests++;
            } else {
                failedTests++;
            }
            
        } catch (SQLException e) {
            failedTests++;
            reportWriter.printf("| %s | ERROR | 0 | ❌ ERROR |\n", tableName);
        }
    }
    
    /**
     * اختبار سلامة البيانات
     */
    private void testDataIntegrity() {
        reportWriter.println("## 🔍 اختبار سلامة البيانات - Data Integrity Test");
        reportWriter.println();
        
        // اختبار البيانات المكررة
        testDuplicateData();
        
        // اختبار القيم الفارغة
        testNullValues();
        
        // اختبار صحة التواريخ
        testDateValidation();
        
        reportWriter.println();
    }
    
    /**
     * اختبار البيانات المكررة
     */
    private void testDuplicateData() {
        totalTests++;
        
        try {
            reportWriter.println("### 🔄 Duplicate Data Test");
            reportWriter.println();
            
            String query = "SELECT ITM_CD, COUNT(*) as duplicate_count " +
                          "FROM IAS_ITM_MST GROUP BY ITM_CD HAVING COUNT(*) > 1";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                boolean hasDuplicates = false;
                reportWriter.println("| Item Code | Duplicate Count |");
                reportWriter.println("|-----------|-----------------|");
                
                while (rs.next()) {
                    hasDuplicates = true;
                    reportWriter.printf("| %s | %d |\n", 
                        rs.getString("ITM_CD"), 
                        rs.getInt("duplicate_count"));
                }
                
                if (!hasDuplicates) {
                    reportWriter.println("| No duplicates found | ✅ |");
                    passedTests++;
                } else {
                    failedTests++;
                }
            }
            
        } catch (SQLException e) {
            failedTests++;
            reportWriter.println("❌ Error testing duplicates: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * اختبار القيم الفارغة
     */
    private void testNullValues() {
        totalTests++;
        
        try {
            reportWriter.println("### ⚠️ Null Values Test");
            reportWriter.println();
            
            String query = "SELECT " +
                          "COUNT(CASE WHEN ITM_CD IS NULL THEN 1 END) as null_codes, " +
                          "COUNT(CASE WHEN ITM_NM IS NULL THEN 1 END) as null_names, " +
                          "COUNT(CASE WHEN ITM_PRICE IS NULL THEN 1 END) as null_prices " +
                          "FROM IAS_ITM_MST";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    int nullCodes = rs.getInt("null_codes");
                    int nullNames = rs.getInt("null_names");
                    int nullPrices = rs.getInt("null_prices");
                    
                    reportWriter.println("| Field | Null Count | Status |");
                    reportWriter.println("|-------|------------|--------|");
                    reportWriter.printf("| ITM_CD | %d | %s |\n", nullCodes, nullCodes == 0 ? "✅" : "❌");
                    reportWriter.printf("| ITM_NM | %d | %s |\n", nullNames, nullNames == 0 ? "✅" : "❌");
                    reportWriter.printf("| ITM_PRICE | %d | %s |\n", nullPrices, nullPrices < 10 ? "✅" : "⚠️");
                    
                    if (nullCodes == 0 && nullNames == 0) {
                        passedTests++;
                    } else {
                        failedTests++;
                    }
                }
            }
            
        } catch (SQLException e) {
            failedTests++;
            reportWriter.println("❌ Error testing null values: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * اختبار صحة التواريخ
     */
    private void testDateValidation() {
        totalTests++;
        
        try {
            reportWriter.println("### 📅 Date Validation Test");
            reportWriter.println();
            
            String query = "SELECT COUNT(*) as invalid_dates FROM IAS_ITM_MST " +
                          "WHERE CREATED_DATE > SYSDATE OR CREATED_DATE < DATE '2000-01-01'";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    int invalidDates = rs.getInt("invalid_dates");
                    
                    reportWriter.println("| Test | Count | Status |");
                    reportWriter.println("|------|-------|--------|");
                    reportWriter.printf("| Invalid Dates | %d | %s |\n", 
                        invalidDates, invalidDates == 0 ? "✅ PASS" : "❌ FAIL");
                    
                    if (invalidDates == 0) {
                        passedTests++;
                    } else {
                        failedTests++;
                    }
                }
            }
            
        } catch (SQLException e) {
            failedTests++;
            reportWriter.println("❌ Error testing dates: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * اختبار الترميز العربي
     */
    private void testArabicEncoding() {
        totalTests++;
        
        reportWriter.println("## 🔤 اختبار الترميز العربي - Arabic Encoding Test");
        reportWriter.println();
        
        try {
            String query = "SELECT ITM_CD, ITM_NM, ITM_NM_AR FROM IAS_ITM_MST " +
                          "WHERE ITM_NM_AR IS NOT NULL AND ROWNUM <= 5";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                reportWriter.println("| Item Code | English Name | Arabic Name | Status |");
                reportWriter.println("|-----------|--------------|-------------|--------|");
                
                boolean hasArabicData = false;
                while (rs.next()) {
                    hasArabicData = true;
                    String itemCode = rs.getString("ITM_CD");
                    String englishName = rs.getString("ITM_NM");
                    String arabicName = rs.getString("ITM_NM_AR");
                    
                    boolean isValidArabic = arabicName != null && 
                                          arabicName.matches(".*[\\u0600-\\u06FF].*");
                    
                    reportWriter.printf("| %s | %s | %s | %s |\n",
                        itemCode != null ? itemCode : "NULL",
                        englishName != null ? englishName : "NULL",
                        arabicName != null ? arabicName : "NULL",
                        isValidArabic ? "✅" : "⚠️");
                }
                
                if (hasArabicData) {
                    passedTests++;
                    reportWriter.println("\n✅ Arabic encoding test passed");
                } else {
                    failedTests++;
                    reportWriter.println("\n⚠️ No Arabic data found");
                }
            }
            
        } catch (SQLException e) {
            failedTests++;
            reportWriter.println("❌ Error testing Arabic encoding: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * اختبار الأداء
     */
    private void testPerformance() {
        reportWriter.println("## ⚡ اختبار الأداء - Performance Test");
        reportWriter.println();
        
        String[] performanceQueries = {
            "SELECT COUNT(*) FROM IAS_ITM_MST",
            "SELECT * FROM IAS_ITM_MST WHERE ROWNUM <= 100",
            "SELECT ITM_CD, ITM_NM FROM IAS_ITM_MST ORDER BY ITM_CD"
        };
        
        reportWriter.println("| Query | Execution Time (ms) | Status |");
        reportWriter.println("|-------|---------------------|--------|");
        
        for (String query : performanceQueries) {
            testQueryPerformance(query);
        }
        
        reportWriter.println();
    }
    
    /**
     * اختبار أداء استعلام محدد
     */
    private void testQueryPerformance(String query) {
        totalTests++;
        
        try {
            long startTime = System.currentTimeMillis();
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                // قراءة النتائج
                while (rs.next()) {
                    // لا نحتاج لفعل شيء، فقط قراءة البيانات
                }
            }
            
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            String status = executionTime < 1000 ? "✅ FAST" : 
                           executionTime < 5000 ? "⚠️ SLOW" : "❌ VERY SLOW";
            
            reportWriter.printf("| %s | %d | %s |\n", 
                query.length() > 50 ? query.substring(0, 47) + "..." : query,
                executionTime,
                status);
            
            if (executionTime < 5000) {
                passedTests++;
            } else {
                failedTests++;
            }
            
        } catch (SQLException e) {
            failedTests++;
            reportWriter.printf("| %s | ERROR | ❌ FAILED |\n", 
                query.length() > 50 ? query.substring(0, 47) + "..." : query);
        }
    }
    
    /**
     * إنشاء إحصائيات البيانات
     */
    private void generateDataStatistics() {
        reportWriter.println("## 📊 إحصائيات البيانات - Data Statistics");
        reportWriter.println();
        
        try {
            // إحصائيات عامة
            String[] tables = {"IAS_ITM_MST", "IAS_ITM_DTL", "MEASUREMENT"};
            
            reportWriter.println("| Table | Row Count | Size Estimate |");
            reportWriter.println("|-------|-----------|---------------|");
            
            for (String table : tables) {
                try {
                    String countQuery = "SELECT COUNT(*) FROM " + table;
                    try (PreparedStatement stmt = connection.prepareStatement(countQuery);
                         ResultSet rs = stmt.executeQuery()) {
                        
                        if (rs.next()) {
                            int rowCount = rs.getInt(1);
                            String sizeEstimate = estimateTableSize(rowCount);
                            
                            reportWriter.printf("| %s | %d | %s |\n", 
                                table, rowCount, sizeEstimate);
                        }
                    }
                } catch (SQLException e) {
                    reportWriter.printf("| %s | ERROR | ERROR |\n", table);
                }
            }
            
        } catch (Exception e) {
            reportWriter.println("❌ Error generating statistics: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * تقدير حجم الجدول
     */
    private String estimateTableSize(int rowCount) {
        if (rowCount < 1000) return "Small";
        if (rowCount < 10000) return "Medium";
        if (rowCount < 100000) return "Large";
        return "Very Large";
    }
    
    /**
     * كتابة ملخص التقرير
     */
    private void writeReportSummary() {
        reportWriter.println("## 📋 ملخص التقرير - Report Summary");
        reportWriter.println();
        
        double successRate = totalTests > 0 ? (double) passedTests / totalTests * 100 : 0;
        
        reportWriter.println("| Metric | Value |");
        reportWriter.println("|--------|-------|");
        reportWriter.printf("| Total Tests | %d |\n", totalTests);
        reportWriter.printf("| Passed Tests | %d |\n", passedTests);
        reportWriter.printf("| Failed Tests | %d |\n", failedTests);
        reportWriter.printf("| Success Rate | %.1f%% |\n", successRate);
        
        reportWriter.println();
        
        if (successRate >= 80) {
            reportWriter.println("🎉 **Overall Status: EXCELLENT** - Data quality is very good!");
        } else if (successRate >= 60) {
            reportWriter.println("✅ **Overall Status: GOOD** - Data quality is acceptable with minor issues.");
        } else if (successRate >= 40) {
            reportWriter.println("⚠️ **Overall Status: FAIR** - Data quality needs improvement.");
        } else {
            reportWriter.println("❌ **Overall Status: POOR** - Data quality requires immediate attention.");
        }
        
        reportWriter.println();
        reportWriter.println("---");
        reportWriter.println();
        reportWriter.println("**Report Generated:** " + new Date());
        reportWriter.println("**Tool:** DataQualityTester v1.0");
    }
    
    /**
     * إغلاق الاتصالات
     */
    private void closeConnections() {
        try {
            if (reportWriter != null) {
                reportWriter.close();
            }
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (Exception e) {
            System.out.println("Error closing connections: " + e.getMessage());
        }
    }
}
