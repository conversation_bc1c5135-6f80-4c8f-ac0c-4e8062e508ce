# TNSNAMES.ORA Network Configuration File
# Ship ERP System - Oracle Database Connections
# تكوين اتصالات قواعد البيانات لنظام إدارة الشحنات
# Generated on: 2025-07-18

# =============================================================================
# MAIN PRODUCTION DATABASE - ORCL
# قاعدة البيانات الرئيسية للإنتاج
# =============================================================================
ORCL =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL)
    )
  )

# =============================================================================
# SHIP ERP DATABASE CONNECTION
# اتصال قاعدة بيانات نظام إدارة الشحنات
# =============================================================================
SHIP_ERP =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL)
      (INSTANCE_NAME = ORCL)
    )
  )

# =============================================================================
# IAS20251 DATABASE CONNECTION
# اتصال قاعدة بيانات IAS20251
# =============================================================================
IAS20251 =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL)
      (INSTANCE_NAME = ORCL)
    )
  )
# =============================================================================
# DEVELOPMENT DATABASE CONNECTION
# اتصال قاعدة بيانات التطوير
# =============================================================================
SHIP_ERP_DEV =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL)
      (INSTANCE_NAME = ORCL)
    )
  )

# =============================================================================
# TEST DATABASE CONNECTION
# اتصال قاعدة بيانات الاختبار
# =============================================================================
SHIP_ERP_TEST =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL)
      (INSTANCE_NAME = ORCL)
    )
  )

# =============================================================================
# BACKUP DATABASE CONNECTION
# اتصال قاعدة بيانات النسخ الاحتياطي
# =============================================================================
SHIP_ERP_BACKUP =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1522))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL_BACKUP)
      (INSTANCE_NAME = ORCL_BACKUP)
    )
  )

# =============================================================================
# HIGH AVAILABILITY CONNECTION WITH FAILOVER
# اتصال عالي التوفر مع التبديل التلقائي
# =============================================================================
SHIP_ERP_HA =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
      (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1522))
    )
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL)
      (FAILOVER_MODE =
        (TYPE = SELECT)
        (METHOD = BASIC)
        (RETRIES = 180)
        (DELAY = 5)
      )
    )
  )

# =============================================================================
# LOAD BALANCING CONNECTION
# اتصال توزيع الأحمال
# =============================================================================
SHIP_ERP_LB =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (LOAD_BALANCE = YES)
      (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
      (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1522))
    )
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL)
    )
  )

# =============================================================================
# SECURE CONNECTION WITH SSL
# اتصال آمن مع SSL
# =============================================================================
SHIP_ERP_SSL =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCPS)(HOST = localhost)(PORT = 2484))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL)
    )
    (SECURITY =
      (SSL_SERVER_CERT_DN = "CN=localhost")
    )
  )

# =============================================================================
# REMOTE DATABASE CONNECTION
# اتصال قاعدة بيانات بعيدة
# =============================================================================
SHIP_ERP_REMOTE =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = remote-server.company.com)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = SHIP_ERP_PROD)
    )
  )

# =============================================================================
# CONNECTION WITH TIMEOUT SETTINGS
# اتصال مع إعدادات المهلة الزمنية
# =============================================================================
SHIP_ERP_TIMEOUT =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ORCL)
    )
    (CONNECT_TIMEOUT = 60)
    (RETRY_COUNT = 3)
    (RETRY_DELAY = 3)
  )

# =============================================================================
# POOLED CONNECTION
# اتصال مجمع
# =============================================================================
SHIP_ERP_POOL =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = POOLED)
      (SERVICE_NAME = ORCL)
    )
  )

# =============================================================================
# CONFIGURATION NOTES
# ملاحظات التكوين
# =============================================================================
# 
# 1. Default Port: 1521 (Standard Oracle port)
# 2. SSL Port: 2484 (Standard Oracle SSL port)
# 3. Backup Port: 1522 (Alternative port for backup)
# 4. Protocol: TCP (Standard), TCPS (SSL)
# 5. Server Types: DEDICATED, SHARED, POOLED
# 
# Connection String Examples:
# أمثلة على سلاسل الاتصال:
# 
# jdbc:oracle:thin:@ORCL
# **************************
# **************************
# *****************************
# 
# =============================================================================
# ENVIRONMENT VARIABLES
# متغيرات البيئة
# =============================================================================
# 
# Set these environment variables for proper Oracle client configuration:
# قم بتعيين متغيرات البيئة هذه للتكوين الصحيح لعميل Oracle:
# 
# TNS_ADMIN=E:\ship_erp\java\network\admin
# ORACLE_HOME=C:\oracle\product\19.0.0\client_1
# PATH=%ORACLE_HOME%\bin;%PATH%
# 
# =============================================================================
