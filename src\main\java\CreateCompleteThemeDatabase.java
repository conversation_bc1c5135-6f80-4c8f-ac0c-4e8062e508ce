import java.sql.*;

/**
 * إنشاء قاعدة بيانات المظاهر الكاملة
 * Create Complete Theme Database
 */
public class CreateCompleteThemeDatabase {
    
    public static void main(String[] args) {
        System.out.println("🎨 إنشاء قاعدة بيانات المظاهر الكاملة...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            createCompleteThemeTable(connection);
            insertAllThemes(connection);
            
            connection.close();
            System.out.println("✅ تم إنشاء قاعدة بيانات المظاهر الكاملة بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء قاعدة بيانات المظاهر: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void createCompleteThemeTable(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء جدول ERP_COMPLETE_THEMES...");
        
        // حذف الجدول إذا كان موجوداً
        try {
            Statement stmt = connection.createStatement();
            stmt.executeUpdate("DROP TABLE ERP_COMPLETE_THEMES CASCADE CONSTRAINTS");
            System.out.println("🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            System.out.println("ℹ️ الجدول غير موجود مسبقاً");
        }
        
        // إنشاء الجدول الجديد
        String createTableSQL = """
            CREATE TABLE ERP_COMPLETE_THEMES (
                THEME_ID NUMBER PRIMARY KEY,
                USER_ID VARCHAR2(50) DEFAULT 'SYSTEM',
                THEME_NAME VARCHAR2(100) NOT NULL UNIQUE,
                THEME_DISPLAY_NAME NVARCHAR2(200),
                THEME_CATEGORY VARCHAR2(50),
                THEME_CLASS_NAME VARCHAR2(500),
                IS_DARK_THEME CHAR(1) DEFAULT 'N',
                IS_AVAILABLE CHAR(1) DEFAULT 'Y',
                THEME_DESCRIPTION NCLOB,
                THEME_AUTHOR VARCHAR2(100),
                THEME_VERSION VARCHAR2(20),
                THEME_LICENSE VARCHAR2(100),
                IS_CURRENT_THEME CHAR(1) DEFAULT 'N',
                LAST_APPLIED_DATE DATE,
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50) DEFAULT USER,
                MODIFIED_DATE DATE DEFAULT SYSDATE,
                MODIFIED_BY VARCHAR2(50) DEFAULT USER,
                NOTES NCLOB
            )
            """;
        
        Statement stmt = connection.createStatement();
        stmt.executeUpdate(createTableSQL);
        System.out.println("✅ تم إنشاء جدول ERP_COMPLETE_THEMES");
        
        // إنشاء sequence للـ ID
        try {
            stmt.executeUpdate("DROP SEQUENCE ERP_COMPLETE_THEMES_SEQ");
        } catch (SQLException e) {
            // Sequence غير موجود
        }
        
        stmt.executeUpdate("CREATE SEQUENCE ERP_COMPLETE_THEMES_SEQ START WITH 1 INCREMENT BY 1");
        System.out.println("✅ تم إنشاء SEQUENCE للجدول");
        
        // إنشاء فهارس
        stmt.executeUpdate("CREATE INDEX IDX_COMPLETE_THEMES_USER ON ERP_COMPLETE_THEMES(USER_ID)");
        stmt.executeUpdate("CREATE INDEX IDX_COMPLETE_THEMES_NAME ON ERP_COMPLETE_THEMES(THEME_NAME)");
        stmt.executeUpdate("CREATE INDEX IDX_COMPLETE_THEMES_CATEGORY ON ERP_COMPLETE_THEMES(THEME_CATEGORY)");
        stmt.executeUpdate("CREATE INDEX IDX_COMPLETE_THEMES_CURRENT ON ERP_COMPLETE_THEMES(IS_CURRENT_THEME)");
        System.out.println("✅ تم إنشاء الفهارس");
        
        stmt.close();
    }
    
    private static void insertAllThemes(Connection connection) throws SQLException {
        System.out.println("📝 إدراج جميع المظاهر...");
        
        String insertSQL = """
            INSERT INTO ERP_COMPLETE_THEMES 
            (THEME_ID, THEME_NAME, THEME_DISPLAY_NAME, THEME_CATEGORY, THEME_CLASS_NAME, 
             IS_DARK_THEME, THEME_DESCRIPTION, THEME_AUTHOR, THEME_VERSION, THEME_LICENSE, IS_CURRENT_THEME)
            VALUES (ERP_COMPLETE_THEMES_SEQ.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
        
        PreparedStatement pstmt = connection.prepareStatement(insertSQL);
        
        // FlatLaf Core Themes
        insertTheme(pstmt, "FlatLaf Light", "FlatLaf فاتح", "FlatLaf Core", 
                   "com.formdev.flatlaf.FlatLightLaf", "N", 
                   "مظهر فاتح حديث وأنيق من FlatLaf", "FormDev", "3.2.5", "Apache 2.0", "Y");
        
        insertTheme(pstmt, "FlatLaf Dark", "FlatLaf مظلم", "FlatLaf Core", 
                   "com.formdev.flatlaf.FlatDarkLaf", "Y", 
                   "مظهر مظلم حديث وأنيق من FlatLaf", "FormDev", "3.2.5", "Apache 2.0", "N");
        
        insertTheme(pstmt, "FlatLaf IntelliJ", "FlatLaf IntelliJ", "FlatLaf Core", 
                   "com.formdev.flatlaf.FlatIntelliJLaf", "N", 
                   "مظهر IntelliJ IDEA الكلاسيكي", "FormDev", "3.2.5", "Apache 2.0", "N");
        
        insertTheme(pstmt, "FlatLaf Darcula", "FlatLaf Darcula", "FlatLaf Core", 
                   "com.formdev.flatlaf.FlatDarculaLaf", "Y", 
                   "مظهر Darcula المظلم الشهير", "FormDev", "3.2.5", "Apache 2.0", "N");
        
        // IntelliJ Light Themes
        insertTheme(pstmt, "Arc Theme", "مظهر Arc", "IntelliJ Light", 
                   "com.formdev.flatlaf.intellijthemes.FlatArcIJTheme", "N", 
                   "مظهر Arc الحديث والأنيق", "Arc Team", "1.0", "GPL 3.0", "N");
        
        insertTheme(pstmt, "Cyan Light", "مظهر Cyan الفاتح", "IntelliJ Light", 
                   "com.formdev.flatlaf.intellijthemes.FlatCyanLightIJTheme", "N", 
                   "مظهر Cyan الفاتح المنعش", "Cyan Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Gray Theme", "المظهر الرمادي", "IntelliJ Light", 
                   "com.formdev.flatlaf.intellijthemes.FlatGrayIJTheme", "N", 
                   "مظهر رمادي هادئ ومريح", "Gray Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Light Flat", "المظهر الفاتح المسطح", "IntelliJ Light", 
                   "com.formdev.flatlaf.intellijthemes.FlatLightFlatIJTheme", "N", 
                   "مظهر فاتح مسطح وبسيط", "Light Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Nord Theme", "مظهر Nord", "IntelliJ Light", 
                   "com.formdev.flatlaf.intellijthemes.FlatNordIJTheme", "N", 
                   "مظهر Nord الاسكندنافي الهادئ", "Nord Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Solarized Light", "مظهر Solarized الفاتح", "IntelliJ Light", 
                   "com.formdev.flatlaf.intellijthemes.FlatSolarizedLightIJTheme", "N", 
                   "مظهر Solarized الفاتح المتوازن", "Solarized Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Vuesion Theme", "مظهر Vuesion", "IntelliJ Light", 
                   "com.formdev.flatlaf.intellijthemes.FlatVuesionIJTheme", "N", 
                   "مظهر Vuesion الحديث والمتطور", "Vuesion Team", "1.0", "MIT", "N");
        
        // IntelliJ Dark Themes
        insertTheme(pstmt, "Arc Dark", "مظهر Arc المظلم", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatArcDarkIJTheme", "Y", 
                   "مظهر Arc المظلم الجميل", "Arc Team", "1.0", "GPL 3.0", "N");
        
        insertTheme(pstmt, "Carbon Theme", "مظهر Carbon", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatCarbonIJTheme", "Y", 
                   "مظهر Carbon المظلم الأنيق", "Carbon Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Cobalt 2", "مظهر Cobalt 2", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatCobalt2IJTheme", "Y", 
                   "مظهر Cobalt 2 الأزرق المميز", "Cobalt Team", "2.0", "MIT", "N");
        
        insertTheme(pstmt, "Dark Purple", "مظهر البنفسجي المظلم", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatDarkPurpleIJTheme", "Y", 
                   "مظهر بنفسجي مظلم رائع", "Purple Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Dracula", "مظهر Dracula", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatDraculaIJTheme", "Y", 
                   "مظهر Dracula الشهير والمحبوب", "Dracula Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Gradianto Dark Fuchsia", "مظهر Gradianto", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatGradiantoDarkFuchsiaIJTheme", "Y", 
                   "مظهر Gradianto بتدرجات جميلة", "Gradianto Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Gruvbox Dark Hard", "مظهر Gruvbox", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatGruvboxDarkHardIJTheme", "Y", 
                   "مظهر Gruvbox المظلم المتباين", "Gruvbox Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Hiberbee Dark", "مظهر Hiberbee", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatHiberbeeDarkIJTheme", "Y", 
                   "مظهر Hiberbee المظلم الجميل", "Hiberbee Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "High Contrast", "مظهر التباين العالي", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatHighContrastIJTheme", "Y", 
                   "مظهر عالي التباين للوضوح", "Contrast Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Material Design Dark", "Material Design المظلم", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatMaterialDesignDarkIJTheme", "Y", 
                   "مظهر Material Design المظلم", "Material Team", "1.0", "Apache 2.0", "N");
        
        insertTheme(pstmt, "Monocai", "مظهر Monocai", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatMonocaiIJTheme", "Y", 
                   "مظهر Monocai الأحادي اللون", "Monocai Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "One Dark", "مظهر One Dark", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatOneDarkIJTheme", "Y", 
                   "مظهر One Dark الشهير من Atom", "One Dark Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Solarized Dark", "مظهر Solarized المظلم", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatSolarizedDarkIJTheme", "Y", 
                   "مظهر Solarized المظلم المتوازن", "Solarized Team", "1.0", "MIT", "N");
        
        insertTheme(pstmt, "Spacegray", "مظهر Spacegray", "IntelliJ Dark", 
                   "com.formdev.flatlaf.intellijthemes.FlatSpacegrayIJTheme", "Y", 
                   "مظهر Spacegray الفضائي", "Spacegray Team", "1.0", "MIT", "N");
        
        pstmt.close();
        
        System.out.println("✅ تم إدراج " + getThemeCount(connection) + " مظهر في قاعدة البيانات");
    }
    
    private static void insertTheme(PreparedStatement pstmt, String name, String displayName, 
                                   String category, String className, String isDark, 
                                   String description, String author, String version, 
                                   String license, String isCurrent) throws SQLException {
        pstmt.setString(1, name);
        pstmt.setString(2, displayName);
        pstmt.setString(3, category);
        pstmt.setString(4, className);
        pstmt.setString(5, isDark);
        pstmt.setString(6, description);
        pstmt.setString(7, author);
        pstmt.setString(8, version);
        pstmt.setString(9, license);
        pstmt.setString(10, isCurrent);
        pstmt.executeUpdate();
    }
    
    private static int getThemeCount(Connection connection) throws SQLException {
        Statement stmt = connection.createStatement();
        ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM ERP_COMPLETE_THEMES");
        rs.next();
        int count = rs.getInt(1);
        rs.close();
        stmt.close();
        return count;
    }
}
