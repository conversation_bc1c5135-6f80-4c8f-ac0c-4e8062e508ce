-- إنشاء جدول شجرة الأنظمة
-- Create System Tree Table
-- Ship ERP System - Dynamic Menu Tree Management

-- حذف الجدول إذا كان موجوداً
DROP TABLE ERP_SYSTEM_TREE CASCADE CONSTRAINTS;

-- إنشاء sequence للمعرف الفريد
DROP SEQUENCE ERP_SYSTEM_TREE_SEQ;
CREATE SEQUENCE ERP_SYSTEM_TREE_SEQ START WITH 1 INCREMENT BY 1;

-- إنشاء الجدول الرئيسي لشجرة الأنظمة
CREATE TABLE ERP_SYSTEM_TREE (
    -- المعرف الفريد
    TREE_ID NUMBER(10) PRIMARY KEY,
    
    -- معرف العقدة الأب (NULL للعقد الجذرية)
    PARENT_ID NUMBER(10),
    
    -- اسم العقدة بالعربية
    NODE_NAME_AR NVARCHAR2(100) NOT NULL,
    
    -- اسم العقدة بالإنجليزية
    NODE_NAME_EN VARCHAR2(100) NOT NULL,
    
    -- وصف العقدة
    NODE_DESCRIPTION NVARCHAR2(500),
    
    -- نوع العقدة (CATEGORY, WINDOW, REPORT, TOOL)
    NODE_TYPE VARCHAR2(20) NOT NULL,
    
    -- اسم الكلاس للنافذة (إذا كان نوع العقدة WINDOW)
    WINDOW_CLASS VARCHAR2(200),
    
    -- مسار الأيقونة
    ICON_PATH VARCHAR2(500),
    
    -- ترتيب العرض
    DISPLAY_ORDER NUMBER(5) DEFAULT 0,
    
    -- مستوى العمق في الشجرة
    TREE_LEVEL NUMBER(3) DEFAULT 0,
    
    -- هل العقدة نشطة
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    
    -- هل العقدة مرئية
    IS_VISIBLE CHAR(1) DEFAULT 'Y' CHECK (IS_VISIBLE IN ('Y', 'N')),
    
    -- صلاحيات الوصول (مفصولة بفواصل)
    ACCESS_PERMISSIONS VARCHAR2(500),
    
    -- معلومات إضافية (JSON format)
    ADDITIONAL_INFO CLOB,
    
    -- تاريخ الإنشاء
    CREATED_DATE DATE DEFAULT SYSDATE,
    
    -- المستخدم المنشئ
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    
    -- تاريخ آخر تحديث
    LAST_UPDATED DATE DEFAULT SYSDATE,
    
    -- المستخدم المحدث
    UPDATED_BY VARCHAR2(50) DEFAULT USER,
    
    -- إصدار السجل (للتحكم في التزامن)
    VERSION_NUMBER NUMBER(10) DEFAULT 1,
    
    -- ملاحظات
    NOTES NVARCHAR2(1000)
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IDX_SYSTEM_TREE_PARENT ON ERP_SYSTEM_TREE(PARENT_ID);
CREATE INDEX IDX_SYSTEM_TREE_TYPE ON ERP_SYSTEM_TREE(NODE_TYPE);
CREATE INDEX IDX_SYSTEM_TREE_ORDER ON ERP_SYSTEM_TREE(DISPLAY_ORDER);
CREATE INDEX IDX_SYSTEM_TREE_LEVEL ON ERP_SYSTEM_TREE(TREE_LEVEL);
CREATE INDEX IDX_SYSTEM_TREE_ACTIVE ON ERP_SYSTEM_TREE(IS_ACTIVE);

-- إضافة قيود المرجعية
ALTER TABLE ERP_SYSTEM_TREE 
ADD CONSTRAINT FK_SYSTEM_TREE_PARENT 
FOREIGN KEY (PARENT_ID) REFERENCES ERP_SYSTEM_TREE(TREE_ID);

-- إنشاء trigger لتحديث التاريخ والإصدار تلقائياً
CREATE OR REPLACE TRIGGER TRG_SYSTEM_TREE_UPDATE
    BEFORE UPDATE ON ERP_SYSTEM_TREE
    FOR EACH ROW
BEGIN
    :NEW.LAST_UPDATED := SYSDATE;
    :NEW.UPDATED_BY := USER;
    :NEW.VERSION_NUMBER := :OLD.VERSION_NUMBER + 1;
END;
/

-- إنشاء trigger لتعيين المعرف الفريد تلقائياً
CREATE OR REPLACE TRIGGER TRG_SYSTEM_TREE_ID
    BEFORE INSERT ON ERP_SYSTEM_TREE
    FOR EACH ROW
    WHEN (NEW.TREE_ID IS NULL)
BEGIN
    :NEW.TREE_ID := ERP_SYSTEM_TREE_SEQ.NEXTVAL;
END;
/

-- إدراج البيانات الأساسية لشجرة النظام
INSERT INTO ERP_SYSTEM_TREE (
    TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, ICON_PATH, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    1, NULL, 'نظام إدارة الشحنات', 'Ship ERP System', 'النظام الرئيسي لإدارة الشحنات',
    'CATEGORY', NULL, 'icons/ship.png', 1, 0
);

-- إدراج الفئات الرئيسية
INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    1, 'إدارة الأصناف', 'Items Management', 'إدارة وتصنيف الأصناف والمنتجات',
    'CATEGORY', 1, 1
);

INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    1, 'إدارة المستخدمين', 'User Management', 'إدارة المستخدمين والصلاحيات',
    'CATEGORY', 2, 1
);

INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    1, 'الإعدادات العامة', 'General Settings', 'الإعدادات العامة للنظام',
    'CATEGORY', 3, 1
);

INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    1, 'التقارير', 'Reports', 'التقارير والإحصائيات',
    'CATEGORY', 4, 1
);

INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    1, 'أدوات النظام', 'System Tools', 'أدوات مساعدة ومراقبة النظام',
    'CATEGORY', 5, 1
);

-- إدراج نوافذ إدارة الأصناف
INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'Items Management'),
    'بيانات الأصناف الحقيقية', 'Real Item Data', 'إدارة بيانات الأصناف الحقيقية',
    'WINDOW', 'RealItemDataWindow', 1, 2
);

INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'Items Management'),
    'بيانات الأصناف الشاملة', 'Comprehensive Item Data', 'إدارة بيانات الأصناف الشاملة',
    'WINDOW', 'ComprehensiveItemDataWindow', 2, 2
);

INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'Items Management'),
    'مجموعات الأصناف', 'Item Groups', 'إدارة مجموعات وتصنيفات الأصناف',
    'WINDOW', 'ItemGroupsManagementWindow', 3, 2
);

INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'Items Management'),
    'وحدات القياس', 'Measurement Units', 'إدارة وحدات القياس والتحويلات',
    'WINDOW', 'MeasurementUnitsWindow', 4, 2
);

-- إدراج نوافذ إدارة المستخدمين
INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'User Management'),
    'إدارة المستخدمين', 'User Management', 'إضافة وتعديل وحذف المستخدمين',
    'WINDOW', 'UserManagementWindow', 1, 2
);

-- إدراج نوافذ الإعدادات العامة
INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'General Settings'),
    'الإعدادات العامة', 'General Settings', 'إعدادات النظام العامة',
    'WINDOW', 'GeneralSettingsWindow', 1, 2
);

-- إدراج أدوات النظام
INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'System Tools'),
    'فحص النظام الشامل', 'System Audit', 'فحص شامل لحالة النظام',
    'TOOL', 'CompleteOracleSystemTest', 1, 2
);

INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'System Tools'),
    'مراقب الأداء', 'Performance Monitor', 'مراقبة أداء النظام والاتصالات',
    'TOOL', 'PerformanceMonitor', 2, 2
);

INSERT INTO ERP_SYSTEM_TREE (
    PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
    NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL
) VALUES (
    (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_EN = 'System Tools'),
    'إدارة الاتصالات', 'Connection Manager', 'إدارة اتصالات قواعد البيانات',
    'TOOL', 'TNSConnectionManager', 3, 2
);

-- حفظ التغييرات
COMMIT;

-- عرض البيانات المدرجة
SELECT 
    TREE_ID,
    PARENT_ID,
    LPAD(' ', (TREE_LEVEL * 2)) || NODE_NAME_AR AS TREE_STRUCTURE,
    NODE_TYPE,
    WINDOW_CLASS,
    DISPLAY_ORDER
FROM ERP_SYSTEM_TREE
ORDER BY TREE_LEVEL, DISPLAY_ORDER;

-- إنشاء view لعرض الشجرة بشكل هرمي
CREATE OR REPLACE VIEW VW_SYSTEM_TREE_HIERARCHY AS
SELECT 
    TREE_ID,
    PARENT_ID,
    NODE_NAME_AR,
    NODE_NAME_EN,
    NODE_DESCRIPTION,
    NODE_TYPE,
    WINDOW_CLASS,
    ICON_PATH,
    DISPLAY_ORDER,
    TREE_LEVEL,
    IS_ACTIVE,
    IS_VISIBLE,
    LPAD(' ', (TREE_LEVEL * 4)) || NODE_NAME_AR AS TREE_DISPLAY,
    SYS_CONNECT_BY_PATH(NODE_NAME_EN, '/') AS FULL_PATH
FROM ERP_SYSTEM_TREE
WHERE IS_ACTIVE = 'Y' AND IS_VISIBLE = 'Y'
START WITH PARENT_ID IS NULL
CONNECT BY PRIOR TREE_ID = PARENT_ID
ORDER SIBLINGS BY DISPLAY_ORDER;

PROMPT 'System Tree Table created successfully!'
PROMPT 'جدول شجرة النظام تم إنشاؤه بنجاح!'
