import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.BorderFactory;
import javax.swing.ButtonGroup;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JRadioButton;
import javax.swing.JScrollPane;
import javax.swing.JSlider;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * أداة اختبار الثيمات والمظاهر
 * Theme and Look & Feel Tester
 */
public class ThemeTester extends JFrame {
    
    private JComboBox<ThemeInfo> themeComboBox;
    private JPanel previewPanel;
    private JTextArea logArea;
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  THEME TESTER");
        System.out.println("  أداة اختبار الثيمات");
        System.out.println("========================================");
        
        SwingUtilities.invokeLater(() -> {
            try {
                new ThemeTester().setVisible(true);
            } catch (Exception e) {
                System.err.println("Error starting ThemeTester: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public ThemeTester() {
        initializeUI();
        loadAvailableThemes();
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeUI() {
        setTitle("أداة اختبار الثيمات - Theme Tester");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);
        
        // إنشاء المكونات الرئيسية
        createMainComponents();
        layoutComponents();
        
        // إعداد الأحداث
        setupEventHandlers();
    }
    
    /**
     * إنشاء المكونات الرئيسية
     */
    private void createMainComponents() {
        // قائمة الثيمات
        themeComboBox = new JComboBox<>();
        themeComboBox.setPreferredSize(new Dimension(300, 30));
        
        // منطقة المعاينة
        previewPanel = new JPanel();
        previewPanel.setLayout(new BorderLayout());
        previewPanel.setBorder(BorderFactory.createTitledBorder("معاينة الثيم - Theme Preview"));
        
        // منطقة السجل
        logArea = new JTextArea(10, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        // إنشاء مكونات المعاينة
        createPreviewComponents();
    }
    
    /**
     * إنشاء مكونات المعاينة
     */
    private void createPreviewComponents() {
        JPanel demoPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // أزرار
        gbc.gridx = 0; gbc.gridy = 0;
        demoPanel.add(new JLabel("الأزرار - Buttons:"), gbc);
        
        gbc.gridx = 1;
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(new JButton("زر عادي"));
        buttonPanel.add(new JButton("Normal Button"));
        JButton disabledBtn = new JButton("معطل");
        disabledBtn.setEnabled(false);
        buttonPanel.add(disabledBtn);
        demoPanel.add(buttonPanel, gbc);
        
        // حقول النص
        gbc.gridx = 0; gbc.gridy = 1;
        demoPanel.add(new JLabel("حقول النص - Text Fields:"), gbc);
        
        gbc.gridx = 1;
        JPanel textPanel = new JPanel(new FlowLayout());
        textPanel.add(new JTextField("نص عربي", 10));
        textPanel.add(new JTextField("English Text", 10));
        demoPanel.add(textPanel, gbc);
        
        // خانات الاختيار
        gbc.gridx = 0; gbc.gridy = 2;
        demoPanel.add(new JLabel("خانات الاختيار - Checkboxes:"), gbc);
        
        gbc.gridx = 1;
        JPanel checkPanel = new JPanel(new FlowLayout());
        checkPanel.add(new JCheckBox("خيار أول", true));
        checkPanel.add(new JCheckBox("Option Two"));
        checkPanel.add(new JCheckBox("خيار ثالث"));
        demoPanel.add(checkPanel, gbc);
        
        // أزرار الراديو
        gbc.gridx = 0; gbc.gridy = 3;
        demoPanel.add(new JLabel("أزرار الراديو - Radio Buttons:"), gbc);
        
        gbc.gridx = 1;
        JPanel radioPanel = new JPanel(new FlowLayout());
        ButtonGroup group = new ButtonGroup();
        JRadioButton radio1 = new JRadioButton("الأول", true);
        JRadioButton radio2 = new JRadioButton("Second");
        JRadioButton radio3 = new JRadioButton("الثالث");
        group.add(radio1);
        group.add(radio2);
        group.add(radio3);
        radioPanel.add(radio1);
        radioPanel.add(radio2);
        radioPanel.add(radio3);
        demoPanel.add(radioPanel, gbc);
        
        // شريط التقدم
        gbc.gridx = 0; gbc.gridy = 4;
        demoPanel.add(new JLabel("شريط التقدم - Progress:"), gbc);
        
        gbc.gridx = 1;
        JProgressBar progressBar = new JProgressBar(0, 100);
        progressBar.setValue(65);
        progressBar.setStringPainted(true);
        progressBar.setString("65%");
        demoPanel.add(progressBar, gbc);
        
        // شريط التمرير
        gbc.gridx = 0; gbc.gridy = 5;
        demoPanel.add(new JLabel("شريط التمرير - Slider:"), gbc);
        
        gbc.gridx = 1;
        JSlider slider = new JSlider(0, 100, 50);
        slider.setMajorTickSpacing(25);
        slider.setPaintTicks(true);
        slider.setPaintLabels(true);
        demoPanel.add(slider, gbc);
        
        previewPanel.add(demoPanel, BorderLayout.CENTER);
    }
    
    /**
     * تخطيط المكونات
     */
    private void layoutComponents() {
        setLayout(new BorderLayout());
        
        // الجزء العلوي - اختيار الثيم
        JPanel topPanel = new JPanel(new FlowLayout());
        topPanel.add(new JLabel("اختر الثيم - Select Theme:"));
        topPanel.add(themeComboBox);
        
        JButton applyButton = new JButton("تطبيق - Apply");
        applyButton.addActionListener(e -> applySelectedTheme());
        topPanel.add(applyButton);
        
        JButton resetButton = new JButton("إعادة تعيين - Reset");
        resetButton.addActionListener(e -> resetToDefault());
        topPanel.add(resetButton);
        
        add(topPanel, BorderLayout.NORTH);
        
        // الجزء الأوسط - المعاينة
        add(previewPanel, BorderLayout.CENTER);
        
        // الجزء السفلي - السجل
        JScrollPane logScrollPane = new JScrollPane(logArea);
        logScrollPane.setBorder(BorderFactory.createTitledBorder("سجل الأحداث - Event Log"));
        logScrollPane.setPreferredSize(new Dimension(0, 150));
        add(logScrollPane, BorderLayout.SOUTH);
    }
    
    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        themeComboBox.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                ThemeInfo selected = (ThemeInfo) themeComboBox.getSelectedItem();
                if (selected != null) {
                    logMessage("تم اختيار الثيم: " + selected.name);
                }
            }
        });
    }
    
    /**
     * تحميل الثيمات المتاحة
     */
    private void loadAvailableThemes() {
        logMessage("تحميل الثيمات المتاحة...");
        
        // الثيمات الافتراضية
        themeComboBox.addItem(new ThemeInfo("System Default", "javax.swing.plaf.system.SystemLookAndFeel"));
        themeComboBox.addItem(new ThemeInfo("Metal", "javax.swing.plaf.metal.MetalLookAndFeel"));
        themeComboBox.addItem(new ThemeInfo("Nimbus", "javax.swing.plaf.nimbus.NimbusLookAndFeel"));
        
        // FlatLaf Themes
        addThemeIfAvailable("FlatLaf Light", "com.formdev.flatlaf.FlatLightLaf");
        addThemeIfAvailable("FlatLaf Dark", "com.formdev.flatlaf.FlatDarkLaf");
        addThemeIfAvailable("FlatLaf IntelliJ", "com.formdev.flatlaf.FlatIntelliJLaf");
        addThemeIfAvailable("FlatLaf Darcula", "com.formdev.flatlaf.FlatDarculaLaf");
        
        // Material UI
        addThemeIfAvailable("Material Light", "mdlaf.MaterialLookAndFeel");
        
        // JTattoo Themes
        addThemeIfAvailable("JTattoo Acryl", "com.jtattoo.plaf.acryl.AcrylLookAndFeel");
        addThemeIfAvailable("JTattoo Aero", "com.jtattoo.plaf.aero.AeroLookAndFeel");
        addThemeIfAvailable("JTattoo Aluminium", "com.jtattoo.plaf.aluminium.AluminiumLookAndFeel");
        addThemeIfAvailable("JTattoo Bernstein", "com.jtattoo.plaf.bernstein.BernsteinLookAndFeel");
        addThemeIfAvailable("JTattoo Fast", "com.jtattoo.plaf.fast.FastLookAndFeel");
        addThemeIfAvailable("JTattoo HiFi", "com.jtattoo.plaf.hifi.HiFiLookAndFeel");
        addThemeIfAvailable("JTattoo Luna", "com.jtattoo.plaf.luna.LunaLookAndFeel");
        addThemeIfAvailable("JTattoo McWin", "com.jtattoo.plaf.mcwin.McWinLookAndFeel");
        addThemeIfAvailable("JTattoo Mint", "com.jtattoo.plaf.mint.MintLookAndFeel");
        addThemeIfAvailable("JTattoo Noire", "com.jtattoo.plaf.noire.NoireLookAndFeel");
        addThemeIfAvailable("JTattoo Smart", "com.jtattoo.plaf.smart.SmartLookAndFeel");
        
        // DarkLaf
        addThemeIfAvailable("DarkLaf", "com.github.weisj.darklaf.DarkLaf");
        
        // SeaGlass
        addThemeIfAvailable("SeaGlass", "com.seaglasslookandfeel.SeaGlassLookAndFeel");
        
        logMessage("تم تحميل " + themeComboBox.getItemCount() + " ثيم");
    }
    
    /**
     * إضافة ثيم إذا كان متاحاً
     */
    private void addThemeIfAvailable(String name, String className) {
        try {
            Class.forName(className);
            themeComboBox.addItem(new ThemeInfo(name, className));
            logMessage("✅ تم العثور على: " + name);
        } catch (ClassNotFoundException e) {
            logMessage("❌ غير متاح: " + name);
        }
    }
    
    /**
     * تطبيق الثيم المحدد
     */
    private void applySelectedTheme() {
        ThemeInfo selected = (ThemeInfo) themeComboBox.getSelectedItem();
        if (selected == null) return;
        
        try {
            logMessage("تطبيق الثيم: " + selected.name);
            
            UIManager.setLookAndFeel(selected.className);
            SwingUtilities.updateComponentTreeUI(this);
            
            logMessage("✅ تم تطبيق الثيم بنجاح: " + selected.name);
            
        } catch (Exception e) {
            logMessage("❌ فشل في تطبيق الثيم: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * إعادة تعيين إلى الثيم الافتراضي
     */
    private void resetToDefault() {
        try {
            logMessage("إعادة تعيين إلى الثيم الافتراضي...");
            
            UIManager.setLookAndFeel("javax.swing.plaf.system.SystemLookAndFeel");
            SwingUtilities.updateComponentTreeUI(this);
            
            themeComboBox.setSelectedIndex(0);
            logMessage("✅ تم إعادة التعيين بنجاح");
            
        } catch (Exception e) {
            logMessage("❌ فشل في إعادة التعيين: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * تسجيل رسالة في السجل
     */
    private void logMessage(String message) {
        String timestamp = java.time.LocalTime.now().toString().substring(0, 8);
        String logEntry = "[" + timestamp + "] " + message + "\n";
        logArea.append(logEntry);
        logArea.setCaretPosition(logArea.getDocument().getLength());
        System.out.println(logEntry.trim());
    }
    
    /**
     * فئة معلومات الثيم
     */
    private static class ThemeInfo {
        String name;
        String className;
        
        ThemeInfo(String name, String className) {
            this.name = name;
            this.className = className;
        }
        
        @Override
        public String toString() {
            return name;
        }
    }
}
