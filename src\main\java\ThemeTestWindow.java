import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.GridLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.SwingUtilities;

/**
 * نافذة اختبار المظاهر الموحدة
 * Unified Theme Test Window
 */
public class ThemeTestWindow extends JFrame {
    
    private JComboBox<String> themeComboBox;
    private JTextArea infoArea;
    private FinalThemeManager themeManager;
    
    public ThemeTestWindow() {
        themeManager = FinalThemeManager.getInstance();
        initializeUI();
        updateInfo();
    }
    
    private void initializeUI() {
        setTitle("🎨 اختبار المظاهر الموحدة - Unified Theme Test");
        setSize(800, 600);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        
        // تطبيق اتجاه اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        setLayout(new BorderLayout());
        
        // اللوحة العلوية
        JPanel topPanel = createTopPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // اللوحة الوسطى
        JPanel centerPanel = createCenterPanel();
        add(centerPanel, BorderLayout.CENTER);
        
        // اللوحة السفلى
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 1, 5, 5));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // العنوان
        JLabel titleLabel = new JLabel("🎨 اختبار المظاهر الموحدة", JLabel.CENTER);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        panel.add(titleLabel);
        
        // لوحة اختيار المظهر
        JPanel themePanel = new JPanel();
        themePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel themeLabel = new JLabel("اختر المظهر:");
        themeLabel.setFont(new Font("Tahoma", Font.PLAIN, 12));
        
        themeComboBox = new JComboBox<>(FinalThemeManager.getAvailableThemes());
        themeComboBox.setSelectedItem(themeManager.getCurrentTheme());
        themeComboBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        themeComboBox.addActionListener(new ThemeChangeListener());
        
        JButton applyButton = new JButton("تطبيق");
        applyButton.addActionListener(e -> applySelectedTheme());
        
        themePanel.add(themeLabel);
        themePanel.add(themeComboBox);
        themePanel.add(applyButton);
        
        panel.add(themePanel);
        
        return panel;
    }
    
    private JPanel createCenterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // منطقة المعلومات
        infoArea = new JTextArea();
        infoArea.setFont(new Font("Courier New", Font.PLAIN, 12));
        infoArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        infoArea.setEditable(false);
        
        JScrollPane scrollPane = new JScrollPane(infoArea);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        panel.add(new JLabel("معلومات المظهر الحالي:", JLabel.RIGHT), BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel();
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JButton refreshButton = new JButton("تحديث المعلومات");
        refreshButton.addActionListener(e -> updateInfo());
        
        JButton testButton = new JButton("اختبار جميع المظاهر");
        testButton.addActionListener(e -> testAllThemes());
        
        JButton resetButton = new JButton("إعادة تعيين");
        resetButton.addActionListener(e -> resetToDefault());
        
        panel.add(refreshButton);
        panel.add(testButton);
        panel.add(resetButton);
        
        return panel;
    }
    
    private void applySelectedTheme() {
        String selectedTheme = (String) themeComboBox.getSelectedItem();
        if (selectedTheme != null) {
            System.out.println("🎨 تطبيق المظهر المحدد: " + selectedTheme);
            
            boolean success = themeManager.applyTheme(selectedTheme);
            if (success) {
                updateInfo();
                SwingUtilities.updateComponentTreeUI(this);
                repaint();
            }
        }
    }
    
    private void updateInfo() {
        StringBuilder info = new StringBuilder();
        info.append("📊 معلومات المظهر الحالي:\n");
        info.append("========================\n\n");
        
        info.append("🎨 المظهر: ").append(themeManager.getCurrentTheme()).append("\n");
        info.append("📋 التفاصيل: ").append(themeManager.getCurrentThemeInfo()).append("\n\n");
        
        info.append("🔧 إعدادات UIManager:\n");
        info.append("===================\n");
        
        String[] keys = {
            "Panel.background", "Button.background", "TextField.background",
            "Label.foreground", "Button.foreground", "TextField.foreground",
            "defaultFont", "Button.arc", "Component.arc"
        };
        
        for (String key : keys) {
            Object value = javax.swing.UIManager.get(key);
            info.append("• ").append(key).append(": ");
            if (value != null) {
                info.append(value.toString());
            } else {
                info.append("غير محدد");
            }
            info.append("\n");
        }
        
        info.append("\n🪟 النوافذ المفتوحة:\n");
        info.append("==================\n");
        
        java.awt.Window[] windows = java.awt.Window.getWindows();
        info.append("عدد النوافذ: ").append(windows.length).append("\n");
        
        for (int i = 0; i < windows.length; i++) {
            java.awt.Window window = windows[i];
            if (window.isDisplayable()) {
                info.append("• ").append(window.getClass().getSimpleName());
                info.append(" - ").append(window.isVisible() ? "مرئية" : "مخفية");
                info.append("\n");
            }
        }
        
        info.append("\n📚 المكتبات المتاحة:\n");
        info.append("===================\n");
        
        String[] libraries = {
            "com.formdev.flatlaf.FlatLightLaf",
            "com.formdev.flatlaf.FlatDarkLaf",
            "com.formdev.flatlaf.FlatIntelliJLaf",
            "com.formdev.flatlaf.FlatDarculaLaf"
        };
        
        for (String lib : libraries) {
            try {
                Class.forName(lib);
                info.append("✅ ").append(lib.substring(lib.lastIndexOf('.') + 1)).append("\n");
            } catch (ClassNotFoundException e) {
                info.append("❌ ").append(lib.substring(lib.lastIndexOf('.') + 1)).append("\n");
            }
        }
        
        infoArea.setText(info.toString());
    }
    
    private void testAllThemes() {
        System.out.println("🧪 اختبار جميع المظاهر...");
        
        String originalTheme = themeManager.getCurrentTheme();
        String[] themes = FinalThemeManager.getAvailableThemes();
        
        new Thread(() -> {
            for (String theme : themes) {
                SwingUtilities.invokeLater(() -> {
                    System.out.println("اختبار: " + theme);
                    themeManager.applyTheme(theme);
                    themeComboBox.setSelectedItem(theme);
                    updateInfo();
                });
                
                try {
                    Thread.sleep(2000); // انتظار ثانيتين
                } catch (InterruptedException e) {
                    break;
                }
            }
            
            // العودة للمظهر الأصلي
            SwingUtilities.invokeLater(() -> {
                themeManager.applyTheme(originalTheme);
                themeComboBox.setSelectedItem(originalTheme);
                updateInfo();
                System.out.println("✅ انتهى اختبار جميع المظاهر");
            });
        }).start();
    }
    
    private void resetToDefault() {
        System.out.println("🔄 إعادة تعيين للمظهر الافتراضي...");
        
        themeManager.applyTheme("FlatLaf Light");
        themeComboBox.setSelectedItem("FlatLaf Light");
        updateInfo();
        SwingUtilities.updateComponentTreeUI(this);
        repaint();
        
        System.out.println("✅ تم إعادة التعيين");
    }
    
    private class ThemeChangeListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            // تحديث المعلومات عند تغيير الاختيار
            updateInfo();
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // تطبيق المظهر الموحد
            FinalThemeManager.initializeDefaultTheme();
            
            System.out.println("🎨 تشغيل نافذة اختبار المظاهر الموحدة");
            System.out.println("Unified Theme Test Window Starting...");
            System.out.println("==========================================");
            
            ThemeTestWindow testWindow = new ThemeTestWindow();
            testWindow.setVisible(true);
            
            System.out.println("✅ تم تشغيل نافذة اختبار المظاهر");
        });
    }
}
