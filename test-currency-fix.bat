@echo off
echo ========================================
echo 🔧 اختبار إصلاح نافذة إدارة العملات
echo Testing Currency Management Window Fix
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/3] تجميع الملفات المحدثة...
echo Compiling Updated Files...
echo ========================================

echo تجميع TreeMenuPanel المحدث...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع TreeMenuPanel بنجاح
) else (
    echo ❌ فشل في تجميع TreeMenuPanel
    pause
    exit /b 1
)

echo تجميع CurrencyManagementWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CurrencyManagementWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CurrencyManagementWindow بنجاح
) else (
    echo ❌ فشل في تجميع CurrencyManagementWindow
    pause
    exit /b 1
)

echo.
echo [2/3] اختبار النافذة...
echo Testing Window...
echo ========================================

echo.
echo اختر طريقة الاختبار:
echo 1. تشغيل نافذة إدارة العملات مباشرة
echo 2. تشغيل التطبيق الكامل (للاختبار عبر الشجرة)
echo 3. كلاهما
echo.

set /p choice="أدخل اختيارك (1-3): "

echo.
if "%choice%"=="1" (
    echo تشغيل نافذة إدارة العملات مباشرة...
    start "Currency Management Window - Direct Test" java -cp "lib\*;." CurrencyManagementWindow
    echo ✅ تم تشغيل النافذة مباشرة
) else if "%choice%"=="2" (
    echo تشغيل التطبيق الكامل...
    start "Enhanced Ship ERP - Currency Test" java -cp "lib\*;." EnhancedShipERP
    echo ✅ تم تشغيل التطبيق الكامل
    echo.
    echo 📋 للاختبار:
    echo 1. انتقل إلى "الإعدادات والإدارة"
    echo 2. افتح "الإعدادات العامة"
    echo 3. انقر نقراً مزدوجاً على "إدارة العملات"
    echo 4. يجب أن تفتح النافذة الشاملة (وليس رسالة "قيد التطوير")
) else if "%choice%"=="3" (
    echo تشغيل كلا الاختبارين...
    
    echo 1. تشغيل النافذة مباشرة...
    start "Currency Management Window - Direct Test" java -cp "lib\*;." CurrencyManagementWindow
    
    timeout /t 2 /nobreak >nul
    
    echo 2. تشغيل التطبيق الكامل...
    start "Enhanced Ship ERP - Currency Test" java -cp "lib\*;." EnhancedShipERP
    
    echo ✅ تم تشغيل كلا الاختبارين
) else (
    echo ❌ اختيار غير صحيح، سيتم تشغيل النافذة مباشرة
    start "Currency Management Window - Direct Test" java -cp "lib\*;." CurrencyManagementWindow
)

echo.
echo [3/3] معلومات الإصلاح...
echo Fix Information...
echo ========================================

echo.
echo 🔧 الإصلاحات المطبقة:
echo ====================
echo ✅ تم إصلاح دالة openCurrencyManagementWindow()
echo ✅ تم إزالة رسالة "قيد التطوير"
echo ✅ تم ربط النافذة الفعلية CurrencyManagementWindow
echo ✅ تم إزالة التكرار من الشجرة
echo ✅ تم الاحتفاظ بالنافذة في "الإعدادات العامة" فقط

echo.
echo 🌳 الموقع الصحيح في الشجرة:
echo ============================
echo نظام إدارة الشحنات
echo └── الإعدادات والإدارة
echo    └── الإعدادات العامة
echo        ├── المتغيرات العامة
echo        ├── إدارة العملات ⭐ مُصلحة
echo        ├── إعدادات النظام العامة
echo        └── إعدادات الأمان

echo.
echo 💰 نافذة إدارة العملات:
echo =========================
echo • الكلاس: CurrencyManagementWindow
echo • التبويبات: 5 تبويبات شاملة
echo • الميزات: إدارة عملات + أسعار صرف + إعدادات + تقارير
echo • الحالة: مطورة ومكتملة

echo.
echo 🧪 نتائج الاختبار المتوقعة:
echo ===========================
echo ✅ النقر على "إدارة العملات" يجب أن يفتح النافذة الشاملة
echo ✅ النافذة تحتوي على 5 تبويبات
echo ✅ تبويب "إدارة العملات" يعمل بالكامل
echo ✅ تبويب "أسعار الصرف" يعمل جزئياً
echo ✅ تبويب "الإعدادات العامة" يعمل بالكامل
echo ❌ لا توجد رسالة "قيد التطوير"

echo.
echo 🔍 للتحقق من الإصلاح:
echo ======================
echo 1. افتح التطبيق الكامل
echo 2. انتقل إلى الإعدادات والإدارة ^> الإعدادات العامة
echo 3. انقر نقراً مزدوجاً على "إدارة العملات"
echo 4. يجب أن تظهر النافذة الشاملة مع 5 تبويبات
echo 5. جرب إضافة عملة جديدة في التبويب الأول

echo.
echo 💡 ملاحظات:
echo ===========
echo • إذا ظهرت رسالة "قيد التطوير" فهناك مشكلة في الربط
echo • إذا لم تفتح النافذة فتحقق من وجود الكلاس
echo • إذا ظهرت أخطاء فتحقق من قاعدة البيانات
echo • النافذة تحتاج اتصال بقاعدة البيانات للعمل الكامل

echo.
echo ========================================
echo ✅ تم إصلاح نافذة إدارة العملات!
echo Currency Management Window Fixed!
echo ========================================

echo.
pause
