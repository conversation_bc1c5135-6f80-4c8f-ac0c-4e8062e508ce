import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.sql.SQLException;

/**
 * أداة بسيطة لإنشاء جداول نظام إدارة القوالب
 */
public class CreateEmailTemplatesTablesSimple {

    public static void main(String[] args) {
        System.out.println("=== إنشاء جداول نظام إدارة القوالب ===");
        
        try {
            createTables();
            System.out.println("✅ تم إنشاء جميع الجداول بنجاح!");
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void createTables() throws SQLException, ClassNotFoundException {
        // تحميل driver
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // الاتصال بقاعدة البيانات
        String url = "*************************************";
        String username = "SHIP_ERP";
        String password = "ship_erp_password";
        
        try (Connection connection = DriverManager.getConnection(url, username, password);
             Statement stmt = connection.createStatement()) {
            
            System.out.println("🔗 تم الاتصال بقاعدة البيانات بنجاح");
            
            // 1. إنشاء جدول فئات القوالب
            System.out.println("📁 إنشاء جدول فئات القوالب...");
            executeSQL(stmt, """
                CREATE TABLE EMAIL_TEMPLATE_CATEGORIES (
                    CATEGORY_ID NUMBER PRIMARY KEY,
                    CATEGORY_NAME_AR NVARCHAR2(100) NOT NULL,
                    CATEGORY_NAME_EN VARCHAR2(100) NOT NULL,
                    DESCRIPTION NVARCHAR2(500),
                    ICON_NAME VARCHAR2(50),
                    COLOR_CODE VARCHAR2(7),
                    SORT_ORDER NUMBER DEFAULT 0,
                    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
                    CREATED_DATE DATE DEFAULT SYSDATE
                )
                """, "EMAIL_TEMPLATE_CATEGORIES");
            
            // 2. إنشاء sequence للفئات
            executeSQL(stmt, """
                CREATE SEQUENCE SEQ_EMAIL_TEMPLATE_CATEGORIES
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                """, "SEQ_EMAIL_TEMPLATE_CATEGORIES");
            
            // 3. إنشاء جدول القوالب
            System.out.println("📧 إنشاء جدول القوالب...");
            executeSQL(stmt, """
                CREATE TABLE EMAIL_TEMPLATES (
                    TEMPLATE_ID NUMBER PRIMARY KEY,
                    TEMPLATE_NAME_AR NVARCHAR2(200) NOT NULL,
                    TEMPLATE_NAME_EN VARCHAR2(200) NOT NULL,
                    CATEGORY_ID NUMBER,
                    SUBJECT_AR NVARCHAR2(500),
                    SUBJECT_EN VARCHAR2(500),
                    BODY_HTML CLOB,
                    BODY_TEXT CLOB,
                    TEMPLATE_TYPE VARCHAR2(20) DEFAULT 'STANDARD',
                    LANGUAGE_CODE VARCHAR2(5) DEFAULT 'AR',
                    IS_HTML CHAR(1) DEFAULT 'Y',
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    USAGE_COUNT NUMBER DEFAULT 0,
                    LAST_USED_DATE DATE,
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CONSTRAINT FK_TEMPLATE_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES EMAIL_TEMPLATE_CATEGORIES(CATEGORY_ID)
                )
                """, "EMAIL_TEMPLATES");
            
            // 4. إنشاء sequence للقوالب
            executeSQL(stmt, """
                CREATE SEQUENCE SEQ_EMAIL_TEMPLATES
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                """, "SEQ_EMAIL_TEMPLATES");
            
            // 5. إدراج فئات افتراضية
            System.out.println("📝 إدراج البيانات الافتراضية...");
            insertDefaultData(stmt);
            
            System.out.println("✅ تم إنشاء جميع الجداول والبيانات الافتراضية");
        }
    }
    
    private static void executeSQL(Statement stmt, String sql, String objectName) {
        try {
            stmt.execute(sql);
            System.out.println("✅ تم إنشاء: " + objectName);
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) { // ORA-00955: name is already used
                System.out.println("⚠️  موجود مسبقاً: " + objectName);
            } else {
                System.err.println("❌ خطأ في إنشاء " + objectName + ": " + e.getMessage());
            }
        }
    }
    
    private static void insertDefaultData(Statement stmt) throws SQLException {
        String[] categories = {
            "INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, SORT_ORDER) VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'عام', 'General', 'قوالب عامة للاستخدام اليومي', 1)",
            "INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, SORT_ORDER) VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'تسويق', 'Marketing', 'قوالب الحملات التسويقية', 2)",
            "INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, SORT_ORDER) VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'إشعارات', 'Notifications', 'قوالب الإشعارات والتنبيهات', 3)",
            "INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, SORT_ORDER) VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'فواتير', 'Invoices', 'قوالب الفواتير والمعاملات المالية', 4)",
            "INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, SORT_ORDER) VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'ترحيب', 'Welcome', 'قوالب الترحيب بالعملاء الجدد', 5)",
            "INSERT INTO EMAIL_TEMPLATE_CATEGORIES (CATEGORY_ID, CATEGORY_NAME_AR, CATEGORY_NAME_EN, DESCRIPTION, SORT_ORDER) VALUES (SEQ_EMAIL_TEMPLATE_CATEGORIES.NEXTVAL, 'تأكيد', 'Confirmation', 'قوالب تأكيد العمليات', 6)"
        };
        
        for (String sql : categories) {
            try {
                stmt.execute(sql);
                System.out.println("✅ تم إدراج فئة");
            } catch (SQLException e) {
                if (e.getErrorCode() != 1) { // ORA-00001: unique constraint violated
                    System.err.println("❌ خطأ في إدراج البيانات: " + e.getMessage());
                }
            }
        }
        
        // إدراج قوالب تجريبية
        String[] templates = {
            "INSERT INTO EMAIL_TEMPLATES (TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN, CATEGORY_ID, SUBJECT_AR, BODY_HTML, TEMPLATE_TYPE) VALUES (SEQ_EMAIL_TEMPLATES.NEXTVAL, 'قالب ترحيب أساسي', 'Basic Welcome Template', 5, 'مرحباً بك معنا!', '<h2>مرحباً بك!</h2><p>نحن سعداء بانضمامك إلينا.</p>', 'STANDARD')",
            "INSERT INTO EMAIL_TEMPLATES (TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN, CATEGORY_ID, SUBJECT_AR, BODY_HTML, TEMPLATE_TYPE) VALUES (SEQ_EMAIL_TEMPLATES.NEXTVAL, 'قالب إشعار عام', 'General Notification', 3, 'إشعار مهم', '<h3>إشعار</h3><p>لديك إشعار جديد.</p>', 'NOTIFICATION')"
        };
        
        for (String sql : templates) {
            try {
                stmt.execute(sql);
                System.out.println("✅ تم إدراج قالب");
            } catch (SQLException e) {
                if (e.getErrorCode() != 1) {
                    System.err.println("❌ خطأ في إدراج القالب: " + e.getMessage());
                }
            }
        }
    }
}
