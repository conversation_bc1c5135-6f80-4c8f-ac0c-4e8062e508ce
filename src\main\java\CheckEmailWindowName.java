import java.sql.*;

/**
 * التحقق من اسم نافذة إدارة حسابات البريد في قاعدة البيانات
 */
public class CheckEmailWindowName {
    
    public static void main(String[] args) {
        System.out.println("🔍 البحث عن نافذة إدارة حسابات البريد في قاعدة البيانات...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            // البحث عن نافذة إدارة حسابات البريد
            String sql = """
                SELECT TREE_ID, NODE_NAME_AR, NODE_NAME_EN, WINDOW_CLASS, NODE_DESCRIPTION
                FROM ERP_SYSTEM_TREE 
                WHERE NODE_NAME_AR LIKE '%حسابات البريد%' 
                OR NODE_NAME_AR LIKE '%إدارة حسابات البريد%'
                OR NODE_NAME_EN LIKE '%Email Accounts%'
                OR WINDOW_CLASS LIKE '%Email%'
                ORDER BY NODE_NAME_AR
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                System.out.println("\n📧 نوافذ البريد الإلكتروني الموجودة:");
                System.out.println("=" .repeat(80));
                
                boolean found = false;
                while (rs.next()) {
                    found = true;
                    System.out.println("🔹 المعرف: " + rs.getInt("TREE_ID"));
                    System.out.println("   الاسم العربي: " + rs.getString("NODE_NAME_AR"));
                    System.out.println("   الاسم الإنجليزي: " + rs.getString("NODE_NAME_EN"));
                    System.out.println("   كلاس النافذة: " + rs.getString("WINDOW_CLASS"));
                    System.out.println("   الوصف: " + rs.getString("NODE_DESCRIPTION"));
                    System.out.println("-" .repeat(60));
                }
                
                if (!found) {
                    System.out.println("❌ لم يتم العثور على نوافذ البريد الإلكتروني");
                }
            }
            
            connection.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في البحث: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
