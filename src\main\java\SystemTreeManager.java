import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;

/**
 * مدير شجرة النظام - System Tree Manager يدير شجرة الأنظمة والنوافذ من قاعدة البيانات
 */
public class SystemTreeManager {

    private static SystemTreeManager instance;
    private Connection connection;
    private TNSConnectionManager tnsManager;

    // أنواع العقد
    public enum NodeType {
        CATEGORY("CATEGORY"), WINDOW("WINDOW"), REPORT("REPORT"), TOOL("TOOL");

        private final String value;

        NodeType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    private SystemTreeManager() {
        try {
            tnsManager = TNSConnectionManager.getInstance();
            connection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم تهيئة مدير شجرة النظام بنجاح");
        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة مدير شجرة النظام: " + e.getMessage());
        }
    }

    public static synchronized SystemTreeManager getInstance() {
        if (instance == null) {
            instance = new SystemTreeManager();
        }
        return instance;
    }

    /**
     * الحصول على شجرة النظام الكاملة
     */
    public DefaultTreeModel getSystemTreeModel() {
        try {
            List<SystemTreeNode> nodes = loadAllNodes();
            DefaultMutableTreeNode root = buildTreeStructure(nodes);
            return new DefaultTreeModel(root);
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحميل شجرة النظام: " + e.getMessage());
            return createDefaultTree();
        }
    }

    /**
     * تحميل جميع عقد الشجرة من قاعدة البيانات
     */
    private List<SystemTreeNode> loadAllNodes() throws SQLException {
        List<SystemTreeNode> nodes = new ArrayList<>();

        String sql = """
                    SELECT
                        TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN,
                        NODE_DESCRIPTION, NODE_TYPE, WINDOW_CLASS, ICON_PATH,
                        DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE
                    FROM ERP_SYSTEM_TREE
                    WHERE IS_ACTIVE = 'Y' AND IS_VISIBLE = 'Y'
                    ORDER BY TREE_LEVEL, DISPLAY_ORDER
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql);
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                SystemTreeNode node = new SystemTreeNode();
                node.treeId = rs.getInt("TREE_ID");
                node.parentId = rs.getObject("PARENT_ID") != null ? rs.getInt("PARENT_ID") : null;
                node.nodeNameAr = rs.getString("NODE_NAME_AR");
                node.nodeNameEn = rs.getString("NODE_NAME_EN");
                node.nodeDescription = rs.getString("NODE_DESCRIPTION");
                node.nodeType = NodeType.valueOf(rs.getString("NODE_TYPE"));
                node.windowClass = rs.getString("WINDOW_CLASS");
                node.iconPath = rs.getString("ICON_PATH");
                node.displayOrder = rs.getInt("DISPLAY_ORDER");
                node.treeLevel = rs.getInt("TREE_LEVEL");
                node.isActive = "Y".equals(rs.getString("IS_ACTIVE"));
                node.isVisible = "Y".equals(rs.getString("IS_VISIBLE"));

                nodes.add(node);
            }
        }

        System.out.println("✅ تم تحميل " + nodes.size() + " عقدة من شجرة النظام");
        return nodes;
    }

    /**
     * بناء هيكل الشجرة من قائمة العقد
     */
    private DefaultMutableTreeNode buildTreeStructure(List<SystemTreeNode> nodes) {
        Map<Integer, DefaultMutableTreeNode> nodeMap = new HashMap<>();
        DefaultMutableTreeNode root = null;

        // إنشاء عقد الشجرة
        for (SystemTreeNode node : nodes) {
            DefaultMutableTreeNode treeNode = new DefaultMutableTreeNode(node);
            nodeMap.put(node.treeId, treeNode);

            if (node.parentId == null) {
                root = treeNode;
            }
        }

        // ربط العقد بآبائها
        for (SystemTreeNode node : nodes) {
            if (node.parentId != null) {
                DefaultMutableTreeNode parentNode = nodeMap.get(node.parentId);
                DefaultMutableTreeNode childNode = nodeMap.get(node.treeId);

                if (parentNode != null && childNode != null) {
                    parentNode.add(childNode);
                }
            }
        }

        return root != null ? root : new DefaultMutableTreeNode("نظام إدارة الشحنات");
    }

    /**
     * إضافة عقدة جديدة إلى الشجرة
     */
    public boolean addNode(SystemTreeNode node) {
        try {
            String sql = """
                        INSERT INTO ERP_SYSTEM_TREE (
                            PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
                            NODE_TYPE, WINDOW_CLASS, ICON_PATH, DISPLAY_ORDER, TREE_LEVEL
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setObject(1, node.parentId);
                stmt.setString(2, node.nodeNameAr);
                stmt.setString(3, node.nodeNameEn);
                stmt.setString(4, node.nodeDescription);
                stmt.setString(5, node.nodeType.getValue());
                stmt.setString(6, node.windowClass);
                stmt.setString(7, node.iconPath);
                stmt.setInt(8, node.displayOrder);
                stmt.setInt(9, node.treeLevel);

                int result = stmt.executeUpdate();
                connection.commit();

                System.out.println("✅ تم إضافة عقدة جديدة: " + node.nodeNameAr);
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إضافة العقدة: " + e.getMessage());
            try {
                connection.rollback();
            } catch (SQLException ex) {
                System.err.println("❌ خطأ في التراجع: " + ex.getMessage());
            }
            return false;
        }
    }

    /**
     * تحديث عقدة موجودة
     */
    public boolean updateNode(SystemTreeNode node) {
        try {
            String sql = """
                        UPDATE ERP_SYSTEM_TREE SET
                            NODE_NAME_AR = ?, NODE_NAME_EN = ?, NODE_DESCRIPTION = ?,
                            NODE_TYPE = ?, WINDOW_CLASS = ?, ICON_PATH = ?,
                            DISPLAY_ORDER = ?, IS_ACTIVE = ?, IS_VISIBLE = ?
                        WHERE TREE_ID = ?
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, node.nodeNameAr);
                stmt.setString(2, node.nodeNameEn);
                stmt.setString(3, node.nodeDescription);
                stmt.setString(4, node.nodeType.getValue());
                stmt.setString(5, node.windowClass);
                stmt.setString(6, node.iconPath);
                stmt.setInt(7, node.displayOrder);
                stmt.setString(8, node.isActive ? "Y" : "N");
                stmt.setString(9, node.isVisible ? "Y" : "N");
                stmt.setInt(10, node.treeId);

                int result = stmt.executeUpdate();
                connection.commit();

                System.out.println("✅ تم تحديث العقدة: " + node.nodeNameAr);
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحديث العقدة: " + e.getMessage());
            return false;
        }
    }

    /**
     * حذف عقدة (إلغاء تفعيل)
     */
    public boolean deleteNode(int treeId) {
        try {
            String sql = "UPDATE ERP_SYSTEM_TREE SET IS_ACTIVE = 'N' WHERE TREE_ID = ?";

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setInt(1, treeId);

                int result = stmt.executeUpdate();
                connection.commit();

                System.out.println("✅ تم حذف العقدة: " + treeId);
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في حذف العقدة: " + e.getMessage());
            return false;
        }
    }

    /**
     * البحث عن عقدة بالاسم
     */
    public SystemTreeNode findNodeByName(String nodeName) {
        try {
            String sql = """
                        SELECT * FROM ERP_SYSTEM_TREE
                        WHERE (NODE_NAME_AR = ? OR NODE_NAME_EN = ?)
                        AND IS_ACTIVE = 'Y'
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, nodeName);
                stmt.setString(2, nodeName);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        SystemTreeNode node = new SystemTreeNode();
                        node.treeId = rs.getInt("TREE_ID");
                        node.nodeNameAr = rs.getString("NODE_NAME_AR");
                        node.nodeNameEn = rs.getString("NODE_NAME_EN");
                        node.windowClass = rs.getString("WINDOW_CLASS");
                        return node;
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في البحث عن العقدة: " + e.getMessage());
        }
        return null;
    }

    /**
     * تسجيل نافذة جديدة تلقائياً
     */
    public boolean registerNewWindow(String windowClass, String nameAr, String nameEn,
            String description, String category) {
        try {
            // البحث عن الفئة الأب
            SystemTreeNode parentNode = findNodeByName(category);
            if (parentNode == null) {
                System.err.println("❌ لم يتم العثور على الفئة الأب: " + category);
                return false;
            }

            // التحقق من عدم وجود النافذة مسبقاً
            SystemTreeNode existingNode = findNodeByName(nameEn);
            if (existingNode != null) {
                System.out.println("⚠️ النافذة موجودة مسبقاً: " + nameEn);
                return true;
            }

            // إنشاء عقدة جديدة
            SystemTreeNode newNode = new SystemTreeNode();
            newNode.parentId = parentNode.treeId;
            newNode.nodeNameAr = nameAr;
            newNode.nodeNameEn = nameEn;
            newNode.nodeDescription = description;
            newNode.nodeType = NodeType.WINDOW;
            newNode.windowClass = windowClass;
            newNode.displayOrder = getNextDisplayOrder(parentNode.treeId);
            newNode.treeLevel = parentNode.treeLevel + 1;

            return addNode(newNode);

        } catch (Exception e) {
            System.err.println("❌ خطأ في تسجيل النافذة الجديدة: " + e.getMessage());
            return false;
        }
    }

    /**
     * الحصول على الترتيب التالي للعرض
     */
    private int getNextDisplayOrder(int parentId) {
        try {
            String sql = "SELECT MAX(DISPLAY_ORDER) FROM ERP_SYSTEM_TREE WHERE PARENT_ID = ?";

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setInt(1, parentId);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getInt(1) + 1;
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في الحصول على الترتيب التالي: " + e.getMessage());
        }
        return 1;
    }

    /**
     * إنشاء شجرة افتراضية في حالة الخطأ
     */
    private DefaultTreeModel createDefaultTree() {
        DefaultMutableTreeNode root = new DefaultMutableTreeNode("نظام إدارة الشحنات");

        DefaultMutableTreeNode itemsNode = new DefaultMutableTreeNode("إدارة الأصناف");
        itemsNode.add(new DefaultMutableTreeNode("بيانات الأصناف الحقيقية"));
        itemsNode.add(new DefaultMutableTreeNode("بيانات الأصناف الشاملة"));
        itemsNode.add(new DefaultMutableTreeNode("مجموعات الأصناف"));
        itemsNode.add(new DefaultMutableTreeNode("وحدات القياس"));

        DefaultMutableTreeNode usersNode = new DefaultMutableTreeNode("إدارة المستخدمين");
        usersNode.add(new DefaultMutableTreeNode("إدارة المستخدمين"));

        DefaultMutableTreeNode settingsNode = new DefaultMutableTreeNode("الإعدادات العامة");
        settingsNode.add(new DefaultMutableTreeNode("الإعدادات العامة"));

        root.add(itemsNode);
        root.add(usersNode);
        root.add(settingsNode);

        return new DefaultTreeModel(root);
    }

    /**
     * طباعة شجرة النظام
     */
    public void printSystemTree() {
        try {
            String sql = """
                        SELECT
                            TREE_ID, PARENT_ID,
                            LPAD(' ', (TREE_LEVEL * 4)) || NODE_NAME_AR AS TREE_DISPLAY,
                            NODE_TYPE, WINDOW_CLASS, TREE_LEVEL, DISPLAY_ORDER
                        FROM ERP_SYSTEM_TREE
                        WHERE IS_ACTIVE = 'Y' AND IS_VISIBLE = 'Y'
                        START WITH PARENT_ID IS NULL
                        CONNECT BY PRIOR TREE_ID = PARENT_ID
                        ORDER SIBLINGS BY DISPLAY_ORDER
                    """;

            System.out.println("\n📊 شجرة النظام الحالية:");
            System.out.println("=" + "=".repeat(60));

            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                while (rs.next()) {
                    String display = rs.getString("TREE_DISPLAY");
                    String type = rs.getString("NODE_TYPE");
                    String windowClass = rs.getString("WINDOW_CLASS");

                    System.out.println(display + " [" + type + "]"
                            + (windowClass != null ? " -> " + windowClass : ""));
                }
            }

            System.out.println("=" + "=".repeat(60));

        } catch (SQLException e) {
            System.err.println("❌ خطأ في طباعة شجرة النظام: " + e.getMessage());
        }
    }

    /**
     * كلاس عقدة شجرة النظام
     */
    public static class SystemTreeNode {
        public int treeId;
        public Integer parentId;
        public String nodeNameAr;
        public String nodeNameEn;
        public String nodeDescription;
        public NodeType nodeType;
        public String windowClass;
        public String iconPath;
        public int displayOrder;
        public int treeLevel;
        public boolean isActive = true;
        public boolean isVisible = true;

        @Override
        public String toString() {
            return nodeNameAr != null ? nodeNameAr : nodeNameEn;
        }
    }

    /**
     * إغلاق الاتصال
     */
    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("✅ تم إغلاق اتصال مدير شجرة النظام");
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إغلاق الاتصال: " + e.getMessage());
        }
    }

    /**
     * اختبار مدير شجرة النظام
     */
    public static void main(String[] args) {
        System.out.println("🔍 اختبار مدير شجرة النظام...");

        try {
            SystemTreeManager manager = SystemTreeManager.getInstance();

            // طباعة شجرة النظام
            manager.printSystemTree();

            // اختبار البحث عن عقدة
            SystemTreeNode node = manager.findNodeByName("Items Management");
            if (node != null) {
                System.out.println("✅ تم العثور على عقدة: " + node.nodeNameAr);
            }

            // إغلاق الاتصال
            manager.close();

            System.out.println("✅ اختبار مدير شجرة النظام مكتمل");

        } catch (Exception e) {
            System.err.println("❌ خطأ في اختبار مدير شجرة النظام: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
