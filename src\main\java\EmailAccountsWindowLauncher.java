import javax.swing.*;

/**
 * مشغل نافذة إدارة حسابات البريد الإلكتروني
 * Email Accounts Window Launcher
 */
public class EmailAccountsWindowLauncher {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق مظهر حديث
                UIManager.setLookAndFeel("com.formdev.flatlaf.FlatDarkLaf");
            } catch (Exception e) {
                try {
                    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
                } catch (Exception ex) {
                    System.err.println("تعذر تطبيق المظهر: " + ex.getMessage());
                }
            }
            
            // إنشاء وعرض النافذة
            try {
                EmailAccountsWindow window = new EmailAccountsWindow();
                window.setVisible(true);
                
                System.out.println("🎉 تم تشغيل نافذة إدارة حسابات البريد الإلكتروني بنجاح!");
                
            } catch (Exception e) {
                System.err.println("❌ خطأ في تشغيل النافذة: " + e.getMessage());
                e.printStackTrace();
                
                JOptionPane.showMessageDialog(null, 
                    "خطأ في تشغيل نافذة إدارة حسابات البريد الإلكتروني:\n" + e.getMessage(),
                    "خطأ في التشغيل", 
                    JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
