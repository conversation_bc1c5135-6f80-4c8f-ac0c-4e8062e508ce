package com.shipment.erp.service;

import com.shipment.erp.model.Currency;
import java.math.BigDecimal;
import java.util.List;

/**
 * خدمة إدارة العملات
 * Currency Management Service
 */
public interface CurrencyService extends BaseService<Currency> {
    
    /**
     * البحث عن العملات النشطة
     * Find active currencies
     */
    List<Currency> findActiveCurrencies();
    
    /**
     * البحث عن عملة بالكود
     * Find currency by code
     */
    Currency findByCode(String code);
    
    /**
     * الحصول على العملة الافتراضية
     * Get default currency
     */
    Currency getDefaultCurrency();
    
    /**
     * تعيين العملة الافتراضية
     * Set default currency
     */
    void setDefaultCurrency(Long currencyId);
    
    /**
     * التحقق من وجود كود العملة
     * Check if currency code exists
     */
    boolean existsByCode(String code);
    
    /**
     * تحديث سعر الصرف
     * Update exchange rate
     */
    void updateExchangeRate(Long currencyId, BigDecimal newRate);
    
    /**
     * تحويل مبلغ من عملة إلى أخرى
     * Convert amount from one currency to another
     */
    BigDecimal convertAmount(BigDecimal amount, String fromCurrency, String toCurrency);
    
    /**
     * تفعيل/إلغاء تفعيل العملة
     * Activate/Deactivate currency
     */
    void toggleActive(Long currencyId);
    
    /**
     * الحصول على العملات الرئيسية
     * Get major currencies
     */
    List<Currency> getMajorCurrencies();
    
    /**
     * تحديث أسعار الصرف من مصدر خارجي
     * Update exchange rates from external source
     */
    void updateExchangeRatesFromSource();
}
