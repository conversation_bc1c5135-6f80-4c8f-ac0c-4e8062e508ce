import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة بيانات الأصناف الشاملة Comprehensive Item Data Window
 * 
 * نافذة متقدمة لعرض وإدارة بيانات الأصناف من جداول IAS_ITM_MST و IAS_ITM_DTL
 */
public class ComprehensiveItemDataWindow extends JFrame {

    private static final int WINDOW_WIDTH = 1200;
    private static final int WINDOW_HEIGHT = 800;

    // مكونات الواجهة
    private JTabbedPane tabbedPane;
    private JTable itemsTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JLabel statusLabel;
    private JLabel statsLabel;

    // قاعدة البيانات
    private Connection connection;

    // إحصائيات
    private int totalItems = 0;
    private int activeItems = 0;
    private int serviceItems = 0;
    private int totalUnits = 0;

    public ComprehensiveItemDataWindow() {
        initializeWindow();
        initializeComponents();
        setupLayout();
        connectToDatabase();
        loadData();
        setupEventHandlers();
    }

    private void initializeWindow() {
        setTitle("نافذة بيانات الأصناف الشاملة - Comprehensive Item Data Window");
        setSize(WINDOW_WIDTH, WINDOW_HEIGHT);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        // إعداد الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("Table.font", arabicFont);
        UIManager.put("TabbedPane.font", arabicFont);

        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void initializeComponents() {
        // إنشاء التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // حقل البحث
        searchField = new JTextField(20);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تسميات الحالة والإحصائيات
        statusLabel = new JLabel("جاري التحميل...");
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        statsLabel = new JLabel("الإحصائيات: ");
        statsLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // جدول البيانات
        String[] columnNames = {"كود الصنف", "اسم الصنف", "الاسم الإنجليزي", "كود المجموعة",
                "سعر التكلفة", "نوع الصنف", "الحالة", "صنف خدمة", "الوحدة الرئيسية"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        itemsTable = new JTable(tableModel);
        itemsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        itemsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        itemsTable.setRowHeight(25);

        // إعداد عرض الأعمدة
        itemsTable.getColumnModel().getColumn(0).setPreferredWidth(100); // كود الصنف
        itemsTable.getColumnModel().getColumn(1).setPreferredWidth(200); // اسم الصنف
        itemsTable.getColumnModel().getColumn(2).setPreferredWidth(200); // الاسم الإنجليزي
        itemsTable.getColumnModel().getColumn(3).setPreferredWidth(80); // كود المجموعة
        itemsTable.getColumnModel().getColumn(4).setPreferredWidth(100); // سعر التكلفة
        itemsTable.getColumnModel().getColumn(5).setPreferredWidth(80); // نوع الصنف
        itemsTable.getColumnModel().getColumn(6).setPreferredWidth(60); // الحالة
        itemsTable.getColumnModel().getColumn(7).setPreferredWidth(80); // صنف خدمة
        itemsTable.getColumnModel().getColumn(8).setPreferredWidth(100); // الوحدة الرئيسية
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // لوحة البحث العلوية
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchPanel.add(new JLabel("البحث:"));
        searchPanel.add(searchField);

        JButton searchButton = new JButton("بحث");
        searchButton.addActionListener(e -> performSearch());
        searchPanel.add(searchButton);

        JButton refreshButton = new JButton("تحديث");
        refreshButton.addActionListener(e -> loadData());
        searchPanel.add(refreshButton);

        add(searchPanel, BorderLayout.NORTH);

        // التبويب الرئيسي للبيانات
        JScrollPane scrollPane = new JScrollPane(itemsTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.addTab("بيانات الأصناف الأساسية", scrollPane);

        // تبويب الإحصائيات
        JPanel statsPanel = createStatsPanel();
        tabbedPane.addTab("الإحصائيات والتقارير", statsPanel);

        add(tabbedPane, BorderLayout.CENTER);

        // لوحة الحالة السفلية
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.add(statsLabel, BorderLayout.EAST);
        statusPanel.setBorder(BorderFactory.createEtchedBorder());

        add(statusPanel, BorderLayout.SOUTH);
    }

    private JPanel createStatsPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;

        // إحصائيات الأصناف
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("إجمالي الأصناف:"), gbc);
        gbc.gridx = 1;
        JLabel totalItemsLabel = new JLabel("0");
        panel.add(totalItemsLabel, gbc);

        gbc.gridx = 0;
        gbc.gridy = 1;
        panel.add(new JLabel("الأصناف النشطة:"), gbc);
        gbc.gridx = 1;
        JLabel activeItemsLabel = new JLabel("0");
        panel.add(activeItemsLabel, gbc);

        gbc.gridx = 0;
        gbc.gridy = 2;
        panel.add(new JLabel("أصناف الخدمة:"), gbc);
        gbc.gridx = 1;
        JLabel serviceItemsLabel = new JLabel("0");
        panel.add(serviceItemsLabel, gbc);

        gbc.gridx = 0;
        gbc.gridy = 3;
        panel.add(new JLabel("إجمالي الوحدات:"), gbc);
        gbc.gridx = 1;
        JLabel totalUnitsLabel = new JLabel("0");
        panel.add(totalUnitsLabel, gbc);

        return panel;
    }

    private void connectToDatabase() {
        try {
            // إعداد خصائص Oracle لدعم الترميز العربي
            java.util.Properties props = new java.util.Properties();
            props.setProperty("user", "ias20251");
            props.setProperty("password", "ys123");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");

            String url = "*************************************";
            connection = DriverManager.getConnection(url, props);
            statusLabel.setText("✅ تم الاتصال بقاعدة البيانات بنجاح مع دعم الترميز العربي");

        } catch (SQLException e) {
            statusLabel.setText("❌ خطأ في الاتصال بقاعدة البيانات");
            System.err.println("خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());

            // إنشاء بيانات تجريبية في حالة عدم توفر قاعدة البيانات
            createSampleData();
        }
    }

    private void loadData() {
        if (connection != null) {
            loadDataFromDatabase();
        } else {
            statusLabel.setText("⚠️ يتم عرض بيانات تجريبية - قاعدة البيانات غير متاحة");
        }
        updateStats();
    }

    private void loadDataFromDatabase() {
        try {
            String sql = """
                    SELECT
                        m.I_CODE,
                        m.I_NAME,
                        m.I_E_NAME,
                        m.G_CODE,
                        m.PRIMARY_COST,
                        m.ITEM_TYPE,
                        CASE WHEN m.INACTIVE = 0 THEN 'نشط' ELSE 'غير نشط' END as STATUS,
                        CASE WHEN m.SERVICE_ITM = 1 THEN 'نعم' ELSE 'لا' END as SERVICE_ITEM,
                        d.ITM_UNT as MAIN_UNIT
                    FROM IAS_ITM_MST m
                    LEFT JOIN IAS_ITM_DTL d ON m.I_CODE = d.I_CODE AND d.MAIN_UNIT = 1
                    ORDER BY m.I_CODE
                    """;

            PreparedStatement stmt = connection.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();

            tableModel.setRowCount(0);
            totalItems = 0;
            activeItems = 0;
            serviceItems = 0;

            while (rs.next()) {
                Object[] row =
                        {rs.getString("I_CODE"), rs.getString("I_NAME"), rs.getString("I_E_NAME"),
                                rs.getString("G_CODE"), rs.getBigDecimal("PRIMARY_COST"),
                                rs.getString("ITEM_TYPE"), rs.getString("STATUS"),
                                rs.getString("SERVICE_ITEM"), rs.getString("MAIN_UNIT")};
                tableModel.addRow(row);

                totalItems++;
                if ("نشط".equals(rs.getString("STATUS"))) {
                    activeItems++;
                }
                if ("نعم".equals(rs.getString("SERVICE_ITEM"))) {
                    serviceItems++;
                }
            }

            rs.close();
            stmt.close();

            statusLabel.setText("✅ تم تحميل " + totalItems + " صنف من قاعدة البيانات");

        } catch (SQLException e) {
            statusLabel.setText("❌ خطأ في تحميل البيانات: " + e.getMessage());
            System.err.println("خطأ في تحميل البيانات: " + e.getMessage());
            createSampleData();
        }
    }

    private void createSampleData() {
        tableModel.setRowCount(0);

        // بيانات تجريبية
        Object[][] sampleData = {
                {"001", "صنف تجريبي 1", "Sample Item 1", "G001", 100.00, "عادي", "نشط", "لا",
                        "قطعة"},
                {"002", "صنف تجريبي 2", "Sample Item 2", "G002", 150.50, "خدمة", "نشط", "نعم",
                        "ساعة"},
                {"003", "صنف تجريبي 3", "Sample Item 3", "G001", 75.25, "عادي", "غير نشط", "لا",
                        "كيلو"}};

        for (Object[] row : sampleData) {
            tableModel.addRow(row);
        }

        totalItems = sampleData.length;
        activeItems = 2;
        serviceItems = 1;
        totalUnits = 3;
    }

    private void performSearch() {
        String searchText = searchField.getText().trim();
        if (searchText.isEmpty()) {
            loadData();
            return;
        }

        // تطبيق فلتر البحث على الجدول
        // هذا تطبيق مبسط - يمكن تحسينه لاحقاً
        statusLabel.setText("🔍 البحث عن: " + searchText);
    }

    private void updateStats() {
        statsLabel.setText(String.format(
                "📊 الإحصائيات: إجمالي الأصناف: %d | النشطة: %d | أصناف الخدمة: %d | الوحدات: %d",
                totalItems, activeItems, serviceItems, totalUnits));
    }

    private void setupEventHandlers() {
        // معالج البحث عند الضغط على Enter
        searchField.addActionListener(e -> performSearch());

        // معالج اختيار صف في الجدول
        itemsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = itemsTable.getSelectedRow();
                if (selectedRow >= 0) {
                    String itemCode = (String) tableModel.getValueAt(selectedRow, 0);
                    statusLabel.setText("تم اختيار الصنف: " + itemCode);
                }
            }
        });

        // معالج إغلاق النافذة
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                if (connection != null) {
                    try {
                        connection.close();
                    } catch (SQLException ex) {
                        System.err.println("خطأ في إغلاق الاتصال: " + ex.getMessage());
                    }
                }
            }
        });
    }

    // طريقة main للاختبار المستقل
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new ComprehensiveItemDataWindow().setVisible(true);
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, "خطأ في تشغيل النافذة: " + e.getMessage());
            }
        });
    }
}
