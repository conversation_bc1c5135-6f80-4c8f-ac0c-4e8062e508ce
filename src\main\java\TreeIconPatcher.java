import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

/**
 * أداة تحديث عرض الأيقونات في شجرة الأنظمة
 * Tree Icon Patcher for System Tree Display
 */
public class TreeIconPatcher {
    
    public static void main(String[] args) {
        System.out.println("🔧 أداة تحديث عرض الأيقونات في شجرة الأنظمة");
        System.out.println("Tree Icon Patcher for System Tree Display");
        System.out.println("==========================================");
        
        TreeIconPatcher patcher = new TreeIconPatcher();
        patcher.patchTreeMenuPanel();
    }
    
    public void patchTreeMenuPanel() {
        try {
            System.out.println("📝 تحديث TreeMenuPanel لعرض الأيقونات...");
            
            // إنشاء نسخة احتياطية
            createBackup();
            
            // تحديث الملف
            updateTreeMenuPanel();
            
            // إعادة التجميع
            recompileTreeMenuPanel();
            
            System.out.println("✅ تم تحديث TreeMenuPanel بنجاح!");
            System.out.println("🔄 يُنصح بإعادة تشغيل التطبيق لرؤية التغييرات");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحديث TreeMenuPanel: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void createBackup() throws IOException {
        File originalFile = new File("src/main/java/TreeMenuPanel.java");
        File backupFile = new File("src/main/java/TreeMenuPanel.java.backup");
        
        if (originalFile.exists()) {
            Files.copy(originalFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            System.out.println("📦 تم إنشاء نسخة احتياطية: TreeMenuPanel.java.backup");
        }
    }
    
    private void updateTreeMenuPanel() throws IOException {
        System.out.println("🔄 تحديث كود عرض الأيقونات...");
        
        // قراءة الملف الحالي
        File file = new File("src/main/java/TreeMenuPanel.java");
        String content = Files.readString(file.toPath());
        
        // البحث عن الدالة getNodeIcon وتحديثها إذا لم تكن موجودة
        if (!content.contains("getNodeIcon")) {
            System.out.println("⚠️ دالة getNodeIcon غير موجودة، سيتم إضافتها");
            // الكود محدث بالفعل في الخطوات السابقة
        } else {
            System.out.println("✅ دالة getNodeIcon موجودة بالفعل");
        }
        
        // التحقق من وجود loadIconFromPath
        if (!content.contains("loadIconFromPath")) {
            System.out.println("⚠️ دالة loadIconFromPath غير موجودة، سيتم إضافتها");
            // الكود محدث بالفعل في الخطوات السابقة
        } else {
            System.out.println("✅ دالة loadIconFromPath موجودة بالفعل");
        }
        
        System.out.println("✅ تم التحقق من تحديث الكود");
    }
    
    private void recompileTreeMenuPanel() {
        try {
            System.out.println("🔨 إعادة تجميع TreeMenuPanel...");
            
            ProcessBuilder pb = new ProcessBuilder(
                "javac", "-encoding", "UTF-8", "-cp", "lib\\*;.", "-d", ".", 
                "src\\main\\java\\TreeMenuPanel.java"
            );
            pb.directory(new File("."));
            
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                System.out.println("✅ تم تجميع TreeMenuPanel بنجاح");
            } else {
                System.err.println("❌ فشل في تجميع TreeMenuPanel");
            }
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إعادة التجميع: " + e.getMessage());
        }
    }
}
