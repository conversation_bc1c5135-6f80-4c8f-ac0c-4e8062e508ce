import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * استعادة شجرة الأنظمة المفقودة Restore Missing System Tree
 */
public class RestoreSystemTree {

    public static void main(String[] args) {
        System.out.println("🔧 استعادة شجرة الأنظمة المفقودة...");

        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();

            restoreMissingCategories(connection);
            restoreMissingWindows(connection);

            connection.close();
            System.out.println("✅ تم استعادة شجرة الأنظمة بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ في استعادة شجرة الأنظمة: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void restoreMissingCategories(Connection connection) throws SQLException {
        System.out.println("📋 استعادة الفئات المفقودة...");

        // قائمة الفئات المطلوبة
        String[][] categories = {
                {"إدارة الشحنات", "Shipment Management", "إدارة شاملة لعمليات الشحن والنقل"},
                {"إدارة العملاء", "Customer Management", "إدارة بيانات العملاء والعلاقات"},
                {"إدارة الموردين", "Supplier Management", "إدارة الموردين والمشتريات"},
                {"إدارة المخازن", "Warehouse Management", "إدارة المخازن والمخزون"},
                {"الحسابات والمالية", "Accounting & Finance", "النظام المحاسبي والمالي"},
                {"إدارة الموظفين", "Employee Management", "إدارة الموارد البشرية والموظفين"},
                {"التقارير والإحصائيات", "Reports & Statistics", "التقارير والإحصائيات الشاملة"}};

        for (String[] category : categories) {
            String nameAr = category[0];
            String nameEn = category[1];
            String description = category[2];

            if (!categoryExists(connection, nameAr)) {
                createCategory(connection, nameAr, nameEn, description);
            } else {
                System.out.println("ℹ️ الفئة موجودة مسبقاً: " + nameAr);
            }
        }
    }

    private static void restoreMissingWindows(Connection connection) throws SQLException {
        System.out.println("📋 استعادة النوافذ المفقودة...");

        // نوافذ إدارة الشحنات
        int shipmentCategoryId = getCategoryId(connection, "إدارة الشحنات");
        if (shipmentCategoryId > 0) {
            addShipmentWindows(connection, shipmentCategoryId);
        }

        // نوافذ إدارة العملاء
        int customerCategoryId = getCategoryId(connection, "إدارة العملاء");
        if (customerCategoryId > 0) {
            addCustomerWindows(connection, customerCategoryId);
        }

        // نوافذ إدارة الموردين
        int supplierCategoryId = getCategoryId(connection, "إدارة الموردين");
        if (supplierCategoryId > 0) {
            addSupplierWindows(connection, supplierCategoryId);
        }

        // نوافذ إدارة المخازن
        int warehouseCategoryId = getCategoryId(connection, "إدارة المخازن");
        if (warehouseCategoryId > 0) {
            addWarehouseWindows(connection, warehouseCategoryId);
        }

        // نوافذ الحسابات والمالية
        int accountingCategoryId = getCategoryId(connection, "الحسابات والمالية");
        if (accountingCategoryId > 0) {
            addAccountingWindows(connection, accountingCategoryId);
        }

        // نوافذ إدارة الموظفين
        int employeeCategoryId = getCategoryId(connection, "إدارة الموظفين");
        if (employeeCategoryId > 0) {
            addEmployeeWindows(connection, employeeCategoryId);
        }

        // نوافذ التقارير
        int reportsCategoryId = getCategoryId(connection, "التقارير والإحصائيات");
        if (reportsCategoryId > 0) {
            addReportsWindows(connection, reportsCategoryId);
        }
    }

    private static boolean categoryExists(Connection connection, String categoryName)
            throws SQLException {
        String sql =
                "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        return false;
    }

    private static void createCategory(Connection connection, String nameAr, String nameEn,
            String description) throws SQLException {
        int nextOrder = getNextRootOrder(connection);

        String sql = """
                INSERT INTO ERP_SYSTEM_TREE
                (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE,
                 DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE,
                 CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
                VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, NULL, ?, ?, ?, 'CATEGORY',
                        ?, 1, 'Y', 'Y',
                        SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, nameAr);
            stmt.setString(2, nameEn);
            stmt.setString(3, description);
            stmt.setInt(4, nextOrder);

            int result = stmt.executeUpdate();
            if (result > 0) {
                System.out.println("✅ تم إنشاء الفئة: " + nameAr);
            }
        }
    }

    private static int getCategoryId(Connection connection, String categoryName)
            throws SQLException {
        String sql =
                "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("TREE_ID");
                }
            }
        }
        return 0;
    }

    private static int getNextRootOrder(Connection connection) throws SQLException {
        String sql =
                "SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID IS NULL";

        try (PreparedStatement stmt = connection.prepareStatement(sql);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        return 1;
    }

    private static void addWindow(Connection connection, int parentId, String nameAr, String nameEn,
            String windowClass, String description, int order) throws SQLException {

        // التحقق من وجود النافذة مسبقاً
        String checkSQL = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = ?";

        try (PreparedStatement stmt = connection.prepareStatement(checkSQL)) {
            stmt.setString(1, windowClass);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next() && rs.getInt(1) > 0) {
                    return; // النافذة موجودة مسبقاً
                }
            }
        }

        String sql = """
                INSERT INTO ERP_SYSTEM_TREE
                (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE,
                 WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE,
                 CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
                VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, ?, ?, ?, ?, 'WINDOW',
                        ?, ?, 2, 'Y', 'Y',
                        SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            stmt.setString(2, nameAr);
            stmt.setString(3, nameEn);
            stmt.setString(4, description);
            stmt.setString(5, windowClass);
            stmt.setInt(6, order);

            int result = stmt.executeUpdate();
            if (result > 0) {
                System.out.println("✅ تم إضافة النافذة: " + nameAr);
            }
        }
    }

    private static void addShipmentWindows(Connection connection, int parentId)
            throws SQLException {
        String[][] windows = {
                {"إدارة الشحنات", "Shipment Management", "ShipmentManagementWindow",
                        "إدارة شاملة للشحنات"},
                {"تتبع الشحنات", "Shipment Tracking", "ShipmentTrackingWindow",
                        "تتبع حالة الشحنات"},
                {"إدارة الحاويات", "Container Management", "ContainerManagementWindow",
                        "إدارة الحاويات"},
                {"جدولة الشحن", "Shipping Schedule", "ShippingScheduleWindow",
                        "جدولة عمليات الشحن"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static void addCustomerWindows(Connection connection, int parentId)
            throws SQLException {
        String[][] windows = {
                {"إدارة العملاء", "Customer Management", "CustomerManagementWindow",
                        "إدارة بيانات العملاء"},
                {"عقود العملاء", "Customer Contracts", "CustomerContractsWindow",
                        "إدارة عقود العملاء"},
                {"فواتير العملاء", "Customer Invoices", "CustomerInvoicesWindow",
                        "إدارة فواتير العملاء"},
                {"خدمة العملاء", "Customer Service", "CustomerServiceWindow", "خدمة ودعم العملاء"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static void addSupplierWindows(Connection connection, int parentId)
            throws SQLException {
        String[][] windows = {
                {"إدارة الموردين", "Supplier Management", "SupplierManagementWindow",
                        "إدارة بيانات الموردين"},
                {"طلبات الشراء", "Purchase Orders", "PurchaseOrdersWindow", "إدارة طلبات الشراء"},
                {"فواتير الموردين", "Supplier Invoices", "SupplierInvoicesWindow",
                        "إدارة فواتير الموردين"},
                {"تقييم الموردين", "Supplier Evaluation", "SupplierEvaluationWindow",
                        "تقييم أداء الموردين"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static void addWarehouseWindows(Connection connection, int parentId)
            throws SQLException {
        String[][] windows = {
                {"إدارة المخازن", "Warehouse Management", "WarehouseManagementWindow",
                        "إدارة المخازن والمواقع"},
                {"إدارة المخزون", "Inventory Management", "InventoryManagementWindow",
                        "إدارة المخزون والكميات"},
                {"حركة المخزون", "Inventory Movement", "InventoryMovementWindow",
                        "تتبع حركة المخزون"},
                {"جرد المخزون", "Inventory Count", "InventoryCountWindow", "عمليات جرد المخزون"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static void addAccountingWindows(Connection connection, int parentId)
            throws SQLException {
        String[][] windows = {
                {"دليل الحسابات", "Chart of Accounts", "ChartOfAccountsWindow",
                        "إدارة دليل الحسابات"},
                {"القيود المحاسبية", "Journal Entries", "JournalEntriesWindow",
                        "إدارة القيود المحاسبية"},
                {"الميزانية العمومية", "Balance Sheet", "BalanceSheetWindow",
                        "عرض الميزانية العمومية"},
                {"قائمة الدخل", "Income Statement", "IncomeStatementWindow",
                        "قائمة الدخل والمصروفات"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static void addEmployeeWindows(Connection connection, int parentId)
            throws SQLException {
        String[][] windows = {
                {"إدارة الموظفين", "Employee Management", "EmployeeManagementWindow",
                        "إدارة بيانات الموظفين"},
                {"الحضور والانصراف", "Attendance", "AttendanceWindow", "تتبع الحضور والانصراف"},
                {"الرواتب والأجور", "Payroll", "PayrollWindow", "إدارة الرواتب والأجور"},
                {"الإجازات", "Leave Management", "LeaveManagementWindow", "إدارة إجازات الموظفين"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }

    private static void addReportsWindows(Connection connection, int parentId) throws SQLException {
        String[][] windows = {
                {"تقارير الشحنات", "Shipment Reports", "ShipmentReportsWindow",
                        "تقارير عمليات الشحن"},
                {"تقارير العملاء", "Customer Reports", "CustomerReportsWindow", "تقارير العملاء"},
                {"التقارير المالية", "Financial Reports", "FinancialReportsWindow",
                        "التقارير المالية"},
                {"تقارير المخزون", "Inventory Reports", "InventoryReportsWindow", "تقارير المخزون"},
                {"لوحة المعلومات", "Dashboard", "DashboardWindow", "لوحة معلومات تفاعلية"}};

        for (int i = 0; i < windows.length; i++) {
            addWindow(connection, parentId, windows[i][0], windows[i][1], windows[i][2],
                    windows[i][3], i + 1);
        }
    }
}
