@echo off
echo ========================================
echo    SHIP ERP SYSTEM WITH DATABASE TREE
echo    نظام إدارة الشحنات مع شجرة قاعدة البيانات
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Starting Ship ERP System with Database Tree Support...
echo [معلومات] بدء تشغيل نظام إدارة الشحنات مع دعم شجرة قاعدة البيانات...
echo.

REM إعداد البيئة
call set-tns-env.bat

echo ========================================
echo    PHASE 1: VERIFY SYSTEM COMPONENTS
echo    المرحلة 1: التحقق من مكونات النظام
echo ========================================

echo [1] Checking compiled classes...

if exist "SystemTreeManager.class" (
    echo    OK: SystemTreeManager.class found
) else (
    echo    ERROR: SystemTreeManager.class missing
    echo    Compiling SystemTreeManager...
    javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;." -d . src\main\java\SystemTreeManager.java
)

if exist "TreeMenuPanel.class" (
    echo    OK: TreeMenuPanel.class found
) else (
    echo    ERROR: TreeMenuPanel.class missing
    echo    Compiling TreeMenuPanel...
    javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;." -d . src\main\java\TreeMenuPanel.java
)

if exist "TNSConnectionManager.class" (
    echo    OK: TNSConnectionManager.class found
) else (
    echo    ERROR: TNSConnectionManager.class missing
    echo    Compiling TNSConnectionManager...
    javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;." -d . src\main\java\TNSConnectionManager.java
)

echo.
echo ========================================
echo    PHASE 2: TEST DATABASE CONNECTION
echo    المرحلة 2: اختبار اتصال قاعدة البيانات
echo ========================================

echo [2] Testing database connection and tree structure...

java %JAVA_TNS_OPTS% -cp "lib\ojdbc11.jar;lib\orai18n.jar;." SystemTreeManager
if errorlevel 1 (
    echo    WARNING: Database tree test failed
    echo    System will use default tree structure
) else (
    echo    OK: Database tree is working correctly
)

echo.
echo ========================================
echo    PHASE 3: LAUNCH APPLICATION
echo    المرحلة 3: تشغيل التطبيق
echo ========================================

echo [3] Starting Ship ERP Main Application...
echo.
echo Features enabled:
echo الميزات المفعلة:
echo - Dynamic system tree from database
echo - Automatic window registration
echo - TNS connection management
echo - Arabic language support
echo - Real-time tree updates
echo.
echo ========================================
echo    APPLICATION STARTING
echo    بدء تشغيل التطبيق
echo ========================================

REM تشغيل التطبيق الرئيسي
java %JAVA_TNS_OPTS% -cp "lib\ojdbc11.jar;lib\orai18n.jar;." TreeMenuPanel

echo.
echo ========================================
echo    APPLICATION CLOSED
echo    تم إغلاق التطبيق
echo ========================================

echo.
echo Thank you for using Ship ERP System!
echo شكراً لاستخدام نظام إدارة الشحنات!
echo.

pause
