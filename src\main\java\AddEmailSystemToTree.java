import java.sql.*;

/**
 * إدراج نظام إدارة البريد الإلكتروني في شجرة الأنظمة
 * Add Email Management System to System Tree
 */
public class AddEmailSystemToTree {
    
    public static void main(String[] args) {
        System.out.println("📧 إدراج نظام إدارة البريد الإلكتروني في شجرة الأنظمة...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            // إنشاء فئة نظام البريد الإلكتروني
            int emailCategoryId = createEmailSystemCategory(connection);
            
            if (emailCategoryId > 0) {
                // إضافة النوافذ الفرعية
                addEmailSystemWindows(connection, emailCategoryId);
            }
            
            connection.close();
            System.out.println("✅ تم إدراج نظام إدارة البريد الإلكتروني في شجرة الأنظمة بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إدراج نظام البريد الإلكتروني: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static int createEmailSystemCategory(Connection connection) throws SQLException {
        System.out.println("📋 إنشاء فئة نظام إدارة البريد الإلكتروني...");
        
        // التحقق من وجود الفئة مسبقاً
        String checkSQL = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = connection.prepareStatement(checkSQL)) {
            stmt.setString(1, "نظام إدارة البريد الإلكتروني");
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    int existingId = rs.getInt("TREE_ID");
                    System.out.println("ℹ️ فئة نظام البريد الإلكتروني موجودة مسبقاً (ID: " + existingId + ")");
                    return existingId;
                }
            }
        }
        
        // الحصول على الترتيب التالي في الجذر
        int nextOrder = getNextRootOrder(connection);
        
        // إدراج الفئة الجديدة
        String insertSQL = """
            INSERT INTO ERP_SYSTEM_TREE 
            (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, 
             DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
             CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
            VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, NULL, ?, ?, ?, 'CATEGORY', 
                    ?, 1, 'Y', 'Y', 
                    SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
            stmt.setString(1, "نظام إدارة البريد الإلكتروني");
            stmt.setString(2, "Email Management System");
            stmt.setString(3, "نظام شامل ومتقدم لإدارة البريد الإلكتروني - يتضمن إدارة الحسابات، الرسائل، القوالب، الحملات، والإحصائيات مع دعم كامل للاتصال بالإنترنت");
            stmt.setInt(4, nextOrder);
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                // الحصول على معرف الفئة المدرجة
                int categoryId = getLastInsertedId(connection);
                System.out.println("✅ تم إنشاء فئة نظام إدارة البريد الإلكتروني (ID: " + categoryId + ")");
                return categoryId;
            } else {
                System.err.println("❌ فشل في إنشاء فئة نظام البريد الإلكتروني");
                return 0;
            }
        }
    }
    
    private static void addEmailSystemWindows(Connection connection, int parentId) throws SQLException {
        System.out.println("📋 إضافة نوافذ نظام البريد الإلكتروني...");
        
        // قائمة النوافذ المطلوبة
        String[][] windows = {
            {"إدارة حسابات البريد", "Email Accounts Management", "EmailAccountsWindow", 
             "إدارة وتكوين حسابات البريد الإلكتروني - IMAP, POP3, SMTP"},
            
            {"صندوق الوارد", "Inbox", "EmailInboxWindow", 
             "عرض وإدارة الرسائل الواردة"},
            
            {"إنشاء رسالة جديدة", "Compose New Message", "EmailComposeWindow", 
             "إنشاء وإرسال رسائل بريد إلكتروني جديدة"},
            
            {"إدارة القوالب", "Templates Management", "EmailTemplatesWindow", 
             "إنشاء وإدارة قوالب البريد الإلكتروني"},
            
            {"إدارة الحملات", "Campaigns Management", "EmailCampaignsWindow", 
             "إنشاء وإدارة حملات البريد الإلكتروني التسويقية"},
            
            {"دفتر العناوين", "Address Book", "EmailAddressBookWindow", 
             "إدارة جهات الاتصال ومجموعات البريد الإلكتروني"},
            
            {"المرشحات والقواعد", "Filters & Rules", "EmailFiltersWindow", 
             "إنشاء وإدارة مرشحات وقواعد البريد الإلكتروني"},
            
            {"التوقيعات", "Signatures", "EmailSignaturesWindow", 
             "إنشاء وإدارة توقيعات البريد الإلكتروني"},
            
            {"الإحصائيات والتقارير", "Statistics & Reports", "EmailReportsWindow", 
             "عرض إحصائيات وتقارير البريد الإلكتروني"},
            
            {"سجلات النظام", "System Logs", "EmailLogsWindow", 
             "عرض سجلات وأخطاء نظام البريد الإلكتروني"},
            
            {"إعدادات النظام", "System Settings", "EmailSettingsWindow", 
             "تكوين إعدادات نظام البريد الإلكتروني"}
        };
        
        for (int i = 0; i < windows.length; i++) {
            String nameAr = windows[i][0];
            String nameEn = windows[i][1];
            String windowClass = windows[i][2];
            String description = windows[i][3];
            
            addWindow(connection, parentId, nameAr, nameEn, windowClass, description, i + 1);
        }
        
        System.out.println("✅ تم إضافة جميع نوافذ نظام البريد الإلكتروني (" + windows.length + " نافذة)");
    }
    
    private static void addWindow(Connection connection, int parentId, String nameAr, String nameEn, 
                                 String windowClass, String description, int order) throws SQLException {
        
        // التحقق من وجود النافذة مسبقاً
        String checkSQL = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE WINDOW_CLASS = ?";
        
        try (PreparedStatement stmt = connection.prepareStatement(checkSQL)) {
            stmt.setString(1, windowClass);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next() && rs.getInt(1) > 0) {
                    System.out.println("ℹ️ النافذة " + nameAr + " موجودة مسبقاً");
                    return;
                }
            }
        }
        
        // إدراج النافذة الجديدة
        String insertSQL = """
            INSERT INTO ERP_SYSTEM_TREE 
            (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, 
             WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
             CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
            VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, ?, ?, ?, ?, 'WINDOW', 
                    ?, ?, 2, 'Y', 'Y', 
                    SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
            stmt.setInt(1, parentId);
            stmt.setString(2, nameAr);
            stmt.setString(3, nameEn);
            stmt.setString(4, description);
            stmt.setString(5, windowClass);
            stmt.setInt(6, order);
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("✅ تم إضافة النافذة: " + nameAr);
            } else {
                System.err.println("❌ فشل في إضافة النافذة: " + nameAr);
            }
        }
    }
    
    private static int getNextRootOrder(Connection connection) throws SQLException {
        String sql = "SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID IS NULL";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        
        return 1;
    }
    
    private static int getLastInsertedId(Connection connection) throws SQLException {
        String sql = "SELECT ERP_SYSTEM_TREE_SEQ.CURRVAL FROM DUAL";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        
        return 0;
    }
}
