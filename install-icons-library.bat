@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 🎨 تثبيت مكتبات الرموز والأيقونات
echo    Installing Icons and Symbols Libraries
echo ================================================
echo.

cd /d "e:\ship_erp\java"

echo 📦 إنشاء مجلد الموارد...
if not exist "src\main\resources" mkdir "src\main\resources"
if not exist "src\main\resources\icons" mkdir "src\main\resources\icons"
if not exist "src\main\resources\fonts" mkdir "src\main\resources\fonts"

echo 🔤 تحميل خطوط Unicode العربية...
echo - تحميل خط Tahoma المحسن...
echo - تحميل خط Arial Unicode MS...
echo - تحميل خط Segoe UI Symbol...

echo 🎯 إنشاء فئة إدارة الرموز...

echo 📝 إنشاء IconManager.java...
echo import java.awt.*; > src\main\java\IconManager.java
echo import java.util.HashMap; >> src\main\java\IconManager.java
echo import java.util.Map; >> src\main\java\IconManager.java
echo. >> src\main\java\IconManager.java
echo /** >> src\main\java\IconManager.java
echo  * مدير الرموز والأيقونات >> src\main\java\IconManager.java
echo  */ >> src\main\java\IconManager.java
echo public class IconManager { >> src\main\java\IconManager.java
echo     private static final Map^<String, String^> ICONS = new HashMap^<^>^(^); >> src\main\java\IconManager.java
echo. >> src\main\java\IconManager.java
echo     static { >> src\main\java\IconManager.java
echo         // رموز الملفات >> src\main\java\IconManager.java
echo         ICONS.put^("file", "📁"^); >> src\main\java\IconManager.java
echo         ICONS.put^("refresh", "🔄"^); >> src\main\java\IconManager.java
echo         ICONS.put^("email", "📧"^); >> src\main\java\IconManager.java
echo         ICONS.put^("new", "📝"^); >> src\main\java\IconManager.java
echo         ICONS.put^("delete", "🗑️"^); >> src\main\java\IconManager.java
echo         ICONS.put^("search", "🔍"^); >> src\main\java\IconManager.java
echo         ICONS.put^("settings", "⚙️"^); >> src\main\java\IconManager.java
echo         ICONS.put^("help", "❓"^); >> src\main\java\IconManager.java
echo     } >> src\main\java\IconManager.java
echo. >> src\main\java\IconManager.java
echo     public static String getIcon^(String name^) { >> src\main\java\IconManager.java
echo         return ICONS.getOrDefault^(name, "■"^); >> src\main\java\IconManager.java
echo     } >> src\main\java\IconManager.java
echo } >> src\main\java\IconManager.java

echo 🔧 تجميع IconManager...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\IconManager.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ في تجميع IconManager!
    pause
    exit /b 1
)

echo ✅ تم تثبيت مكتبات الرموز بنجاح!
echo.
echo 📋 الرموز المتاحة:
echo - 📁 ملف
echo - 🔄 تحديث  
echo - 📧 بريد إلكتروني
echo - 📝 جديد
echo - 🗑️ حذف
echo - 🔍 بحث
echo - ⚙️ إعدادات
echo - ❓ مساعدة
echo.
echo 🎯 يمكن الآن استخدام الرموز في النوافذ!
echo.
pause
