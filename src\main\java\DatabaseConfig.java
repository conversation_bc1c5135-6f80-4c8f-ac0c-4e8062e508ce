import java.util.Properties;

public class DatabaseConfig {
    private String host = "localhost";
    private String port = "1521";
    private String serviceName = "ORCL";
    private String tnsName = "ORCL"; // TNS Name from tnsnames.ora
    private String username = "ias20251";
    private String password = "ys123";

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getConnectionUrl() {
        return "jdbc:oracle:thin:@" + host + ":" + port + ":" + serviceName;
    }

    public String getTnsConnectionUrl() {
        return "jdbc:oracle:thin:@" + tnsName;
    }

    public String getTnsName() {
        return tnsName;
    }

    public void setTnsName(String tnsName) {
        this.tnsName = tnsName;
    }

    public Properties getConnectionProperties() {
        Properties props = new Properties();
        props.setProperty("user", username);
        props.setProperty("password", password);
        props.setProperty("oracle.jdbc.defaultNChar", "true");
        return props;
    }

    public static class DatabaseInfo {
        public String name;
        public String status;

        public DatabaseInfo(String name, String status) {
            this.name = name;
            this.status = status;
        }
    }

    public DatabaseInfo getDatabaseInfo() {
        return new DatabaseInfo(serviceName, "Connected");
    }
}
