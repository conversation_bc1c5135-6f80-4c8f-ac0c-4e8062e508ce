package com.shipment.erp.service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import com.shipment.erp.model.BaseEntity;
import com.shipment.erp.repository.BaseRepository;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;

/**
 * التطبيق الأساسي لـ BaseService
 */
@Transactional
public abstract class BaseServiceImpl<T extends BaseEntity> implements BaseService<T> {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final BaseRepository<T> repository;
    protected final Validator validator;

    public BaseServiceImpl(BaseRepository<T> repository, Validator validator) {
        this.repository = repository;
        this.validator = validator;
    }

    @Override
    public T save(T entity) {
        logger.debug("حفظ كيان: {}", entity);

        if (entity.isNew()) {
            validate(entity);
        } else {
            validateForUpdate(entity);
        }

        T savedEntity = repository.save(entity);
        logger.info("تم حفظ الكيان بنجاح: {}", savedEntity);

        return savedEntity;
    }

    @Override
    public List<T> saveAll(List<T> entities) {
        logger.debug("حفظ {} كيان", entities.size());

        for (T entity : entities) {
            if (entity.isNew()) {
                validate(entity);
            } else {
                validateForUpdate(entity);
            }
        }

        List<T> savedEntities = repository.saveAll(entities);
        logger.info("تم حفظ {} كيان بنجاح", savedEntities.size());

        return savedEntities;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<T> findById(Long id) {
        logger.debug("البحث عن كيان بالمعرف: {}", id);
        return repository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public T getById(Long id) {
        logger.debug("الحصول على كيان بالمعرف: {}", id);
        return findById(id).orElseThrow(
                () -> new EntityNotFoundException("لم يتم العثور على الكيان بالمعرف: " + id));
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        return repository.existsById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findAll() {
        logger.debug("الحصول على جميع الكيانات");
        return repository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findAll(int page, int size) {
        logger.debug("الحصول على الكيانات - الصفحة: {}, الحجم: {}", page, size);
        return repository.findAll(page, size);
    }

    @Override
    @Transactional(readOnly = true)
    public long count() {
        return repository.count();
    }

    @Override
    public void deleteById(Long id) {
        logger.debug("حذف كيان بالمعرف: {}", id);
        validateForDelete(id);
        repository.deleteById(id);
        logger.info("تم حذف الكيان بالمعرف: {}", id);
    }

    @Override
    public void delete(T entity) {
        logger.debug("حذف كيان: {}", entity);
        validateForDelete(entity.getId());
        repository.delete(entity);
        logger.info("تم حذف الكيان: {}", entity);
    }

    @Override
    public void deleteAll(List<T> entities) {
        logger.debug("حذف {} كيان", entities.size());
        for (T entity : entities) {
            validateForDelete(entity.getId());
        }
        repository.deleteAll(entities);
        logger.info("تم حذف {} كيان", entities.size());
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findActive() {
        logger.debug("البحث عن الكيانات النشطة");
        return repository.findActive();
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findInactive() {
        logger.debug("البحث عن الكيانات غير النشطة");
        return repository.findInactive();
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> search(String searchText) {
        logger.debug("البحث بالنص: {}", searchText);
        return repository.search(searchText);
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> search(String searchText, int page, int size) {
        logger.debug("البحث بالنص: {} - الصفحة: {}, الحجم: {}", searchText, page, size);
        return repository.search(searchText, page, size);
    }

    @Override
    @Transactional(readOnly = true)
    public long countSearch(String searchText) {
        return repository.countSearch(searchText);
    }

    @Override
    public void updateActiveStatus(Long id, boolean isActive) {
        logger.debug("تحديث حالة النشاط للكيان {}: {}", id, isActive);
        repository.updateActiveStatus(id, isActive);
        logger.info("تم تحديث حالة النشاط للكيان {}: {}", id, isActive);
    }

    @Override
    public void activate(Long id) {
        updateActiveStatus(id, true);
    }

    @Override
    public void deactivate(Long id) {
        updateActiveStatus(id, false);
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findLatest(int limit) {
        logger.debug("الحصول على آخر {} كيان", limit);
        return repository.findLatest(limit);
    }

    @Override
    public void validate(T entity) {
        logger.debug("التحقق من صحة الكيان: {}", entity);

        Set<ConstraintViolation<T>> violations = validator.validate(entity);
        if (!violations.isEmpty()) {
            StringBuilder sb = new StringBuilder("أخطاء في التحقق من صحة البيانات:\n");
            for (ConstraintViolation<T> violation : violations) {
                sb.append("- ").append(violation.getMessage()).append("\n");
            }
            throw new ValidationException(sb.toString());
        }

        // تحقق إضافي مخصص
        performCustomValidation(entity);
    }

    @Override
    public void validateForUpdate(T entity) {
        validate(entity);
        // تحقق إضافي للتحديث
        performUpdateValidation(entity);
    }

    @Override
    public void validateForDelete(Long id) {
        if (!existsById(id)) {
            throw new EntityNotFoundException("لم يتم العثور على الكيان بالمعرف: " + id);
        }
        // تحقق إضافي للحذف
        performDeleteValidation(id);
    }

    @Override
    public void refresh(T entity) {
        repository.refresh(entity);
    }

    @Override
    public void detach(T entity) {
        repository.detach(entity);
    }

    @Override
    @Transactional(readOnly = true)
    public byte[] exportData(String format) {
        logger.info("تصدير البيانات بتنسيق: {}", format);
        // تطبيق افتراضي - يجب تخصيصه في الكلاسات الفرعية
        throw new UnsupportedOperationException("تصدير البيانات غير مدعوم في هذا الكيان");
    }

    @Override
    public void importData(byte[] data, String format) {
        logger.info("استيراد البيانات بتنسيق: {}", format);
        // تطبيق افتراضي - يجب تخصيصه في الكلاسات الفرعية
        throw new UnsupportedOperationException("استيراد البيانات غير مدعوم في هذا الكيان");
    }

    /**
     * تحقق مخصص إضافي
     */
    protected void performCustomValidation(T entity) {
        // يمكن تخصيصه في الكلاسات الفرعية
    }

    /**
     * تحقق إضافي للتحديث
     */
    protected void performUpdateValidation(T entity) {
        // يمكن تخصيصه في الكلاسات الفرعية
    }

    /**
     * تحقق إضافي للحذف
     */
    protected void performDeleteValidation(Long id) {
        // يمكن تخصيصه في الكلاسات الفرعية
    }

    /**
     * الحصول على Repository
     */
    protected BaseRepository<T> getRepository() {
        return repository;
    }

    /**
     * استثناء عدم وجود الكيان
     */
    public static class EntityNotFoundException extends RuntimeException {
        public EntityNotFoundException(String message) {
            super(message);
        }
    }

    /**
     * استثناء التحقق من صحة البيانات
     */
    public static class ValidationException extends RuntimeException {
        public ValidationException(String message) {
            super(message);
        }
    }
}
