import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Properties;

/**
 * إنشاء جدول المتغيرات العامة للتطبيق Create Application Variables Table
 */
public class CreateAppVariablesTable {

    public static void main(String[] args) {
        try {
            System.out.println("🔄 إنشاء جدول المتغيرات العامة للتطبيق...");

            Connection connection = getConnection();

            // إنشاء الجدول
            createAppVariablesTable(connection);

            // إدراج البيانات الأساسية
            insertBasicVariables(connection);

            // إنشاء الفهارس والـ Views
            createIndexesAndViews(connection);

            connection.close();

            System.out.println("✅ تم إنشاء جدول المتغيرات العامة بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء جدول المتغيرات العامة: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");

            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");

            String url = "*************************************";
            return DriverManager.getConnection(url, props);

        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }

    private static void createAppVariablesTable(Connection conn) throws SQLException {
        System.out.println("📊 إنشاء جدول ERP_APP_VARIABLES...");

        // حذف الجدول إذا كان موجوداً
        try {
            conn.createStatement().execute("DROP TABLE ERP_APP_VARIABLES CASCADE CONSTRAINTS");
            System.out.println("⚠️ تم حذف الجدول الموجود");
        } catch (SQLException e) {
            // تجاهل الخطأ إذا لم يكن الجدول موجوداً
        }

        // حذف Sequence إذا كان موجوداً
        try {
            conn.createStatement().execute("DROP SEQUENCE ERP_APP_VARIABLES_SEQ");
        } catch (SQLException e) {
            // تجاهل الخطأ
        }

        // إنشاء Sequence
        String createSeq = "CREATE SEQUENCE ERP_APP_VARIABLES_SEQ START WITH 1 INCREMENT BY 1";
        conn.createStatement().execute(createSeq);

        // إنشاء الجدول
        String createTable = """
                    CREATE TABLE ERP_APP_VARIABLES (
                        VAR_ID NUMBER(10) PRIMARY KEY,
                        VAR_NAME VARCHAR2(100) NOT NULL UNIQUE,
                        VAR_NAME_AR NVARCHAR2(200) NOT NULL,
                        VAR_NAME_EN VARCHAR2(200) NOT NULL,
                        VAR_DESCRIPTION NVARCHAR2(500),
                        VAR_VALUE NVARCHAR2(2000),
                        DEFAULT_VALUE NVARCHAR2(2000),
                        DATA_TYPE VARCHAR2(20) DEFAULT 'STRING' NOT NULL,
                        VAR_CATEGORY VARCHAR2(50) DEFAULT 'SYSTEM' NOT NULL,
                        VAR_GROUP VARCHAR2(100),
                        PRIORITY_LEVEL NUMBER(1) DEFAULT 2,
                        IS_REQUIRED CHAR(1) DEFAULT 'N',
                        IS_READONLY CHAR(1) DEFAULT 'N',
                        IS_ENCRYPTED CHAR(1) DEFAULT 'N',
                        IS_ACTIVE CHAR(1) DEFAULT 'Y',
                        IS_VISIBLE CHAR(1) DEFAULT 'Y',
                        ALLOWED_VALUES CLOB,
                        MIN_VALUE NUMBER,
                        MAX_VALUE NUMBER,
                        MIN_LENGTH NUMBER,
                        MAX_LENGTH NUMBER,
                        VALIDATION_PATTERN VARCHAR2(500),
                        VALIDATION_MESSAGE NVARCHAR2(500),
                        UNIT_OF_MEASURE VARCHAR2(50),
                        DISPLAY_FORMAT VARCHAR2(100),
                        DISPLAY_ORDER NUMBER(5) DEFAULT 0,
                        HELP_TEXT NVARCHAR2(1000),
                        HELP_URL VARCHAR2(500),
                        ADDITIONAL_INFO CLOB,
                        CREATED_DATE DATE DEFAULT SYSDATE,
                        CREATED_BY VARCHAR2(50) DEFAULT USER,
                        LAST_UPDATED DATE DEFAULT SYSDATE,
                        UPDATED_BY VARCHAR2(50) DEFAULT USER,
                        LAST_ACCESSED DATE,
                        LAST_ACCESSED_BY VARCHAR2(50),
                        ACCESS_COUNT NUMBER(10) DEFAULT 0,
                        VERSION_NUMBER NUMBER(10) DEFAULT 1,
                        NOTES NVARCHAR2(1000)
                    )
                """;

        conn.createStatement().execute(createTable);
        System.out.println("✅ تم إنشاء جدول ERP_APP_VARIABLES");

        // إنشاء Triggers
        createTriggers(conn);
    }

    private static void createTriggers(Connection conn) throws SQLException {
        System.out.println("🔧 إنشاء Triggers...");

        // Trigger لتحديث التاريخ والإصدار
        String updateTrigger = """
                    CREATE OR REPLACE TRIGGER TRG_APP_VAR_UPDATE
                        BEFORE UPDATE ON ERP_APP_VARIABLES
                        FOR EACH ROW
                    BEGIN
                        :NEW.LAST_UPDATED := SYSDATE;
                        :NEW.UPDATED_BY := USER;
                        :NEW.VERSION_NUMBER := :OLD.VERSION_NUMBER + 1;
                    END;
                """;
        conn.createStatement().execute(updateTrigger);

        // Trigger لتعيين المعرف الفريد
        String idTrigger = """
                    CREATE OR REPLACE TRIGGER TRG_APP_VAR_ID
                        BEFORE INSERT ON ERP_APP_VARIABLES
                        FOR EACH ROW
                        WHEN (NEW.VAR_ID IS NULL)
                    BEGIN
                        :NEW.VAR_ID := ERP_APP_VARIABLES_SEQ.NEXTVAL;
                    END;
                """;
        conn.createStatement().execute(idTrigger);

        System.out.println("✅ تم إنشاء Triggers");
    }

    private static void insertBasicVariables(Connection conn) throws SQLException {
        System.out.println("📝 إدراج المتغيرات الأساسية...");

        String[][] basicVars = {
                {"APP_NAME", "اسم التطبيق", "Application Name", "اسم التطبيق الرئيسي",
                        "نظام إدارة الشحنات", "STRING", "SYSTEM", "APPLICATION", "1", "Y", "1"},
                {"APP_VERSION", "إصدار التطبيق", "Application Version", "رقم إصدار التطبيق الحالي",
                        "1.0.0", "STRING", "SYSTEM", "APPLICATION", "1", "Y", "2"},
                {"DEFAULT_LANGUAGE", "اللغة الافتراضية", "Default Language",
                        "اللغة الافتراضية للتطبيق", "ar", "STRING", "UI", "LOCALIZATION", "2", "Y",
                        "3"},
                {"DATE_FORMAT", "تنسيق التاريخ", "Date Format", "تنسيق عرض التاريخ في التطبيق",
                        "dd/MM/yyyy", "STRING", "UI", "FORMATTING", "2", "Y", "4"},
                {"SESSION_TIMEOUT", "انتهاء الجلسة", "Session Timeout",
                        "مدة انتهاء الجلسة بالدقائق", "30", "NUMBER", "SECURITY", "SESSION", "1",
                        "Y", "5"},
                {"MAX_LOGIN_ATTEMPTS", "محاولات تسجيل الدخول", "Max Login Attempts",
                        "عدد محاولات تسجيل الدخول المسموحة", "3", "NUMBER", "SECURITY",
                        "AUTHENTICATION", "1", "Y", "6"},
                {"BACKUP_ENABLED", "تفعيل النسخ الاحتياطي", "Backup Enabled",
                        "تفعيل النسخ الاحتياطي التلقائي", "true", "BOOLEAN", "SYSTEM", "BACKUP",
                        "2", "Y", "7"},
                {"LOG_LEVEL", "مستوى السجلات", "Log Level", "مستوى تفصيل السجلات", "INFO", "STRING",
                        "SYSTEM", "LOGGING", "2", "Y", "8"},
                {"COMPANY_NAME", "اسم الشركة", "Company Name", "اسم الشركة المستخدمة للنظام",
                        "شركة الشحنات المتقدمة", "STRING", "BUSINESS", "COMPANY", "1", "Y", "9"},
                {"CURRENCY_CODE", "رمز العملة", "Currency Code", "رمز العملة الافتراضية", "SAR",
                        "STRING", "BUSINESS", "FINANCIAL", "1", "Y", "10"}};

        String sql = """
                    INSERT INTO ERP_APP_VARIABLES (
                        VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
                        VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
                        PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            for (String[] var : basicVars) {
                stmt.setString(1, var[0]); // VAR_NAME
                stmt.setString(2, var[1]); // VAR_NAME_AR
                stmt.setString(3, var[2]); // VAR_NAME_EN
                stmt.setString(4, var[3]); // VAR_DESCRIPTION
                stmt.setString(5, var[4]); // VAR_VALUE
                stmt.setString(6, var[4]); // DEFAULT_VALUE
                stmt.setString(7, var[5]); // DATA_TYPE
                stmt.setString(8, var[6]); // VAR_CATEGORY
                stmt.setString(9, var[7]); // VAR_GROUP
                stmt.setInt(10, Integer.parseInt(var[8])); // PRIORITY_LEVEL
                stmt.setString(11, var[9]); // IS_REQUIRED
                stmt.setInt(12, Integer.parseInt(var[10])); // DISPLAY_ORDER

                stmt.executeUpdate();
                System.out.println("  ✅ " + var[1]);
            }
        }

        System.out.println("✅ تم إدراج " + basicVars.length + " متغير أساسي");
    }

    private static void createIndexesAndViews(Connection conn) throws SQLException {
        System.out.println("🔍 إنشاء الفهارس والـ Views...");

        // إنشاء الفهارس
        String[] indexes = {"CREATE INDEX IDX_APP_VAR_NAME ON ERP_APP_VARIABLES(VAR_NAME)",
                "CREATE INDEX IDX_APP_VAR_CATEGORY ON ERP_APP_VARIABLES(VAR_CATEGORY)",
                "CREATE INDEX IDX_APP_VAR_GROUP ON ERP_APP_VARIABLES(VAR_GROUP)",
                "CREATE INDEX IDX_APP_VAR_TYPE ON ERP_APP_VARIABLES(DATA_TYPE)",
                "CREATE INDEX IDX_APP_VAR_ACTIVE ON ERP_APP_VARIABLES(IS_ACTIVE)",
                "CREATE INDEX IDX_APP_VAR_PRIORITY ON ERP_APP_VARIABLES(PRIORITY_LEVEL)"};

        for (String sql : indexes) {
            try {
                conn.createStatement().execute(sql);
            } catch (SQLException e) {
                System.err.println("⚠️ تحذير في إنشاء فهرس: " + e.getMessage());
            }
        }

        // إنشاء View
        String createView = """
                    CREATE OR REPLACE VIEW VW_APP_VARIABLES_ORGANIZED AS
                    SELECT
                        VAR_ID, VAR_NAME, VAR_NAME_AR, VAR_NAME_EN, VAR_DESCRIPTION,
                        VAR_VALUE, DEFAULT_VALUE, DATA_TYPE, VAR_CATEGORY, VAR_GROUP,
                        PRIORITY_LEVEL, IS_REQUIRED, IS_READONLY, IS_ENCRYPTED,
                        IS_ACTIVE, IS_VISIBLE, DISPLAY_ORDER,
                        CASE VAR_CATEGORY
                            WHEN 'SYSTEM' THEN 'إعدادات النظام'
                            WHEN 'UI' THEN 'إعدادات الواجهة'
                            WHEN 'DATABASE' THEN 'إعدادات قاعدة البيانات'
                            WHEN 'SECURITY' THEN 'إعدادات الأمان'
                            WHEN 'BUSINESS' THEN 'إعدادات العمل'
                            ELSE VAR_CATEGORY
                        END AS CATEGORY_NAME_AR,
                        CASE PRIORITY_LEVEL
                            WHEN 1 THEN 'عالي'
                            WHEN 2 THEN 'متوسط'
                            WHEN 3 THEN 'منخفض'
                            ELSE 'غير محدد'
                        END AS PRIORITY_NAME_AR
                    FROM ERP_APP_VARIABLES
                    WHERE IS_ACTIVE = 'Y'
                    ORDER BY VAR_CATEGORY, VAR_GROUP, DISPLAY_ORDER
                """;

        conn.createStatement().execute(createView);
        System.out.println("✅ تم إنشاء الفهارس والـ Views");
    }
}
