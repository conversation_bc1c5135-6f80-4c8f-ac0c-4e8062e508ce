package com.shipment.erp.service;

import java.util.List;
import java.util.Optional;
import com.shipment.erp.model.BaseEntity;
import com.shipment.erp.repository.SimpleBaseRepository;

/**
 * تنفيذ مبسط للخدمات الأساسية Simple Base Service Implementation
 */
public abstract class SimpleBaseServiceImpl<T extends BaseEntity> implements SimpleBaseService<T> {

    protected final SimpleBaseRepository<T> repository;

    public SimpleBaseServiceImpl(SimpleBaseRepository<T> repository) {
        this.repository = repository;
    }

    @Override
    public T save(T entity) {
        if (entity.getId() == null) {
            entity.setCreatedDate(java.time.LocalDateTime.now());
        } else {
            entity.setModifiedDate(java.time.LocalDateTime.now());
        }
        return repository.save(entity);
    }

    @Override
    public Optional<T> findById(Long id) {
        return repository.findById(id);
    }

    @Override
    public List<T> findAll() {
        return repository.findAll();
    }

    @Override
    public void delete(Long id) {
        repository.deleteById(id);
    }

    @Override
    public void delete(T entity) {
        repository.delete(entity);
    }

    @Override
    public boolean existsById(Long id) {
        return repository.existsById(id);
    }

    @Override
    public long count() {
        return repository.count();
    }

    @Override
    public List<T> findAllById(Iterable<Long> ids) {
        return repository.findAllById(ids);
    }

    @Override
    public List<T> saveAll(Iterable<T> entities) {
        return repository.saveAll(entities);
    }

    @Override
    public void deleteAll() {
        repository.deleteAll();
    }

    @Override
    public void deleteAll(Iterable<T> entities) {
        repository.deleteAll(entities);
    }

    @Override
    public void deleteAllById(Iterable<Long> ids) {
        repository.deleteAllById(ids);
    }
}
