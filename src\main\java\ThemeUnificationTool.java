import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

/**
 * أداة توحيد المظاهر في جميع النوافذ
 * Theme Unification Tool for All Windows
 */
public class ThemeUnificationTool {
    
    private List<String> modifiedFiles = new ArrayList<>();
    private List<String> backupFiles = new ArrayList<>();
    
    public static void main(String[] args) {
        System.out.println("🔧 أداة توحيد المظاهر في جميع النوافذ");
        System.out.println("Theme Unification Tool for All Windows");
        System.out.println("==========================================");
        
        ThemeUnificationTool tool = new ThemeUnificationTool();
        tool.unifyAllThemes();
    }
    
    public void unifyAllThemes() {
        try {
            System.out.println("🚀 بدء عملية توحيد المظاهر...");
            
            // إنشاء نسخ احتياطية
            createBackups();
            
            // تحديث الملفات
            updateMainApplications();
            updateUtilityWindows();
            updateTestWindows();
            
            // إعادة التجميع
            recompileModifiedFiles();
            
            // إنشاء ملف الإعدادات الموحد
            createUnifiedSettings();
            
            System.out.println("\n✅ تم توحيد المظاهر بنجاح!");
            showSummary();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في توحيد المظاهر: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void createBackups() {
        System.out.println("\n📦 إنشاء نسخ احتياطية...");
        
        String[] filesToBackup = {
            "src/main/java/EnhancedShipERP.java",
            "src/main/java/SimpleShipERP.java",
            "src/main/java/AdvancedSystemTreeFixer.java",
            "src/main/java/SystemTreeWithIcons.java",
            "src/main/java/ItemGroupsDisplayFixer.java",
            "src/main/java/ItemGroupsComparison.java",
            "src/main/java/IconTestViewer.java"
        };
        
        for (String filePath : filesToBackup) {
            try {
                File originalFile = new File(filePath);
                if (originalFile.exists()) {
                    File backupFile = new File(filePath + ".backup");
                    Files.copy(originalFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    backupFiles.add(backupFile.getAbsolutePath());
                    System.out.println("✅ نسخة احتياطية: " + originalFile.getName());
                }
            } catch (IOException e) {
                System.err.println("❌ فشل في إنشاء نسخة احتياطية لـ " + filePath);
            }
        }
    }
    
    private void updateMainApplications() {
        System.out.println("\n🔄 تحديث التطبيقات الرئيسية...");
        
        // تحديث EnhancedShipERP
        updateEnhancedShipERP();
        
        // تحديث SimpleShipERP
        updateSimpleShipERP();
    }
    
    private void updateEnhancedShipERP() {
        try {
            File file = new File("src/main/java/EnhancedShipERP.java");
            if (!file.exists()) return;
            
            String content = Files.readString(file.toPath());
            
            // استبدال إعدادات المظهر القديمة
            String oldThemeCode = """
                try {
                    // تطبيق FlatLaf Look and Feel
                    FlatLightLaf.setup();

                    // تفعيل الرسوم المتحركة
                    FlatAnimatedLafChange.showSnapshot();

                    // إعدادات إضافية للـ Look and Feel
                    UIManager.put("Button.arc", 8);
                    UIManager.put("Component.arc", 8);
                    UIManager.put("ProgressBar.arc", 8);
                    UIManager.put("TextComponent.arc", 8);

                    // تحسين الخطوط
                    UIManager.put("defaultFont", new Font("Segoe UI", Font.PLAIN, 13));

                    showLoginScreen();

                } catch (Exception e) {
                    e.printStackTrace();
                    // العودة للـ Look and Feel الافتراضي في حالة الخطأ
                    try {
                        UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
                        showLoginScreen();
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }""";
            
            String newThemeCode = """
                try {
                    // تطبيق المظهر الموحد
                    UnifiedThemeManager.initializeDefaultTheme();
                    showLoginScreen();

                } catch (Exception e) {
                    e.printStackTrace();
                    // العودة للمظهر الافتراضي في حالة الخطأ
                    try {
                        UnifiedThemeManager.getInstance().applyTheme(UnifiedThemeManager.Theme.FLAT_LIGHT);
                        showLoginScreen();
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }""";
            
            content = content.replace(oldThemeCode, newThemeCode);
            
            Files.writeString(file.toPath(), content);
            modifiedFiles.add(file.getName());
            System.out.println("✅ تم تحديث EnhancedShipERP.java");
            
        } catch (IOException e) {
            System.err.println("❌ فشل في تحديث EnhancedShipERP.java: " + e.getMessage());
        }
    }
    
    private void updateSimpleShipERP() {
        try {
            File file = new File("src/main/java/SimpleShipERP.java");
            if (!file.exists()) return;
            
            String content = Files.readString(file.toPath());
            
            // استبدال إعدادات المظهر القديمة
            String oldThemeCode = """
                try {
                    // تطبيق مظهر حديث
                    UIManager.setLookAndFeel("com.formdev.flatlaf.FlatLightLaf");
                } catch (Exception e) {
                    System.out.println("تعذر تطبيق المظهر الحديث، سيتم استخدام المظهر الافتراضي");
                }""";
            
            String newThemeCode = """
                // تطبيق المظهر الموحد
                UnifiedThemeManager.initializeDefaultTheme();""";
            
            content = content.replace(oldThemeCode, newThemeCode);
            
            Files.writeString(file.toPath(), content);
            modifiedFiles.add(file.getName());
            System.out.println("✅ تم تحديث SimpleShipERP.java");
            
        } catch (IOException e) {
            System.err.println("❌ فشل في تحديث SimpleShipERP.java: " + e.getMessage());
        }
    }
    
    private void updateUtilityWindows() {
        System.out.println("\n🔄 تحديث النوافذ المساعدة...");
        
        String[] utilityFiles = {
            "src/main/java/AdvancedSystemTreeFixer.java",
            "src/main/java/SystemTreeWithIcons.java",
            "src/main/java/ItemGroupsDisplayFixer.java",
            "src/main/java/ItemGroupsComparison.java"
        };
        
        for (String filePath : utilityFiles) {
            updateUtilityWindow(filePath);
        }
    }
    
    private void updateUtilityWindow(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) return;
            
            String content = Files.readString(file.toPath());
            
            // استبدال إعدادات المظهر في main method
            content = content.replaceAll(
                "UIManager\\.setLookAndFeel\\([^)]+\\);?",
                "UnifiedThemeManager.initializeDefaultTheme();"
            );
            
            content = content.replaceAll(
                "com\\.formdev\\.flatlaf\\.FlatDarkLaf\\.setup\\(\\);?",
                "UnifiedThemeManager.initializeDefaultTheme();"
            );
            
            content = content.replaceAll(
                "com\\.formdev\\.flatlaf\\.FlatLightLaf\\.setup\\(\\);?",
                "UnifiedThemeManager.initializeDefaultTheme();"
            );
            
            Files.writeString(file.toPath(), content);
            modifiedFiles.add(file.getName());
            System.out.println("✅ تم تحديث " + file.getName());
            
        } catch (IOException e) {
            System.err.println("❌ فشل في تحديث " + filePath + ": " + e.getMessage());
        }
    }
    
    private void updateTestWindows() {
        System.out.println("\n🔄 تحديث نوافذ الاختبار...");
        
        String[] testFiles = {
            "src/main/java/IconTestViewer.java",
            "src/main/java/ThemeTest.java",
            "src/main/java/WorkingThemeWindow.java",
            "src/main/java/CompleteThemeWindow.java"
        };
        
        for (String filePath : testFiles) {
            updateTestWindow(filePath);
        }
    }
    
    private void updateTestWindow(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) return;
            
            String content = Files.readString(file.toPath());
            
            // استبدال إعدادات المظهر في main method
            content = content.replaceAll(
                "UIManager\\.setLookAndFeel\\([^)]+\\);?",
                "UnifiedThemeManager.initializeDefaultTheme();"
            );
            
            Files.writeString(file.toPath(), content);
            modifiedFiles.add(file.getName());
            System.out.println("✅ تم تحديث " + file.getName());
            
        } catch (IOException e) {
            System.err.println("❌ فشل في تحديث " + filePath + ": " + e.getMessage());
        }
    }
    
    private void recompileModifiedFiles() {
        System.out.println("\n🔨 إعادة تجميع الملفات المحدثة...");
        
        try {
            // تجميع المدير الموحد أولاً
            ProcessBuilder pb = new ProcessBuilder(
                "javac", "-encoding", "UTF-8", "-cp", "lib\\*;.", "-d", ".", 
                "src\\main\\java\\UnifiedThemeManager.java"
            );
            pb.directory(new File("."));
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                System.out.println("✅ تم تجميع UnifiedThemeManager.java");
            } else {
                System.err.println("❌ فشل في تجميع UnifiedThemeManager.java");
            }
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إعادة التجميع: " + e.getMessage());
        }
    }
    
    private void createUnifiedSettings() {
        System.out.println("\n⚙️ إنشاء ملف الإعدادات الموحد...");
        
        try {
            // إنشاء مدير المظاهر الموحد وحفظ الإعدادات الافتراضية
            UnifiedThemeManager manager = UnifiedThemeManager.getInstance();
            manager.applyTheme(UnifiedThemeManager.Theme.FLAT_LIGHT);
            
            System.out.println("✅ تم إنشاء ملف الإعدادات الموحد");
            
        } catch (Exception e) {
            System.err.println("❌ فشل في إنشاء ملف الإعدادات: " + e.getMessage());
        }
    }
    
    private void showSummary() {
        System.out.println("\n📋 ملخص عملية التوحيد:");
        System.out.println("======================");
        
        System.out.println("📁 الملفات المحدثة (" + modifiedFiles.size() + "):");
        for (String file : modifiedFiles) {
            System.out.println("  ✅ " + file);
        }
        
        System.out.println("\n📦 النسخ الاحتياطية (" + backupFiles.size() + "):");
        for (String backup : backupFiles) {
            System.out.println("  💾 " + backup);
        }
        
        System.out.println("\n🎯 النتائج:");
        System.out.println("• تم توحيد جميع المظاهر تحت UnifiedThemeManager");
        System.out.println("• تم إنشاء نسخ احتياطية من جميع الملفات");
        System.out.println("• تم إنشاء ملف إعدادات موحد");
        System.out.println("• جميع النوافذ ستستخدم نفس المظهر");
        
        System.out.println("\n💡 للاستخدام:");
        System.out.println("• استخدم UnifiedThemeManager.initializeDefaultTheme() في بداية التطبيق");
        System.out.println("• استخدم UnifiedThemeManager.getInstance().applyTheme() لتغيير المظهر");
        System.out.println("• جميع الإعدادات محفوظة في theme-settings.properties");
    }
}
