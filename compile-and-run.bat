@echo off
echo ========================================
echo   COMPILE AND RUN SHIP ERP SYSTEM
echo   تجميع وتشغيل نظام Ship ERP
echo ========================================

cd /d "d:\java\java"

echo.
echo [INFO] Starting compilation and execution...
echo [INFO] بدء التجميع والتشغيل...
echo.

REM تنظيف الملفات المجمعة السابقة
echo [1] Cleaning previous compiled files...
echo [1] تنظيف الملفات المجمعة السابقة...
del /q *.class 2>nul
del /q src\main\java\*.class 2>nul

echo.
echo [2] Compiling core classes...
echo [2] تجميع الكلاسات الأساسية...

REM تجميع الكلاسات الأساسية أولاً
javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;lib\flatlaf-3.2.5.jar" -d . src\main\java\SettingsManager.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile SettingsManager
    goto ERROR
)

javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;lib\flatlaf-3.2.5.jar;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile TreeMenuPanel
    goto ERROR
)

javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;lib\flatlaf-3.2.5.jar;." -d . src\main\java\GeneralSettingsWindow.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile GeneralSettingsWindow
    goto ERROR
)

javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;lib\flatlaf-3.2.5.jar;." -d . src\main\java\UserManagementWindow.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile UserManagementWindow
    goto ERROR
)

echo.
echo [3] Compiling main window...
echo [3] تجميع النافذة الرئيسية...

javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;lib\flatlaf-3.2.5.jar;." -d . src\main\java\EnhancedMainWindow.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile EnhancedMainWindow
    goto ERROR
)

echo.
echo [4] Compiling main class...
echo [4] تجميع الكلاس الرئيسي...

javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;lib\flatlaf-3.2.5.jar;." -d . src\main\java\ShipERPMain.java
if %errorlevel% neq 0 (
    echo ❌ Failed to compile ShipERPMain
    goto ERROR
)

echo ✅ Compilation successful!
echo ✅ تم التجميع بنجاح!

echo.
echo [5] Running Ship ERP System...
echo [5] تشغيل نظام Ship ERP...

REM تشغيل التطبيق
java -cp "lib\*;." -Dfile.encoding=UTF-8 -Djava.awt.headless=false -Duser.language=ar -Duser.country=SA ShipERPMain

if %errorlevel% equ 0 (
    echo ✅ Application ran successfully!
    echo ✅ تم تشغيل التطبيق بنجاح!
) else (
    echo ❌ Application failed to run
    echo ❌ فشل في تشغيل التطبيق
)

goto END

:ERROR
echo.
echo ❌ Compilation failed!
echo ❌ فشل التجميع!
echo.
echo Trying alternative compilation method...
echo محاولة طريقة تجميع بديلة...

REM محاولة تجميع جميع الملفات معاً
echo.
echo [ALT] Compiling all files together...
echo [ALT] تجميع جميع الملفات معاً...

javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;lib\flatlaf-3.2.5.jar" -d . src\main\java\*.java

if %errorlevel% equ 0 (
    echo ✅ Alternative compilation successful!
    echo ✅ نجح التجميع البديل!
    
    echo.
    echo [ALT] Running Ship ERP System...
    echo [ALT] تشغيل نظام Ship ERP...
    
    java -cp "lib\*;." -Dfile.encoding=UTF-8 -Djava.awt.headless=false -Duser.language=ar -Duser.country=SA ShipERPMain
) else (
    echo ❌ Alternative compilation also failed
    echo ❌ فشل التجميع البديل أيضاً
    
    echo.
    echo Trying to run existing compiled classes...
    echo محاولة تشغيل الكلاسات المجمعة الموجودة...
    
    java -cp "lib\*;." -Dfile.encoding=UTF-8 -Djava.awt.headless=false -Duser.language=ar -Duser.country=SA ShipERPMain
)

:END
echo.
echo ========================================
echo   COMPILATION AND RUN COMPLETED
echo   تم الانتهاء من التجميع والتشغيل
echo ========================================

echo.
echo If the application didn't start, try:
echo إذا لم يبدأ التطبيق، جرب:
echo.
echo 1. Check if Oracle database is running
echo    تحقق من تشغيل قاعدة بيانات Oracle
echo.
echo 2. Verify database connection settings
echo    تحقق من إعدادات اتصال قاعدة البيانات
echo.
echo 3. Run database connection test:
echo    شغل اختبار اتصال قاعدة البيانات:
echo    java -cp "lib\*;." DatabaseConnectionTest
echo.

pause
