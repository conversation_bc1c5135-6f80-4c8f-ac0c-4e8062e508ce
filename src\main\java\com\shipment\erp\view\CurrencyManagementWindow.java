package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSeparator;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumnModel;
import com.shipment.erp.model.Currency;
import com.shipment.erp.service.CurrencyService;

/**
 * نافذة إدارة العملات Currency Management Window
 */
public class CurrencyManagementWindow extends JDialog {

    private Font arabicFont;
    private JTable currenciesTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> statusFilterCombo;

    private CurrencyService currencyService;
    private List<Currency> currenciesList;

    public CurrencyManagementWindow(JFrame parent) {
        super(parent, "إدارة العملات", true);

        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeServices();
        initializeComponents();
        setupLayout();
        loadCurrenciesData();

        setSize(1100, 700);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(true);
        setMinimumSize(new Dimension(900, 500));
    }

    private void initializeServices() {
        // TODO: Initialize CurrencyService through dependency injection
        // currencyService = ApplicationContext.getBean(CurrencyService.class);
    }

    private void initializeComponents() {
        // شريط الأدوات العلوي
        JPanel toolbarPanel = createToolbarPanel();

        // جدول العملات
        createCurrenciesTable();
        JScrollPane tableScrollPane = new JScrollPane(currenciesTable);
        tableScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();

        // تخطيط النافذة
        setLayout(new BorderLayout());
        add(toolbarPanel, BorderLayout.NORTH);
        add(tableScrollPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }

    private JPanel createToolbarPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // أزرار العمليات
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addButton = new JButton("إضافة عملة جديدة");
        addButton.setFont(arabicFont);
        addButton.addActionListener(e -> addCurrency());

        JButton editButton = new JButton("تعديل");
        editButton.setFont(arabicFont);
        editButton.addActionListener(e -> editCurrency());

        JButton deleteButton = new JButton("حذف");
        deleteButton.setFont(arabicFont);
        deleteButton.addActionListener(e -> deleteCurrency());

        JButton setDefaultButton = new JButton("تعيين كافتراضية");
        setDefaultButton.setFont(arabicFont);
        setDefaultButton.addActionListener(e -> setDefaultCurrency());

        JButton updateRatesButton = new JButton("تحديث الأسعار");
        updateRatesButton.setFont(arabicFont);
        updateRatesButton.addActionListener(e -> updateExchangeRates());

        JButton refreshButton = new JButton("تحديث");
        refreshButton.setFont(arabicFont);
        refreshButton.addActionListener(e -> loadCurrenciesData());

        buttonsPanel.add(addButton);
        buttonsPanel.add(editButton);
        buttonsPanel.add(deleteButton);
        buttonsPanel.add(new JSeparator(SwingConstants.VERTICAL));
        buttonsPanel.add(setDefaultButton);
        buttonsPanel.add(updateRatesButton);
        buttonsPanel.add(new JSeparator(SwingConstants.VERTICAL));
        buttonsPanel.add(refreshButton);

        // شريط البحث والفلترة
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);

        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                filterCurrencies();
            }
        });

        JLabel statusLabel = new JLabel("الحالة:");
        statusLabel.setFont(arabicFont);

        statusFilterCombo = new JComboBox<>(new String[] {"الكل", "نشط", "غير نشط"});
        statusFilterCombo.setFont(arabicFont);
        statusFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusFilterCombo.addActionListener(e -> filterCurrencies());

        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        searchPanel.add(Box.createHorizontalStrut(10));
        searchPanel.add(statusLabel);
        searchPanel.add(statusFilterCombo);

        panel.add(buttonsPanel, BorderLayout.EAST);
        panel.add(searchPanel, BorderLayout.WEST);

        return panel;
    }

    private void createCurrenciesTable() {
        String[] columnNames = {"الرقم", "الكود", "اسم العملة", "اسم العملة (إنجليزي)", "الرمز",
                "سعر الصرف", "افتراضية", "الحالة", "تاريخ الإنشاء"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        currenciesTable = new JTable(tableModel);
        currenciesTable.setFont(arabicFont);
        currenciesTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        currenciesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        currenciesTable.setRowHeight(25);

        // تخصيص عرض الأعمدة
        TableColumnModel columnModel = currenciesTable.getColumnModel();
        columnModel.getColumn(0).setPreferredWidth(60); // الرقم
        columnModel.getColumn(1).setPreferredWidth(80); // الكود
        columnModel.getColumn(2).setPreferredWidth(120); // اسم العملة
        columnModel.getColumn(3).setPreferredWidth(120); // اسم العملة (إنجليزي)
        columnModel.getColumn(4).setPreferredWidth(60); // الرمز
        columnModel.getColumn(5).setPreferredWidth(100); // سعر الصرف
        columnModel.getColumn(6).setPreferredWidth(80); // افتراضية
        columnModel.getColumn(7).setPreferredWidth(80); // الحالة
        columnModel.getColumn(8).setPreferredWidth(120); // تاريخ الإنشاء

        // تخصيص رأس الجدول
        JTableHeader header = currenciesTable.getTableHeader();
        header.setFont(arabicFont);
        header.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إضافة مستمع النقر المزدوج
        currenciesTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    editCurrency();
                }
            }
        });
    }

    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);

        panel.add(statusLabel);
        return panel;
    }

    private void loadCurrenciesData() {
        // TODO: Load data from CurrencyService
        // currenciesList = currencyService.findAll();

        // بيانات تجريبية
        currenciesList = createSampleData();
        updateTableData();
    }

    private List<Currency> createSampleData() {
        Currency currency1 = new Currency();
        currency1.setId(1L);
        currency1.setCode("SAR");
        currency1.setName("ريال سعودي");
        currency1.setNameEn("Saudi Riyal");
        currency1.setSymbol("ر.س");
        currency1.setExchangeRate(BigDecimal.ONE);
        currency1.setDefault(true);
        currency1.setActive(true);
        currency1.setCreatedDate(java.time.LocalDateTime.now());

        Currency currency2 = new Currency();
        currency2.setId(2L);
        currency2.setCode("USD");
        currency2.setName("دولار أمريكي");
        currency2.setNameEn("US Dollar");
        currency2.setSymbol("$");
        currency2.setExchangeRate(new BigDecimal("3.75"));
        currency2.setDefault(false);
        currency2.setActive(true);
        currency2.setCreatedDate(java.time.LocalDateTime.now());

        Currency currency3 = new Currency();
        currency3.setId(3L);
        currency3.setCode("EUR");
        currency3.setName("يورو");
        currency3.setNameEn("Euro");
        currency3.setSymbol("€");
        currency3.setExchangeRate(new BigDecimal("4.10"));
        currency3.setDefault(false);
        currency3.setActive(true);
        currency3.setCreatedDate(java.time.LocalDateTime.now());

        return java.util.Arrays.asList(currency1, currency2, currency3);
    }

    private void updateTableData() {
        tableModel.setRowCount(0);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (Currency currency : currenciesList) {
            Object[] row = {currency.getId(), currency.getCode(), currency.getName(),
                    currency.getNameEn(), currency.getSymbol(), currency.getExchangeRate(),
                    currency.isDefault() ? "نعم" : "لا", currency.isActive() ? "نشط" : "غير نشط",
                    currency.getCreatedDate() != null ? dateFormat.format(currency.getCreatedDate())
                            : ""};
            tableModel.addRow(row);
        }
    }

    private void filterCurrencies() {
        // TODO: Implement filtering logic
        updateTableData();
    }

    private void addCurrency() {
        CurrencyFormDialog dialog = new CurrencyFormDialog(this, "إضافة عملة جديدة", null);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            Currency newCurrency = dialog.getCurrency();
            // TODO: Save using CurrencyService
            // currencyService.save(newCurrency);
            loadCurrenciesData();
        }
    }

    private void editCurrency() {
        int selectedRow = currenciesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار عملة للتعديل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Currency selectedCurrency = currenciesList.get(selectedRow);
        CurrencyFormDialog dialog = new CurrencyFormDialog(this, "تعديل العملة", selectedCurrency);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            Currency updatedCurrency = dialog.getCurrency();
            // TODO: Update using CurrencyService
            // currencyService.save(updatedCurrency);
            loadCurrenciesData();
        }
    }

    private void deleteCurrency() {
        int selectedRow = currenciesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار عملة للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Currency selectedCurrency = currenciesList.get(selectedRow);

        if (selectedCurrency.isDefault()) {
            JOptionPane.showMessageDialog(this, "لا يمكن حذف العملة الافتراضية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return;
        }

        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف العملة: " + selectedCurrency.getName() + "؟", "تأكيد الحذف",
                JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Delete using CurrencyService
            // currencyService.delete(selectedCurrency.getId());
            loadCurrenciesData();
        }
    }

    private void setDefaultCurrency() {
        int selectedRow = currenciesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار عملة لتعيينها كافتراضية", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Currency selectedCurrency = currenciesList.get(selectedRow);

        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من تعيين العملة: " + selectedCurrency.getName() + " كعملة افتراضية؟",
                "تأكيد التعيين", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Set default using CurrencyService
            // currencyService.setDefaultCurrency(selectedCurrency.getId());
            loadCurrenciesData();
        }
    }

    private void updateExchangeRates() {
        int result = JOptionPane.showConfirmDialog(this,
                "هل تريد تحديث أسعار الصرف من المصدر الخارجي؟\n" + "هذا قد يستغرق بعض الوقت",
                "تحديث أسعار الصرف", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Update rates using CurrencyService
            // currencyService.updateExchangeRatesFromSource();
            JOptionPane.showMessageDialog(this, "تم تحديث أسعار الصرف بنجاح", "نجح التحديث",
                    JOptionPane.INFORMATION_MESSAGE);
            loadCurrenciesData();
        }
    }
}
