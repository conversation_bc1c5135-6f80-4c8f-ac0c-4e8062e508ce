import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.sql.SQLException;

/**
 * إصلاح جدول EMAIL_TEMPLATES
 */
public class FixEmailTemplatesTable {

    public static void main(String[] args) {
        System.out.println("=== إصلاح جدول EMAIL_TEMPLATES ===");
        
        try {
            fixTable();
            System.out.println("✅ تم إصلاح الجدول بنجاح!");
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void fixTable() throws SQLException, ClassNotFoundException {
        // تحميل driver
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // الاتصال بقاعدة البيانات
        String url = "*************************************";
        String username = "SHIP_ERP";
        String password = "ship_erp_password";
        
        try (Connection connection = DriverManager.getConnection(url, username, password);
             Statement stmt = connection.createStatement()) {
            
            System.out.println("🔗 تم الاتصال بقاعدة البيانات بنجاح");
            
            // 1. حذف الجدول الموجود إذا كان موجوداً
            System.out.println("🗑️ حذف الجدول الموجود...");
            try {
                stmt.execute("DROP TABLE EMAIL_TEMPLATES CASCADE CONSTRAINTS");
                System.out.println("✅ تم حذف الجدول القديم");
            } catch (SQLException e) {
                if (e.getErrorCode() == 942) { // ORA-00942: table or view does not exist
                    System.out.println("⚠️  الجدول غير موجود مسبقاً");
                } else {
                    System.err.println("❌ خطأ في حذف الجدول: " + e.getMessage());
                }
            }
            
            // 2. حذف sequence إذا كان موجوداً
            try {
                stmt.execute("DROP SEQUENCE SEQ_EMAIL_TEMPLATES");
                System.out.println("✅ تم حذف sequence القديم");
            } catch (SQLException e) {
                if (e.getErrorCode() == 2289) { // ORA-02289: sequence does not exist
                    System.out.println("⚠️  sequence غير موجود مسبقاً");
                }
            }
            
            // 3. إنشاء الجدول الجديد بالشكل الصحيح
            System.out.println("📧 إنشاء جدول EMAIL_TEMPLATES الجديد...");
            String createTableSQL = """
                CREATE TABLE EMAIL_TEMPLATES (
                    TEMPLATE_ID NUMBER PRIMARY KEY,
                    TEMPLATE_NAME_AR NVARCHAR2(200) NOT NULL,
                    TEMPLATE_NAME_EN VARCHAR2(200) NOT NULL,
                    CATEGORY_ID NUMBER,
                    SUBJECT_AR NVARCHAR2(500),
                    SUBJECT_EN VARCHAR2(500),
                    BODY_HTML CLOB,
                    BODY_TEXT CLOB,
                    TEMPLATE_TYPE VARCHAR2(20) DEFAULT 'STANDARD',
                    LANGUAGE_CODE VARCHAR2(5) DEFAULT 'AR',
                    IS_HTML CHAR(1) DEFAULT 'Y',
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    IS_PUBLIC CHAR(1) DEFAULT 'N',
                    USAGE_COUNT NUMBER DEFAULT 0,
                    LAST_USED_DATE DATE,
                    TAGS NVARCHAR2(1000),
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    CONSTRAINT FK_TEMPLATE_CATEGORY FOREIGN KEY (CATEGORY_ID) 
                        REFERENCES EMAIL_TEMPLATE_CATEGORIES(CATEGORY_ID)
                )
                """;
            
            stmt.execute(createTableSQL);
            System.out.println("✅ تم إنشاء جدول EMAIL_TEMPLATES");
            
            // 4. إنشاء sequence جديد
            System.out.println("🔢 إنشاء sequence جديد...");
            stmt.execute("""
                CREATE SEQUENCE SEQ_EMAIL_TEMPLATES
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                """);
            System.out.println("✅ تم إنشاء SEQ_EMAIL_TEMPLATES");
            
            // 5. إدراج قوالب تجريبية
            System.out.println("📝 إدراج قوالب تجريبية...");
            insertSampleTemplates(stmt);
            
            System.out.println("✅ تم إصلاح الجدول وإدراج البيانات التجريبية");
        }
    }
    
    private static void insertSampleTemplates(Statement stmt) throws SQLException {
        String[] templates = {
            """
            INSERT INTO EMAIL_TEMPLATES (
                TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN, CATEGORY_ID, 
                SUBJECT_AR, SUBJECT_EN, BODY_HTML, BODY_TEXT, TEMPLATE_TYPE, LANGUAGE_CODE
            ) VALUES (
                SEQ_EMAIL_TEMPLATES.NEXTVAL, 'قالب ترحيب أساسي', 'Basic Welcome Template', 5,
                'مرحباً بك معنا!', 'Welcome to our service!',
                '<h2>مرحباً بك!</h2><p>نحن سعداء بانضمامك إلينا. نتطلع للعمل معك.</p>',
                'مرحباً بك! نحن سعداء بانضمامك إلينا. نتطلع للعمل معك.',
                'STANDARD', 'AR'
            )
            """,
            """
            INSERT INTO EMAIL_TEMPLATES (
                TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN, CATEGORY_ID, 
                SUBJECT_AR, SUBJECT_EN, BODY_HTML, BODY_TEXT, TEMPLATE_TYPE, LANGUAGE_CODE
            ) VALUES (
                SEQ_EMAIL_TEMPLATES.NEXTVAL, 'قالب إشعار عام', 'General Notification Template', 3,
                'إشعار مهم', 'Important Notification',
                '<h3>إشعار</h3><p>لديك إشعار جديد يتطلب انتباهك.</p>',
                'إشعار: لديك إشعار جديد يتطلب انتباهك.',
                'STANDARD', 'AR'
            )
            """,
            """
            INSERT INTO EMAIL_TEMPLATES (
                TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN, CATEGORY_ID, 
                SUBJECT_AR, SUBJECT_EN, BODY_HTML, BODY_TEXT, TEMPLATE_TYPE, LANGUAGE_CODE
            ) VALUES (
                SEQ_EMAIL_TEMPLATES.NEXTVAL, 'قالب فاتورة', 'Invoice Template', 4,
                'فاتورة رقم {invoice_number}', 'Invoice #{invoice_number}',
                '<h2>فاتورة</h2><p>رقم الفاتورة: {invoice_number}</p><p>المبلغ: {amount}</p>',
                'فاتورة رقم: {invoice_number} - المبلغ: {amount}',
                'STANDARD', 'AR'
            )
            """,
            """
            INSERT INTO EMAIL_TEMPLATES (
                TEMPLATE_ID, TEMPLATE_NAME_AR, TEMPLATE_NAME_EN, CATEGORY_ID, 
                SUBJECT_AR, SUBJECT_EN, BODY_HTML, BODY_TEXT, TEMPLATE_TYPE, LANGUAGE_CODE
            ) VALUES (
                SEQ_EMAIL_TEMPLATES.NEXTVAL, 'قالب تسويقي', 'Marketing Template', 2,
                'عرض خاص لك!', 'Special Offer for You!',
                '<h2>عرض خاص!</h2><p>لا تفوت هذا العرض المحدود.</p>',
                'عرض خاص! لا تفوت هذا العرض المحدود.',
                'CAMPAIGN', 'AR'
            )
            """
        };
        
        for (String sql : templates) {
            try {
                stmt.execute(sql);
                System.out.println("✅ تم إدراج قالب");
            } catch (SQLException e) {
                System.err.println("❌ خطأ في إدراج القالب: " + e.getMessage());
            }
        }
    }
}
