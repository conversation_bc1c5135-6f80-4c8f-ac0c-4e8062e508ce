# Download Missing Libraries Script for Ship ERP System

Write-Host "========================================" -ForegroundColor Green
Write-Host "  Download Missing Libraries" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Create lib directory if it doesn't exist
if (!(Test-Path "lib")) {
    New-Item -ItemType Directory -Path "lib"
    Write-Host "Created lib directory" -ForegroundColor Green
}

# List of required libraries
$libraries = @(
    @{
        Name = "Apache POI OOXML"
        Url = "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml/5.2.4/poi-ooxml-5.2.4.jar"
        File = "lib\poi-ooxml-5.2.4.jar"
    },
    @{
        Name = "Apache POI"
        Url = "https://repo1.maven.org/maven2/org/apache/poi/poi/5.2.4/poi-5.2.4.jar"
        File = "lib\poi-5.2.4.jar"
    },
    @{
        Name = "iText Kernel"
        Url = "https://repo1.maven.org/maven2/com/itextpdf/kernel/7.2.5/kernel-7.2.5.jar"
        File = "lib\itext-kernel-7.2.5.jar"
    },
    @{
        Name = "iText Layout"
        Url = "https://repo1.maven.org/maven2/com/itextpdf/layout/7.2.5/layout-7.2.5.jar"
        File = "lib\itext-layout-7.2.5.jar"
    },
    @{
        Name = "iText IO"
        Url = "https://repo1.maven.org/maven2/com/itextpdf/io/7.2.5/io-7.2.5.jar"
        File = "lib\itext-io-7.2.5.jar"
    },
    @{
        Name = "Jackson Databind"
        Url = "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar"
        File = "lib\jackson-databind-2.15.2.jar"
    },
    @{
        Name = "Jackson Core"
        Url = "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar"
        File = "lib\jackson-core-2.15.2.jar"
    },
    @{
        Name = "Jackson Annotations"
        Url = "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar"
        File = "lib\jackson-annotations-2.15.2.jar"
    },
    @{
        Name = "BCrypt"
        Url = "https://repo1.maven.org/maven2/org/mindrot/jbcrypt/0.4/jbcrypt-0.4.jar"
        File = "lib\jbcrypt-0.4.jar"
    },
    @{
        Name = "Typesafe Config"
        Url = "https://repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2.jar"
        File = "lib\config-1.4.2.jar"
    }
)

# Download libraries
$successCount = 0
$totalCount = $libraries.Count

foreach ($lib in $libraries) {
    Write-Host "Downloading $($lib.Name)..." -ForegroundColor Yellow
    
    try {
        # Check if file already exists
        if (Test-Path $lib.File) {
            Write-Host "  $($lib.Name) already exists" -ForegroundColor Green
            $successCount++
            continue
        }
        
        # Download the file
        Invoke-WebRequest -Uri $lib.Url -OutFile $lib.File -UseBasicParsing
        
        # Verify download
        if (Test-Path $lib.File) {
            $fileSize = (Get-Item $lib.File).Length
            $sizeKB = [math]::Round($fileSize/1KB, 2)
            Write-Host "  Downloaded $($lib.Name) successfully ($sizeKB KB)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "  Failed to download $($lib.Name)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "  Error downloading $($lib.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 500
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Download Report" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "Downloaded: $successCount of $totalCount libraries" -ForegroundColor Green
Write-Host "Directory: lib\" -ForegroundColor Cyan

# List downloaded libraries
Write-Host ""
Write-Host "Libraries in lib directory:" -ForegroundColor Cyan
Get-ChildItem "lib\*.jar" | ForEach-Object {
    $size = [math]::Round($_.Length/1KB, 2)
    Write-Host "  $($_.Name) ($size KB)" -ForegroundColor White
}

Write-Host ""
Write-Host "Library download completed!" -ForegroundColor Green
