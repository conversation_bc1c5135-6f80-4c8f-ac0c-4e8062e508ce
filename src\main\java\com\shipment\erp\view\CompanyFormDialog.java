package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dialog;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import com.shipment.erp.model.Company;

/**
 * نموذج إدخال/تعديل بيانات الشركة Company Form Dialog
 */
public class CompanyFormDialog extends JDialog {

    private Font arabicFont;
    private Company company;
    private boolean confirmed = false;

    // حقول الإدخال
    private JTextField nameField;
    private JTextField nameEnField;
    private JTextArea addressArea;
    private JTextField cityField;
    private JTextField countryField;
    private JTextField phoneField;
    private JTextField faxField;
    private JTextField emailField;
    private JTextField websiteField;
    private JTextField taxNumberField;
    private JTextField commercialRegisterField;
    private JCheckBox activeCheckBox;

    public CompanyFormDialog(Dialog parent, String title, Company company) {
        super(parent, title, true);

        this.company = company;
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeComponents();
        setupLayout();
        loadData();

        setSize(600, 700);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }

    private void initializeComponents() {
        // حقول الإدخال
        nameField = new JTextField(30);
        nameField.setFont(arabicFont);
        nameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        nameEnField = new JTextField(30);
        nameEnField.setFont(arabicFont);
        nameEnField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        addressArea = new JTextArea(3, 30);
        addressArea.setFont(arabicFont);
        addressArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        addressArea.setLineWrap(true);
        addressArea.setWrapStyleWord(true);

        cityField = new JTextField(20);
        cityField.setFont(arabicFont);
        cityField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        countryField = new JTextField(20);
        countryField.setFont(arabicFont);
        countryField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        phoneField = new JTextField(20);
        phoneField.setFont(arabicFont);
        phoneField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        faxField = new JTextField(20);
        faxField.setFont(arabicFont);
        faxField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        emailField = new JTextField(30);
        emailField.setFont(arabicFont);
        emailField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        websiteField = new JTextField(30);
        websiteField.setFont(arabicFont);
        websiteField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        taxNumberField = new JTextField(20);
        taxNumberField.setFont(arabicFont);
        taxNumberField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        commercialRegisterField = new JTextField(20);
        commercialRegisterField.setFont(arabicFont);
        commercialRegisterField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        activeCheckBox = new JCheckBox("نشط");
        activeCheckBox.setFont(arabicFont);
        activeCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        activeCheckBox.setSelected(true);
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // اسم الشركة بالعربية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("اسم الشركة (عربي):*"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(nameField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // اسم الشركة بالإنجليزية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("اسم الشركة (إنجليزي):"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(nameEnField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // العنوان
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("العنوان:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.BOTH;
        mainPanel.add(new JScrollPane(addressArea), gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // المدينة
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("المدينة:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(cityField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // البلد
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("البلد:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(countryField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // الهاتف
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الهاتف:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(phoneField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // الفاكس
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الفاكس:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(faxField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // البريد الإلكتروني
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("البريد الإلكتروني:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(emailField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // الموقع الإلكتروني
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الموقع الإلكتروني:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(websiteField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // الرقم الضريبي
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الرقم الضريبي:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(taxNumberField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // السجل التجاري
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("السجل التجاري:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(commercialRegisterField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // الحالة
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الحالة:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(activeCheckBox, gbc);
        row++;

        // أزرار التحكم
        JPanel buttonsPanel = createButtonsPanel();

        add(mainPanel, BorderLayout.CENTER);
        add(buttonsPanel, BorderLayout.SOUTH);
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    private JPanel createButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton saveButton = new JButton("حفظ");
        saveButton.setFont(arabicFont);
        saveButton.setPreferredSize(new Dimension(100, 30));
        saveButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (validateInput()) {
                    saveData();
                    confirmed = true;
                    dispose();
                }
            }
        });

        JButton cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.setPreferredSize(new Dimension(100, 30));
        cancelButton.addActionListener(e -> dispose());

        panel.add(saveButton);
        panel.add(cancelButton);

        return panel;
    }

    private void loadData() {
        if (company != null) {
            nameField.setText(company.getName());
            nameEnField.setText(company.getNameEn());
            addressArea.setText(company.getAddress());
            cityField.setText(company.getCity());
            countryField.setText(company.getCountry());
            phoneField.setText(company.getPhone());
            faxField.setText(company.getFax());
            emailField.setText(company.getEmail());
            websiteField.setText(company.getWebsite());
            taxNumberField.setText(company.getTaxNumber());
            commercialRegisterField.setText(company.getCommercialRegister());
            activeCheckBox.setSelected(company.getIsActive());
        }
    }

    private boolean validateInput() {
        if (nameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم الشركة", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            nameField.requestFocus();
            return false;
        }

        String email = emailField.getText().trim();
        if (!email.isEmpty() && !isValidEmail(email)) {
            JOptionPane.showMessageDialog(this, "البريد الإلكتروني غير صحيح", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            emailField.requestFocus();
            return false;
        }

        return true;
    }

    private boolean isValidEmail(String email) {
        return email.matches("^[A-Za-z0-9+_.-]+@(.+)$");
    }

    private void saveData() {
        if (company == null) {
            company = new Company();
        }

        company.setName(nameField.getText().trim());
        company.setNameEn(nameEnField.getText().trim());
        company.setAddress(addressArea.getText().trim());
        company.setCity(cityField.getText().trim());
        company.setCountry(countryField.getText().trim());
        company.setPhone(phoneField.getText().trim());
        company.setFax(faxField.getText().trim());
        company.setEmail(emailField.getText().trim());
        company.setWebsite(websiteField.getText().trim());
        company.setTaxNumber(taxNumberField.getText().trim());
        company.setCommercialRegister(commercialRegisterField.getText().trim());
        company.setIsActive(activeCheckBox.isSelected());
    }

    public Company getCompany() {
        return company;
    }

    public boolean isConfirmed() {
        return confirmed;
    }
}
