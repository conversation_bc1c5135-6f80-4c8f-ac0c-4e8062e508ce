@echo off
echo ========================================
echo 💰 إدراج نافذة إعدادات العملة مباشرة
echo Direct Insert Currency Settings Window
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/2] تجميع الملف...
echo Compiling File...
echo ========================================

javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\DirectInsertCurrency.java
if %errorlevel% equ 0 (
    echo ✅ تم التجميع بنجاح
) else (
    echo ❌ فشل في التجميع
    pause
    exit /b 1
)

echo.
echo [2/2] تشغيل الإدراج المباشر...
echo Running Direct Insert...
echo ========================================

java -cp "lib\*;." DirectInsertCurrency

echo.
echo ========================================
echo ✅ انتهت العملية!
echo Operation Completed!
echo ========================================

echo.
echo 📋 ملخص العملية:
echo ================
echo • تم البحث عن فئة "الإعدادات العامة"
echo • تم إدراج نافذة "إعدادات العملة" مباشرة
echo • تم التحقق من نجاح الإدراج
echo • النافذة جاهزة للاستخدام في الشجرة

echo.
echo 🌳 للوصول للنافذة:
echo ==================
echo 1. افتح التطبيق الرئيسي (EnhancedShipERP)
echo 2. انتقل إلى "الإعدادات والإدارة"
echo 3. افتح "الإعدادات العامة"
echo 4. ابحث عن "إعدادات العملة"
echo 5. انقر نقراً مزدوجاً لفتح النافذة

echo.
echo 💰 تفاصيل النافذة:
echo ==================
echo • الاسم: إعدادات العملة
echo • النوع: WINDOW
echo • الكلاس: CurrencySettingsWindow
echo • الحالة: قيد التطوير
echo • الوصف: إعدادات وإدارة العملات المستخدمة في النظام

echo.
pause
