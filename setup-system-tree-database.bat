@echo off
echo ========================================
echo    SETUP SYSTEM TREE DATABASE
echo    اعداد قاعدة بيانات شجرة النظام
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Setting up System Tree Database...
echo [معلومات] اعداد قاعدة بيانات شجرة النظام...
echo.

REM اعداد البيئة
call set-tns-env.bat

echo ========================================
echo    PHASE 1: VERIFY DATABASE CONNECTION
echo    المرحلة 1: التحقق من اتصال قاعدة البيانات
echo ========================================

echo [1] Testing database connection...

REM اختبار الاتصال بقاعدة البيانات
java %JAVA_TNS_OPTS% -cp . -Doracle.jdbc.defaultNChar=true ^
-Djava.sql.DriverManager.logWriter=java.io.PrintWriter ^
-classpath "lib\ojdbc11.jar;lib\orai18n.jar;." ^
oracle.jdbc.OracleDriver

if errorlevel 1 (
    echo ERROR: Database connection failed
    goto :error
)

echo OK: Database connection verified

echo.
echo ========================================
echo    PHASE 2: CREATE SYSTEM TREE TABLE
echo    المرحلة 2: انشاء جدول شجرة النظام
echo ========================================

echo [2] Creating system tree table and data...

REM تشغيل سكريپت SQL لانشاء الجدول
sqlplus -S SHIP_ERP/ship_erp_password@SHIP_ERP @scripts\create_system_tree_table.sql

if errorlevel 1 (
    echo WARNING: SQL script execution may have issues
    echo Will try alternative method...
    
    REM محاولة بديلة باستخدام Java
    echo Trying Java-based table creation...
    java %JAVA_TNS_OPTS% -cp "lib\ojdbc11.jar;lib\orai18n.jar;." CreateSystemTreeTable
)

echo OK: System tree table setup completed

echo.
echo ========================================
echo    PHASE 3: COMPILE SYSTEM TREE MANAGER
echo    المرحلة 3: تجميع مدير شجرة النظام
echo ========================================

echo [3] Compiling SystemTreeManager...

javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;." -d . src\main\java\SystemTreeManager.java
if errorlevel 1 (
    echo ERROR: Failed to compile SystemTreeManager
    goto :error
)

echo OK: SystemTreeManager compiled successfully

echo.
echo ========================================
echo    PHASE 4: TEST SYSTEM TREE MANAGER
echo    المرحلة 4: اختبار مدير شجرة النظام
echo ========================================

echo [4] Testing SystemTreeManager...

java %JAVA_TNS_OPTS% -cp "lib\ojdbc11.jar;lib\orai18n.jar;." SystemTreeManager
if errorlevel 1 (
    echo WARNING: SystemTreeManager test failed
) else (
    echo OK: SystemTreeManager test passed
)

echo.
echo ========================================
echo    PHASE 5: UPDATE TREE MENU PANEL
echo    المرحلة 5: تحديث لوحة قائمة الشجرة
echo ========================================

echo [5] Compiling updated TreeMenuPanel...

javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar;." -d . src\main\java\TreeMenuPanel.java
if errorlevel 1 (
    echo ERROR: Failed to compile TreeMenuPanel
    goto :error
)

echo OK: TreeMenuPanel compiled successfully

echo.
echo ========================================
echo    PHASE 6: VERIFICATION
echo    المرحلة 6: التحقق
echo ========================================

echo [6] Verifying system tree setup...

echo Checking compiled classes:
if exist "SystemTreeManager.class" (
    echo OK: SystemTreeManager.class found
) else (
    echo ERROR: SystemTreeManager.class missing
)

if exist "TreeMenuPanel.class" (
    echo OK: TreeMenuPanel.class found
) else (
    echo ERROR: TreeMenuPanel.class missing
)

echo.
echo Checking database table:
echo Testing database table access...

java %JAVA_TNS_OPTS% -cp "lib\ojdbc11.jar;lib\orai18n.jar;." -Dtest.mode=verify SystemTreeManager

echo.
echo ========================================
echo    SETUP COMPLETED
echo    اكتمل الاعداد
echo ========================================

echo.
echo [SUCCESS] System Tree Database setup completed!
echo [نجح] اكتمل اعداد قاعدة بيانات شجرة النظام!
echo.
echo Features enabled:
echo الميزات المفعلة:
echo - Dynamic system tree from database
echo - Automatic window registration
echo - Tree structure management
echo - Real-time tree updates
echo.
echo Database table: ERP_SYSTEM_TREE
echo View: VW_SYSTEM_TREE_HIERARCHY
echo Manager class: SystemTreeManager
echo.
echo Next steps:
echo الخطوات التالية:
echo 1. Run the main system: start-system.bat
echo 2. Test tree functionality
echo 3. Add new windows automatically
echo.

goto :end

:error
echo.
echo ========================================
echo    ERROR OCCURRED
echo    حدث خطأ
echo ========================================
echo.
echo [ERROR] System Tree Database setup failed!
echo [خطأ] فشل اعداد قاعدة بيانات شجرة النظام!
echo.
echo Troubleshooting:
echo استكشاف الاخطاء:
echo 1. Check Oracle database is running
echo 2. Verify connection credentials
echo 3. Check SQL script syntax
echo 4. Verify Java compilation
echo.
echo Manual setup:
echo الاعداد اليدوي:
echo 1. Run: sqlplus SHIP_ERP/ship_erp_password@SHIP_ERP
echo 2. Execute: @scripts\create_system_tree_table.sql
echo 3. Compile: javac SystemTreeManager.java
echo.

:end
pause
