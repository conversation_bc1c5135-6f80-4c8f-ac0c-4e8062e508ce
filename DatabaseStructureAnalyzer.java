import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.Properties;

/**
 * محلل بنية قاعدة البيانات الشامل
 * Comprehensive Database Structure Analyzer
 */
public class DatabaseStructureAnalyzer {
    
    private Connection connection;
    private PrintWriter reportWriter;
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  DATABASE STRUCTURE ANALYZER");
        System.out.println("  محلل بنية قاعدة البيانات");
        System.out.println("========================================");
        
        DatabaseStructureAnalyzer analyzer = new DatabaseStructureAnalyzer();
        
        // تحليل SHIP_ERP
        analyzer.analyzeSchema("SHIP_ERP", "ship_erp_password");
        
        // تحليل IAS20251
        analyzer.analyzeSchema("ias20251", "ys123");
        
        System.out.println("\n✅ تم الانتهاء من تحليل قواعد البيانات");
    }
    
    /**
     * تحليل مخطط قاعدة بيانات محدد
     */
    public void analyzeSchema(String username, String password) {
        System.out.println("\n🔍 Analyzing schema: " + username);
        System.out.println("🔍 تحليل مخطط: " + username);
        
        try {
            // إنشاء ملف التقرير
            String reportFile = "DATABASE_ANALYSIS_" + username + "_" +
                              new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new java.util.Date()) + ".md";
            reportWriter = new PrintWriter(new FileWriter(reportFile), true);
            
            // الاتصال بقاعدة البيانات
            if (connectToDatabase(username, password)) {
                
                writeReportHeader(username);
                
                // تحليل الجداول
                analyzeTables();
                
                // تحليل الفهارس
                analyzeIndexes();
                
                // تحليل المفاتيح الخارجية
                analyzeForeignKeys();
                
                // تحليل الإجراءات المخزنة
                analyzeStoredProcedures();
                
                // إحصائيات البيانات
                analyzeDataStatistics();
                
                writeReportFooter();
                
                System.out.println("📄 Report saved: " + reportFile);
                
            } else {
                System.out.println("❌ Failed to connect to " + username);
            }
            
        } catch (Exception e) {
            System.out.println("❌ Error analyzing " + username + ": " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeConnections();
        }
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private boolean connectToDatabase(String username, String password) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            String url = "*************************************";
            connection = DriverManager.getConnection(url, props);
            
            return connection != null && !connection.isClosed();
            
        } catch (Exception e) {
            System.out.println("Connection error: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * كتابة رأس التقرير
     */
    private void writeReportHeader(String schema) {
        reportWriter.println("# تحليل بنية قاعدة البيانات");
        reportWriter.println("## Database Structure Analysis Report");
        reportWriter.println();
        reportWriter.println("**Schema:** " + schema);
        reportWriter.println("**Date:** " + new Date());
        reportWriter.println("**Generated by:** DatabaseStructureAnalyzer");
        reportWriter.println();
        reportWriter.println("---");
        reportWriter.println();
    }
    
    /**
     * تحليل الجداول
     */
    private void analyzeTables() {
        reportWriter.println("## 📋 تحليل الجداول - Tables Analysis");
        reportWriter.println();
        
        try {
            String query = "SELECT TABLE_NAME, NUM_ROWS, BLOCKS, AVG_ROW_LEN " +
                          "FROM USER_TABLES ORDER BY TABLE_NAME";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                reportWriter.println("| Table Name | Rows | Blocks | Avg Row Length |");
                reportWriter.println("|------------|------|--------|----------------|");
                
                while (rs.next()) {
                    String tableName = rs.getString("TABLE_NAME");
                    String numRows = rs.getString("NUM_ROWS");
                    String blocks = rs.getString("BLOCKS");
                    String avgRowLen = rs.getString("AVG_ROW_LEN");
                    
                    reportWriter.printf("| %s | %s | %s | %s |\n", 
                        tableName, 
                        numRows != null ? numRows : "N/A",
                        blocks != null ? blocks : "N/A",
                        avgRowLen != null ? avgRowLen : "N/A");
                    
                    // تحليل تفصيلي لكل جدول
                    analyzeTableStructure(tableName);
                }
            }
            
        } catch (SQLException e) {
            reportWriter.println("❌ Error analyzing tables: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * تحليل بنية جدول محدد
     */
    private void analyzeTableStructure(String tableName) {
        try {
            String query = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE, DATA_DEFAULT " +
                          "FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ? ORDER BY COLUMN_ID";
            
            try (PreparedStatement stmt = connection.prepareStatement(query)) {
                stmt.setString(1, tableName);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    reportWriter.println();
                    reportWriter.println("### 📊 " + tableName + " Structure");
                    reportWriter.println();
                    reportWriter.println("| Column | Type | Length | Nullable | Default |");
                    reportWriter.println("|--------|------|--------|----------|---------|");
                    
                    while (rs.next()) {
                        String columnName = rs.getString("COLUMN_NAME");
                        String dataType = rs.getString("DATA_TYPE");
                        String dataLength = rs.getString("DATA_LENGTH");
                        String nullable = rs.getString("NULLABLE");
                        String dataDefault = rs.getString("DATA_DEFAULT");
                        
                        reportWriter.printf("| %s | %s | %s | %s | %s |\n",
                            columnName,
                            dataType,
                            dataLength != null ? dataLength : "",
                            "Y".equals(nullable) ? "Yes" : "No",
                            dataDefault != null ? dataDefault.trim() : "");
                    }
                }
            }
            
        } catch (SQLException e) {
            reportWriter.println("❌ Error analyzing table " + tableName + ": " + e.getMessage());
        }
    }
    
    /**
     * تحليل الفهارس
     */
    private void analyzeIndexes() {
        reportWriter.println("\n## 🔍 تحليل الفهارس - Indexes Analysis");
        reportWriter.println();
        
        try {
            String query = "SELECT INDEX_NAME, TABLE_NAME, UNIQUENESS, STATUS " +
                          "FROM USER_INDEXES ORDER BY TABLE_NAME, INDEX_NAME";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                reportWriter.println("| Index Name | Table | Unique | Status |");
                reportWriter.println("|------------|-------|--------|--------|");
                
                while (rs.next()) {
                    reportWriter.printf("| %s | %s | %s | %s |\n",
                        rs.getString("INDEX_NAME"),
                        rs.getString("TABLE_NAME"),
                        rs.getString("UNIQUENESS"),
                        rs.getString("STATUS"));
                }
            }
            
        } catch (SQLException e) {
            reportWriter.println("❌ Error analyzing indexes: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * تحليل المفاتيح الخارجية
     */
    private void analyzeForeignKeys() {
        reportWriter.println("## 🔗 تحليل المفاتيح الخارجية - Foreign Keys Analysis");
        reportWriter.println();
        
        try {
            String query = "SELECT CONSTRAINT_NAME, TABLE_NAME, R_CONSTRAINT_NAME, STATUS " +
                          "FROM USER_CONSTRAINTS WHERE CONSTRAINT_TYPE = 'R' " +
                          "ORDER BY TABLE_NAME";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                reportWriter.println("| Constraint | Table | References | Status |");
                reportWriter.println("|------------|-------|------------|--------|");
                
                while (rs.next()) {
                    reportWriter.printf("| %s | %s | %s | %s |\n",
                        rs.getString("CONSTRAINT_NAME"),
                        rs.getString("TABLE_NAME"),
                        rs.getString("R_CONSTRAINT_NAME"),
                        rs.getString("STATUS"));
                }
            }
            
        } catch (SQLException e) {
            reportWriter.println("❌ Error analyzing foreign keys: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * تحليل الإجراءات المخزنة
     */
    private void analyzeStoredProcedures() {
        reportWriter.println("## ⚙️ تحليل الإجراءات المخزنة - Stored Procedures Analysis");
        reportWriter.println();
        
        try {
            String query = "SELECT OBJECT_NAME, OBJECT_TYPE, STATUS, CREATED, LAST_DDL_TIME " +
                          "FROM USER_OBJECTS WHERE OBJECT_TYPE IN ('PROCEDURE', 'FUNCTION', 'PACKAGE') " +
                          "ORDER BY OBJECT_TYPE, OBJECT_NAME";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                reportWriter.println("| Name | Type | Status | Created | Last Modified |");
                reportWriter.println("|------|------|--------|---------|---------------|");
                
                while (rs.next()) {
                    reportWriter.printf("| %s | %s | %s | %s | %s |\n",
                        rs.getString("OBJECT_NAME"),
                        rs.getString("OBJECT_TYPE"),
                        rs.getString("STATUS"),
                        rs.getString("CREATED"),
                        rs.getString("LAST_DDL_TIME"));
                }
            }
            
        } catch (SQLException e) {
            reportWriter.println("❌ Error analyzing stored procedures: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * تحليل إحصائيات البيانات
     */
    private void analyzeDataStatistics() {
        reportWriter.println("## 📊 إحصائيات البيانات - Data Statistics");
        reportWriter.println();
        
        // إحصائيات عامة
        reportWriter.println("### General Statistics");
        reportWriter.println();
        
        try {
            // عدد الجداول
            String tablesQuery = "SELECT COUNT(*) FROM USER_TABLES";
            try (PreparedStatement stmt = connection.prepareStatement(tablesQuery);
                 ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    reportWriter.println("- **Total Tables:** " + rs.getInt(1));
                }
            }
            
            // عدد الفهارس
            String indexesQuery = "SELECT COUNT(*) FROM USER_INDEXES";
            try (PreparedStatement stmt = connection.prepareStatement(indexesQuery);
                 ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    reportWriter.println("- **Total Indexes:** " + rs.getInt(1));
                }
            }
            
            // عدد المفاتيح الخارجية
            String fkQuery = "SELECT COUNT(*) FROM USER_CONSTRAINTS WHERE CONSTRAINT_TYPE = 'R'";
            try (PreparedStatement stmt = connection.prepareStatement(fkQuery);
                 ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    reportWriter.println("- **Total Foreign Keys:** " + rs.getInt(1));
                }
            }
            
        } catch (SQLException e) {
            reportWriter.println("❌ Error getting statistics: " + e.getMessage());
        }
        
        reportWriter.println();
    }
    
    /**
     * كتابة ذيل التقرير
     */
    private void writeReportFooter() {
        reportWriter.println("---");
        reportWriter.println();
        reportWriter.println("**Report Generated:** " + new Date());
        reportWriter.println("**Tool:** DatabaseStructureAnalyzer v1.0");
        reportWriter.println();
        reportWriter.println("🎉 **Analysis Complete!**");
    }
    
    /**
     * إغلاق الاتصالات
     */
    private void closeConnections() {
        try {
            if (reportWriter != null) {
                reportWriter.close();
            }
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (Exception e) {
            System.out.println("Error closing connections: " + e.getMessage());
        }
    }
}
