import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * إضافة نافذة إعدادات العملة إلى شجرة الأنظمة
 * Add Currency Settings Window to System Tree
 */
public class AddCurrencySettingsWindow {
    
    private static final String DB_URL = "***********************************";
    private static final String DB_USER = "ship_erp";
    private static final String DB_PASSWORD = "ship_erp";
    
    public static void main(String[] args) {
        System.out.println("💰 إضافة نافذة إعدادات العملة إلى شجرة الأنظمة");
        System.out.println("Adding Currency Settings Window to System Tree");
        System.out.println("==============================================");
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            
            // 1. فحص الهيكل الحالي
            checkCurrentStructure(conn);
            
            // 2. البحث عن فئة "الإعدادات العامة"
            int generalSettingsId = findGeneralSettingsCategory(conn);
            
            // 3. البحث عن "المتغيرات العامة"
            int globalVariablesId = findGlobalVariablesWindow(conn);
            
            // 4. إضافة نافذة "إعدادات العملة"
            addCurrencySettingsWindow(conn, generalSettingsId, globalVariablesId);
            
            // 5. التحقق من النتيجة
            verifyAddition(conn);
            
            System.out.println("\n✅ تم إضافة نافذة إعدادات العملة بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkCurrentStructure(Connection conn) throws SQLException {
        System.out.println("\n🔍 فحص الهيكل الحالي لشجرة الأنظمة...");
        
        String sql = """
            SELECT 
                TREE_ID,
                PARENT_ID,
                LPAD(' ', (TREE_LEVEL * 4)) || NODE_NAME_AR AS TREE_STRUCTURE,
                NODE_TYPE,
                DISPLAY_ORDER
            FROM ERP_SYSTEM_TREE
            WHERE NODE_NAME_AR LIKE '%إعدادات%' OR NODE_NAME_AR LIKE '%متغيرات%'
            ORDER BY TREE_LEVEL, DISPLAY_ORDER
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            System.out.println("📋 العقد المتعلقة بالإعدادات:");
            while (rs.next()) {
                System.out.printf("ID: %d | %s | %s | ترتيب: %d%n",
                    rs.getInt("TREE_ID"),
                    rs.getString("TREE_STRUCTURE"),
                    rs.getString("NODE_TYPE"),
                    rs.getInt("DISPLAY_ORDER"));
            }
        }
    }
    
    private static int findGeneralSettingsCategory(Connection conn) throws SQLException {
        System.out.println("\n🔍 البحث عن فئة 'الإعدادات العامة'...");
        
        String sql = """
            SELECT TREE_ID, NODE_NAME_AR, NODE_TYPE 
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR = 'الإعدادات العامة' 
            AND NODE_TYPE = 'CATEGORY'
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                int id = rs.getInt("TREE_ID");
                System.out.println("✅ تم العثور على فئة 'الإعدادات العامة' - ID: " + id);
                return id;
            } else {
                throw new SQLException("❌ لم يتم العثور على فئة 'الإعدادات العامة'");
            }
        }
    }
    
    private static int findGlobalVariablesWindow(Connection conn) throws SQLException {
        System.out.println("\n🔍 البحث عن نافذة 'المتغيرات العامة'...");
        
        String sql = """
            SELECT TREE_ID, NODE_NAME_AR, DISPLAY_ORDER, PARENT_ID
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR LIKE '%متغيرات%' 
            OR NODE_NAME_AR LIKE '%Global Variables%'
            OR NODE_NAME_AR LIKE '%العامة%'
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            int globalVarId = -1;
            while (rs.next()) {
                int id = rs.getInt("TREE_ID");
                String name = rs.getString("NODE_NAME_AR");
                int order = rs.getInt("DISPLAY_ORDER");
                int parentId = rs.getInt("PARENT_ID");
                
                System.out.printf("🔍 عقدة: %s (ID: %d, ترتيب: %d, أب: %d)%n", 
                    name, id, order, parentId);
                
                if (name.contains("متغيرات") || name.contains("Variables")) {
                    globalVarId = id;
                }
            }
            
            if (globalVarId != -1) {
                System.out.println("✅ تم العثور على المتغيرات العامة - ID: " + globalVarId);
                return globalVarId;
            } else {
                System.out.println("⚠️ لم يتم العثور على 'المتغيرات العامة' - سيتم إضافة النافذة في نهاية الفئة");
                return -1;
            }
        }
    }
    
    private static void addCurrencySettingsWindow(Connection conn, int generalSettingsId, int globalVariablesId) 
            throws SQLException {
        System.out.println("\n💰 إضافة نافذة 'إعدادات العملة'...");
        
        // تحديد ترتيب العرض
        int displayOrder = getNextDisplayOrder(conn, generalSettingsId, globalVariablesId);
        
        // فحص وجود النافذة مسبقاً
        if (currencySettingsExists(conn)) {
            System.out.println("⚠️ نافذة 'إعدادات العملة' موجودة مسبقاً");
            return;
        }
        
        // إدراج النافذة الجديدة
        String insertSql = """
            INSERT INTO ERP_SYSTEM_TREE (
                TREE_ID,
                PARENT_ID,
                NODE_NAME_AR,
                NODE_NAME_EN,
                NODE_DESCRIPTION,
                NODE_TYPE,
                WINDOW_CLASS,
                ICON_PATH,
                DISPLAY_ORDER,
                TREE_LEVEL,
                IS_ACTIVE,
                IS_VISIBLE,
                ACCESS_PERMISSIONS,
                ADDITIONAL_INFO,
                CREATED_DATE,
                CREATED_BY,
                LAST_UPDATED,
                UPDATED_BY,
                VERSION_NUMBER
            ) VALUES (
                (SELECT NVL(MAX(TREE_ID), 0) + 1 FROM ERP_SYSTEM_TREE),
                ?,
                'إعدادات العملة',
                'Currency Settings',
                'إعدادات وإدارة العملات المستخدمة في النظام - قيد التطوير',
                'WINDOW',
                'CurrencySettingsWindow',
                'icons/currency.png',
                ?,
                2,
                'Y',
                'Y',
                'ADMIN,SETTINGS_MANAGER',
                'STATUS:UNDER_DEVELOPMENT;PRIORITY:MEDIUM;MODULE:SETTINGS',
                SYSDATE,
                USER,
                SYSDATE,
                USER,
                1
            )
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(insertSql)) {
            stmt.setInt(1, generalSettingsId);
            stmt.setInt(2, displayOrder);
            
            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println("✅ تم إدراج نافذة 'إعدادات العملة' بنجاح");
                System.out.println("📍 الموقع: تحت 'الإعدادات العامة'");
                System.out.println("📊 ترتيب العرض: " + displayOrder);
                System.out.println("🏷️ الحالة: قيد التطوير");
                
                conn.commit();
            } else {
                throw new SQLException("فشل في إدراج النافذة");
            }
        }
    }
    
    private static int getNextDisplayOrder(Connection conn, int generalSettingsId, int globalVariablesId) 
            throws SQLException {
        
        if (globalVariablesId != -1) {
            // إذا وُجدت المتغيرات العامة، ضع النافذة الجديدة بعدها
            String sql = """
                SELECT DISPLAY_ORDER 
                FROM ERP_SYSTEM_TREE 
                WHERE TREE_ID = ?
            """;
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setInt(1, globalVariablesId);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int globalVarOrder = rs.getInt("DISPLAY_ORDER");
                        System.out.println("📍 سيتم وضع النافذة بعد 'المتغيرات العامة' (ترتيب: " + (globalVarOrder + 1) + ")");
                        return globalVarOrder + 1;
                    }
                }
            }
        }
        
        // إذا لم توجد المتغيرات العامة، ضع النافذة في نهاية الفئة
        String sql = """
            SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 AS NEXT_ORDER
            FROM ERP_SYSTEM_TREE 
            WHERE PARENT_ID = ?
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, generalSettingsId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    int nextOrder = rs.getInt("NEXT_ORDER");
                    System.out.println("📍 سيتم وضع النافذة في نهاية فئة 'الإعدادات العامة' (ترتيب: " + nextOrder + ")");
                    return nextOrder;
                }
            }
        }
        
        return 1; // افتراضي
    }
    
    private static boolean currencySettingsExists(Connection conn) throws SQLException {
        String sql = """
            SELECT COUNT(*) 
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR = 'إعدادات العملة' 
            OR NODE_NAME_EN = 'Currency Settings'
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        }
        return false;
    }
    
    private static void verifyAddition(Connection conn) throws SQLException {
        System.out.println("\n✅ التحقق من إضافة النافذة...");
        
        String sql = """
            SELECT 
                TREE_ID,
                PARENT_ID,
                NODE_NAME_AR,
                NODE_NAME_EN,
                NODE_DESCRIPTION,
                WINDOW_CLASS,
                DISPLAY_ORDER,
                TREE_LEVEL,
                IS_ACTIVE,
                ADDITIONAL_INFO,
                CREATED_DATE
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR = 'إعدادات العملة'
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                System.out.println("📋 تفاصيل النافذة المضافة:");
                System.out.println("=======================");
                System.out.println("🆔 معرف العقدة: " + rs.getInt("TREE_ID"));
                System.out.println("👨‍👩‍👧‍👦 معرف الأب: " + rs.getInt("PARENT_ID"));
                System.out.println("🏷️ الاسم العربي: " + rs.getString("NODE_NAME_AR"));
                System.out.println("🏷️ الاسم الإنجليزي: " + rs.getString("NODE_NAME_EN"));
                System.out.println("📝 الوصف: " + rs.getString("NODE_DESCRIPTION"));
                System.out.println("💻 كلاس النافذة: " + rs.getString("WINDOW_CLASS"));
                System.out.println("📊 ترتيب العرض: " + rs.getInt("DISPLAY_ORDER"));
                System.out.println("📏 مستوى الشجرة: " + rs.getInt("TREE_LEVEL"));
                System.out.println("✅ نشط: " + rs.getString("IS_ACTIVE"));
                System.out.println("ℹ️ معلومات إضافية: " + rs.getString("ADDITIONAL_INFO"));
                
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
                System.out.println("📅 تاريخ الإنشاء: " + sdf.format(rs.getTimestamp("CREATED_DATE")));
                
            } else {
                throw new SQLException("❌ لم يتم العثور على النافذة المضافة!");
            }
        }
        
        // عرض الهيكل المحدث
        showUpdatedStructure(conn);
    }
    
    private static void showUpdatedStructure(Connection conn) throws SQLException {
        System.out.println("\n🌳 الهيكل المحدث لفئة 'الإعدادات العامة':");
        System.out.println("============================================");
        
        String sql = """
            SELECT 
                TREE_ID,
                LPAD(' ', ((TREE_LEVEL - 1) * 4)) || NODE_NAME_AR AS TREE_STRUCTURE,
                NODE_TYPE,
                DISPLAY_ORDER,
                CASE 
                    WHEN ADDITIONAL_INFO LIKE '%UNDER_DEVELOPMENT%' THEN '🚧 قيد التطوير'
                    WHEN IS_ACTIVE = 'Y' THEN '✅ نشط'
                    ELSE '❌ غير نشط'
                END AS STATUS
            FROM ERP_SYSTEM_TREE
            WHERE TREE_ID IN (
                SELECT TREE_ID FROM ERP_SYSTEM_TREE 
                WHERE NODE_NAME_AR = 'الإعدادات العامة'
                UNION
                SELECT TREE_ID FROM ERP_SYSTEM_TREE 
                WHERE PARENT_ID = (SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = 'الإعدادات العامة')
            )
            ORDER BY TREE_LEVEL, DISPLAY_ORDER
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                System.out.printf("%-40s | %-10s | ترتيب: %-3d | %s%n",
                    rs.getString("TREE_STRUCTURE"),
                    rs.getString("NODE_TYPE"),
                    rs.getInt("DISPLAY_ORDER"),
                    rs.getString("STATUS"));
            }
        }
    }
}
