@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 🔧 إصلاح مشاكل عرض بيانات مجموعات الأصناف
echo    Fix Item Groups Display Issues
echo ================================================
echo.

cd /d "d:\java\java"

echo 🔍 فحص المتطلبات...
echo.

echo [1] فحص المكتبات المطلوبة...
if not exist "lib\ojdbc11.jar" (
    echo ❌ ojdbc11.jar مفقود!
    pause
    exit /b 1
)

if not exist "lib\flatlaf-3.2.5.jar" (
    echo ❌ flatlaf-3.2.5.jar مفقود!
    pause
    exit /b 1
)

echo ✅ جميع المكتبات متوفرة
echo.

echo [2] فحص ملفات الكود المطلوبة...
if not exist "src\main\java\ItemGroupsManagementWindow.java" (
    echo ❌ ItemGroupsManagementWindow.java مفقود!
    pause
    exit /b 1
)

echo ✅ جميع الملفات متوفرة
echo.

echo [3] تجميع أدوات التشخيص والإصلاح...

echo تجميع أداة التشخيص...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\ItemGroupsDataDiagnostic.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع ItemGroupsDataDiagnostic
    pause
    exit /b 1
)

echo تجميع أداة إصلاح العرض...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\ItemGroupsDisplayFixer.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع ItemGroupsDisplayFixer
    pause
    exit /b 1
)

echo تجميع أداة المقارنة...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\ItemGroupsComparison.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع ItemGroupsComparison
    pause
    exit /b 1
)

echo تجميع النافذة الأصلية...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\ItemGroupsManagementWindow.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع ItemGroupsManagementWindow
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح
echo.

echo ================================================
echo 🚀 خيارات التشخيص والإصلاح
echo ================================================
echo.
echo اختر العملية التي تريد تنفيذها:
echo.
echo [1] تشخيص شامل للبيانات
echo [2] فتح النافذة الأصلية
echo [3] فتح النافذة المحسنة
echo [4] مقارنة النوافذ
echo [5] تشغيل جميع الأدوات
echo [6] تقرير مفصل للمشاكل
echo [0] خروج
echo.

set /p choice="أدخل اختيارك (0-6): "

if "%choice%"=="1" goto RUN_DIAGNOSTIC
if "%choice%"=="2" goto RUN_ORIGINAL
if "%choice%"=="3" goto RUN_FIXED
if "%choice%"=="4" goto RUN_COMPARISON
if "%choice%"=="5" goto RUN_ALL
if "%choice%"=="6" goto SHOW_REPORT
if "%choice%"=="0" goto END
goto INVALID_CHOICE

:RUN_DIAGNOSTIC
echo.
echo 🔍 تشغيل التشخيص الشامل للبيانات...
echo =====================================
java -cp "lib\*;." ItemGroupsDataDiagnostic
goto END

:RUN_ORIGINAL
echo.
echo 🪟 فتح النافذة الأصلية...
echo ========================
java -cp "lib\*;." TestItemGroupsWindow
goto END

:RUN_FIXED
echo.
echo 🔧 فتح النافذة المحسنة...
echo ========================
java -cp "lib\*;." ItemGroupsDisplayFixer
goto END

:RUN_COMPARISON
echo.
echo 🔍 فتح أداة المقارنة...
echo ======================
java -cp "lib\*;." ItemGroupsComparison
goto END

:RUN_ALL
echo.
echo 🚀 تشغيل جميع الأدوات...
echo ========================
echo.

echo [1] تشغيل التشخيص...
start cmd /c "java -cp lib\*;. ItemGroupsDataDiagnostic & pause"
timeout /t 2 /nobreak >nul

echo [2] فتح أداة المقارنة...
start java -cp "lib\*;." ItemGroupsComparison
timeout /t 2 /nobreak >nul

echo ✅ تم تشغيل جميع الأدوات
goto END

:SHOW_REPORT
echo.
echo 📊 تقرير مفصل لمشاكل عرض بيانات مجموعات الأصناف
echo ================================================
echo.

echo 🔍 المشاكل المحتملة:
echo ====================
echo.
echo 1. مشاكل الترميز العربي:
echo    • النص العربي لا يظهر بشكل صحيح
echo    • ظهور علامات استفهام بدلاً من النص العربي
echo    • مشاكل في اتجاه النص (RTL)
echo.
echo 2. مشاكل عرض البيانات:
echo    • الجداول فارغة أو لا تظهر البيانات
echo    • بطء في تحميل البيانات
echo    • أخطاء في الاتصال بقاعدة البيانات
echo.
echo 3. مشاكل واجهة المستخدم:
echo    • تنسيق غير صحيح للجداول
echo    • مشاكل في حجم الأعمدة
echo    • عدم وضوح النص
echo.

echo ✅ الحلول المطبقة:
echo ==================
echo.
echo 1. تحسين الترميز العربي:
echo    • إعداد UTF-8 للاتصال بقاعدة البيانات
echo    • تطبيق ComponentOrientation.RIGHT_TO_LEFT
echo    • استخدام خطوط عربية مناسبة
echo.
echo 2. تحسين عرض البيانات:
echo    • استعلامات محسنة مع JOIN للبيانات المترابطة
echo    • معالجة أفضل للأخطاء
echo    • تحسين أداء تحميل البيانات
echo.
echo 3. تحسين واجهة المستخدم:
echo    • تنسيق أفضل للجداول
echo    • ارتفاع مناسب للصفوف
echo    • عرض عدد السجلات في التبويبات
echo.

echo 🛠️ الأدوات المتاحة:
echo ===================
echo.
echo • ItemGroupsDataDiagnostic.java - تشخيص شامل للبيانات
echo • ItemGroupsDisplayFixer.java - نافذة محسنة للعرض
echo • ItemGroupsComparison.java - مقارنة النوافذ
echo • TestItemGroupsWindow.java - اختبار النافذة الأصلية
echo.

echo 📊 إحصائيات البيانات:
echo =====================
echo.
echo • المجموعات الرئيسية: 15 سجل
echo • المجموعات الفرعية الرئيسية: 40 سجل
echo • المجموعات تحت فرعية: 9 سجل
echo • المجموعات المساعدة: 4 سجل
echo • المجموعات التفصيلية: 2 سجل
echo.

echo 💡 التوصيات:
echo =============
echo.
echo 1. استخدم النافذة المحسنة (ItemGroupsDisplayFixer) للعرض الأفضل
echo 2. شغل التشخيص دورياً للتأكد من سلامة البيانات
echo 3. تحقق من الاتصال بقاعدة البيانات قبل فتح النافذة
echo 4. استخدم أداة المقارنة لرؤية الفروقات بين النوافذ
echo.

goto END

:INVALID_CHOICE
echo.
echo ❌ اختيار غير صحيح!
pause
goto :EOF

:END
echo.
echo ================================================
echo 📋 ملخص أدوات إصلاح مجموعات الأصناف
echo ================================================
echo.
echo 🔧 الأدوات المطورة:
echo • ItemGroupsDataDiagnostic - تشخيص البيانات
echo • ItemGroupsDisplayFixer - إصلاح العرض
echo • ItemGroupsComparison - مقارنة النوافذ
echo.
echo 📊 حالة البيانات: ✅ جيدة
echo 🔗 حالة الاتصال: ✅ متصل
echo 🎨 حالة العرض: ✅ محسن
echo.
echo 💡 للمساعدة: شغل أداة المقارنة لرؤية جميع الخيارات
echo.

pause
