import java.awt.Color;
import java.awt.Window;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.UIManager.LookAndFeelInfo;

/**
 * مدير المظاهر العملي - يعمل مع المكتبات المتاحة فقط Working Theme Manager - Works with Available
 * Libraries Only
 */
public class WorkingThemeManager {

    private static WorkingThemeManager instance;
    private Map<String, ThemeInfo> availableThemes;
    private Connection connection;
    private String currentTheme = "System Default";

    /**
     * معلومات المظهر
     */
    public static class ThemeInfo {
        public final String name;
        public final String displayName;
        public final String category;
        public final String className;
        public final boolean isDark;
        public final String description;
        public final String author;
        public final String version;
        public final boolean isAvailable;

        public ThemeInfo(String name, String displayName, String category, String className,
                boolean isDark, String description, String author, String version) {
            this.name = name;
            this.displayName = displayName;
            this.category = category;
            this.className = className;
            this.isDark = isDark;
            this.description = description;
            this.author = author;
            this.version = version;
            this.isAvailable = checkAvailability();
        }

        private boolean checkAvailability() {
            if (className == null || className.isEmpty())
                return true;
            try {
                Class.forName(className);
                return true;
            } catch (ClassNotFoundException e) {
                return false;
            }
        }
    }

    private WorkingThemeManager() {
        initializeAvailableThemes();
        connectToDatabase();
        loadSavedTheme();
    }

    public static WorkingThemeManager getInstance() {
        if (instance == null) {
            instance = new WorkingThemeManager();
        }
        return instance;
    }

    /**
     * تهيئة المظاهر المتاحة فقط
     */
    private void initializeAvailableThemes() {
        availableThemes = new LinkedHashMap<>();
        System.out.println("🎨 تهيئة المظاهر المتاحة...");

        // System Themes - متاحة دائماً
        addTheme("System Default", "مظهر النظام الافتراضي", "System", "", false,
                "مظهر النظام الافتراضي - يتكيف مع نظام التشغيل", "System", "Default");

        addTheme("Metal", "مظهر Metal", "System", "javax.swing.plaf.metal.MetalLookAndFeel", false,
                "مظهر Java Metal الكلاسيكي - متاح دائماً", "Oracle", "Java");

        addTheme("Nimbus", "مظهر Nimbus", "System", "javax.swing.plaf.nimbus.NimbusLookAndFeel",
                false, "مظهر Nimbus الحديث والجميل - متاح دائماً", "Oracle", "Java");

        // Windows Theme - متاح على Windows فقط
        addTheme("Windows", "مظهر Windows", "System",
                "com.sun.java.swing.plaf.windows.WindowsLookAndFeel", false,
                "مظهر Windows الأصلي - متاح على Windows فقط", "Microsoft", "Windows");

        // محاولة إضافة FlatLaf إذا كان متاحاً
        tryAddFlatLafThemes();

        // محاولة إضافة JTattoo إذا كان متاحاً
        tryAddJTattooThemes();

        // إضافة مظاهر مخصصة بسيطة
        addCustomThemes();

        int availableCount =
                (int) availableThemes.values().stream().filter(t -> t.isAvailable).count();
        System.out.println("✅ تم تهيئة " + availableThemes.size() + " مظهر، منها " + availableCount
                + " متاح للاستخدام");
    }

    private void tryAddFlatLafThemes() {
        try {
            // محاولة تحميل FlatLaf
            Class.forName("com.formdev.flatlaf.FlatLightLaf");

            addTheme("FlatLaf Light", "FlatLaf فاتح", "FlatLaf", "com.formdev.flatlaf.FlatLightLaf",
                    false, "مظهر FlatLaf الفاتح الحديث والأنيق", "FormDev", "3.x");

            addTheme("FlatLaf Dark", "FlatLaf مظلم", "FlatLaf", "com.formdev.flatlaf.FlatDarkLaf",
                    true, "مظهر FlatLaf المظلم الحديث والأنيق", "FormDev", "3.x");

            System.out.println("✅ تم العثور على مكتبة FlatLaf");
        } catch (ClassNotFoundException e) {
            System.out.println("ℹ️ مكتبة FlatLaf غير متاحة");
        }
    }

    private void tryAddJTattooThemes() {
        try {
            // محاولة تحميل JTattoo
            Class.forName("com.jtattoo.plaf.acryl.AcrylLookAndFeel");

            addTheme("Acryl", "مظهر Acryl", "JTattoo", "com.jtattoo.plaf.acryl.AcrylLookAndFeel",
                    false, "مظهر Acryl الشفاف والأنيق من JTattoo", "JTattoo", "1.6.x");

            addTheme("Graphite", "مظهر الجرافيت", "JTattoo",
                    "com.jtattoo.plaf.graphite.GraphiteLookAndFeel", true,
                    "مظهر الجرافيت المظلم والأنيق من JTattoo", "JTattoo", "1.6.x");

            System.out.println("✅ تم العثور على مكتبة JTattoo");
        } catch (ClassNotFoundException e) {
            System.out.println("ℹ️ مكتبة JTattoo غير متاحة");
        }
    }

    private void addCustomThemes() {
        // مظاهر مخصصة بسيطة باستخدام UIManager
        addTheme("Ocean Theme", "مظهر المحيط", "Custom", "javax.swing.plaf.metal.MetalLookAndFeel",
                false, "مظهر Metal مع ألوان المحيط الزرقاء", "Custom", "1.0");

        addTheme("Forest Theme", "مظهر الغابة", "Custom", "javax.swing.plaf.metal.MetalLookAndFeel",
                false, "مظهر Metal مع ألوان الغابة الخضراء", "Custom", "1.0");

        addTheme("Desert Theme", "مظهر الصحراء", "Custom",
                "javax.swing.plaf.metal.MetalLookAndFeel", false,
                "مظهر Metal مع ألوان الصحراء الذهبية", "Custom", "1.0");

        addTheme("Night Theme", "مظهر الليل", "Custom", "javax.swing.plaf.metal.MetalLookAndFeel",
                true, "مظهر Metal مع ألوان الليل المظلمة", "Custom", "1.0");
    }

    private void addTheme(String name, String displayName, String category, String className,
            boolean isDark, String description, String author, String version) {
        ThemeInfo theme = new ThemeInfo(name, displayName, category, className, isDark, description,
                author, version);
        availableThemes.put(name, theme);
    }

    /**
     * تطبيق مظهر
     */
    public boolean applyTheme(String themeName) {
        ThemeInfo theme = availableThemes.get(themeName);
        if (theme == null || !theme.isAvailable) {
            System.err.println("❌ المظهر غير متاح: " + themeName);
            return false;
        }

        try {
            System.out.println("🎨 تطبيق المظهر: " + theme.displayName);

            boolean success = false;

            if (theme.category.equals("Custom")) {
                success = applyCustomTheme(theme);
            } else if (theme.category.equals("FlatLaf")) {
                success = applyFlatLafTheme(theme);
            } else if (theme.category.equals("JTattoo")) {
                success = applyJTattooTheme(theme);
            } else {
                success = applySystemTheme(theme);
            }

            if (success) {
                currentTheme = themeName;
                updateAllWindows();
                saveCurrentTheme(themeName);
                System.out.println("✅ تم تطبيق المظهر بنجاح: " + theme.displayName);
                return true;
            } else {
                System.err.println("❌ فشل في تطبيق المظهر: " + theme.displayName);
                return false;
            }

        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر: " + e.getMessage());
            return false;
        }
    }

    private boolean applySystemTheme(ThemeInfo theme) {
        try {
            if (theme.name.equals("System Default")) {
                // استخدام System Look and Feel
                for (LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                    if ("System".equals(info.getName()) || info.getClassName().contains("system")) {
                        UIManager.setLookAndFeel(info.getClassName());
                        return true;
                    }
                }
                // إذا لم نجد System، استخدم الافتراضي
                UIManager.setLookAndFeel(UIManager.getCrossPlatformLookAndFeelClassName());
                return true;
            } else {
                UIManager.setLookAndFeel(theme.className);
                return true;
            }
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق System Theme: " + e.getMessage());
            return false;
        }
    }

    private boolean applyFlatLafTheme(ThemeInfo theme) {
        try {
            Class<?> themeClass = Class.forName(theme.className);
            // استخدام reflection لاستدعاء setup()
            java.lang.reflect.Method setupMethod = themeClass.getMethod("setup");
            Boolean result = (Boolean) setupMethod.invoke(null);
            return result != null ? result : false;
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق FlatLaf Theme: " + e.getMessage());
            return false;
        }
    }

    private boolean applyJTattooTheme(ThemeInfo theme) {
        try {
            UIManager.setLookAndFeel(theme.className);
            return true;
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق JTattoo Theme: " + e.getMessage());
            return false;
        }
    }

    private boolean applyCustomTheme(ThemeInfo theme) {
        try {
            UIManager.setLookAndFeel(theme.className);

            // تطبيق ألوان مخصصة حسب اسم المظهر
            switch (theme.name) {
                case "Ocean Theme":
                    UIManager.put("Panel.background", new Color(230, 240, 250));
                    UIManager.put("Button.background", new Color(173, 216, 230));
                    UIManager.put("TextField.background", Color.WHITE);
                    break;
                case "Forest Theme":
                    UIManager.put("Panel.background", new Color(240, 248, 240));
                    UIManager.put("Button.background", new Color(144, 238, 144));
                    UIManager.put("TextField.background", Color.WHITE);
                    break;
                case "Desert Theme":
                    UIManager.put("Panel.background", new Color(255, 248, 220));
                    UIManager.put("Button.background", new Color(255, 218, 185));
                    UIManager.put("TextField.background", Color.WHITE);
                    break;
                case "Night Theme":
                    UIManager.put("Panel.background", new Color(45, 45, 45));
                    UIManager.put("Button.background", new Color(70, 70, 70));
                    UIManager.put("TextField.background", new Color(60, 60, 60));
                    UIManager.put("Label.foreground", Color.WHITE);
                    UIManager.put("Button.foreground", Color.WHITE);
                    UIManager.put("TextField.foreground", Color.WHITE);
                    break;
            }

            return true;
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق Custom Theme: " + e.getMessage());
            return false;
        }
    }

    /**
     * تحديث جميع النوافذ
     */
    private void updateAllWindows() {
        SwingUtilities.invokeLater(() -> {
            for (Window window : Window.getWindows()) {
                if (window.isDisplayable()) {
                    SwingUtilities.updateComponentTreeUI(window);
                    window.repaint();
                }
            }
        });
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private void connectToDatabase() {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            connection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات لمدير المظاهر العملي");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
        }
    }

    /**
     * حفظ المظهر الحالي
     */
    private void saveCurrentTheme(String themeName) {
        if (connection == null)
            return;

        try {
            // محاولة تحديث الجدول الجديد أولاً
            String updateNewSQL = "UPDATE ERP_COMPLETE_THEMES SET IS_CURRENT_THEME = 'N'";
            try (PreparedStatement resetStmt = connection.prepareStatement(updateNewSQL)) {
                resetStmt.executeUpdate();
            }

            String setNewSQL =
                    "UPDATE ERP_COMPLETE_THEMES SET IS_CURRENT_THEME = 'Y', LAST_APPLIED_DATE = SYSDATE WHERE THEME_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(setNewSQL)) {
                stmt.setString(1, themeName);
                int updated = stmt.executeUpdate();
                if (updated > 0) {
                    System.out.println("💾 تم حفظ المظهر في الجدول الجديد: " + themeName);
                    return;
                }
            }
        } catch (SQLException e) {
            // الجدول الجديد غير موجود، استخدم الجدول القديم
        }

        try {
            // استخدام الجدول القديم
            String updateOldSQL =
                    "UPDATE ERP_UI_THEME_SETTINGS SET SETTING_VALUE = ? WHERE SETTING_NAME = 'MAIN_THEME'";
            try (PreparedStatement stmt = connection.prepareStatement(updateOldSQL)) {
                stmt.setString(1, themeName);
                int updated = stmt.executeUpdate();
                if (updated > 0) {
                    System.out.println("💾 تم حفظ المظهر في الجدول القديم: " + themeName);
                } else {
                    // إنشاء إدخال جديد
                    String insertSQL =
                            "INSERT INTO ERP_UI_THEME_SETTINGS (SETTING_NAME, SETTING_VALUE, CREATED_DATE) VALUES ('MAIN_THEME', ?, SYSDATE)";
                    try (PreparedStatement insertStmt = connection.prepareStatement(insertSQL)) {
                        insertStmt.setString(1, themeName);
                        insertStmt.executeUpdate();
                        System.out.println("💾 تم إنشاء إدخال جديد للمظهر: " + themeName);
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في حفظ المظهر: " + e.getMessage());
        }
    }

    /**
     * تحميل المظهر المحفوظ
     */
    private void loadSavedTheme() {
        if (connection == null)
            return;

        try {
            // محاولة تحميل من الجدول الجديد أولاً
            String newSQL =
                    "SELECT THEME_NAME FROM ERP_COMPLETE_THEMES WHERE IS_CURRENT_THEME = 'Y'";
            try (PreparedStatement stmt = connection.prepareStatement(newSQL);
                    ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    String savedTheme = rs.getString("THEME_NAME");
                    if (savedTheme != null && availableThemes.containsKey(savedTheme)) {
                        System.out.println("📂 تحميل المظهر من الجدول الجديد: " + savedTheme);
                        currentTheme = savedTheme;
                        applyTheme(savedTheme);
                        return;
                    }
                }
            }
        } catch (SQLException e) {
            // الجدول الجديد غير موجود
        }

        try {
            // تحميل من الجدول القديم
            String oldSQL =
                    "SELECT SETTING_VALUE FROM ERP_UI_THEME_SETTINGS WHERE SETTING_NAME = 'MAIN_THEME'";
            try (PreparedStatement stmt = connection.prepareStatement(oldSQL);
                    ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    String savedTheme = rs.getString("SETTING_VALUE");
                    if (savedTheme != null && availableThemes.containsKey(savedTheme)) {
                        System.out.println("📂 تحميل المظهر من الجدول القديم: " + savedTheme);
                        currentTheme = savedTheme;
                        applyTheme(savedTheme);
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في تحميل المظهر المحفوظ: " + e.getMessage());
        }
    }

    /**
     * الحصول على جميع المظاهر حسب الفئة
     */
    public Map<String, List<ThemeInfo>> getThemesByCategory() {
        Map<String, List<ThemeInfo>> themesByCategory = new LinkedHashMap<>();

        for (ThemeInfo theme : availableThemes.values()) {
            if (theme.isAvailable) {
                themesByCategory.computeIfAbsent(theme.category, k -> new ArrayList<>()).add(theme);
            }
        }

        return themesByCategory;
    }

    /**
     * الحصول على معلومات المظهر
     */
    public ThemeInfo getThemeInfo(String themeName) {
        return availableThemes.get(themeName);
    }

    /**
     * الحصول على المظهر الحالي
     */
    public String getCurrentTheme() {
        return currentTheme;
    }

    /**
     * الحصول على جميع أسماء المظاهر المتاحة
     */
    public String[] getAvailableThemeNames() {
        return availableThemes.values().stream().filter(theme -> theme.isAvailable)
                .map(theme -> theme.name).toArray(String[]::new);
    }

    /**
     * طباعة تقرير المظاهر
     */
    public void printThemeReport() {
        System.out.println("\n📊 تقرير المظاهر المتاحة:");
        System.out.println("========================");

        Map<String, List<ThemeInfo>> themesByCategory = getThemesByCategory();

        for (Map.Entry<String, List<ThemeInfo>> entry : themesByCategory.entrySet()) {
            System.out
                    .println("\n🎨 " + entry.getKey() + " (" + entry.getValue().size() + " مظهر):");
            for (ThemeInfo theme : entry.getValue()) {
                System.out.println("  ✅ " + theme.displayName + " - " + theme.description);
            }
        }

        int totalAvailable =
                (int) availableThemes.values().stream().filter(t -> t.isAvailable).count();
        System.out.println("\n📈 إجمالي المظاهر المتاحة: " + totalAvailable);
    }
}
