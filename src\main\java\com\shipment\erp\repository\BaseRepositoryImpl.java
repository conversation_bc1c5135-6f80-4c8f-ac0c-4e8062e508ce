package com.shipment.erp.repository;

import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Optional;
import org.springframework.transaction.annotation.Transactional;
import com.shipment.erp.model.BaseEntity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 * التطبيق الأساسي لـ BaseRepository
 */
@Transactional
public abstract class BaseRepositoryImpl<T extends BaseEntity> implements BaseRepository<T> {

    @PersistenceContext
    protected EntityManager entityManager;

    private final Class<T> entityClass;

    @SuppressWarnings("unchecked")
    public BaseRepositoryImpl() {
        this.entityClass = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass())
                .getActualTypeArguments()[0];
    }

    @Override
    public T save(T entity) {
        if (entity.isNew()) {
            entityManager.persist(entity);
            return entity;
        } else {
            return entityManager.merge(entity);
        }
    }

    @Override
    public List<T> saveAll(List<T> entities) {
        for (T entity : entities) {
            save(entity);
        }
        return entities;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<T> findById(Long id) {
        T entity = entityManager.find(entityClass, id);
        return Optional.ofNullable(entity);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        return findById(id).isPresent();
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findAll() {
        String queryStr = "SELECT e FROM " + entityClass.getSimpleName() + " e ORDER BY e.id DESC";
        TypedQuery<T> query = entityManager.createQuery(queryStr, entityClass);
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findAll(int page, int size) {
        String queryStr = "SELECT e FROM " + entityClass.getSimpleName() + " e ORDER BY e.id DESC";
        TypedQuery<T> query = entityManager.createQuery(queryStr, entityClass);
        query.setFirstResult(page * size);
        query.setMaxResults(size);
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public long count() {
        String queryStr = "SELECT COUNT(e) FROM " + entityClass.getSimpleName() + " e";
        TypedQuery<Long> query = entityManager.createQuery(queryStr, Long.class);
        return query.getSingleResult();
    }

    @Override
    public void deleteById(Long id) {
        Optional<T> entity = findById(id);
        entity.ifPresent(this::delete);
    }

    @Override
    public void delete(T entity) {
        if (entityManager.contains(entity)) {
            entityManager.remove(entity);
        } else {
            entityManager.remove(entityManager.merge(entity));
        }
    }

    @Override
    public void deleteAll(List<T> entities) {
        for (T entity : entities) {
            delete(entity);
        }
    }

    @Override
    public void deleteAll() {
        String queryStr = "DELETE FROM " + entityClass.getSimpleName();
        Query query = entityManager.createQuery(queryStr);
        query.executeUpdate();
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findActive() {
        try {
            String queryStr = "SELECT e FROM " + entityClass.getSimpleName()
                    + " e WHERE e.isActive = true ORDER BY e.id DESC";
            TypedQuery<T> query = entityManager.createQuery(queryStr, entityClass);
            return query.getResultList();
        } catch (Exception e) {
            // إذا لم يكن للكيان حقل isActive، إرجاع جميع الكيانات
            return findAll();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findInactive() {
        try {
            String queryStr = "SELECT e FROM " + entityClass.getSimpleName()
                    + " e WHERE e.isActive = false ORDER BY e.id DESC";
            TypedQuery<T> query = entityManager.createQuery(queryStr, entityClass);
            return query.getResultList();
        } catch (Exception e) {
            // إذا لم يكن للكيان حقل isActive، إرجاع قائمة فارغة
            return List.of();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> search(String searchText) {
        return search(searchText, 0, 100); // افتراضياً أول 100 نتيجة
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> search(String searchText, int page, int size) {
        // تطبيق افتراضي - يجب تخصيصه في الكلاسات الفرعية
        return findAll(page, size);
    }

    @Override
    @Transactional(readOnly = true)
    public long countSearch(String searchText) {
        // تطبيق افتراضي - يجب تخصيصه في الكلاسات الفرعية
        return count();
    }

    @Override
    public void updateActiveStatus(Long id, boolean isActive) {
        try {
            String queryStr = "UPDATE " + entityClass.getSimpleName()
                    + " e SET e.isActive = :isActive WHERE e.id = :id";
            Query query = entityManager.createQuery(queryStr);
            query.setParameter("isActive", isActive);
            query.setParameter("id", id);
            query.executeUpdate();
        } catch (Exception e) {
            // إذا لم يكن للكيان حقل isActive، تجاهل العملية
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findLatest(int limit) {
        String queryStr =
                "SELECT e FROM " + entityClass.getSimpleName() + " e ORDER BY e.createdDate DESC";
        TypedQuery<T> query = entityManager.createQuery(queryStr, entityClass);
        query.setMaxResults(limit);
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    @SuppressWarnings("unchecked")
    public List<T> executeQuery(String queryStr, Object... parameters) {
        Query query = entityManager.createQuery(queryStr);
        for (int i = 0; i < parameters.length; i++) {
            query.setParameter(i + 1, parameters[i]);
        }
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    @SuppressWarnings("unchecked")
    public Optional<T> executeSingleQuery(String queryStr, Object... parameters) {
        try {
            Query query = entityManager.createQuery(queryStr);
            for (int i = 0; i < parameters.length; i++) {
                query.setParameter(i + 1, parameters[i]);
            }
            T result = (T) query.getSingleResult();
            return Optional.ofNullable(result);
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    public int executeUpdate(String queryStr, Object... parameters) {
        Query query = entityManager.createQuery(queryStr);
        for (int i = 0; i < parameters.length; i++) {
            query.setParameter(i + 1, parameters[i]);
        }
        return query.executeUpdate();
    }

    @Override
    public void refresh(T entity) {
        entityManager.refresh(entity);
    }

    @Override
    public void detach(T entity) {
        entityManager.detach(entity);
    }

    @Override
    public void clear() {
        entityManager.clear();
    }

    @Override
    public void flush() {
        entityManager.flush();
    }

    /**
     * الحصول على EntityManager
     */
    protected EntityManager getEntityManager() {
        return entityManager;
    }

    /**
     * الحصول على كلاس الكيان
     */
    protected Class<T> getEntityClass() {
        return entityClass;
    }
}
