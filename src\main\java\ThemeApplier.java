import java.awt.Component;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import com.formdev.flatlaf.FlatDarculaLaf;
import com.formdev.flatlaf.FlatDarkLaf;
import com.formdev.flatlaf.FlatIntelliJLaf;
import com.formdev.flatlaf.FlatLightLaf;

/**
 * مطبق المظاهر - Theme Applier كلاس مساعد لتطبيق المظاهر بشكل صحيح
 */
public class ThemeApplier {

    /**
     * تطبيق المظهر المحدد
     */
    public static boolean applyTheme(String themeName, Component parentComponent) {
        try {
            System.out.println("🎨 تطبيق المظهر: " + themeName);

            boolean themeChanged = false;

            switch (themeName) {
                case "FlatLaf Light":
                    if (FlatLightLaf.setup()) {
                        themeChanged = true;
                        System.out.println("  ✅ تم تطبيق FlatLaf Light");
                    }
                    break;

                case "FlatLaf Dark":
                    if (FlatDarkLaf.setup()) {
                        themeChanged = true;
                        System.out.println("  ✅ تم تطبيق FlatLaf Dark");
                    }
                    break;

                case "FlatLaf IntelliJ":
                    if (FlatIntelliJLaf.setup()) {
                        themeChanged = true;
                        System.out.println("  ✅ تم تطبيق FlatLaf IntelliJ");
                    }
                    break;

                case "FlatLaf Darcula":
                    if (FlatDarculaLaf.setup()) {
                        themeChanged = true;
                        System.out.println("  ✅ تم تطبيق FlatLaf Darcula");
                    }
                    break;

                case "System Default":
                    try {
                        // تحديد المظهر حسب نظام التشغيل
                        String osName = System.getProperty("os.name").toLowerCase();
                        if (osName.contains("windows")) {
                            UIManager.setLookAndFeel(
                                    "com.sun.java.swing.plaf.windows.WindowsLookAndFeel");
                        } else if (osName.contains("mac")) {
                            UIManager.setLookAndFeel("com.apple.laf.AquaLookAndFeel");
                        } else {
                            UIManager.setLookAndFeel("javax.swing.plaf.metal.MetalLookAndFeel");
                        }
                        themeChanged = true;
                        System.out.println("  ✅ تم تطبيق System Default للنظام: " + osName);
                    } catch (Exception ex) {
                        System.err.println("  ⚠️ فشل System Default، استخدام FlatLaf Light");
                        FlatLightLaf.setup();
                        themeChanged = true;
                    }
                    break;

                case "Metal":
                    UIManager.setLookAndFeel("javax.swing.plaf.metal.MetalLookAndFeel");
                    themeChanged = true;
                    System.out.println("  ✅ تم تطبيق Metal");
                    break;

                case "Nimbus":
                    UIManager.setLookAndFeel("javax.swing.plaf.nimbus.NimbusLookAndFeel");
                    themeChanged = true;
                    System.out.println("  ✅ تم تطبيق Nimbus");
                    break;

                case "Windows":
                    try {
                        UIManager.setLookAndFeel(
                                "com.sun.java.swing.plaf.windows.WindowsLookAndFeel");
                        themeChanged = true;
                        System.out.println("  ✅ تم تطبيق Windows");
                    } catch (Exception ex) {
                        System.err.println("  ⚠️ Windows LAF غير متاح");
                        FlatLightLaf.setup();
                        themeChanged = true;
                    }
                    break;

                case "GTK+":
                    try {
                        UIManager.setLookAndFeel("com.sun.java.swing.plaf.gtk.GTKLookAndFeel");
                        themeChanged = true;
                        System.out.println("  ✅ تم تطبيق GTK+");
                    } catch (Exception ex) {
                        System.err.println("  ⚠️ GTK+ LAF غير متاح");
                        FlatLightLaf.setup();
                        themeChanged = true;
                    }
                    break;

                default:
                    FlatLightLaf.setup();
                    themeChanged = true;
                    System.out.println("  ✅ تم تطبيق المظهر الافتراضي");
                    break;
            }

            if (themeChanged) {
                // تحديث جميع النوافذ المفتوحة
                updateAllWindows();

                System.out.println("🔄 تم تحديث جميع المكونات");

                // عرض رسالة تأكيد
                if (parentComponent != null) {
                    SwingUtilities.invokeLater(() -> {
                        JOptionPane.showMessageDialog(parentComponent,
                                "تم تطبيق المظهر: " + themeName + "\n" + "المظهر الحالي: "
                                        + UIManager.getLookAndFeel().getName(),
                                "تم تطبيق المظهر", JOptionPane.INFORMATION_MESSAGE);
                    });
                }

                return true;
            }

        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر: " + e.getMessage());
            e.printStackTrace();

            if (parentComponent != null) {
                JOptionPane.showMessageDialog(parentComponent,
                        "خطأ في تطبيق المظهر:\n" + e.getMessage() + "\n\n" + "المظهر الحالي: "
                                + UIManager.getLookAndFeel().getName(),
                        "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }

        return false;
    }

    /**
     * تحديث جميع النوافذ المفتوحة
     */
    private static void updateAllWindows() {
        // تحديث جميع النوافذ المفتوحة
        for (java.awt.Window window : java.awt.Window.getWindows()) {
            if (window.isDisplayable()) {
                SwingUtilities.updateComponentTreeUI(window);

                // إعادة رسم النافذة
                if (window instanceof JFrame) {
                    JFrame frame = (JFrame) window;
                    frame.repaint();
                    frame.revalidate();
                } else if (window instanceof JDialog) {
                    JDialog dialog = (JDialog) window;
                    dialog.repaint();
                    dialog.revalidate();
                }
            }
        }
    }

    /**
     * الحصول على قائمة المظاهر المتاحة
     */
    public static String[] getAvailableThemes() {
        return new String[] {"FlatLaf Light", "FlatLaf Dark", "FlatLaf IntelliJ", "FlatLaf Darcula",
                "System Default", "Metal", "Nimbus", "Windows", "GTK+"};
    }

    /**
     * التحقق من توفر المظهر
     */
    public static boolean isThemeAvailable(String themeName) {
        try {
            switch (themeName) {
                case "FlatLaf Light":
                case "FlatLaf Dark":
                case "FlatLaf IntelliJ":
                case "FlatLaf Darcula":
                case "Metal":
                case "Nimbus":
                    return true;

                case "System Default":
                    return true; // متاح دائماً

                case "Windows":
                    try {
                        Class.forName("com.sun.java.swing.plaf.windows.WindowsLookAndFeel");
                        return System.getProperty("os.name").toLowerCase().contains("windows");
                    } catch (ClassNotFoundException e) {
                        return false;
                    }

                case "GTK+":
                    try {
                        Class.forName("com.sun.java.swing.plaf.gtk.GTKLookAndFeel");
                        return System.getProperty("os.name").toLowerCase().contains("linux");
                    } catch (ClassNotFoundException e) {
                        return false;
                    }

                default:
                    return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * الحصول على اسم المظهر الحالي
     */
    public static String getCurrentThemeName() {
        String currentLAF = UIManager.getLookAndFeel().getClass().getName();

        if (currentLAF.contains("FlatLightLaf")) {
            return "FlatLaf Light";
        } else if (currentLAF.contains("FlatDarkLaf")) {
            return "FlatLaf Dark";
        } else if (currentLAF.contains("FlatIntelliJLaf")) {
            return "FlatLaf IntelliJ";
        } else if (currentLAF.contains("FlatDarculaLaf")) {
            return "FlatLaf Darcula";
        } else if (currentLAF.contains("MetalLookAndFeel")) {
            return "Metal";
        } else if (currentLAF.contains("NimbusLookAndFeel")) {
            return "Nimbus";
        } else if (currentLAF.contains("WindowsLookAndFeel")) {
            return "Windows";
        } else if (currentLAF.contains("GTKLookAndFeel")) {
            return "GTK+";
        } else {
            return "System Default";
        }
    }
}
