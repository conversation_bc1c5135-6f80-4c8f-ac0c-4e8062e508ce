import java.awt.BorderLayout;
import java.awt.FlowLayout;
import java.awt.Font;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * اختبار نافذة إعدادات الواجهة
 * Test UI Settings Window
 */
public class TestUISettings {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  TESTING UI SETTINGS WINDOW");
        System.out.println("  اختبار نافذة إعدادات الواجهة");
        System.out.println("========================================");
        
        SwingUtilities.invokeLater(() -> {
            try {
                // تعيين Look and Feel
                UIManager.setLookAndFeel("javax.swing.plaf.system.SystemLookAndFeel");
                
                System.out.println("✅ System Look and Feel applied");
                
                // إنشاء نافذة رئيسية بسيطة
                JFrame mainFrame = new JFrame("Test Main Window");
                mainFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
                mainFrame.setSize(400, 300);
                mainFrame.setLocationRelativeTo(null);
                
                // إضافة زر لفتح نافذة الإعدادات
                JButton openSettingsButton = new JButton("Open UI Settings");
                openSettingsButton.setFont(new Font("Tahoma", Font.PLAIN, 14));
                
                openSettingsButton.addActionListener(e -> {
                    try {
                        System.out.println("Opening UI Settings Window...");
                        AdvancedUISettingsWindow settingsWindow = new AdvancedUISettingsWindow(mainFrame);
                        settingsWindow.setVisible(true);
                        System.out.println("✅ UI Settings Window opened successfully");
                    } catch (Exception ex) {
                        System.err.println("❌ Error opening UI Settings Window: " + ex.getMessage());
                        ex.printStackTrace();
                        
                        JOptionPane.showMessageDialog(mainFrame,
                            "Error opening UI Settings Window:\n" + ex.getMessage(),
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                    }
                });
                
                // تخطيط النافذة
                mainFrame.setLayout(new BorderLayout());
                
                JPanel centerPanel = new JPanel(new FlowLayout());
                centerPanel.add(new JLabel("Click the button to open UI Settings:"));
                centerPanel.add(openSettingsButton);
                
                mainFrame.add(centerPanel, BorderLayout.CENTER);
                
                // إضافة معلومات
                JTextArea infoArea = new JTextArea(8, 40);
                infoArea.setEditable(false);
                infoArea.setText(
                    "UI Settings Window Features:\n" +
                    "• 20+ Themes (FlatLaf, Material, JTattoo, etc.)\n" +
                    "• Font customization\n" +
                    "• Interface options\n" +
                    "• Color customization\n" +
                    "• Live preview\n" +
                    "• Database integration\n" +
                    "• Arabic support\n\n" +
                    "Click 'Open UI Settings' to test the window."
                );
                
                JScrollPane scrollPane = new JScrollPane(infoArea);
                scrollPane.setBorder(BorderFactory.createTitledBorder("Information"));
                mainFrame.add(scrollPane, BorderLayout.SOUTH);
                
                // عرض النافذة
                mainFrame.setVisible(true);
                
                System.out.println("✅ Test window displayed successfully");
                System.out.println("Click the 'Open UI Settings' button to test the settings window");
                
            } catch (Exception e) {
                System.err.println("❌ Error in test application: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
}
