import java.sql.*;
import java.util.Properties;

/**
 * تنظيم وترتيب شجرة النظام
 * Organize and Sort System Tree
 */
public class OrganizeSystemTree {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔄 تنظيم وترتيب شجرة النظام...");
            
            Connection connection = getConnection();
            
            // تنظيم الهيكل الهرمي
            organizeHierarchy(connection);
            
            // ترتيب العناصر
            sortElements(connection);
            
            // تحديث مستويات العمق
            updateTreeLevels(connection);
            
            connection.close();
            
            System.out.println("✅ تم تنظيم شجرة النظام بنجاح!");
            
            // اختبار النتيجة
            testResult();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تنظيم شجرة النظام: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    private static void organizeHierarchy(Connection conn) throws SQLException {
        System.out.println("📁 تنظيم الهيكل الهرمي...");
        
        // نقل النوافذ إلى الفئات الصحيحة
        moveWindowsToCorrectCategories(conn);
        
        // نقل الأدوات إلى فئة أدوات النظام
        moveToolsToSystemTools(conn);
        
        conn.commit();
        System.out.println("✅ تم تنظيم الهيكل الهرمي");
    }
    
    private static void moveWindowsToCorrectCategories(Connection conn) throws SQLException {
        System.out.println("  🔄 نقل النوافذ إلى الفئات الصحيحة...");
        
        // الحصول على معرفات الفئات
        int itemsId = getCategoryId(conn, "إدارة الأصناف");
        int usersId = getCategoryId(conn, "إدارة المستخدمين");
        int settingsId = getCategoryId(conn, "الإعدادات العامة");
        int reportsId = getCategoryId(conn, "التقارير");
        int toolsId = getCategoryId(conn, "أدوات النظام");
        
        // نقل نوافذ الأصناف
        if (itemsId > 0) {
            moveWindowToCategory(conn, "بيانات الأصناف الحقيقية", itemsId, 2);
            moveWindowToCategory(conn, "بيانات الأصناف الشاملة", itemsId, 2);
            moveWindowToCategory(conn, "مجموعات الأصناف", itemsId, 2);
            moveWindowToCategory(conn, "وحدات القياس", itemsId, 2);
        }
        
        // نقل نوافذ المستخدمين
        if (usersId > 0) {
            moveWindowToCategory(conn, "صلاحيات المستخدمين", usersId, 2);
            moveWindowToCategory(conn, "مجموعات المستخدمين", usersId, 2);
        }
        
        // نقل نوافذ الإعدادات
        if (settingsId > 0) {
            moveWindowToCategory(conn, "إعدادات قاعدة البيانات", settingsId, 2);
            moveWindowToCategory(conn, "إعدادات الأمان", settingsId, 2);
            moveWindowToCategory(conn, "تكوين النظام", settingsId, 2);
        }
        
        // نقل نوافذ التقارير
        if (reportsId > 0) {
            moveWindowToCategory(conn, "تقرير الأصناف", reportsId, 2);
            moveWindowToCategory(conn, "تقرير المستخدمين", reportsId, 2);
            moveWindowToCategory(conn, "تقرير النظام", reportsId, 2);
            moveWindowToCategory(conn, "التقارير المخصصة", reportsId, 2);
        }
        
        // نقل النوافذ الإضافية إلى أدوات النظام
        if (toolsId > 0) {
            moveWindowToCategory(conn, "البحث المتقدم", toolsId, 2);
            moveWindowToCategory(conn, "النسخ الاحتياطي والاستعادة", toolsId, 2);
            moveWindowToCategory(conn, "سجل النظام", toolsId, 2);
            moveWindowToCategory(conn, "مركز الإشعارات", toolsId, 2);
        }
    }
    
    private static void moveToolsToSystemTools(Connection conn) throws SQLException {
        System.out.println("  🔧 نقل الأدوات إلى فئة أدوات النظام...");
        
        int toolsId = getCategoryId(conn, "أدوات النظام");
        
        if (toolsId > 0) {
            moveToolToCategory(conn, "فحص النظام الشامل", toolsId, 2);
            moveToolToCategory(conn, "مراقب الأداء", toolsId, 2);
            moveToolToCategory(conn, "إدارة الاتصالات", toolsId, 2);
            moveToolToCategory(conn, "مدير الأمان", toolsId, 2);
            moveToolToCategory(conn, "مدير التكوين", toolsId, 2);
        }
    }
    
    private static void sortElements(Connection conn) throws SQLException {
        System.out.println("📊 ترتيب العناصر...");
        
        // ترتيب الفئات الرئيسية
        sortMainCategories(conn);
        
        // ترتيب النوافذ داخل كل فئة
        sortWindowsInCategories(conn);
        
        conn.commit();
        System.out.println("✅ تم ترتيب العناصر");
    }
    
    private static void sortMainCategories(Connection conn) throws SQLException {
        System.out.println("  📁 ترتيب الفئات الرئيسية...");
        
        String[] categories = {
            "إدارة الأصناف",
            "إدارة المستخدمين", 
            "الإعدادات العامة",
            "التقارير",
            "أدوات النظام"
        };
        
        for (int i = 0; i < categories.length; i++) {
            updateDisplayOrder(conn, categories[i], i + 1);
        }
    }
    
    private static void sortWindowsInCategories(Connection conn) throws SQLException {
        System.out.println("  🪟 ترتيب النوافذ داخل الفئات...");
        
        // ترتيب نوافذ إدارة الأصناف
        int itemsId = getCategoryId(conn, "إدارة الأصناف");
        if (itemsId > 0) {
            updateWindowOrder(conn, "بيانات الأصناف الحقيقية", 1);
            updateWindowOrder(conn, "بيانات الأصناف الشاملة", 2);
            updateWindowOrder(conn, "مجموعات الأصناف", 3);
            updateWindowOrder(conn, "وحدات القياس", 4);
        }
        
        // ترتيب نوافذ إدارة المستخدمين
        int usersId = getCategoryId(conn, "إدارة المستخدمين");
        if (usersId > 0) {
            updateWindowOrder(conn, "إدارة المستخدمين", 1);
            updateWindowOrder(conn, "صلاحيات المستخدمين", 2);
            updateWindowOrder(conn, "مجموعات المستخدمين", 3);
        }
        
        // ترتيب نوافذ الإعدادات
        int settingsId = getCategoryId(conn, "الإعدادات العامة");
        if (settingsId > 0) {
            updateWindowOrder(conn, "الإعدادات العامة", 1);
            updateWindowOrder(conn, "إعدادات قاعدة البيانات", 2);
            updateWindowOrder(conn, "إعدادات الأمان", 3);
            updateWindowOrder(conn, "تكوين النظام", 4);
        }
        
        // ترتيب نوافذ التقارير
        int reportsId = getCategoryId(conn, "التقارير");
        if (reportsId > 0) {
            updateWindowOrder(conn, "تقرير الأصناف", 1);
            updateWindowOrder(conn, "تقرير المستخدمين", 2);
            updateWindowOrder(conn, "تقرير النظام", 3);
            updateWindowOrder(conn, "التقارير المخصصة", 4);
        }
        
        // ترتيب أدوات النظام
        int toolsId = getCategoryId(conn, "أدوات النظام");
        if (toolsId > 0) {
            updateWindowOrder(conn, "فحص النظام الشامل", 1);
            updateWindowOrder(conn, "مراقب الأداء", 2);
            updateWindowOrder(conn, "إدارة الاتصالات", 3);
            updateWindowOrder(conn, "مدير الأمان", 4);
            updateWindowOrder(conn, "مدير التكوين", 5);
            updateWindowOrder(conn, "البحث المتقدم", 6);
            updateWindowOrder(conn, "النسخ الاحتياطي والاستعادة", 7);
            updateWindowOrder(conn, "سجل النظام", 8);
            updateWindowOrder(conn, "مركز الإشعارات", 9);
        }
    }
    
    private static void updateTreeLevels(Connection conn) throws SQLException {
        System.out.println("📏 تحديث مستويات العمق...");
        
        // تحديث مستوى الفئات الرئيسية إلى 1
        String updateCategories = """
            UPDATE ERP_SYSTEM_TREE 
            SET TREE_LEVEL = 1 
            WHERE PARENT_ID = 1 AND NODE_TYPE = 'CATEGORY'
        """;
        conn.createStatement().executeUpdate(updateCategories);
        
        // تحديث مستوى النوافذ والأدوات إلى 2
        String updateWindows = """
            UPDATE ERP_SYSTEM_TREE 
            SET TREE_LEVEL = 2 
            WHERE PARENT_ID IN (
                SELECT TREE_ID FROM ERP_SYSTEM_TREE 
                WHERE PARENT_ID = 1 AND NODE_TYPE = 'CATEGORY'
            )
        """;
        conn.createStatement().executeUpdate(updateWindows);
        
        conn.commit();
        System.out.println("✅ تم تحديث مستويات العمق");
    }
    
    private static int getCategoryId(Connection conn, String categoryName) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }
    
    private static void moveWindowToCategory(Connection conn, String windowName, int categoryId, int level) throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET PARENT_ID = ?, TREE_LEVEL = ? WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, categoryId);
            stmt.setInt(2, level);
            stmt.setString(3, windowName);
            
            int updated = stmt.executeUpdate();
            if (updated > 0) {
                System.out.println("    ✅ نُقل: " + windowName);
            }
        }
    }
    
    private static void moveToolToCategory(Connection conn, String toolName, int categoryId, int level) throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET PARENT_ID = ?, TREE_LEVEL = ? WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, categoryId);
            stmt.setInt(2, level);
            stmt.setString(3, toolName);
            
            int updated = stmt.executeUpdate();
            if (updated > 0) {
                System.out.println("    ✅ نُقل: " + toolName);
            }
        }
    }
    
    private static void updateDisplayOrder(Connection conn, String nodeName, int order) throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET DISPLAY_ORDER = ? WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, order);
            stmt.setString(2, nodeName);
            
            stmt.executeUpdate();
        }
    }
    
    private static void updateWindowOrder(Connection conn, String windowName, int order) throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET DISPLAY_ORDER = ? WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, order);
            stmt.setString(2, windowName);
            
            stmt.executeUpdate();
        }
    }
    
    private static void testResult() {
        System.out.println("\n🔍 اختبار النتيجة النهائية...");
        
        try {
            SystemTreeManager manager = SystemTreeManager.getInstance();
            manager.printSystemTree();
            manager.close();
        } catch (Exception e) {
            System.err.println("❌ خطأ في اختبار النتيجة: " + e.getMessage());
        }
    }
}
