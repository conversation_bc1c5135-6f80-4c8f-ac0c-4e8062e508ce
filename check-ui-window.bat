@echo off
echo ========================================
echo   CHECKING UI SETTINGS WINDOW STATUS
echo   فحص حالة نافذة إعدادات الواجهة
echo ========================================

cd /d "d:\java\java"

echo.
echo [INFO] Checking Java processes...
echo [INFO] فحص عمليات Java...

tasklist /fi "imagename eq java.exe" /fo table

echo.
echo [INFO] Window processes status:
echo [INFO] حالة عمليات النوافذ:

for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| find "java.exe"') do (
    echo Found Java process: %%i
)

echo.
echo [INFO] If you see multiple Java processes above, the UI Settings Window is running.
echo [INFO] إذا رأيت عدة عمليات Java أعلاه، فإن نافذة إعدادات الواجهة تعمل.
echo.
echo [INFO] Look for the window in your taskbar or use Alt+Tab to switch between windows.
echo [INFO] ابحث عن النافذة في شريط المهام أو استخدم Alt+Tab للتنقل بين النوافذ.
echo.

echo ========================================
echo   UI SETTINGS WINDOW FEATURES
echo   ميزات نافذة إعدادات الواجهة
echo ========================================
echo.
echo The Advanced UI Settings Window includes 5 tabs:
echo تتضمن نافذة إعدادات الواجهة المتطورة 5 تبويبات:
echo.
echo 1. 🎨 THEMES TAB (تبويب الثيمات):
echo    • 19+ themes available
echo    • FlatLaf, JTattoo, SeaGlass collections
echo    • Instant theme application
echo    • Theme information display
echo.
echo 2. 🔤 FONTS TAB (تبويب الخطوط):
echo    • All system fonts available
echo    • Font size adjustment (8-72)
echo    • Arabic and English preview
echo    • Real-time font preview
echo.
echo 3. 🖥️ INTERFACE TAB (تبويب الواجهة):
echo    • Enable/disable animations
echo    • Enable/disable sounds
echo    • Enable/disable tooltips
echo    • RTL (Right-to-Left) support
echo    • Transparency level control
echo.
echo 4. 🌈 COLORS TAB (تبويب الألوان):
echo    • Full color picker
echo    • Accent color selection
echo    • Real-time color preview
echo.
echo 5. 👁️ PREVIEW TAB (تبويب المعاينة):
echo    • Live component preview
echo    • Real-time theme testing
echo    • Sample UI elements
echo.
echo BOTTOM PANEL (الجزء السفلي):
echo    • Event log with timestamps
echo    • Save/Load/Reset buttons
echo    • Database integration status
echo.

pause
