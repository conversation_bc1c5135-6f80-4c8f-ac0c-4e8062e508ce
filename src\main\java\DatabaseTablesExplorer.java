import java.sql.*;
import java.util.*;

/**
 * مستكشف جداول قاعدة البيانات الشامل
 * Comprehensive Database Tables Explorer
 */
public class DatabaseTablesExplorer {
    
    public static void main(String[] args) {
        System.out.println("🗄️ مستكشف جداول قاعدة البيانات الشامل");
        System.out.println("========================================");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            exploreAllTables(connection);
            
            connection.close();
            System.out.println("✅ انتهى استكشاف قاعدة البيانات");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في استكشاف قاعدة البيانات: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void exploreAllTables(Connection connection) throws SQLException {
        System.out.println("\n📋 استكشاف جميع الجداول...");
        
        // الحصول على جميع الجداول
        DatabaseMetaData metaData = connection.getMetaData();
        ResultSet tables = metaData.getTables(null, null, "%", new String[]{"TABLE"});
        
        Map<String, List<String>> categorizedTables = new HashMap<>();
        int totalTables = 0;
        
        while (tables.next()) {
            String tableName = tables.getString("TABLE_NAME");
            String category = categorizeTable(tableName);
            
            categorizedTables.computeIfAbsent(category, k -> new ArrayList<>()).add(tableName);
            totalTables++;
        }
        tables.close();
        
        System.out.println("✅ تم العثور على " + totalTables + " جدول");
        
        // عرض الجداول مصنفة
        displayCategorizedTables(connection, categorizedTables);
        
        // إحصائيات مفصلة
        displayDetailedStatistics(connection, categorizedTables);
    }
    
    private static String categorizeTable(String tableName) {
        String upperName = tableName.toUpperCase();
        
        // جداول النظام
        if (upperName.startsWith("ERP_SYSTEM") || upperName.startsWith("SYS_") || 
            upperName.contains("SYSTEM") || upperName.contains("CONFIG")) {
            return "🔧 جداول النظام";
        }
        
        // جداول المظاهر
        if (upperName.contains("THEME") || upperName.contains("UI_")) {
            return "🎨 جداول المظاهر";
        }
        
        // جداول الأصناف والمخزون
        if (upperName.contains("ITEM") || upperName.contains("STOCK") || 
            upperName.contains("INVENTORY") || upperName.contains("CATEGORY")) {
            return "📦 جداول الأصناف والمخزون";
        }
        
        // جداول الشحن
        if (upperName.contains("SHIP") || upperName.contains("CARGO") || 
            upperName.contains("VESSEL") || upperName.contains("CONTAINER")) {
            return "🚢 جداول الشحن";
        }
        
        // جداول المالية
        if (upperName.contains("ACCOUNT") || upperName.contains("FINANCE") || 
            upperName.contains("PAYMENT") || upperName.contains("INVOICE")) {
            return "💰 جداول المالية";
        }
        
        // جداول العملاء والموردين
        if (upperName.contains("CUSTOMER") || upperName.contains("SUPPLIER") || 
            upperName.contains("VENDOR") || upperName.contains("CLIENT")) {
            return "👥 جداول العملاء والموردين";
        }
        
        // جداول الموظفين
        if (upperName.contains("EMPLOYEE") || upperName.contains("USER") || 
            upperName.contains("STAFF") || upperName.contains("HR")) {
            return "👨‍💼 جداول الموظفين";
        }
        
        // جداول التقارير
        if (upperName.contains("REPORT") || upperName.contains("LOG") || 
            upperName.contains("AUDIT") || upperName.contains("HISTORY")) {
            return "📊 جداول التقارير والسجلات";
        }
        
        // جداول أساسية
        if (upperName.contains("COMPANY") || upperName.contains("BRANCH") || 
            upperName.contains("LOCATION") || upperName.contains("CURRENCY")) {
            return "🏢 جداول أساسية";
        }
        
        return "📋 جداول أخرى";
    }
    
    private static void displayCategorizedTables(Connection connection, Map<String, List<String>> categorizedTables) {
        System.out.println("\n📊 الجداول مصنفة حسب الفئة:");
        System.out.println("========================================");
        
        for (Map.Entry<String, List<String>> entry : categorizedTables.entrySet()) {
            String category = entry.getKey();
            List<String> tables = entry.getValue();
            
            System.out.println("\n" + category + " (" + tables.size() + " جدول):");
            
            for (String tableName : tables) {
                int rowCount = getTableRowCount(connection, tableName);
                String status = rowCount > 0 ? "✅" : "⚪";
                System.out.println("  " + status + " " + tableName + " (" + String.format("%,d", rowCount) + " صف)");
            }
        }
    }
    
    private static void displayDetailedStatistics(Connection connection, Map<String, List<String>> categorizedTables) {
        System.out.println("\n📈 إحصائيات مفصلة:");
        System.out.println("========================================");
        
        int totalTables = 0;
        long totalRows = 0;
        int tablesWithData = 0;
        
        for (List<String> tables : categorizedTables.values()) {
            for (String tableName : tables) {
                totalTables++;
                int rowCount = getTableRowCount(connection, tableName);
                totalRows += rowCount;
                if (rowCount > 0) {
                    tablesWithData++;
                }
            }
        }
        
        System.out.println("📊 إجمالي الجداول: " + totalTables);
        System.out.println("📊 الجداول التي تحتوي على بيانات: " + tablesWithData);
        System.out.println("📊 الجداول الفارغة: " + (totalTables - tablesWithData));
        System.out.println("📊 إجمالي الصفوف: " + String.format("%,d", totalRows));
        System.out.println("📊 متوسط الصفوف لكل جدول: " + String.format("%.1f", (double)totalRows / totalTables));
        
        // أكبر الجداول
        System.out.println("\n🔝 أكبر 10 جداول:");
        List<TableInfo> allTables = new ArrayList<>();
        for (List<String> tables : categorizedTables.values()) {
            for (String tableName : tables) {
                int rowCount = getTableRowCount(connection, tableName);
                allTables.add(new TableInfo(tableName, rowCount));
            }
        }
        
        allTables.sort((a, b) -> Integer.compare(b.rowCount, a.rowCount));
        for (int i = 0; i < Math.min(10, allTables.size()); i++) {
            TableInfo table = allTables.get(i);
            System.out.println("  " + (i + 1) + ". " + table.name + " - " + String.format("%,d", table.rowCount) + " صف");
        }
    }
    
    private static int getTableRowCount(Connection connection, String tableName) {
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
            int count = 0;
            if (rs.next()) {
                count = rs.getInt(1);
            }
            rs.close();
            stmt.close();
            return count;
        } catch (SQLException e) {
            return 0; // في حالة عدم إمكانية الوصول للجدول
        }
    }
    
    static class TableInfo {
        String name;
        int rowCount;
        
        TableInfo(String name, int rowCount) {
            this.name = name;
            this.rowCount = rowCount;
        }
    }
}
