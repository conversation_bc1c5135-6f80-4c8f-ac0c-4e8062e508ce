package com.shipment.erp.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import com.shipment.erp.model.AuditLog;

/**
 * خدمة إدارة سجل التدقيق Audit Log Management Service
 */
public interface AuditLogService {

    /**
     * تسجيل عملية تدقيق Log audit operation
     */
    void logOperation(Long userId, String action, String tableName, Long recordId, String oldValues,
            String newValues, String ipAddress, String userAgent);

    /**
     * البحث عن سجلات التدقيق حسب المستخدم Find audit logs by user
     */
    List<AuditLog> findByUserId(Long userId);

    /**
     * البحث عن سجلات التدقيق حسب الجدول Find audit logs by table
     */
    List<AuditLog> findByTableName(String tableName);

    /**
     * البحث عن سجلات التدقيق حسب العملية Find audit logs by action
     */
    List<AuditLog> findByAction(String action);

    /**
     * البحث عن سجلات التدقيق حسب النطاق الزمني Find audit logs by date range
     */
    List<AuditLog> findByDateRange(Date startDate, Date endDate);

    /**
     * البحث عن سجلات التدقيق حسب السجل المحدد Find audit logs by record
     */
    List<AuditLog> findByTableNameAndRecordId(String tableName, Long recordId);

    /**
     * البحث عن سجلات التدقيق حسب عنوان IP Find audit logs by IP address
     */
    List<AuditLog> findByIpAddress(String ipAddress);

    /**
     * الحصول على إحصائيات التدقيق Get audit statistics
     */
    Map<String, Long> getAuditStatistics();

    /**
     * الحصول على أكثر المستخدمين نشاطاً Get most active users
     */
    List<Object[]> getMostActiveUsers(int limit);

    /**
     * الحصول على أكثر العمليات تكراراً Get most frequent operations
     */
    List<Object[]> getMostFrequentOperations(int limit);

    /**
     * تنظيف سجلات التدقيق القديمة Clean old audit logs
     */
    int cleanOldLogs(Date beforeDate);

    /**
     * تصدير سجلات التدقيق Export audit logs
     */
    void exportAuditLogs(Date startDate, Date endDate, String filePath);
}
