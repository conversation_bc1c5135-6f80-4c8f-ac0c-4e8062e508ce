import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.GridLayout;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * اختبار سريع لنافذة إعدادات الواجهة
 */
public class QuickUITest {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  QUICK UI SETTINGS TEST");
        System.out.println("  اختبار سريع لإعدادات الواجهة");
        System.out.println("========================================");
        
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق Look and Feel الافتراضي
                UIManager.setLookAndFeel("javax.swing.plaf.system.SystemLookAndFeel");
                
                System.out.println("✅ System Look and Feel applied");
                
                // إنشاء نافذة اختبار بسيطة
                JFrame testFrame = new JFrame("UI Settings Test");
                testFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
                testFrame.setSize(500, 400);
                testFrame.setLocationRelativeTo(null);
                
                // إنشاء المحتوى
                JPanel mainPanel = new JPanel(new BorderLayout());
                
                // العنوان
                JLabel titleLabel = new JLabel("Advanced UI Settings Window Test", SwingConstants.CENTER);
                titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
                titleLabel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
                mainPanel.add(titleLabel, BorderLayout.NORTH);
                
                // المحتوى الرئيسي
                JPanel centerPanel = new JPanel(new GridLayout(3, 1, 10, 10));
                centerPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
                
                // زر فتح نافذة الإعدادات
                JButton openSettingsBtn = new JButton("Open Advanced UI Settings");
                openSettingsBtn.setFont(new Font("Tahoma", Font.PLAIN, 14));
                openSettingsBtn.addActionListener(e -> {
                    try {
                        System.out.println("Opening Advanced UI Settings Window...");
                        AdvancedUISettingsWindow settingsWindow = new AdvancedUISettingsWindow(testFrame);
                        settingsWindow.setVisible(true);
                        System.out.println("✅ Advanced UI Settings Window opened successfully");
                    } catch (Exception ex) {
                        System.err.println("❌ Error opening Advanced UI Settings Window: " + ex.getMessage());
                        ex.printStackTrace();
                        JOptionPane.showMessageDialog(testFrame,
                            "Error opening Advanced UI Settings Window:\n" + ex.getMessage(),
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                    }
                });
                
                // زر اختبار الثيمات
                JButton testThemesBtn = new JButton("Test Theme Application");
                testThemesBtn.setFont(new Font("Tahoma", Font.PLAIN, 14));
                testThemesBtn.addActionListener(e -> {
                    try {
                        // تطبيق FlatLaf إذا كان متاحاً
                        UIManager.setLookAndFeel("com.formdev.flatlaf.FlatLightLaf");
                        SwingUtilities.updateComponentTreeUI(testFrame);
                        System.out.println("✅ FlatLaf Light theme applied");
                        JOptionPane.showMessageDialog(testFrame, "FlatLaf Light theme applied successfully!");
                    } catch (Exception ex) {
                        System.err.println("❌ Error applying theme: " + ex.getMessage());
                        JOptionPane.showMessageDialog(testFrame,
                            "Error applying theme:\n" + ex.getMessage(),
                            "Theme Error",
                            JOptionPane.ERROR_MESSAGE);
                    }
                });
                
                // زر اختبار قاعدة البيانات
                JButton testDBBtn = new JButton("Test Database Connection");
                testDBBtn.setFont(new Font("Tahoma", Font.PLAIN, 14));
                testDBBtn.addActionListener(e -> {
                    try {
                        boolean connected = EnhancedSettingsManager.testDatabaseConnection();
                        if (connected) {
                            System.out.println("✅ Database connection successful");
                            JOptionPane.showMessageDialog(testFrame, "Database connection successful!");
                        } else {
                            System.err.println("❌ Database connection failed");
                            JOptionPane.showMessageDialog(testFrame, "Database connection failed!", "Database Error", JOptionPane.ERROR_MESSAGE);
                        }
                    } catch (Exception ex) {
                        System.err.println("❌ Error testing database: " + ex.getMessage());
                        JOptionPane.showMessageDialog(testFrame,
                            "Error testing database:\n" + ex.getMessage(),
                            "Database Error",
                            JOptionPane.ERROR_MESSAGE);
                    }
                });
                
                centerPanel.add(openSettingsBtn);
                centerPanel.add(testThemesBtn);
                centerPanel.add(testDBBtn);
                
                mainPanel.add(centerPanel, BorderLayout.CENTER);
                
                // معلومات إضافية
                JTextArea infoArea = new JTextArea();
                infoArea.setEditable(false);
                infoArea.setText(
                    "Advanced UI Settings Features:\n\n" +
                    "• 19+ Themes (FlatLaf, JTattoo, SeaGlass, etc.)\n" +
                    "• Font customization with Arabic support\n" +
                    "• Interface options (animations, sounds, tooltips)\n" +
                    "• Color customization with live preview\n" +
                    "• Database integration for settings persistence\n" +
                    "• Real-time theme application\n" +
                    "• RTL (Right-to-Left) text support\n\n" +
                    "Click the buttons above to test the features."
                );
                infoArea.setFont(new Font("Tahoma", Font.PLAIN, 12));
                infoArea.setBackground(new Color(248, 248, 248));
                
                JScrollPane scrollPane = new JScrollPane(infoArea);
                scrollPane.setBorder(BorderFactory.createTitledBorder("Information"));
                scrollPane.setPreferredSize(new Dimension(0, 150));
                mainPanel.add(scrollPane, BorderLayout.SOUTH);
                
                testFrame.add(mainPanel);
                testFrame.setVisible(true);
                
                System.out.println("✅ Test window displayed successfully");
                System.out.println("Click 'Open Advanced UI Settings' to test the settings window");
                
            } catch (Exception e) {
                System.err.println("❌ Error in test application: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
}
