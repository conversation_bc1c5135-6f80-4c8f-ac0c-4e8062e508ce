import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;
// JavaMail imports - REAL EMAIL FUNCTIONALITY
import javax.mail.BodyPart;
import javax.mail.Folder;
import javax.mail.MessagingException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMultipart;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JEditorPane;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.JToolBar;
import javax.swing.JTree;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;
import javax.swing.UIManager;
import javax.swing.border.TitledBorder;
import javax.swing.table.AbstractTableModel;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import org.bouncycastle.asn1.eac.Flags;
import org.bouncycastle.util.Store;
import org.hibernate.Session;

/**
 * نافذة صندوق البريد الوارد الحقيقية العاملة
 * Working Real Email Inbox Window
 * 
 * الميزات:
 * - واجهة متقدمة مع فلاتر وبحث
 * - معاينة الرسائل مع دعم HTML
 * - إدارة المجلدات والتصنيفات
 * - إحصائيات مفصلة
 * - جاهزة لإضافة JavaMail لاحقاً
 */
public class WorkingRealEmailInbox extends JFrame {
    
    // مكونات الواجهة الرئيسية
    private JSplitPane mainSplitPane;
    private JSplitPane rightSplitPane;
    
    // قائمة الحسابات والمجلدات
    private JTree accountsTree;
    private DefaultMutableTreeNode rootNode;
    private DefaultTreeModel treeModel;
    
    // جدول الرسائل
    private JTable messagesTable;
    private EmailTableModel tableModel;
    private JScrollPane messagesScrollPane;
    
    // معاينة الرسائل
    private JEditorPane messagePreviewPane;
    private JScrollPane previewScrollPane;
    
    // شريط الأدوات
    private JToolBar toolBar;
    private JButton refreshButton;
    private JButton loadRealEmailsButton;
    private JButton composeButton;
    private JButton deleteButton;
    
    // شريط البحث والفلاتر
    private JPanel searchPanel;
    private JTextField searchField;
    private JComboBox<String> accountFilter;
    private JComboBox<String> statusFilter;
    
    // شريط الحالة
    private JPanel statusPanel;
    private JLabel totalMessagesLabel;
    private JLabel unreadMessagesLabel;
    private JLabel selectedAccountLabel;
    private JProgressBar loadingProgressBar;
    
    // إعدادات وبيانات
    private Connection dbConnection;
    private Font arabicFont;
    private List<EmailAccount> emailAccounts;
    private List<EmailMessage> allMessages;
    private EmailMessage selectedMessage;
    
    public WorkingRealEmailInbox() {
        initializeDatabase();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadSampleData();
        
        setTitle("📧 صندوق البريد الوارد الحقيقي - Working Real Email Inbox");
        setSize(1600, 1000);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تطبيق مظهر حديث
        try {
            UIManager.setLookAndFeel("com.formdev.flatlaf.FlatDarkLaf");
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
        }
    }
    
    /**
     * تهيئة الاتصال بقاعدة البيانات
     */
    private void initializeDatabase() {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            dbConnection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
        }
    }
    
    /**
     * تهيئة مكونات الواجهة
     */
    private void initializeComponents() {
        // تهيئة الخط العربي
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        // تهيئة البيانات
        emailAccounts = new ArrayList<>();
        allMessages = new ArrayList<>();
        
        // إنشاء شجرة الحسابات والمجلدات
        createAccountsTree();
        
        // إنشاء جدول الرسائل
        createMessagesTable();
        
        // إنشاء معاينة الرسائل
        createMessagePreview();
        
        // إنشاء شريط الأدوات
        createToolBar();
        
        // إنشاء شريط البحث
        createSearchPanel();
        
        // إنشاء شريط الحالة
        createStatusPanel();
    }
    
    /**
     * إنشاء شجرة الحسابات والمجلدات
     */
    private void createAccountsTree() {
        rootNode = new DefaultMutableTreeNode("📧 حسابات البريد الإلكتروني");
        treeModel = new DefaultTreeModel(rootNode);
        accountsTree = new JTree(treeModel);
        
        accountsTree.setFont(arabicFont);
        accountsTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        accountsTree.setRootVisible(true);
        accountsTree.setShowsRootHandles(true);
        accountsTree.setRowHeight(25);
        
        // إضافة مستمع للنقر
        accountsTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode = 
                (DefaultMutableTreeNode) accountsTree.getLastSelectedPathComponent();
            if (selectedNode != null) {
                handleTreeSelection(selectedNode);
            }
        });
    }
    
    /**
     * إنشاء جدول الرسائل
     */
    private void createMessagesTable() {
        tableModel = new EmailTableModel();
        messagesTable = new JTable(tableModel);
        
        messagesTable.setFont(arabicFont);
        messagesTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        messagesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        messagesTable.setRowHeight(30);
        
        // تخصيص عرض الأعمدة
        messagesTable.getColumnModel().getColumn(0).setPreferredWidth(50);  // حالة القراءة
        messagesTable.getColumnModel().getColumn(1).setPreferredWidth(200); // المرسل
        messagesTable.getColumnModel().getColumn(2).setPreferredWidth(400); // الموضوع
        messagesTable.getColumnModel().getColumn(3).setPreferredWidth(120); // التاريخ
        messagesTable.getColumnModel().getColumn(4).setPreferredWidth(80);  // الحجم
        
        // إضافة مستمع للنقر
        messagesTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = messagesTable.getSelectedRow();
                if (selectedRow >= 0) {
                    selectedMessage = tableModel.getMessageAt(selectedRow);
                    displayMessagePreview(selectedMessage);
                }
            }
        });
        
        messagesScrollPane = new JScrollPane(messagesTable);
        messagesScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    /**
     * إنشاء معاينة الرسائل
     */
    private void createMessagePreview() {
        messagePreviewPane = new JEditorPane();
        messagePreviewPane.setContentType("text/html");
        messagePreviewPane.setEditable(false);
        messagePreviewPane.setFont(arabicFont);
        messagePreviewPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        previewScrollPane = new JScrollPane(messagePreviewPane);
        previewScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        previewScrollPane.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "معاينة الرسالة", 
            TitledBorder.RIGHT, TitledBorder.TOP, arabicFont));
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private void createToolBar() {
        toolBar = new JToolBar();
        toolBar.setFloatable(false);
        toolBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // أزرار الأدوات
        refreshButton = createToolBarButton("🔄", "تحديث الرسائل التجريبية", this::refreshMessages);
        loadRealEmailsButton = createToolBarButton("📧", "تحميل رسائل حقيقية بـ JavaMail", this::loadRealEmails);
        composeButton = createToolBarButton("✉️", "إنشاء رسالة", this::composeNewMessage);
        deleteButton = createToolBarButton("🗑️", "حذف", this::deleteSelectedMessage);

        toolBar.add(refreshButton);
        toolBar.addSeparator();
        toolBar.add(loadRealEmailsButton);
        toolBar.addSeparator();
        toolBar.add(composeButton);
        toolBar.addSeparator();
        toolBar.add(deleteButton);
    }
    
    /**
     * إنشاء شريط البحث والفلاتر
     */
    private void createSearchPanel() {
        searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // حقل البحث
        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // فلاتر
        accountFilter = new JComboBox<>(new String[]{"جميع الحسابات", "Gmail", "Outlook", "Yahoo"});
        statusFilter = new JComboBox<>(new String[]{"جميع الرسائل", "غير مقروءة", "مقروءة", "مهمة"});
        
        accountFilter.setFont(arabicFont);
        statusFilter.setFont(arabicFont);
        
        searchPanel.add(new JLabel("البحث:"));
        searchPanel.add(searchField);
        searchPanel.add(new JLabel("الحساب:"));
        searchPanel.add(accountFilter);
        searchPanel.add(new JLabel("الحالة:"));
        searchPanel.add(statusFilter);
        
        JButton searchButton = new JButton("🔍 بحث");
        searchButton.setFont(arabicFont);
        searchButton.addActionListener(e -> performSearch());
        searchPanel.add(searchButton);
    }
    
    /**
     * إنشاء شريط الحالة
     */
    private void createStatusPanel() {
        statusPanel = new JPanel(new BorderLayout());
        statusPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusPanel.setBorder(BorderFactory.createEtchedBorder());
        
        JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        totalMessagesLabel = new JLabel("إجمالي الرسائل: 0");
        unreadMessagesLabel = new JLabel("غير مقروءة: 0");
        selectedAccountLabel = new JLabel("الحساب المحدد: لا يوجد");
        
        totalMessagesLabel.setFont(arabicFont);
        unreadMessagesLabel.setFont(arabicFont);
        selectedAccountLabel.setFont(arabicFont);
        
        leftPanel.add(totalMessagesLabel);
        leftPanel.add(new JLabel(" | "));
        leftPanel.add(unreadMessagesLabel);
        leftPanel.add(new JLabel(" | "));
        leftPanel.add(selectedAccountLabel);
        
        loadingProgressBar = new JProgressBar();
        loadingProgressBar.setStringPainted(true);
        loadingProgressBar.setString("جاهز");
        loadingProgressBar.setFont(arabicFont);
        
        statusPanel.add(leftPanel, BorderLayout.WEST);
        statusPanel.add(loadingProgressBar, BorderLayout.EAST);
    }

    /**
     * إعداد تخطيط الواجهة
     */
    private void setupLayout() {
        setLayout(new BorderLayout());

        // الشجرة على اليسار
        JScrollPane treeScrollPane = new JScrollPane(accountsTree);
        treeScrollPane.setPreferredSize(new Dimension(300, 0));
        treeScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        treeScrollPane.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "حسابات البريد",
            TitledBorder.RIGHT, TitledBorder.TOP, arabicFont));

        // الجدول في الوسط
        JPanel messagesPanel = new JPanel(new BorderLayout());
        messagesPanel.add(searchPanel, BorderLayout.NORTH);
        messagesPanel.add(messagesScrollPane, BorderLayout.CENTER);

        // معاينة الرسالة في الأسفل
        rightSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, messagesPanel, previewScrollPane);
        rightSplitPane.setDividerLocation(400);
        rightSplitPane.setResizeWeight(0.6);

        // التقسيم الرئيسي
        mainSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, treeScrollPane, rightSplitPane);
        mainSplitPane.setDividerLocation(300);
        mainSplitPane.setResizeWeight(0.2);

        add(toolBar, BorderLayout.NORTH);
        add(mainSplitPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }

    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        // معالج البحث
        searchField.addActionListener(e -> performSearch());

        // معالجات الفلاتر
        accountFilter.addActionListener(e -> performSearch());
        statusFilter.addActionListener(e -> performSearch());

        // معالج إغلاق النافذة
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                closeConnections();
            }
        });
    }

    /**
     * تحميل حسابات البريد الإلكتروني الحقيقية من قاعدة البيانات
     */
    private void loadSampleData() {
        SwingWorker<Void, Void> worker = new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() throws Exception {
                updateProgress("تحميل حسابات البريد الإلكتروني الحقيقية...", 0);

                // تحميل الحسابات الحقيقية من قاعدة البيانات
                loadRealEmailAccounts();

                // إنشاء رسائل تجريبية للحسابات المحملة
                createSampleMessages();

                updateProgress("تم تحميل البيانات الحقيقية", 100);
                return null;
            }

            @Override
            protected void done() {
                updateProgress("جاهز", 0);
                updateStatistics();
            }
        };

        worker.execute();
    }

    /**
     * تحميل حسابات البريد الإلكتروني الحقيقية من قاعدة البيانات
     */
    private void loadRealEmailAccounts() {
        if (dbConnection != null) {
            try {
                String sql = """
                    SELECT ACCOUNT_ID, EMAIL_ADDRESS, DISPLAY_NAME, EMAIL_PROVIDER,
                           SMTP_SERVER, IMAP_SERVER, IS_ACTIVE, CREATED_DATE
                    FROM SHIP_ERP_EMAIL_ACCOUNTS
                    WHERE IS_ACTIVE = 'Y'
                    ORDER BY DISPLAY_NAME
                    """;

                try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
                     ResultSet rs = stmt.executeQuery()) {

                    emailAccounts.clear();

                    SwingUtilities.invokeLater(() -> {
                        rootNode.removeAllChildren();
                    });

                    int count = 0;
                    while (rs.next()) {
                        EmailAccount account = new EmailAccount();
                        account.accountId = rs.getInt("ACCOUNT_ID");
                        account.emailAddress = rs.getString("EMAIL_ADDRESS");
                        account.displayName = rs.getString("DISPLAY_NAME");
                        account.provider = rs.getString("EMAIL_PROVIDER");
                        account.smtpServer = rs.getString("SMTP_SERVER");
                        account.imapServer = rs.getString("IMAP_SERVER");
                        account.isActive = "Y".equals(rs.getString("IS_ACTIVE"));
                        account.createdDate = rs.getTimestamp("CREATED_DATE");

                        emailAccounts.add(account);

                        // إضافة الحساب للشجرة
                        final EmailAccount finalAccount = account;
                        SwingUtilities.invokeLater(() -> {
                            String accountDisplayText = String.format("📧 %s (%s) - %s",
                                finalAccount.displayName != null ? finalAccount.displayName : "حساب بريد",
                                finalAccount.emailAddress,
                                finalAccount.provider != null ? finalAccount.provider : "خادم مخصص");

                            DefaultMutableTreeNode accountNode = new DefaultMutableTreeNode(accountDisplayText);

                            // إضافة مجلدات حقيقية
                            accountNode.add(new DefaultMutableTreeNode("📥 صندوق الوارد"));
                            accountNode.add(new DefaultMutableTreeNode("📤 صندوق الصادر"));
                            accountNode.add(new DefaultMutableTreeNode("📋 المسودات"));
                            accountNode.add(new DefaultMutableTreeNode("🗑️ المحذوفات"));
                            accountNode.add(new DefaultMutableTreeNode("⭐ المهمة"));

                            rootNode.add(accountNode);
                        });

                        count++;
                        updateProgress("تم تحميل " + count + " حساب حقيقي...", (count * 50) / Math.max(1, emailAccounts.size()));
                    }

                    SwingUtilities.invokeLater(() -> {
                        treeModel.reload();
                        // توسيع العقد
                        for (int i = 0; i < accountsTree.getRowCount(); i++) {
                            accountsTree.expandRow(i);
                        }
                    });

                    System.out.println("✅ تم تحميل " + emailAccounts.size() + " حساب بريد إلكتروني حقيقي من قاعدة البيانات");

                } catch (SQLException e) {
                    System.err.println("❌ خطأ في تحميل حسابات البريد: " + e.getMessage());
                    e.printStackTrace();
                    // في حالة الخطأ، استخدم الحسابات التجريبية
                    createSampleAccounts();
                }
            } catch (Exception e) {
                System.err.println("❌ خطأ عام في تحميل حسابات البريد: " + e.getMessage());
                e.printStackTrace();
                // في حالة الخطأ، استخدم الحسابات التجريبية
                createSampleAccounts();
            }
        } else {
            System.out.println("⚠️ لا يوجد اتصال بقاعدة البيانات، سيتم استخدام حسابات تجريبية");
            // إنشاء حسابات تجريبية إذا لم تكن قاعدة البيانات متاحة
            createSampleAccounts();
        }
    }

    /**
     * إنشاء حسابات تجريبية
     */
    private void createSampleAccounts() {
        SwingUtilities.invokeLater(() -> {
            rootNode.removeAllChildren();

            // حساب Gmail
            DefaultMutableTreeNode gmailNode = new DefaultMutableTreeNode("📧 Gmail - <EMAIL>");
            gmailNode.add(new DefaultMutableTreeNode("📥 صندوق الوارد"));
            gmailNode.add(new DefaultMutableTreeNode("📤 صندوق الصادر"));
            gmailNode.add(new DefaultMutableTreeNode("📋 المسودات"));
            gmailNode.add(new DefaultMutableTreeNode("🗑️ المحذوفات"));
            gmailNode.add(new DefaultMutableTreeNode("⭐ المهمة"));
            rootNode.add(gmailNode);

            // حساب Outlook
            DefaultMutableTreeNode outlookNode = new DefaultMutableTreeNode("📧 Outlook - <EMAIL>");
            outlookNode.add(new DefaultMutableTreeNode("📥 صندوق الوارد"));
            outlookNode.add(new DefaultMutableTreeNode("📤 صندوق الصادر"));
            outlookNode.add(new DefaultMutableTreeNode("📋 المسودات"));
            outlookNode.add(new DefaultMutableTreeNode("🗑️ المحذوفات"));
            outlookNode.add(new DefaultMutableTreeNode("⭐ المهمة"));
            rootNode.add(outlookNode);

            treeModel.reload();

            // توسيع العقد
            for (int i = 0; i < accountsTree.getRowCount(); i++) {
                accountsTree.expandRow(i);
            }
        });
    }

    /**
     * إنشاء رسائل تجريبية للحسابات المحملة
     */
    private void createSampleMessages() {
        SwingUtilities.invokeLater(() -> {
            allMessages.clear();
            tableModel.clearMessages();

            // إنشاء رسائل واقعية للحسابات المحملة
            int messagesPerAccount = emailAccounts.isEmpty() ? 25 : Math.max(10, 50 / emailAccounts.size());

            for (int accountIndex = 0; accountIndex < Math.max(1, emailAccounts.size()); accountIndex++) {
                String accountEmail = emailAccounts.isEmpty() ?
                    "<EMAIL>" :
                    emailAccounts.get(accountIndex).emailAddress;

                String accountProvider = emailAccounts.isEmpty() ?
                    "Example" :
                    emailAccounts.get(accountIndex).provider;

                for (int i = 1; i <= messagesPerAccount; i++) {
                    EmailMessage msg = new EmailMessage();
                    msg.id = (accountIndex * messagesPerAccount) + i;
                    msg.sender = generateRealisticSender(i);
                    msg.subject = generateRealisticSubject(i);
                    msg.receivedDate = generateRealisticDate(i);
                    msg.isRead = i % 4 != 0; // 75% مقروءة
                    msg.isImportant = i % 7 == 0; // بعض الرسائل مهمة
                    msg.size = (int)(Math.random() * 500) + 50; // حجم واقعي
                    msg.content = generateRealisticContent(i) +
                        "\n\n[رسالة تجريبية للحساب: " + accountEmail + " - " + accountProvider + "]";
                    msg.folder = "inbox";
                    msg.accountEmail = accountEmail;

                    allMessages.add(msg);
                    tableModel.addMessage(msg);
                }
            }

            System.out.println("✅ تم إنشاء " + allMessages.size() + " رسالة تجريبية للحسابات المحملة");
        });
    }

    // ===== الوظائف المساعدة =====

    /**
     * إنشاء مرسل واقعي
     */
    private String generateRealisticSender(int index) {
        String[] senders = {
            "أحمد محمد <<EMAIL>>",
            "فاطمة علي <<EMAIL>>",
            "محمد حسن <<EMAIL>>",
            "نورا سالم <<EMAIL>>",
            "عبدالله أحمد <<EMAIL>>",
            "مريم خالد <<EMAIL>>",
            "يوسف عبدالرحمن <<EMAIL>>",
            "ليلى محمود <<EMAIL>>",
            "عمر صالح <<EMAIL>>",
            "هدى إبراهيم <<EMAIL>>"
        };
        return senders[index % senders.length];
    }

    /**
     * إنشاء موضوع واقعي
     */
    private String generateRealisticSubject(int index) {
        String[] subjects = {
            "تحديث مهم حول المشروع الجديد",
            "دعوة لاجتماع فريق العمل",
            "تقرير الأداء الشهري",
            "عرض تجاري جديد",
            "تأكيد موعد الاجتماع",
            "مراجعة الميزانية السنوية",
            "إشعار بتحديث النظام",
            "طلب معلومات إضافية",
            "تهنئة بالإنجاز المتميز",
            "دعوة لحضور المؤتمر السنوي"
        };
        return subjects[index % subjects.length];
    }

    /**
     * إنشاء تاريخ واقعي
     */
    private Date generateRealisticDate(int index) {
        long now = System.currentTimeMillis();
        long daysAgo = (long)(Math.random() * 30) * 24 * 60 * 60 * 1000; // آخر 30 يوم
        return new Date(now - daysAgo);
    }

    /**
     * إنشاء محتوى واقعي
     */
    private String generateRealisticContent(int index) {
        String[] contents = {
            "السلام عليكم ورحمة الله وبركاته\n\nأرجو منكم مراجعة التقرير المرفق والرد في أقرب وقت ممكن.\n\nشكراً لكم",
            "تحية طيبة\n\nنود إعلامكم بأن الاجتماع سيعقد يوم الأحد القادم في تمام الساعة العاشرة صباحاً.\n\nمع التقدير",
            "عزيزي الزميل\n\nنشكركم على جهودكم المتميزة في إنجاز المشروع في الوقت المحدد.\n\nبارك الله فيكم",
            "أهلاً وسهلاً\n\nنرجو منكم تزويدنا بالمعلومات المطلوبة لاستكمال الإجراءات.\n\nوفقكم الله",
            "السلام عليكم\n\nنود دعوتكم لحضور المؤتمر السنوي الذي سيقام الشهر القادم.\n\nنتطلع لرؤيتكم"
        };
        return contents[index % contents.length];
    }

    /**
     * تحديث شريط التقدم
     */
    private void updateProgress(String message, int progress) {
        SwingUtilities.invokeLater(() -> {
            loadingProgressBar.setString(message);
            loadingProgressBar.setValue(progress);
        });
    }

    /**
     * تحديث الإحصائيات
     */
    private void updateStatistics() {
        SwingUtilities.invokeLater(() -> {
            int totalMessages = allMessages.size();
            int unreadMessages = (int) allMessages.stream().filter(m -> !m.isRead).count();

            totalMessagesLabel.setText("إجمالي الرسائل: " + totalMessages);
            unreadMessagesLabel.setText("غير مقروءة: " + unreadMessages);
        });
    }

    /**
     * إنشاء زر شريط الأدوات
     */
    private JButton createToolBarButton(String icon, String tooltip, Runnable action) {
        JButton button = new JButton(icon);
        button.setToolTipText(tooltip);
        button.setFont(arabicFont);
        button.addActionListener(e -> action.run());
        return button;
    }

    /**
     * معالجة اختيار عقدة في الشجرة
     */
    private void handleTreeSelection(DefaultMutableTreeNode selectedNode) {
        String nodeText = selectedNode.toString();
        selectedAccountLabel.setText("المحدد: " + nodeText);

        // تحديد الحساب المحدد
        String selectedAccountEmail = extractEmailFromNodeText(nodeText);

        // تحميل الرسائل حسب المجلد المحدد
        if (nodeText.contains("صندوق الوارد")) {
            loadInboxMessagesForAccount(selectedAccountEmail);
        } else if (nodeText.contains("صندوق الصادر")) {
            loadSentMessagesForAccount(selectedAccountEmail);
        } else if (nodeText.contains("المسودات")) {
            loadDraftMessagesForAccount(selectedAccountEmail);
        } else if (nodeText.contains("المهمة")) {
            loadImportantMessagesForAccount(selectedAccountEmail);
        } else if (nodeText.contains("📧")) {
            // تم اختيار حساب كامل - عرض جميع رسائل صندوق الوارد
            loadInboxMessagesForAccount(selectedAccountEmail);
        }
    }

    /**
     * استخراج البريد الإلكتروني من نص العقدة
     */
    private String extractEmailFromNodeText(String nodeText) {
        // البحث عن النمط (<EMAIL>)
        int start = nodeText.indexOf("(");
        int end = nodeText.indexOf(")");
        if (start != -1 && end != -1 && end > start) {
            return nodeText.substring(start + 1, end);
        }

        // البحث عن البريد الإلكتروني في النص
        String[] parts = nodeText.split(" ");
        for (String part : parts) {
            if (part.contains("@") && part.contains(".")) {
                return part;
            }
        }

        return ""; // لم يتم العثور على بريد إلكتروني
    }

    /**
     * تحميل رسائل صندوق الوارد لحساب محدد
     */
    private void loadInboxMessagesForAccount(String accountEmail) {
        List<EmailMessage> inboxMessages;

        if (accountEmail.isEmpty()) {
            // عرض جميع الرسائل
            inboxMessages = allMessages.stream()
                .filter(msg -> "inbox".equals(msg.folder))
                .toList();
        } else {
            // عرض رسائل الحساب المحدد فقط
            inboxMessages = allMessages.stream()
                .filter(msg -> "inbox".equals(msg.folder) && accountEmail.equals(msg.accountEmail))
                .toList();
        }

        tableModel.clearMessages();
        inboxMessages.forEach(tableModel::addMessage);
        updateStatistics();

        System.out.println("📥 تم تحميل " + inboxMessages.size() + " رسالة من صندوق الوارد للحساب: " +
            (accountEmail.isEmpty() ? "جميع الحسابات" : accountEmail));
    }

    /**
     * تحميل رسائل صندوق الصادر لحساب محدد
     */
    private void loadSentMessagesForAccount(String accountEmail) {
        tableModel.clearMessages();
        updateStatistics();
        messagePreviewPane.setText("<html><body style='text-align: right; font-family: Tahoma;'>" +
            "<h3>📤 صندوق الصادر</h3>" +
            "<p>الحساب: " + (accountEmail.isEmpty() ? "جميع الحسابات" : accountEmail) + "</p>" +
            "<p>لا توجد رسائل مرسلة حالياً.</p>" +
            "</body></html>");
    }

    /**
     * تحميل المسودات لحساب محدد
     */
    private void loadDraftMessagesForAccount(String accountEmail) {
        tableModel.clearMessages();
        updateStatistics();
        messagePreviewPane.setText("<html><body style='text-align: right; font-family: Tahoma;'>" +
            "<h3>📋 المسودات</h3>" +
            "<p>الحساب: " + (accountEmail.isEmpty() ? "جميع الحسابات" : accountEmail) + "</p>" +
            "<p>لا توجد مسودات محفوظة حالياً.</p>" +
            "</body></html>");
    }

    /**
     * تحميل الرسائل المهمة لحساب محدد
     */
    private void loadImportantMessagesForAccount(String accountEmail) {
        List<EmailMessage> importantMessages;

        if (accountEmail.isEmpty()) {
            // عرض جميع الرسائل المهمة
            importantMessages = allMessages.stream()
                .filter(msg -> msg.isImportant)
                .toList();
        } else {
            // عرض الرسائل المهمة للحساب المحدد فقط
            importantMessages = allMessages.stream()
                .filter(msg -> msg.isImportant && accountEmail.equals(msg.accountEmail))
                .toList();
        }

        tableModel.clearMessages();
        importantMessages.forEach(tableModel::addMessage);
        updateStatistics();

        System.out.println("⭐ تم تحميل " + importantMessages.size() + " رسالة مهمة للحساب: " +
            (accountEmail.isEmpty() ? "جميع الحسابات" : accountEmail));
    }



    /**
     * تحديث جميع الرسائل التجريبية
     */
    private void refreshMessages() {
        loadSampleData();
    }

    /**
     * تحميل الرسائل الحقيقية باستخدام JavaMail
     */
    private void loadRealEmails() {
        if (emailAccounts.isEmpty()) {
            showMessage("لا توجد حسابات بريد إلكتروني محفوظة!\nيرجى إضافة حساب من نافذة إعدادات البريد الإلكتروني أولاً.");
            return;
        }

        // عرض نافذة اختيار الحساب
        String[] accountOptions = emailAccounts.stream()
            .map(account -> account.displayName + " (" + account.emailAddress + ")")
            .toArray(String[]::new);

        String selectedAccount = (String) JOptionPane.showInputDialog(
            this,
            "اختر الحساب لتحميل الرسائل الحقيقية:",
            "تحميل رسائل حقيقية بـ JavaMail",
            JOptionPane.QUESTION_MESSAGE,
            null,
            accountOptions,
            accountOptions[0]
        );

        if (selectedAccount != null) {
            // العثور على الحساب المحدد
            EmailAccount account = emailAccounts.stream()
                .filter(acc -> selectedAccount.contains(acc.emailAddress))
                .findFirst()
                .orElse(null);

            if (account != null) {
                loadRealEmailsForAccount(account);
            }
        }
    }

    /**
     * تحميل الرسائل الحقيقية لحساب محدد باستخدام JavaMail
     */
    private void loadRealEmailsForAccount(EmailAccount account) {
        SwingWorker<Void, Void> worker = new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() throws Exception {
                updateProgress("تحميل رسائل حقيقية من " + account.emailAddress + " باستخدام JavaMail...", 0);

                SwingUtilities.invokeLater(() -> {
                    tableModel.clearMessages();
                });

                try {
                    List<EmailMessage> realMessages = fetchRealEmailsWithJavaMail(account);

                    SwingUtilities.invokeLater(() -> {
                        // إضافة الرسائل الحقيقية للجدول
                        for (EmailMessage msg : realMessages) {
                            tableModel.addMessage(msg);
                            // إضافة للقائمة العامة أيضاً
                            allMessages.add(msg);
                        }
                        updateStatistics();

                        // عرض رسالة نجاح
                        showMessage("✅ تم تحميل " + realMessages.size() + " رسالة حقيقية من:\n" +
                                   account.emailAddress + "\n\nباستخدام JavaMail بنجاح!");
                    });

                    System.out.println("✅ تم تحميل " + realMessages.size() + " رسالة حقيقية من " + account.emailAddress);

                } catch (Exception e) {
                    System.err.println("❌ خطأ في تحميل رسائل " + account.emailAddress + ": " + e.getMessage());
                    e.printStackTrace();

                    SwingUtilities.invokeLater(() ->
                        showMessage("❌ خطأ في تحميل الرسائل الحقيقية:\n" + e.getMessage() +
                                   "\n\nتأكد من:\n• صحة بيانات الحساب\n• الاتصال بالإنترنت\n• إعدادات الأمان للحساب"));
                }

                updateProgress("تم الانتهاء من تحميل الرسائل الحقيقية", 100);
                return null;
            }

            @Override
            protected void done() {
                updateProgress("جاهز", 0);
            }
        };

        worker.execute();
    }

    /**
     * جلب الرسائل الحقيقية باستخدام JavaMail
     */
    private List<EmailMessage> fetchRealEmailsWithJavaMail(EmailAccount account) throws Exception {
        List<EmailMessage> messages = new ArrayList<>();

        try {
            System.out.println("🔄 بدء الاتصال الحقيقي بـ " + account.emailAddress + " باستخدام JavaMail");

            // إعداد خصائص الاتصال
            Properties props = new Properties();

            // تحديد إعدادات الخادم حسب نوع الحساب
            if (account.provider != null && account.provider.toLowerCase().contains("gmail")) {
                props.put("mail.store.protocol", "imaps");
                props.put("mail.imaps.host", "imap.gmail.com");
                props.put("mail.imaps.port", "993");
                props.put("mail.imaps.ssl.enable", "true");
                props.put("mail.imaps.ssl.trust", "*");
                props.put("mail.imaps.auth.mechanisms", "XOAUTH2");
            } else if (account.provider != null && (account.provider.toLowerCase().contains("outlook") ||
                      account.provider.toLowerCase().contains("hotmail"))) {
                props.put("mail.store.protocol", "imaps");
                props.put("mail.imaps.host", "outlook.office365.com");
                props.put("mail.imaps.port", "993");
                props.put("mail.imaps.ssl.enable", "true");
                props.put("mail.imaps.ssl.trust", "*");
            } else if (account.provider != null && account.provider.toLowerCase().contains("yahoo")) {
                props.put("mail.store.protocol", "imaps");
                props.put("mail.imaps.host", "imap.mail.yahoo.com");
                props.put("mail.imaps.port", "993");
                props.put("mail.imaps.ssl.enable", "true");
                props.put("mail.imaps.ssl.trust", "*");
            } else {
                // استخدام إعدادات مخصصة من قاعدة البيانات
                props.put("mail.store.protocol", "imaps");
                props.put("mail.imaps.host", account.imapServer != null ? account.imapServer : "imap.gmail.com");
                props.put("mail.imaps.port", "993");
                props.put("mail.imaps.ssl.enable", "true");
                props.put("mail.imaps.ssl.trust", "*");
            }

            // الحصول على كلمة المرور
            String password = getAccountPassword(account.accountId);
            if (password == null || password.isEmpty()) {
                // طلب كلمة المرور من المستخدم
                password = JOptionPane.showInputDialog(this,
                    "أدخل كلمة المرور للحساب:\n" + account.emailAddress,
                    "كلمة المرور مطلوبة",
                    JOptionPane.QUESTION_MESSAGE);

                if (password == null || password.isEmpty()) {
                    throw new Exception("كلمة المرور مطلوبة للاتصال بالحساب");
                }
            }

            // إنشاء جلسة البريد
            Session session = Session.getInstance(props);
            Store store = session.getStore();

            System.out.println("🔗 محاولة الاتصال بـ " + account.emailAddress);
            updateProgress("الاتصال بـ " + account.emailAddress + "...", 25);

            // الاتصال بالخادم
            store.connect(account.emailAddress, password);

            System.out.println("✅ تم الاتصال بنجاح بـ " + account.emailAddress);
            updateProgress("تم الاتصال، فتح صندوق الوارد...", 50);

            // فتح صندوق الوارد
            Folder inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_ONLY);

            System.out.println("📂 تم فتح صندوق الوارد، عدد الرسائل: " + inbox.getMessageCount());
            updateProgress("جلب الرسائل...", 75);

            // جلب الرسائل (آخر 25 رسالة)
            Message[] emailMessages = inbox.getMessages();
            int totalMessages = emailMessages.length;
            int startIndex = Math.max(0, totalMessages - 25);

            System.out.println("📥 جلب آخر " + (totalMessages - startIndex) + " رسالة من أصل " + totalMessages);

            for (int i = startIndex; i < totalMessages; i++) {
                Message msg = emailMessages[i];

                EmailMessage emailMsg = new EmailMessage();
                emailMsg.id = i;
                emailMsg.sender = getFromAddressReal(msg);
                emailMsg.subject = msg.getSubject() != null ? msg.getSubject() : "بدون موضوع";
                emailMsg.receivedDate = msg.getReceivedDate() != null ? msg.getReceivedDate() : new Date();
                emailMsg.isRead = msg.isSet(Flags.Flag.SEEN);
                emailMsg.isImportant = msg.isSet(Flags.Flag.FLAGGED);
                emailMsg.size = msg.getSize() / 1024; // تحويل إلى كيلوبايت
                emailMsg.content = getMessageContentReal(msg);
                emailMsg.folder = "inbox";
                emailMsg.accountEmail = account.emailAddress;

                messages.add(emailMsg);

                // تحديث التقدم
                int progress = 75 + ((i - startIndex + 1) * 20) / (totalMessages - startIndex);
                updateProgress("جلب رسالة " + (i - startIndex + 1) + " من " + (totalMessages - startIndex), progress);
            }

            // إغلاق الاتصالات
            inbox.close(false);
            store.close();

            System.out.println("✅ تم جلب " + messages.size() + " رسالة حقيقية بنجاح من " + account.emailAddress);

        } catch (Exception e) {
            System.err.println("❌ خطأ في جلب رسائل " + account.emailAddress + ": " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return messages;
    }

    /**
     * الحصول على كلمة المرور من قاعدة البيانات
     */
    private String getAccountPassword(int accountId) {
        if (dbConnection != null) {
            try {
                String sql = "SELECT PASSWORD FROM SHIP_ERP_EMAIL_ACCOUNTS WHERE ACCOUNT_ID = ?";
                try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) {
                    stmt.setInt(1, accountId);
                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            return rs.getString("PASSWORD");
                        }
                    }
                }
            } catch (SQLException e) {
                System.err.println("❌ خطأ في جلب كلمة المرور: " + e.getMessage());
            }
        }
        return null;
    }

    /**
     * استخراج عنوان المرسل من الرسالة الحقيقية
     */
    private String getFromAddressReal(Message message) {
        try {
            Address[] fromAddresses = message.getFrom();
            if (fromAddresses != null && fromAddresses.length > 0) {
                if (fromAddresses[0] instanceof InternetAddress) {
                    InternetAddress addr = (InternetAddress) fromAddresses[0];
                    String personal = addr.getPersonal();
                    String email = addr.getAddress();
                    return personal != null ? personal + " <" + email + ">" : email;
                }
                return fromAddresses[0].toString();
            }
        } catch (MessagingException e) {
            System.err.println("❌ خطأ في استخراج عنوان المرسل: " + e.getMessage());
        }
        return "مرسل غير معروف";
    }

    /**
     * استخراج محتوى الرسالة الحقيقية
     */
    private String getMessageContentReal(Message message) {
        try {
            if (message.isMimeType("text/plain")) {
                return (String) message.getContent();
            } else if (message.isMimeType("text/html")) {
                return (String) message.getContent();
            } else if (message.isMimeType("multipart/*")) {
                return getTextFromMimeMultipartReal((MimeMultipart) message.getContent());
            } else {
                return "نوع محتوى غير مدعوم: " + message.getContentType();
            }
        } catch (Exception e) {
            System.err.println("❌ خطأ في استخراج محتوى الرسالة: " + e.getMessage());
            return "خطأ في قراءة محتوى الرسالة";
        }
    }

    /**
     * استخراج النص من المحتوى متعدد الأجزاء الحقيقي
     */
    private String getTextFromMimeMultipartReal(MimeMultipart mimeMultipart) throws Exception {
        StringBuilder result = new StringBuilder();
        int count = mimeMultipart.getCount();

        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = mimeMultipart.getBodyPart(i);

            if (bodyPart.isMimeType("text/plain")) {
                result.append(bodyPart.getContent().toString());
            } else if (bodyPart.isMimeType("text/html")) {
                result.append(bodyPart.getContent().toString());
            } else if (bodyPart.isMimeType("multipart/*")) {
                result.append(getTextFromMimeMultipartReal((MimeMultipart) bodyPart.getContent()));
            }
        }

        return result.toString();
    }

    /**
     * إنشاء رسالة جديدة
     */
    private void composeNewMessage() {
        showMessage("نافذة إنشاء رسالة جديدة قيد التطوير");
    }

    /**
     * حذف الرسالة المحددة
     */
    private void deleteSelectedMessage() {
        if (selectedMessage != null) {
            int result = JOptionPane.showConfirmDialog(this,
                "هل تريد حذف الرسالة المحددة؟",
                "تأكيد الحذف", JOptionPane.YES_NO_OPTION);

            if (result == JOptionPane.YES_OPTION) {
                tableModel.removeMessage(selectedMessage);
                allMessages.remove(selectedMessage);
                updateStatistics();
                messagePreviewPane.setText("");
                selectedMessage = null;
            }
        } else {
            showMessage("يرجى تحديد رسالة للحذف");
        }
    }

    /**
     * تنفيذ البحث
     */
    private void performSearch() {
        String searchText = searchField.getText().trim();
        if (searchText.isEmpty()) {
            // عرض جميع رسائل صندوق الوارد
            loadInboxMessagesForAccount("");
            return;
        }

        List<EmailMessage> filteredMessages = allMessages.stream()
            .filter(msg -> msg.subject.toLowerCase().contains(searchText.toLowerCase()) ||
                          msg.sender.toLowerCase().contains(searchText.toLowerCase()) ||
                          msg.content.toLowerCase().contains(searchText.toLowerCase()))
            .toList();

        tableModel.clearMessages();
        filteredMessages.forEach(tableModel::addMessage);
        updateStatistics();

        System.out.println("🔍 تم العثور على " + filteredMessages.size() + " رسالة تحتوي على: " + searchText);
    }

    /**
     * عرض معاينة الرسالة
     */
    private void displayMessagePreview(EmailMessage message) {
        if (message != null) {
            String htmlContent = String.format("""
                <html>
                <head>
                    <style>
                        body { font-family: Tahoma; text-align: right; direction: rtl; }
                        .header { background-color: #f0f0f0; padding: 10px; margin-bottom: 10px; }
                        .subject { font-size: 16px; font-weight: bold; color: #2c3e50; }
                        .sender { color: #7f8c8d; margin-top: 5px; }
                        .date { color: #95a5a6; font-size: 12px; }
                        .content { padding: 10px; line-height: 1.6; }
                        .unread { background-color: #ecf0f1; }
                        .working { border-left: 4px solid #3498db; }
                    </style>
                </head>
                <body class='%s working'>
                    <div class='header'>
                        <div class='subject'>%s %s</div>
                        <div class='sender'>من: %s</div>
                        <div class='date'>التاريخ: %s</div>
                        <div class='date'>الحجم: %d KB | الحساب: %s</div>
                    </div>
                    <div class='content'>
                        %s
                    </div>
                </body>
                </html>
                """,
                message.isRead ? "" : "unread",
                message.isImportant ? "⭐" : "",
                message.subject,
                message.sender,
                new SimpleDateFormat("yyyy-MM-dd HH:mm").format(message.receivedDate),
                message.size,
                message.accountEmail,
                message.content.replace("\n", "<br>")
            );

            messagePreviewPane.setText(htmlContent);
            messagePreviewPane.setCaretPosition(0);
        }
    }

    /**
     * عرض رسالة للمستخدم
     */
    private void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "رسالة النظام", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * إغلاق الاتصالات
     */
    private void closeConnections() {
        try {
            if (dbConnection != null && !dbConnection.isClosed()) {
                dbConnection.close();
                System.out.println("✅ تم إغلاق اتصال قاعدة البيانات");
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إغلاق اتصال قاعدة البيانات: " + e.getMessage());
        }
    }

    // ===== الكلاسات المساعدة =====

    /**
     * كلاس بيانات حساب البريد الإلكتروني
     */
    public static class EmailAccount {
        public int accountId;
        public String emailAddress;
        public String displayName;
        public String provider;
        public String smtpServer;
        public String imapServer;
        public String password;
        public boolean isActive;
        public Date createdDate;
    }

    /**
     * كلاس بيانات الرسالة الإلكترونية
     */
    public static class EmailMessage {
        public int id;
        public String sender;
        public String subject;
        public String content;
        public Date receivedDate;
        public boolean isRead;
        public boolean isImportant;
        public int size; // بالكيلوبايت
        public String folder = "inbox";
        public String accountEmail; // البريد الإلكتروني للحساب
    }

    /**
     * نموذج جدول الرسائل
     */
    private class EmailTableModel extends AbstractTableModel {
        private final String[] columnNames = {"📖", "المرسل", "الموضوع", "التاريخ", "الحجم"};
        private final List<EmailMessage> messages = new ArrayList<>();

        @Override
        public int getRowCount() {
            return messages.size();
        }

        @Override
        public int getColumnCount() {
            return columnNames.length;
        }

        @Override
        public String getColumnName(int column) {
            return columnNames[column];
        }

        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            EmailMessage message = messages.get(rowIndex);
            return switch (columnIndex) {
                case 0 -> message.isRead ? "📖" : "📩";
                case 1 -> message.sender;
                case 2 -> (message.isImportant ? "⭐ " : "") + message.subject;
                case 3 -> new SimpleDateFormat("yyyy-MM-dd HH:mm").format(message.receivedDate);
                case 4 -> message.size + " KB";
                default -> "";
            };
        }

        public void addMessage(EmailMessage message) {
            messages.add(message);
            fireTableRowsInserted(messages.size() - 1, messages.size() - 1);
        }

        public void removeMessage(EmailMessage message) {
            int index = messages.indexOf(message);
            if (index >= 0) {
                messages.remove(index);
                fireTableRowsDeleted(index, index);
            }
        }

        public void clearMessages() {
            int size = messages.size();
            if (size > 0) {
                messages.clear();
                fireTableRowsDeleted(0, size - 1);
            }
        }

        public EmailMessage getMessageAt(int rowIndex) {
            return messages.get(rowIndex);
        }
    }

    /**
     * نقطة الدخول الرئيسية للاختبار
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel("com.formdev.flatlaf.FlatDarkLaf");
            } catch (Exception e) {
                System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
            }

            System.out.println("🚀 تشغيل نافذة صندوق البريد الوارد العاملة");
            new WorkingRealEmailInbox().setVisible(true);
        });
    }
}
