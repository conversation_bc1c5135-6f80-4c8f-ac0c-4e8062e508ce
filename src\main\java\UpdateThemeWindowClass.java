import java.sql.*;

/**
 * تحديث اسم كلاس نافذة المظاهر في قاعدة البيانات
 * Update Theme Window Class Name in Database
 */
public class UpdateThemeWindowClass {
    
    public static void main(String[] args) {
        System.out.println("🔄 تحديث اسم كلاس نافذة المظاهر في قاعدة البيانات...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            updateThemeWindowClass(connection);
            
            connection.close();
            System.out.println("✅ تم تحديث اسم كلاس نافذة المظاهر بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحديث اسم كلاس نافذة المظاهر: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * تحديث اسم كلاس نافذة المظاهر
     */
    private static void updateThemeWindowClass(Connection connection) throws SQLException {
        System.out.println("📋 تحديث اسم كلاس نافذة إعدادات الواجهة والمظهر...");
        
        // البحث عن النافذة الحالية
        String searchSQL = "SELECT TREE_ID, NODE_NAME_AR, CLASS_NAME FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR LIKE '%مظهر%' OR NODE_NAME_AR LIKE '%واجهة%'";
        
        try (PreparedStatement searchStmt = connection.prepareStatement(searchSQL);
             ResultSet rs = searchStmt.executeQuery()) {
            
            while (rs.next()) {
                int treeId = rs.getInt("TREE_ID");
                String nodeName = rs.getString("NODE_NAME_AR");
                String currentClassName = rs.getString("CLASS_NAME");
                
                System.out.println("  وجدت نافذة: " + nodeName + " (ID: " + treeId + ")");
                System.out.println("  الكلاس الحالي: " + currentClassName);
                
                if ("UIThemeSettingsWindow".equals(currentClassName)) {
                    // تحديث اسم الكلاس إلى النافذة الجديدة
                    String updateSQL = "UPDATE ERP_SYSTEM_TREE SET CLASS_NAME = ? WHERE TREE_ID = ?";
                    
                    try (PreparedStatement updateStmt = connection.prepareStatement(updateSQL)) {
                        updateStmt.setString(1, "ComprehensiveThemeSettingsWindow");
                        updateStmt.setInt(2, treeId);
                        
                        int updated = updateStmt.executeUpdate();
                        
                        if (updated > 0) {
                            System.out.println("  ✅ تم تحديث الكلاس إلى: ComprehensiveThemeSettingsWindow");
                        } else {
                            System.err.println("  ❌ فشل في تحديث الكلاس");
                        }
                    }
                }
            }
        }
        
        // التحقق من النتيجة
        System.out.println("\n📋 التحقق من النتيجة:");
        try (PreparedStatement checkStmt = connection.prepareStatement(searchSQL);
             ResultSet rs = checkStmt.executeQuery()) {
            
            while (rs.next()) {
                String nodeName = rs.getString("NODE_NAME_AR");
                String className = rs.getString("CLASS_NAME");
                System.out.println("  " + nodeName + " -> " + className);
            }
        }
    }
}
