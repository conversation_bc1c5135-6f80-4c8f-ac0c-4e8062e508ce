Write-Host "Downloading FlatLaf libraries directly..." -ForegroundColor Green

Set-Location "e:\ship_erp\java\lib"

# Remove small corrupted files
$corruptedFiles = @("flatlaf-3.2.5.jar", "flatlaf-extras-3.2.5.jar", "flatlaf-intellij-themes-3.2.5.jar", "jtattoo-1.6.13.jar")
foreach ($file in $corruptedFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        if ($size -lt 10000) {
            Write-Host "Removing corrupted file: $file ($size bytes)" -ForegroundColor Yellow
            Remove-Item $file -Force
        }
    }
}

Write-Host "Downloading FlatLaf Core..." -ForegroundColor Cyan
Invoke-WebRequest -Uri "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar" -OutFile "flatlaf-3.2.5.jar"

Write-Host "Downloading FlatLaf Extras..." -ForegroundColor Cyan
Invoke-WebRequest -Uri "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar" -OutFile "flatlaf-extras-3.2.5.jar"

Write-Host "Downloading FlatLaf IntelliJ Themes..." -ForegroundColor Cyan
Invoke-WebRequest -Uri "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-intellij-themes/3.2.5/flatlaf-intellij-themes-3.2.5.jar" -OutFile "flatlaf-intellij-themes-3.2.5.jar"

Write-Host "Downloading JTattoo..." -ForegroundColor Cyan
Invoke-WebRequest -Uri "https://search.maven.org/remotecontent?filepath=com/jtattoo/JTattoo/1.6.13/JTattoo-1.6.13.jar" -OutFile "jtattoo-1.6.13.jar"

Write-Host "Verifying downloads..." -ForegroundColor Green
$files = @("flatlaf-3.2.5.jar", "flatlaf-extras-3.2.5.jar", "flatlaf-intellij-themes-3.2.5.jar", "jtattoo-1.6.13.jar")
foreach ($file in $files) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        if ($size -gt 10000) {
            Write-Host "  OK: $file ($size bytes)" -ForegroundColor Green
        } else {
            Write-Host "  FAILED: $file ($size bytes)" -ForegroundColor Red
        }
    } else {
        Write-Host "  MISSING: $file" -ForegroundColor Red
    }
}

Write-Host "Download completed!" -ForegroundColor Green
