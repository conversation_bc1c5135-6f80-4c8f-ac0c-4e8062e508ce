@echo off
echo ========================================
echo 🔄 اختبار استيراد البيانات من IAS20251
echo Testing Data Import from IAS20251
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/4] التحقق من الجداول الموجودة...
echo Checking Existing Tables...
echo ========================================

echo التحقق من جدول ERP_SUPPLIERS...
sqlplus -s ship_erp/ship_erp_password@localhost:1521/ORCL << EOF
SELECT 'ERP_SUPPLIERS Table Status: ' || COUNT(*) || ' records' FROM ERP_SUPPLIERS;
EXIT;
EOF

echo.
echo [2/4] تجميع نافذة إدارة الموردين المحدثة...
echo Compiling Updated Suppliers Management Window...
echo ========================================

echo تجميع SuppliersManagementWindow مع زر الاستيراد...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\SuppliersManagementWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع النافذة بنجاح
) else (
    echo ❌ فشل في تجميع النافذة
    pause
    exit /b 1
)

echo.
echo [3/4] تشغيل نافذة إدارة الموردين...
echo Running Suppliers Management Window...
echo ========================================

echo تشغيل النافذة مع زر الاستيراد...
start "Suppliers Management - Import Ready" java -cp "lib\*;." SuppliersManagementWindow

echo.
echo [4/4] تعليمات الاستيراد...
echo Import Instructions...
echo ========================================

echo.
echo 🔄 خطوات اختبار الاستيراد:
echo ============================

echo.
echo 📋 الخطوة 1: فتح نافذة إدارة الموردين
echo ------------------------------------------
echo 1. تأكد من فتح النافذة بنجاح
echo 2. ابحث عن زر "استيراد من IAS20251"
echo 3. تأكد من وجود الزر في شريط الأزرار

echo.
echo 📋 الخطوة 2: بدء عملية الاستيراد
echo --------------------------------
echo 1. انقر على زر "استيراد من IAS20251"
echo 2. ستظهر نافذة تأكيد العملية
echo 3. اقرأ التفاصيل واضغط "نعم" للمتابعة

echo.
echo 📋 الخطوة 3: مراقبة عملية الاستيراد
echo ------------------------------------
echo 1. ستفتح نافذة تقدم الاستيراد
echo 2. راقب الرسائل التالية:
echo    • 🚀 بدء عملية استيراد البيانات
echo    • 🔗 الاتصال بقاعدة IAS20251
echo    • ✅ تم الاتصال بنجاح
echo    • 📖 قراءة البيانات من V_DETAILS
echo    • 📊 بدء نسخ البيانات
echo    • 📊 تم نسخ X سجل...
echo    • 🎉 تم الانتهاء من الاستيراد

echo.
echo 📋 الخطوة 4: التحقق من النتائج
echo ------------------------------
echo 1. انتظر حتى ظهور رسالة "تم الانتهاء"
echo 2. اضغط "إغلاق" لإغلاق نافذة التقدم
echo 3. تحقق من تحديث جدول الموردين تلقائياً
echo 4. تأكد من ظهور البيانات المستوردة

echo.
echo 🎯 النتائج المتوقعة:
echo ===================
echo ✅ اتصال ناجح بقاعدة IAS20251
echo ✅ قراءة جميع البيانات من V_DETAILS
echo ✅ نسخ البيانات إلى ERP_SUPPLIERS
echo ✅ تحديث الجدول تلقائياً
echo ✅ عرض إحصائيات الاستيراد

echo.
echo 🔧 في حالة وجود مشاكل:
echo =========================

echo.
echo ❌ خطأ في الاتصال بـ IAS20251:
echo • تحقق من تشغيل Oracle Database
echo • تأكد من وجود المستخدم ias20251
echo • تحقق من كلمة المرور: ias20251_password

echo.
echo ❌ خطأ في قراءة V_DETAILS:
echo • تأكد من وجود جدول V_DETAILS
echo • تحقق من صلاحيات المستخدم
echo • تأكد من بنية الجدول

echo.
echo ❌ خطأ في الكتابة لـ ERP_SUPPLIERS:
echo • تحقق من مساحة قاعدة البيانات
echo • تأكد من عدم وجود تضارب في المفاتيح
echo • راجع رسائل الخطأ في النافذة

echo.
echo 📊 معلومات قواعد البيانات:
echo ============================

echo.
echo 🗄️ قاعدة المصدر (IAS20251):
echo • المستخدم: ias20251
echo • كلمة المرور: ias20251_password
echo • الجدول: V_DETAILS
echo • الغرض: مصدر البيانات

echo.
echo 🗄️ قاعدة الهدف (SHIP_ERP):
echo • المستخدم: ship_erp
echo • كلمة المرور: ship_erp_password
echo • الجدول: ERP_SUPPLIERS
echo • الغرض: وجهة البيانات

echo.
echo 🔄 عملية الاستيراد:
echo ===================
echo • قراءة جميع الأعمدة من V_DETAILS
echo • تطبيق نفس البنية على ERP_SUPPLIERS
echo • نسخ البيانات سجل بسجل
echo • معالجة الأخطاء والاستثناءات
echo • تقرير مفصل عن النتائج

echo.
echo 📈 الإحصائيات المتوقعة:
echo ========================
echo • إجمالي السجلات المقروءة
echo • عدد السجلات المنسوخة بنجاح
echo • عدد السجلات التي فشلت
echo • وقت بداية ونهاية العملية
echo • تفاصيل أي أخطاء حدثت

echo.
echo 🎨 ميزات الاستيراد:
echo ==================
echo ✅ واجهة مستخدم تفاعلية
echo ✅ نافذة تقدم مفصلة
echo ✅ معالجة الأخطاء الذكية
echo ✅ تحديث تلقائي للجدول
echo ✅ إحصائيات شاملة
echo ✅ دعم كامل للغة العربية

echo.
echo 🚀 الخطوات التالية:
echo ===================
echo 1. اختبر عملية الاستيراد
echo 2. تحقق من صحة البيانات
echo 3. قارن البيانات بين الجدولين
echo 4. اختبر وظائف النافذة الأخرى
echo 5. تأكد من عمل جميع العمليات

echo.
echo 💡 نصائح مهمة:
echo ===============
echo • تأكد من عمل نسخة احتياطية قبل الاستيراد
echo • راقب استهلاك الذاكرة أثناء الاستيراد
echo • لا تغلق النافذة أثناء عملية الاستيراد
echo • تحقق من سجلات قاعدة البيانات
echo • اختبر على بيانات صغيرة أولاً

echo.
echo ========================================
echo ✅ جاهز لاختبار الاستيراد!
echo Ready for Import Testing!
echo ========================================

echo.
echo 🎯 للبدء:
echo =========
echo 1. افتح نافذة إدارة الموردين (تم تشغيلها)
echo 2. انقر على زر "استيراد من IAS20251"
echo 3. اتبع التعليمات في النافذة
echo 4. راقب التقدم والنتائج

echo.
pause
