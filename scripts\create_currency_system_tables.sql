-- =====================================================
-- إنشاء جداول نظام إدارة العملات الشامل
-- Create Comprehensive Currency Management System Tables
-- =====================================================

PROMPT '💰 إنشاء جداول نظام إدارة العملات الشامل'
PROMPT 'Creating Comprehensive Currency Management System Tables'
PROMPT '========================================================='

-- تعيين التنسيق للعرض
SET PAGESIZE 50
SET LINESIZE 120
SET SERVEROUTPUT ON

-- =====================================================
-- 1. جدول العملات الرئيسي
-- Main Currencies Table
-- =====================================================

PROMPT
PROMPT '[1] إنشاء جدول العملات الرئيسي...'
PROMPT '=================================='

DROP TABLE ERP_CURRENCIES CASCADE CONSTRAINTS;
DROP SEQUENCE ERP_CURRENCIES_SEQ;

CREATE TABLE ERP_CURRENCIES (
    CURRENCY_ID NUMBER(10) PRIMARY KEY,
    CURRENCY_CODE VARCHAR2(10) NOT NULL UNIQUE,
    CURRENCY_NAME_AR VARCHAR2(100) NOT NULL,
    CURRENCY_NAME_EN VARCHAR2(100) NOT NULL,
    CURRENCY_SYMBOL VARCHAR2(10),
    SYMBOL_POSITION VARCHAR2(10) DEFAULT 'BEFORE', -- BEFORE, AFTER
    DECIMAL_PLACES NUMBER(2) DEFAULT 2,
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    IS_DEFAULT CHAR(1) DEFAULT 'N' CHECK (IS_DEFAULT IN ('Y', 'N')),
    COUNTRY_CODE VARCHAR2(5),
    DISPLAY_FORMAT VARCHAR2(50) DEFAULT '#,##0.00',
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    LAST_UPDATED DATE DEFAULT SYSDATE,
    UPDATED_BY VARCHAR2(50) DEFAULT USER,
    VERSION_NUMBER NUMBER(5) DEFAULT 1
);

CREATE SEQUENCE ERP_CURRENCIES_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول العملات الرئيسي'

-- =====================================================
-- 2. جدول أسعار الصرف
-- Exchange Rates Table
-- =====================================================

PROMPT
PROMPT '[2] إنشاء جدول أسعار الصرف...'
PROMPT '============================='

CREATE TABLE ERP_EXCHANGE_RATES (
    RATE_ID NUMBER(10) PRIMARY KEY,
    FROM_CURRENCY_ID NUMBER(10) NOT NULL,
    TO_CURRENCY_ID NUMBER(10) NOT NULL,
    EXCHANGE_RATE NUMBER(15,6) NOT NULL,
    RATE_DATE DATE DEFAULT SYSDATE,
    RATE_SOURCE VARCHAR2(50), -- MANUAL, API, BANK, etc.
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    CONSTRAINT FK_EXCHANGE_FROM_CURRENCY FOREIGN KEY (FROM_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID),
    CONSTRAINT FK_EXCHANGE_TO_CURRENCY FOREIGN KEY (TO_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID),
    CONSTRAINT UK_EXCHANGE_RATE UNIQUE (FROM_CURRENCY_ID, TO_CURRENCY_ID, RATE_DATE)
);

CREATE SEQUENCE ERP_EXCHANGE_RATES_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول أسعار الصرف'

-- =====================================================
-- 3. جدول تاريخ أسعار الصرف
-- Exchange Rate History Table
-- =====================================================

PROMPT
PROMPT '[3] إنشاء جدول تاريخ أسعار الصرف...'
PROMPT '================================='

CREATE TABLE ERP_EXCHANGE_RATE_HISTORY (
    HISTORY_ID NUMBER(10) PRIMARY KEY,
    FROM_CURRENCY_ID NUMBER(10) NOT NULL,
    TO_CURRENCY_ID NUMBER(10) NOT NULL,
    OLD_RATE NUMBER(15,6),
    NEW_RATE NUMBER(15,6) NOT NULL,
    CHANGE_DATE DATE DEFAULT SYSDATE,
    CHANGE_REASON VARCHAR2(200),
    RATE_SOURCE VARCHAR2(50),
    CHANGED_BY VARCHAR2(50) DEFAULT USER,
    CONSTRAINT FK_HISTORY_FROM_CURRENCY FOREIGN KEY (FROM_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID),
    CONSTRAINT FK_HISTORY_TO_CURRENCY FOREIGN KEY (TO_CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID)
);

CREATE SEQUENCE ERP_EXCHANGE_RATE_HISTORY_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول تاريخ أسعار الصرف'

-- =====================================================
-- 4. جدول إعدادات النظام للعملات
-- Currency System Settings Table
-- =====================================================

PROMPT
PROMPT '[4] إنشاء جدول إعدادات النظام...'
PROMPT '==============================='

CREATE TABLE ERP_CURRENCY_SETTINGS (
    SETTING_ID NUMBER(10) PRIMARY KEY,
    SETTING_KEY VARCHAR2(100) NOT NULL UNIQUE,
    SETTING_VALUE VARCHAR2(500),
    SETTING_DESCRIPTION VARCHAR2(200),
    SETTING_TYPE VARCHAR2(20) DEFAULT 'STRING', -- STRING, NUMBER, BOOLEAN, DATE
    IS_SYSTEM CHAR(1) DEFAULT 'N' CHECK (IS_SYSTEM IN ('Y', 'N')),
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50) DEFAULT USER,
    LAST_UPDATED DATE DEFAULT SYSDATE,
    UPDATED_BY VARCHAR2(50) DEFAULT USER
);

CREATE SEQUENCE ERP_CURRENCY_SETTINGS_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول إعدادات النظام'

-- =====================================================
-- 5. جدول مصادر أسعار الصرف الخارجية
-- External Exchange Rate Sources Table
-- =====================================================

PROMPT
PROMPT '[5] إنشاء جدول مصادر أسعار الصرف الخارجية...'
PROMPT '==========================================='

CREATE TABLE ERP_EXCHANGE_RATE_SOURCES (
    SOURCE_ID NUMBER(10) PRIMARY KEY,
    SOURCE_NAME VARCHAR2(100) NOT NULL,
    SOURCE_URL VARCHAR2(500),
    API_KEY VARCHAR2(200),
    UPDATE_FREQUENCY NUMBER(5) DEFAULT 60, -- minutes
    LAST_UPDATE DATE,
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    PRIORITY_ORDER NUMBER(3) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50) DEFAULT USER
);

CREATE SEQUENCE ERP_EXCHANGE_RATE_SOURCES_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول مصادر أسعار الصرف الخارجية'

-- =====================================================
-- 6. جدول سجل التغييرات (Audit Log)
-- Currency Audit Log Table
-- =====================================================

PROMPT
PROMPT '[6] إنشاء جدول سجل التغييرات...'
PROMPT '=============================='

CREATE TABLE ERP_CURRENCY_AUDIT_LOG (
    LOG_ID NUMBER(10) PRIMARY KEY,
    TABLE_NAME VARCHAR2(50) NOT NULL,
    RECORD_ID NUMBER(10) NOT NULL,
    OPERATION VARCHAR2(10) NOT NULL, -- INSERT, UPDATE, DELETE
    OLD_VALUES CLOB,
    NEW_VALUES CLOB,
    CHANGE_DATE DATE DEFAULT SYSDATE,
    CHANGED_BY VARCHAR2(50) DEFAULT USER,
    IP_ADDRESS VARCHAR2(50),
    SESSION_ID VARCHAR2(100)
);

CREATE SEQUENCE ERP_CURRENCY_AUDIT_LOG_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول سجل التغييرات'

-- =====================================================
-- 7. جدول صلاحيات العملات
-- Currency Permissions Table
-- =====================================================

PROMPT
PROMPT '[7] إنشاء جدول صلاحيات العملات...'
PROMPT '================================'

CREATE TABLE ERP_CURRENCY_PERMISSIONS (
    PERMISSION_ID NUMBER(10) PRIMARY KEY,
    USER_ID VARCHAR2(50) NOT NULL,
    PERMISSION_TYPE VARCHAR2(50) NOT NULL, -- VIEW, ADD, EDIT, DELETE, MANAGE_RATES
    CURRENCY_ID NUMBER(10), -- NULL means all currencies
    GRANTED_DATE DATE DEFAULT SYSDATE,
    GRANTED_BY VARCHAR2(50) DEFAULT USER,
    EXPIRY_DATE DATE,
    IS_ACTIVE CHAR(1) DEFAULT 'Y' CHECK (IS_ACTIVE IN ('Y', 'N')),
    CONSTRAINT FK_PERMISSION_CURRENCY FOREIGN KEY (CURRENCY_ID) REFERENCES ERP_CURRENCIES(CURRENCY_ID)
);

CREATE SEQUENCE ERP_CURRENCY_PERMISSIONS_SEQ START WITH 1 INCREMENT BY 1;

PROMPT '✅ تم إنشاء جدول صلاحيات العملات'

-- =====================================================
-- 8. إنشاء الفهارس للأداء
-- Create Performance Indexes
-- =====================================================

PROMPT
PROMPT '[8] إنشاء الفهارس للأداء...'
PROMPT '========================='

-- فهارس جدول أسعار الصرف
CREATE INDEX IDX_EXCHANGE_RATES_DATE ON ERP_EXCHANGE_RATES(RATE_DATE);
CREATE INDEX IDX_EXCHANGE_RATES_ACTIVE ON ERP_EXCHANGE_RATES(IS_ACTIVE);

-- فهارس جدول تاريخ أسعار الصرف
CREATE INDEX IDX_EXCHANGE_HISTORY_DATE ON ERP_EXCHANGE_RATE_HISTORY(CHANGE_DATE);

-- فهارس جدول سجل التغييرات
CREATE INDEX IDX_AUDIT_LOG_DATE ON ERP_CURRENCY_AUDIT_LOG(CHANGE_DATE);
CREATE INDEX IDX_AUDIT_LOG_TABLE ON ERP_CURRENCY_AUDIT_LOG(TABLE_NAME);

-- فهارس جدول الصلاحيات
CREATE INDEX IDX_PERMISSIONS_USER ON ERP_CURRENCY_PERMISSIONS(USER_ID);
CREATE INDEX IDX_PERMISSIONS_TYPE ON ERP_CURRENCY_PERMISSIONS(PERMISSION_TYPE);

PROMPT '✅ تم إنشاء الفهارس للأداء'

-- =====================================================
-- 9. إدراج البيانات الافتراضية
-- Insert Default Data
-- =====================================================

PROMPT
PROMPT '[9] إدراج البيانات الافتراضية...'
PROMPT '==============================='

-- العملات الافتراضية
INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, IS_DEFAULT, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'USD', 'الدولار الأمريكي', 'US Dollar', '$', 'BEFORE', 2, 'Y', 'US');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'EUR', 'اليورو', 'Euro', '€', 'BEFORE', 2, 'EU');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'SAR', 'الريال السعودي', 'Saudi Riyal', 'ر.س', 'AFTER', 2, 'SA');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'AED', 'الدرهم الإماراتي', 'UAE Dirham', 'د.إ', 'AFTER', 2, 'AE');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'EGP', 'الجنيه المصري', 'Egyptian Pound', 'ج.م', 'AFTER', 2, 'EG');

INSERT INTO ERP_CURRENCIES (CURRENCY_ID, CURRENCY_CODE, CURRENCY_NAME_AR, CURRENCY_NAME_EN, CURRENCY_SYMBOL, SYMBOL_POSITION, DECIMAL_PLACES, COUNTRY_CODE)
VALUES (ERP_CURRENCIES_SEQ.NEXTVAL, 'JOD', 'الدينار الأردني', 'Jordanian Dinar', 'د.أ', 'AFTER', 3, 'JO');

-- أسعار الصرف الافتراضية (مقابل الدولار)
INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
SELECT ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 1, 3, 3.75, 'SYSTEM' FROM DUAL; -- USD to SAR

INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
SELECT ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 1, 4, 3.67, 'SYSTEM' FROM DUAL; -- USD to AED

INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
SELECT ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 1, 5, 30.9, 'SYSTEM' FROM DUAL; -- USD to EGP

INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
SELECT ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 1, 6, 0.71, 'SYSTEM' FROM DUAL; -- USD to JOD

INSERT INTO ERP_EXCHANGE_RATES (RATE_ID, FROM_CURRENCY_ID, TO_CURRENCY_ID, EXCHANGE_RATE, RATE_SOURCE)
SELECT ERP_EXCHANGE_RATES_SEQ.NEXTVAL, 2, 1, 1.18, 'SYSTEM' FROM DUAL; -- EUR to USD

-- الإعدادات الافتراضية
INSERT INTO ERP_CURRENCY_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESCRIPTION, SETTING_TYPE, IS_SYSTEM)
VALUES (ERP_CURRENCY_SETTINGS_SEQ.NEXTVAL, 'DEFAULT_CURRENCY', 'USD', 'العملة الافتراضية للنظام', 'STRING', 'Y');

INSERT INTO ERP_CURRENCY_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESCRIPTION, SETTING_TYPE, IS_SYSTEM)
VALUES (ERP_CURRENCY_SETTINGS_SEQ.NEXTVAL, 'AUTO_UPDATE_RATES', 'Y', 'تحديث أسعار الصرف تلقائياً', 'BOOLEAN', 'Y');

INSERT INTO ERP_CURRENCY_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESCRIPTION, SETTING_TYPE, IS_SYSTEM)
VALUES (ERP_CURRENCY_SETTINGS_SEQ.NEXTVAL, 'UPDATE_FREQUENCY', '60', 'تكرار التحديث بالدقائق', 'NUMBER', 'Y');

INSERT INTO ERP_CURRENCY_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESCRIPTION, SETTING_TYPE, IS_SYSTEM)
VALUES (ERP_CURRENCY_SETTINGS_SEQ.NEXTVAL, 'DECIMAL_PLACES', '2', 'عدد الخانات العشرية الافتراضي', 'NUMBER', 'Y');

-- مصادر أسعار الصرف الافتراضية
INSERT INTO ERP_EXCHANGE_RATE_SOURCES (SOURCE_ID, SOURCE_NAME, SOURCE_URL, UPDATE_FREQUENCY, PRIORITY_ORDER)
VALUES (ERP_EXCHANGE_RATE_SOURCES_SEQ.NEXTVAL, 'البنك المركزي السعودي', 'https://www.sama.gov.sa/ar-sa/EconomicReports/Pages/ExchangeRate.aspx', 1440, 1);

INSERT INTO ERP_EXCHANGE_RATE_SOURCES (SOURCE_ID, SOURCE_NAME, SOURCE_URL, UPDATE_FREQUENCY, PRIORITY_ORDER)
VALUES (ERP_EXCHANGE_RATE_SOURCES_SEQ.NEXTVAL, 'European Central Bank', 'https://www.ecb.europa.eu/stats/policy_and_exchange_rates/', 1440, 2);

COMMIT;

PROMPT '✅ تم إدراج البيانات الافتراضية'

-- =====================================================
-- 10. عرض ملخص الجداول المنشأة
-- Display Summary of Created Tables
-- =====================================================

PROMPT
PROMPT '[10] ملخص الجداول المنشأة...'
PROMPT '=========================='

SELECT 
    'ERP_CURRENCIES' AS TABLE_NAME,
    COUNT(*) AS RECORD_COUNT,
    'جدول العملات الرئيسي' AS DESCRIPTION
FROM ERP_CURRENCIES
UNION ALL
SELECT 
    'ERP_EXCHANGE_RATES',
    COUNT(*),
    'جدول أسعار الصرف'
FROM ERP_EXCHANGE_RATES
UNION ALL
SELECT 
    'ERP_EXCHANGE_RATE_HISTORY',
    COUNT(*),
    'جدول تاريخ أسعار الصرف'
FROM ERP_EXCHANGE_RATE_HISTORY
UNION ALL
SELECT 
    'ERP_CURRENCY_SETTINGS',
    COUNT(*),
    'جدول إعدادات النظام'
FROM ERP_CURRENCY_SETTINGS
UNION ALL
SELECT 
    'ERP_EXCHANGE_RATE_SOURCES',
    COUNT(*),
    'جدول مصادر أسعار الصرف'
FROM ERP_EXCHANGE_RATE_SOURCES
UNION ALL
SELECT 
    'ERP_CURRENCY_AUDIT_LOG',
    COUNT(*),
    'جدول سجل التغييرات'
FROM ERP_CURRENCY_AUDIT_LOG
UNION ALL
SELECT 
    'ERP_CURRENCY_PERMISSIONS',
    COUNT(*),
    'جدول صلاحيات العملات'
FROM ERP_CURRENCY_PERMISSIONS;

PROMPT
PROMPT '🎉 تم إنشاء نظام إدارة العملات الشامل بنجاح!'
PROMPT '=============================================='

-- إعادة تعيين الإعدادات
SET PAGESIZE 14
SET LINESIZE 80
SET SERVEROUTPUT OFF
