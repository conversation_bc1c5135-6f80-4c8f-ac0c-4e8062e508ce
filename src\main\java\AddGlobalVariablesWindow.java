import java.sql.*;
import java.util.Properties;

/**
 * إضافة نافذة المتغيرات العامة إلى شجرة النظام
 * Add Global Variables Window to System Tree
 */
public class AddGlobalVariablesWindow {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔄 إضافة نافذة المتغيرات العامة إلى شجرة النظام...");
            
            Connection connection = getConnection();
            
            // إضافة النافذة إلى شجرة النظام
            addWindowToSystemTree(connection);
            
            connection.close();
            
            System.out.println("✅ تم إضافة نافذة المتغيرات العامة بنجاح!");
            
            // اختبار النتيجة
            testResult();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إضافة نافذة المتغيرات العامة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    private static void addWindowToSystemTree(Connection conn) throws SQLException {
        System.out.println("📋 إضافة نافذة المتغيرات العامة إلى فئة الإعدادات العامة...");
        
        // الحصول على معرف فئة الإعدادات العامة
        int settingsId = getCategoryId(conn, "الإعدادات العامة");
        
        if (settingsId == 0) {
            System.err.println("❌ لم يتم العثور على فئة الإعدادات العامة");
            return;
        }
        
        System.out.println("  معرف فئة الإعدادات العامة: " + settingsId);
        
        // التحقق من عدم وجود النافذة مسبقاً
        if (windowExists(conn, "المتغيرات العامة")) {
            System.out.println("  ⚠️ نافذة المتغيرات العامة موجودة مسبقاً");
            return;
        }
        
        // الحصول على الترتيب التالي
        int nextOrder = getNextDisplayOrder(conn, settingsId);
        
        // إدراج النافذة الجديدة
        String sql = """
            INSERT INTO ERP_SYSTEM_TREE (
                PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
                NODE_TYPE, WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL,
                IS_ACTIVE, IS_VISIBLE
            ) VALUES (?, ?, ?, ?, 'WINDOW', ?, ?, 2, 'Y', 'Y')
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, settingsId);
            stmt.setString(2, "المتغيرات العامة");
            stmt.setString(3, "Global Variables");
            stmt.setString(4, "إدارة المتغيرات العامة للتطبيق وإعداداته الأساسية");
            stmt.setString(5, "GlobalVariablesWindow");
            stmt.setInt(6, nextOrder);
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("  ✅ تم إدراج نافذة المتغيرات العامة بنجاح");
            } else {
                System.err.println("  ❌ فشل في إدراج نافذة المتغيرات العامة");
            }
        }
    }
    
    private static int getCategoryId(Connection conn, String categoryName) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }
    
    private static boolean windowExists(Connection conn, String windowName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, windowName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        return false;
    }
    
    private static int getNextDisplayOrder(Connection conn, int parentId) throws SQLException {
        String sql = "SELECT MAX(DISPLAY_ORDER) FROM ERP_SYSTEM_TREE WHERE PARENT_ID = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) + 1;
                }
            }
        }
        return 1;
    }
    
    private static void testResult() {
        System.out.println("\n🔍 اختبار النتيجة...");
        
        try {
            SystemTreeManager manager = SystemTreeManager.getInstance();
            manager.printSystemTree();
            manager.close();
        } catch (Exception e) {
            System.err.println("❌ خطأ في اختبار النتيجة: " + e.getMessage());
        }
    }
}
