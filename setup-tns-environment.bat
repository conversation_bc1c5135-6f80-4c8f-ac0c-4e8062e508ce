@echo off
setlocal enabledelayedexpansion
echo ========================================
echo    TNS ENVIRONMENT SETUP
echo    اعداد بيئة TNS
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Setting up TNS environment for Ship ERP System...
echo [معلومات] اعداد بيئة TNS لنظام ادارة الشحنات...
echo.

echo ========================================
echo    PHASE 1: VERIFY TNS FILES
echo    المرحلة 1: التحقق من ملفات TNS
echo ========================================

echo [1] Checking TNS configuration files...

if exist "network\admin\tnsnames.ora" (
    echo OK: tnsnames.ora found
) else (
    echo WARNING: tnsnames.ora missing
    echo Creating directory structure...
    if not exist "network" mkdir network
    if not exist "network\admin" mkdir network\admin
    echo # TNS Names file created > network\admin\tnsnames.ora
    echo INFO: Basic tnsnames.ora created
)

if exist "network\admin\sqlnet.ora" (
    echo OK: sqlnet.ora found
) else (
    echo WARNING: sqlnet.ora missing
    echo Creating basic sqlnet.ora...
    echo NAMES.DIRECTORY_PATH = ^(TNSNAMES, EZCONNECT, HOSTNAME^) > network\admin\sqlnet.ora
    echo INFO: Basic sqlnet.ora created
)

if exist "network\admin\listener.ora" (
    echo OK: listener.ora found
) else (
    echo INFO: listener.ora missing ^(optional for client^)
)

echo.
echo ========================================
echo    PHASE 2: SET ENVIRONMENT VARIABLES
echo    المرحلة 2: تعيين متغيرات البيئة
echo ========================================

echo [2] Setting TNS environment variables...

set TNS_ADMIN=%CD%\network\admin
set ORACLE_NET_TNS_ADMIN=%TNS_ADMIN%

echo TNS_ADMIN set to: %TNS_ADMIN%
echo ORACLE_NET_TNS_ADMIN set to: %ORACLE_NET_TNS_ADMIN%

REM تعيين متغيرات Java للـ TNS
set JAVA_TNS_OPTS=-Doracle.net.tns_admin="%TNS_ADMIN%" -Doracle.jdbc.defaultNChar=true -Dfile.encoding=UTF-8

echo Java TNS Options: %JAVA_TNS_OPTS%

echo.
echo ========================================
echo    PHASE 3: CREATE TRACE DIRECTORIES
echo    المرحلة 3: انشاء مجلدات التتبع
echo ========================================

echo [3] Creating trace and log directories...

if not exist "network\admin\trace" (
    mkdir network\admin\trace
    echo OK: Trace directory created
) else (
    echo OK: Trace directory exists
)

if not exist "network\admin\log" (
    mkdir network\admin\log
    echo OK: Log directory created
) else (
    echo OK: Log directory exists
)

echo.
echo ========================================
echo    PHASE 4: TEST TNS CONFIGURATION
echo    المرحلة 4: اختبار تكوين TNS
echo ========================================

echo [4] Testing TNS configuration...

echo Testing TNS name resolution...

REM اختبار tnsping اذا كان متاحا
where tnsping >nul 2>&1
if !errorlevel! equ 0 (
    echo Testing ORCL connection...
    tnsping ORCL

    echo Testing SHIP_ERP connection...
    tnsping SHIP_ERP

    echo Testing IAS20251 connection...
    tnsping IAS20251
) else (
    echo WARNING: tnsping not available ^(Oracle client not in PATH^)
    echo Will test connections through Java application
)

echo.
echo ========================================
echo    PHASE 5: JAVA TNS TEST
echo    المرحلة 5: اختبار TNS عبر Java
echo ========================================

echo [5] Testing TNS through Java application...

if exist "TNSConnectionManager.class" (
    echo Running TNS Connection Manager test...
    java %JAVA_TNS_OPTS% -cp . TNSConnectionManager
) else (
    echo WARNING: TNSConnectionManager not compiled yet
    echo Compile first using: javac -d . src\main\java\TNSConnectionManager.java
)

echo.
echo ========================================
echo    PHASE 6: CREATE ENVIRONMENT SCRIPT
echo    المرحلة 6: انشاء سكريپت البيئة
echo ========================================

echo [6] Creating environment setup script...

echo @echo off > set-tns-env.bat
echo REM TNS Environment Setup for Ship ERP >> set-tns-env.bat
echo set TNS_ADMIN=%CD%\network\admin >> set-tns-env.bat
echo set ORACLE_NET_TNS_ADMIN=%%TNS_ADMIN%% >> set-tns-env.bat
echo set JAVA_TNS_OPTS=-Doracle.net.tns_admin="%%TNS_ADMIN%%" -Doracle.jdbc.defaultNChar=true -Dfile.encoding=UTF-8 >> set-tns-env.bat
echo echo TNS Environment configured >> set-tns-env.bat
echo echo TNS_ADMIN: %%TNS_ADMIN%% >> set-tns-env.bat

echo OK: Environment script created: set-tns-env.bat

echo.
echo ========================================
echo    CONFIGURATION SUMMARY
echo    ملخص التكوين
echo ========================================

echo.
echo TNS Configuration Summary:
echo ملخص تكوين TNS:
echo.
echo Files and Directories:
echo الملفات والمجلدات:
echo   - TNS_ADMIN: %TNS_ADMIN%
echo   - tnsnames.ora: %TNS_ADMIN%\tnsnames.ora
echo   - sqlnet.ora: %TNS_ADMIN%\sqlnet.ora
echo   - listener.ora: %TNS_ADMIN%\listener.ora
echo   - Trace directory: %TNS_ADMIN%\trace
echo   - Log directory: %TNS_ADMIN%\log
echo.
echo Available TNS Names:
echo اسماء TNS المتاحة:
echo   - ORCL ^(Main Oracle instance^)
echo   - SHIP_ERP ^(Ship ERP database^)
echo   - IAS20251 ^(IAS reference database^)
echo   - SHIP_ERP_DEV ^(Development database^)
echo   - SHIP_ERP_TEST ^(Test database^)
echo   - SHIP_ERP_HA ^(High availability^)
echo   - SHIP_ERP_SSL ^(SSL connection^)
echo.
echo Environment Variables:
echo متغيرات البيئة:
echo   - TNS_ADMIN=%TNS_ADMIN%
echo   - ORACLE_NET_TNS_ADMIN=%ORACLE_NET_TNS_ADMIN%
echo   - JAVA_TNS_OPTS=%JAVA_TNS_OPTS%
echo.
echo Usage Examples:
echo امثلة الاستخدام:
echo   - Java: **************************
echo   - Java: **************************
echo   - Java: *****************************
echo   - SQL*Plus: sqlplus SHIP_ERP/password@SHIP_ERP
echo.

echo ========================================
echo    TNS SETUP COMPLETED
echo    اكتمل اعداد TNS
echo ========================================

echo.
echo [SUCCESS] TNS environment setup completed!
echo [نجح] اكتمل اعداد بيئة TNS!
echo.
echo Next steps:
echo الخطوات التالية:
echo 1. Compile TNSConnectionManager: javac -d . src\main\java\TNSConnectionManager.java
echo 2. Test TNS connections: java %JAVA_TNS_OPTS% -cp . TNSConnectionManager
echo 3. Use TNS names in your applications
echo 4. Run set-tns-env.bat before starting applications
echo.
echo The TNS configuration is now ready for use.
echo تكوين TNS جاهز الان للاستخدام.
echo.

pause
