import java.sql.*;

/**
 * إصلاح هيكل PARENT_ID في جدول ERP_SYSTEM_TREE
 * Fix PARENT_ID Structure in ERP_SYSTEM_TREE Table
 */
public class FixParentIdStructure {
    
    public static void main(String[] args) {
        System.out.println("🔧 إصلاح هيكل PARENT_ID في جدول ERP_SYSTEM_TREE...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            // الخطوة 1: إصلاح الفئات الرئيسية
            fixMainCategories(connection);
            
            // الخطوة 2: إصلاح النوافذ الفرعية
            fixSubWindows(connection);
            
            // الخطوة 3: إصلاح ترقيم المستويات
            fixTreeLevels(connection);
            
            // الخطوة 4: إصلاح ترتيب العرض
            fixDisplayOrder(connection);
            
            connection.close();
            System.out.println("🎉 تم إصلاح هيكل PARENT_ID بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إصلاح هيكل PARENT_ID: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void fixMainCategories(Connection connection) throws SQLException {
        System.out.println("📋 إصلاح الفئات الرئيسية...");
        
        // تحديد الفئات التي يجب أن تكون رئيسية
        String[] mainCategories = {
            "نظام إدارة الشحنات",
            "إدارة الأصناف", 
            "إدارة الشحنات",
            "إدارة العملاء",
            "إدارة الموردين",
            "إدارة المخازن",
            "الحسابات والمالية",
            "إدارة الموظفين",
            "إدارة المستخدمين",
            "التقارير والإحصائيات",
            "التقارير",
            "الإعدادات العامة",
            "أدوات النظام",
            "نظام إدارة البريد الإلكتروني"
        };
        
        // إصلاح كل فئة رئيسية
        for (int i = 0; i < mainCategories.length; i++) {
            String categoryName = mainCategories[i];
            
            String updateSQL = """
                UPDATE ERP_SYSTEM_TREE 
                SET PARENT_ID = NULL,
                    TREE_LEVEL = 1,
                    DISPLAY_ORDER = ?,
                    IS_ACTIVE = 'Y',
                    IS_VISIBLE = 'Y',
                    LAST_UPDATED = SYSDATE,
                    UPDATED_BY = 'SYSTEM'
                WHERE NODE_NAME_AR = ? 
                AND NODE_TYPE = 'CATEGORY'
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(updateSQL)) {
                stmt.setInt(1, i + 1);
                stmt.setString(2, categoryName);
                int updated = stmt.executeUpdate();
                if (updated > 0) {
                    System.out.println("✅ تم إصلاح الفئة الرئيسية: " + categoryName);
                }
            }
        }
    }
    
    private static void fixSubWindows(Connection connection) throws SQLException {
        System.out.println("🪟 إصلاح النوافذ الفرعية...");
        
        // الحصول على جميع الفئات الرئيسية
        String getCategoriesSQL = """
            SELECT TREE_ID, NODE_NAME_AR 
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_TYPE = 'CATEGORY' 
            AND PARENT_ID IS NULL 
            AND IS_ACTIVE = 'Y'
            ORDER BY DISPLAY_ORDER
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(getCategoriesSQL);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                int categoryId = rs.getInt("TREE_ID");
                String categoryName = rs.getString("NODE_NAME_AR");
                
                // إصلاح النوافذ التابعة لهذه الفئة
                fixWindowsForCategory(connection, categoryId, categoryName);
            }
        }
    }
    
    private static void fixWindowsForCategory(Connection connection, int categoryId, String categoryName) throws SQLException {
        System.out.println("🔧 إصلاح نوافذ فئة: " + categoryName);
        
        // البحث عن النوافذ التي تنتمي لهذه الفئة بناءً على الاسم أو الكلاس
        String findWindowsSQL = """
            SELECT TREE_ID, NODE_NAME_AR, WINDOW_CLASS
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_TYPE = 'WINDOW' 
            AND IS_ACTIVE = 'Y'
            AND (
                PARENT_ID = ? 
                OR PARENT_ID IN (
                    SELECT TREE_ID FROM ERP_SYSTEM_TREE 
                    WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'
                )
                OR (? = 'إدارة الأصناف' AND (
                    NODE_NAME_AR LIKE '%أصناف%' OR 
                    NODE_NAME_AR LIKE '%مخزون%' OR 
                    NODE_NAME_AR LIKE '%وحدات القياس%' OR
                    WINDOW_CLASS LIKE '%Item%' OR
                    WINDOW_CLASS LIKE '%Inventory%'
                ))
                OR (? = 'إدارة الشحنات' AND (
                    NODE_NAME_AR LIKE '%شحن%' OR 
                    NODE_NAME_AR LIKE '%حاوي%' OR
                    WINDOW_CLASS LIKE '%Shipment%' OR
                    WINDOW_CLASS LIKE '%Container%'
                ))
                OR (? = 'إدارة العملاء' AND (
                    NODE_NAME_AR LIKE '%عملاء%' OR 
                    NODE_NAME_AR LIKE '%عقود%' OR
                    WINDOW_CLASS LIKE '%Customer%'
                ))
                OR (? = 'إدارة الموردين' AND (
                    NODE_NAME_AR LIKE '%مورد%' OR 
                    NODE_NAME_AR LIKE '%شراء%' OR
                    WINDOW_CLASS LIKE '%Supplier%' OR
                    WINDOW_CLASS LIKE '%Purchase%'
                ))
                OR (? = 'إدارة المخازن' AND (
                    NODE_NAME_AR LIKE '%مخزن%' OR 
                    NODE_NAME_AR LIKE '%جرد%' OR
                    WINDOW_CLASS LIKE '%Warehouse%'
                ))
                OR (? = 'الحسابات والمالية' AND (
                    NODE_NAME_AR LIKE '%حساب%' OR 
                    NODE_NAME_AR LIKE '%مالي%' OR 
                    NODE_NAME_AR LIKE '%ميزانية%' OR
                    WINDOW_CLASS LIKE '%Account%' OR
                    WINDOW_CLASS LIKE '%Financial%'
                ))
                OR (? = 'إدارة الموظفين' AND (
                    NODE_NAME_AR LIKE '%موظف%' OR 
                    NODE_NAME_AR LIKE '%راتب%' OR 
                    NODE_NAME_AR LIKE '%حضور%' OR
                    WINDOW_CLASS LIKE '%Employee%' OR
                    WINDOW_CLASS LIKE '%Payroll%'
                ))
                OR (? = 'التقارير والإحصائيات' AND (
                    NODE_NAME_AR LIKE '%تقرير%' OR 
                    NODE_NAME_AR LIKE '%إحصائ%' OR 
                    NODE_NAME_AR LIKE '%لوحة%' OR
                    WINDOW_CLASS LIKE '%Report%' OR
                    WINDOW_CLASS LIKE '%Dashboard%'
                ))
                OR (? = 'الإعدادات العامة' AND (
                    NODE_NAME_AR LIKE '%إعداد%' OR 
                    NODE_NAME_AR LIKE '%متغير%' OR 
                    NODE_NAME_AR LIKE '%مظهر%' OR
                    WINDOW_CLASS LIKE '%Settings%' OR
                    WINDOW_CLASS LIKE '%Config%' OR
                    WINDOW_CLASS LIKE '%Theme%'
                ))
                OR (? = 'أدوات النظام' AND (
                    NODE_NAME_AR LIKE '%مراقب%' OR 
                    NODE_NAME_AR LIKE '%سجل%' OR 
                    NODE_NAME_AR LIKE '%نسخ%' OR 
                    NODE_NAME_AR LIKE '%صيانة%' OR
                    WINDOW_CLASS LIKE '%System%' OR
                    WINDOW_CLASS LIKE '%Monitor%' OR
                    WINDOW_CLASS LIKE '%Backup%'
                ))
                OR (? = 'نظام إدارة البريد الإلكتروني' AND (
                    NODE_NAME_AR LIKE '%بريد%' OR 
                    NODE_NAME_AR LIKE '%رسال%' OR 
                    NODE_NAME_AR LIKE '%حمل%' OR
                    WINDOW_CLASS LIKE '%Email%'
                ))
            )
            ORDER BY DISPLAY_ORDER
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(findWindowsSQL)) {
            stmt.setInt(1, categoryId);
            for (int i = 2; i <= 13; i++) {
                stmt.setString(i, categoryName);
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                int order = 1;
                while (rs.next()) {
                    int windowId = rs.getInt("TREE_ID");
                    String windowName = rs.getString("NODE_NAME_AR");
                    
                    // تحديث النافذة لتنتمي للفئة الصحيحة
                    String updateWindowSQL = """
                        UPDATE ERP_SYSTEM_TREE 
                        SET PARENT_ID = ?,
                            TREE_LEVEL = 2,
                            DISPLAY_ORDER = ?,
                            IS_ACTIVE = 'Y',
                            IS_VISIBLE = 'Y',
                            LAST_UPDATED = SYSDATE,
                            UPDATED_BY = 'SYSTEM'
                        WHERE TREE_ID = ?
                        """;
                    
                    try (PreparedStatement updateStmt = connection.prepareStatement(updateWindowSQL)) {
                        updateStmt.setInt(1, categoryId);
                        updateStmt.setInt(2, order);
                        updateStmt.setInt(3, windowId);
                        updateStmt.executeUpdate();
                        
                        System.out.println("  ✅ تم إصلاح النافذة: " + windowName);
                        order++;
                    }
                }
            }
        }
    }
    
    private static void fixTreeLevels(Connection connection) throws SQLException {
        System.out.println("📊 إصلاح مستويات الشجرة...");
        
        // إصلاح مستويات الفئات الرئيسية
        String updateCategoriesSQL = """
            UPDATE ERP_SYSTEM_TREE 
            SET TREE_LEVEL = 1 
            WHERE NODE_TYPE = 'CATEGORY' 
            AND PARENT_ID IS NULL
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(updateCategoriesSQL)) {
            stmt.executeUpdate();
        }
        
        // إصلاح مستويات النوافذ الفرعية
        String updateWindowsSQL = """
            UPDATE ERP_SYSTEM_TREE 
            SET TREE_LEVEL = 2 
            WHERE NODE_TYPE = 'WINDOW' 
            AND PARENT_ID IS NOT NULL
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(updateWindowsSQL)) {
            stmt.executeUpdate();
        }
        
        System.out.println("✅ تم إصلاح مستويات الشجرة");
    }
    
    private static void fixDisplayOrder(Connection connection) throws SQLException {
        System.out.println("🔢 إصلاح ترتيب العرض...");
        
        // إصلاح ترتيب الفئات الرئيسية
        String getCategoriesSQL = """
            SELECT TREE_ID, NODE_NAME_AR 
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_TYPE = 'CATEGORY' 
            AND PARENT_ID IS NULL 
            AND IS_ACTIVE = 'Y'
            ORDER BY NODE_NAME_AR
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(getCategoriesSQL);
             ResultSet rs = stmt.executeQuery()) {
            
            int order = 1;
            while (rs.next()) {
                int categoryId = rs.getInt("TREE_ID");
                
                String updateOrderSQL = """
                    UPDATE ERP_SYSTEM_TREE 
                    SET DISPLAY_ORDER = ? 
                    WHERE TREE_ID = ?
                    """;
                
                try (PreparedStatement updateStmt = connection.prepareStatement(updateOrderSQL)) {
                    updateStmt.setInt(1, order);
                    updateStmt.setInt(2, categoryId);
                    updateStmt.executeUpdate();
                    order++;
                }
            }
        }
        
        System.out.println("✅ تم إصلاح ترتيب العرض");
    }
}
