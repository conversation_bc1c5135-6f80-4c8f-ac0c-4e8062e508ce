import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;

import javax.swing.UIManager;

/**
 * مدير الإعدادات المحسن مع دعم قاعدة البيانات
 * Enhanced Settings Manager with Database Support
 */
public class EnhancedSettingsManager {
    
    // إعدادات قاعدة البيانات
    private static final String DB_URL = "*************************************";
    private static final String DB_USER = "ias20251";
    private static final String DB_PASSWORD = "ys123";
    
    // ملف الإعدادات الاحتياطي
    private static final String SETTINGS_FILE = "ship_erp_settings.properties";
    
    // مفاتيح الإعدادات
    public static final String CURRENT_THEME = "CURRENT_THEME";
    public static final String CURRENT_THEME_NAME = "CURRENT_THEME_NAME";
    public static final String FONT_FAMILY = "FONT_FAMILY";
    public static final String FONT_SIZE = "FONT_SIZE";
    public static final String ENABLE_ANIMATIONS = "ENABLE_ANIMATIONS";
    public static final String ENABLE_SOUNDS = "ENABLE_SOUNDS";
    public static final String ENABLE_TOOLTIPS = "ENABLE_TOOLTIPS";
    public static final String RTL_SUPPORT = "RTL_SUPPORT";
    public static final String TRANSPARENCY_LEVEL = "TRANSPARENCY_LEVEL";
    public static final String ACCENT_COLOR = "ACCENT_COLOR";
    public static final String LANGUAGE = "LANGUAGE";
    
    /**
     * الحصول على اتصال قاعدة البيانات
     */
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", DB_USER);
            props.setProperty("password", DB_PASSWORD);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            return DriverManager.getConnection(DB_URL, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    /**
     * إنشاء جدول الإعدادات إذا لم يكن موجوداً
     */
    public static void initializeSettingsTable() {
        try (Connection conn = getConnection()) {
            String createTableSQL = """
                CREATE TABLE UI_SETTINGS (
                    SETTING_ID VARCHAR2(50) PRIMARY KEY,
                    SETTING_VALUE NVARCHAR2(500),
                    SETTING_TYPE VARCHAR2(20),
                    DESCRIPTION NVARCHAR2(200),
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    MODIFIED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER
                )
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(createTableSQL)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جدول UI_SETTINGS");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() != 955) { // Table already exists
                System.err.println("خطأ في إنشاء جدول الإعدادات: " + e.getMessage());
            }
        }
    }
    
    /**
     * حفظ إعداد في قاعدة البيانات
     */
    public static void saveSetting(String settingId, String value, String type, String description) {
        try (Connection conn = getConnection()) {
            String sql = """
                MERGE INTO UI_SETTINGS 
                USING DUAL ON (SETTING_ID = ?)
                WHEN MATCHED THEN 
                    UPDATE SET SETTING_VALUE = ?, MODIFIED_DATE = SYSDATE
                WHEN NOT MATCHED THEN 
                    INSERT (SETTING_ID, SETTING_VALUE, SETTING_TYPE, DESCRIPTION) 
                    VALUES (?, ?, ?, ?)
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, settingId);
                stmt.setString(2, value);
                stmt.setString(3, settingId);
                stmt.setString(4, value);
                stmt.setString(5, type);
                stmt.setString(6, description);
                
                stmt.executeUpdate();
            }
            
            // حفظ احتياطي في الملف
            saveToFile(settingId, value);
            
        } catch (SQLException e) {
            System.err.println("خطأ في حفظ الإعداد " + settingId + ": " + e.getMessage());
            // حفظ في الملف كبديل
            saveToFile(settingId, value);
        }
    }
    
    /**
     * تحميل إعداد من قاعدة البيانات
     */
    public static String loadSetting(String settingId, String defaultValue) {
        try (Connection conn = getConnection()) {
            String sql = "SELECT SETTING_VALUE FROM UI_SETTINGS WHERE SETTING_ID = ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, settingId);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getString("SETTING_VALUE");
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في تحميل الإعداد " + settingId + ": " + e.getMessage());
            // محاولة التحميل من الملف
            return loadFromFile(settingId, defaultValue);
        }
        
        return defaultValue;
    }
    
    /**
     * حفظ جميع الإعدادات الحالية
     */
    public static void saveCurrentUISettings() {
        try {
            // حفظ الثيم الحالي
            String currentLaf = UIManager.getLookAndFeel().getClass().getName();
            saveSetting(CURRENT_THEME, currentLaf, "STRING", "الثيم الحالي");
            
            // حفظ إعدادات أخرى افتراضية
            saveSetting(FONT_FAMILY, "Tahoma", "STRING", "عائلة الخط");
            saveSetting(FONT_SIZE, "12", "INTEGER", "حجم الخط");
            saveSetting(ENABLE_ANIMATIONS, "true", "BOOLEAN", "تفعيل الحركات");
            saveSetting(ENABLE_SOUNDS, "true", "BOOLEAN", "تفعيل الأصوات");
            saveSetting(ENABLE_TOOLTIPS, "true", "BOOLEAN", "تفعيل التلميحات");
            saveSetting(RTL_SUPPORT, "true", "BOOLEAN", "دعم RTL");
            saveSetting(TRANSPARENCY_LEVEL, "100", "INTEGER", "مستوى الشفافية");
            saveSetting(ACCENT_COLOR, "#0078d4", "STRING", "لون التمييز");
            saveSetting(LANGUAGE, "العربية", "STRING", "اللغة");
            
            System.out.println("✅ تم حفظ إعدادات الواجهة الحالية");
            
        } catch (Exception e) {
            System.err.println("خطأ في حفظ إعدادات الواجهة: " + e.getMessage());
        }
    }
    
    /**
     * تطبيق الثيم المحفوظ
     */
    public static void applyStoredTheme() {
        try {
            String savedTheme = loadSetting(CURRENT_THEME, "javax.swing.plaf.system.SystemLookAndFeel");
            
            UIManager.setLookAndFeel(savedTheme);
            
            System.out.println("✅ تم تطبيق الثيم المحفوظ: " + savedTheme);
            
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق الثيم المحفوظ: " + e.getMessage());
            // تطبيق الثيم الافتراضي
            try {
                UIManager.setLookAndFeel("javax.swing.plaf.system.SystemLookAndFeel");
            } catch (Exception ex) {
                System.err.println("خطأ في تطبيق الثيم الافتراضي: " + ex.getMessage());
            }
        }
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public static Properties getAllSettings() {
        Properties settings = new Properties();
        
        try (Connection conn = getConnection()) {
            String sql = "SELECT SETTING_ID, SETTING_VALUE FROM UI_SETTINGS";
            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                while (rs.next()) {
                    settings.setProperty(rs.getString("SETTING_ID"), rs.getString("SETTING_VALUE"));
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في تحميل جميع الإعدادات: " + e.getMessage());
            // تحميل من الملف كبديل
            return loadAllFromFile();
        }
        
        return settings;
    }
    
    /**
     * حذف جميع الإعدادات
     */
    public static void clearAllSettings() {
        try (Connection conn = getConnection()) {
            String sql = "DELETE FROM UI_SETTINGS";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                int deletedRows = stmt.executeUpdate();
                System.out.println("تم حذف " + deletedRows + " إعداد من قاعدة البيانات");
            }
        } catch (SQLException e) {
            System.err.println("خطأ في حذف الإعدادات: " + e.getMessage());
        }
        
        // حذف ملف الإعدادات أيضاً
        try {
            File settingsFile = new File(SETTINGS_FILE);
            if (settingsFile.exists()) {
                settingsFile.delete();
                System.out.println("تم حذف ملف الإعدادات");
            }
        } catch (Exception e) {
            System.err.println("خطأ في حذف ملف الإعدادات: " + e.getMessage());
        }
    }
    
    /**
     * حفظ في الملف (نسخة احتياطية)
     */
    private static void saveToFile(String key, String value) {
        try {
            Properties properties = new Properties();
            
            // تحميل الإعدادات الموجودة
            File settingsFile = new File(SETTINGS_FILE);
            if (settingsFile.exists()) {
                try (FileInputStream fis = new FileInputStream(settingsFile)) {
                    properties.load(fis);
                }
            }
            
            // إضافة الإعداد الجديد
            properties.setProperty(key, value);
            
            // حفظ الملف
            try (FileOutputStream fos = new FileOutputStream(SETTINGS_FILE)) {
                properties.store(fos, "Ship ERP UI Settings - إعدادات واجهة نظام إدارة الشحنات");
            }
            
        } catch (IOException e) {
            System.err.println("خطأ في حفظ الإعداد في الملف: " + e.getMessage());
        }
    }
    
    /**
     * تحميل من الملف
     */
    private static String loadFromFile(String key, String defaultValue) {
        try {
            Properties properties = new Properties();
            File settingsFile = new File(SETTINGS_FILE);
            
            if (settingsFile.exists()) {
                try (FileInputStream fis = new FileInputStream(settingsFile)) {
                    properties.load(fis);
                    return properties.getProperty(key, defaultValue);
                }
            }
        } catch (IOException e) {
            System.err.println("خطأ في تحميل الإعداد من الملف: " + e.getMessage());
        }
        
        return defaultValue;
    }
    
    /**
     * تحميل جميع الإعدادات من الملف
     */
    private static Properties loadAllFromFile() {
        Properties properties = new Properties();
        
        try {
            File settingsFile = new File(SETTINGS_FILE);
            if (settingsFile.exists()) {
                try (FileInputStream fis = new FileInputStream(settingsFile)) {
                    properties.load(fis);
                }
            }
        } catch (IOException e) {
            System.err.println("خطأ في تحميل الإعدادات من الملف: " + e.getMessage());
        }
        
        return properties;
    }
    
    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    public static boolean testDatabaseConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            System.err.println("فشل اختبار الاتصال بقاعدة البيانات: " + e.getMessage());
            return false;
        }
    }
}
