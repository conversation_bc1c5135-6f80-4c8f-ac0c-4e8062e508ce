@echo off
echo Downloading Working Theme Libraries
echo ===================================

cd /d "e:\ship_erp\java\lib"

echo Cleaning up small/corrupted files...
for %%f in (*.jar) do (
    for /f %%s in ('dir /b "%%f" 2^>nul ^| find /c "%%f"') do (
        if exist "%%f" (
            for %%a in ("%%f") do (
                if %%~za LSS 10000 (
                    echo Removing small file: %%f ^(%%~za bytes^)
                    del "%%f"
                )
            )
        )
    )
)

echo.
echo Downloading essential working libraries...

echo [1] FlatLaf Core - Working version...
curl -L -o flatlaf-3.2.5.jar "https://github.com/JFormDesigner/FlatLaf/releases/download/3.2.5/flatlaf-3.2.5.jar"

echo [2] FlatLaf Extras - Working version...
curl -L -o flatlaf-extras-3.2.5.jar "https://github.com/JFormDesigner/FlatLaf/releases/download/3.2.5/flatlaf-extras-3.2.5.jar"

echo [3] FlatLaf IntelliJ Themes - Working version...
curl -L -o flatlaf-intellij-themes-3.2.5.jar "https://github.com/JFormDesigner/FlatLaf/releases/download/3.2.5/flatlaf-intellij-themes-3.2.5.jar"

echo [4] JTattoo - Direct download...
curl -L -o jtattoo-1.6.13.jar "http://www.jtattoo.net/JTattoo.jar"

echo [5] Substance - Alternative source...
curl -L -o substance-8.0.02.jar "https://central.sonatype.com/artifact/org.pushingpixels/substance/8.0.02/jar"

echo [6] JSON Processing...
curl -L -o json-20230227.jar "https://central.sonatype.com/artifact/org.json/json/20230227/jar"

echo [7] Apache Commons IO...
curl -L -o commons-io-2.11.0.jar "https://central.sonatype.com/artifact/commons-io/commons-io/2.11.0/jar"

echo [8] Apache Commons Lang...
curl -L -o commons-lang3-3.12.0.jar "https://central.sonatype.com/artifact/org.apache.commons/commons-lang3/3.12.0/jar"

echo.
echo Verifying file sizes...
for %%f in (*.jar) do (
    for %%a in ("%%f") do (
        if %%~za GTR 10000 (
            echo   OK: %%f ^(%%~za bytes^)
        ) else (
            echo   SMALL: %%f ^(%%~za bytes^) - May be corrupted
        )
    )
)

echo.
echo Download completed!
pause
