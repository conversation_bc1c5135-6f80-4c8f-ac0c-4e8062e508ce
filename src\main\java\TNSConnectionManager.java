import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * مدير اتصالات TNS - TNS Connection Manager يدير الاتصالات باستخدام ملف tnsnames.ora
 */
public class TNSConnectionManager {

    private static TNSConnectionManager instance;
    private Map<String, DatabaseConnection> connections;
    private String tnsAdminPath;

    // معلومات الاتصالات المتاحة
    public static final String SHIP_ERP_TNS = "SHIP_ERP";
    public static final String IAS20251_TNS = "IAS20251";
    public static final String ORCL_TNS = "ORCL";
    public static final String SHIP_ERP_DEV_TNS = "SHIP_ERP_DEV";
    public static final String SHIP_ERP_TEST_TNS = "SHIP_ERP_TEST";
    public static final String SHIP_ERP_HA_TNS = "SHIP_ERP_HA";

    private TNSConnectionManager() {
        initializeTNSManager();
    }

    public static synchronized TNSConnectionManager getInstance() {
        if (instance == null) {
            instance = new TNSConnectionManager();
        }
        return instance;
    }

    /**
     * تهيئة مدير TNS
     */
    private void initializeTNSManager() {
        try {
            // تحديد مسار TNS_ADMIN
            tnsAdminPath = System.getProperty("user.dir") + File.separator + "network"
                    + File.separator + "admin";

            // تعيين متغير البيئة TNS_ADMIN
            System.setProperty("oracle.net.tns_admin", tnsAdminPath);

            // تهيئة خريطة الاتصالات
            connections = new HashMap<>();

            // إضافة اتصالات النظام
            addSystemConnections();

            // التحقق من وجود ملف tnsnames.ora
            verifyTNSConfiguration();

            System.out.println("✅ تم تهيئة مدير اتصالات TNS بنجاح");
            System.out.println("   TNS_ADMIN Path: " + tnsAdminPath);

        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة مدير اتصالات TNS: " + e.getMessage());
        }
    }

    /**
     * إضافة اتصالات النظام
     */
    private void addSystemConnections() {
        // اتصال SHIP_ERP الرئيسي
        connections.put(SHIP_ERP_TNS, new DatabaseConnection(SHIP_ERP_TNS, "SHIP_ERP",
                "ship_erp_password", "قاعدة بيانات نظام إدارة الشحنات الرئيسية"));

        // اتصال IAS20251
        connections.put(IAS20251_TNS, new DatabaseConnection(IAS20251_TNS, "ias20251", "ys123",
                "قاعدة بيانات IAS20251 المرجعية"));

        // اتصال ORCL العام
        connections.put(ORCL_TNS,
                new DatabaseConnection(ORCL_TNS, "system", "oracle", "قاعدة البيانات العامة ORCL"));

        // اتصال التطوير
        connections.put(SHIP_ERP_DEV_TNS, new DatabaseConnection(SHIP_ERP_DEV_TNS, "SHIP_ERP_DEV",
                "dev_password", "قاعدة بيانات التطوير"));

        // اتصال الاختبار
        connections.put(SHIP_ERP_TEST_TNS, new DatabaseConnection(SHIP_ERP_TEST_TNS,
                "SHIP_ERP_TEST", "test_password", "قاعدة بيانات الاختبار"));

        // اتصال عالي التوفر
        connections.put(SHIP_ERP_HA_TNS, new DatabaseConnection(SHIP_ERP_HA_TNS, "SHIP_ERP",
                "ship_erp_password", "اتصال عالي التوفر مع التبديل التلقائي"));
    }

    /**
     * التحقق من تكوين TNS
     */
    private void verifyTNSConfiguration() {
        File tnsFile = new File(tnsAdminPath, "tnsnames.ora");
        File sqlnetFile = new File(tnsAdminPath, "sqlnet.ora");

        if (tnsFile.exists()) {
            System.out.println("✅ ملف tnsnames.ora موجود: " + tnsFile.getAbsolutePath());
        } else {
            System.err.println("❌ ملف tnsnames.ora غير موجود: " + tnsFile.getAbsolutePath());
        }

        if (sqlnetFile.exists()) {
            System.out.println("✅ ملف sqlnet.ora موجود: " + sqlnetFile.getAbsolutePath());
        } else {
            System.err.println("⚠️ ملف sqlnet.ora غير موجود: " + sqlnetFile.getAbsolutePath());
        }
    }

    /**
     * الحصول على اتصال باستخدام TNS Name
     */
    public Connection getConnection(String tnsName) throws SQLException {
        DatabaseConnection dbConn = connections.get(tnsName);
        if (dbConn == null) {
            throw new SQLException("TNS Name غير معروف: " + tnsName);
        }

        return createConnection(dbConn);
    }

    /**
     * الحصول على اتصال SHIP_ERP
     */
    public Connection getShipErpConnection() throws SQLException {
        return getConnection(SHIP_ERP_TNS);
    }

    /**
     * الحصول على اتصال IAS20251
     */
    public Connection getIas20251Connection() throws SQLException {
        return getConnection(IAS20251_TNS);
    }

    /**
     * الحصول على اتصال عالي التوفر
     */
    public Connection getHighAvailabilityConnection() throws SQLException {
        return getConnection(SHIP_ERP_HA_TNS);
    }

    /**
     * إنشاء اتصال فعلي
     */
    private Connection createConnection(DatabaseConnection dbConn) throws SQLException {
        try {
            // تحميل Oracle JDBC Driver
            Class.forName("oracle.jdbc.driver.OracleDriver");

            // إعداد خصائص الاتصال
            Properties props = new Properties();
            props.setProperty("user", dbConn.username);
            props.setProperty("password", getDecryptedPassword(dbConn.password));
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");

            // إنشاء URL الاتصال باستخدام TNS
            String url = "jdbc:oracle:thin:@" + dbConn.tnsName;

            System.out.println(
                    "🔄 الاتصال بـ " + dbConn.description + " (" + dbConn.tnsName + ")...");

            Connection connection = DriverManager.getConnection(url, props);

            System.out.println("✅ تم الاتصال بنجاح بـ " + dbConn.description);

            return connection;

        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver غير موجود", e);
        } catch (SQLException e) {
            System.err.println("❌ فشل الاتصال بـ " + dbConn.description + ": " + e.getMessage());
            throw e;
        }
    }

    /**
     * فك تشفير كلمة المرور
     */
    private String getDecryptedPassword(String password) {
        try {
            // استخدام فك التشفير البسيط
            if (password != null && password.startsWith("ENC:")) {
                return password.substring(4); // إزالة البادئة ENC:
            }
            return password;
        } catch (Exception e) {
            // إذا فشل فك التشفير، إرجاع كلمة المرور كما هي
            return password;
        }
    }

    /**
     * اختبار جميع الاتصالات
     */
    public void testAllConnections() {
        System.out.println("\n🔍 اختبار جميع اتصالات TNS...");
        System.out.println("=" + "=".repeat(50));

        for (Map.Entry<String, DatabaseConnection> entry : connections.entrySet()) {
            String tnsName = entry.getKey();
            DatabaseConnection dbConn = entry.getValue();

            try (Connection conn = getConnection(tnsName)) {
                if (conn != null && !conn.isClosed()) {
                    System.out.println("✅ " + tnsName + " - " + dbConn.description);
                } else {
                    System.out.println("❌ " + tnsName + " - اتصال فاشل");
                }
            } catch (SQLException e) {
                System.out.println("❌ " + tnsName + " - " + e.getMessage());
            }
        }

        System.out.println("=" + "=".repeat(50));
    }

    /**
     * الحصول على قائمة الاتصالات المتاحة
     */
    public void printAvailableConnections() {
        System.out.println("\n📋 الاتصالات المتاحة:");
        System.out.println("=" + "=".repeat(50));

        for (Map.Entry<String, DatabaseConnection> entry : connections.entrySet()) {
            String tnsName = entry.getKey();
            DatabaseConnection dbConn = entry.getValue();

            System.out.println("🔗 " + tnsName);
            System.out.println("   المستخدم: " + dbConn.username);
            System.out.println("   الوصف: " + dbConn.description);
            System.out.println();
        }

        System.out.println("=" + "=".repeat(50));
    }

    /**
     * كلاس معلومات الاتصال
     */
    private static class DatabaseConnection {
        public final String tnsName;
        public final String username;
        public final String password;
        public final String description;

        public DatabaseConnection(String tnsName, String username, String password,
                String description) {
            this.tnsName = tnsName;
            this.username = username;
            this.password = password;
            this.description = description;
        }
    }

    /**
     * اختبار مدير اتصالات TNS
     */
    public static void main(String[] args) {
        System.out.println("🔍 اختبار مدير اتصالات TNS...");

        TNSConnectionManager manager = TNSConnectionManager.getInstance();

        // طباعة الاتصالات المتاحة
        manager.printAvailableConnections();

        // اختبار جميع الاتصالات
        manager.testAllConnections();

        System.out.println("✅ اختبار مدير اتصالات TNS مكتمل");
    }
}
