import javax.mail.*;
import java.util.Properties;

/**
 * اختبار بسيط لمكتبة JavaMail
 */
public class TestJavaMail {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔍 اختبار مكتبة JavaMail...");
            
            // اختبار إنشاء Session
            Properties props = new Properties();
            props.setProperty("mail.smtp.host", "smtp.gmail.com");
            props.setProperty("mail.smtp.port", "587");
            
            Session session = Session.getInstance(props);
            System.out.println("✅ تم إنشاء Session بنجاح");
            
            // اختبار إنشاء Store
            Store store = session.getStore("imap");
            System.out.println("✅ تم إنشاء Store بنجاح");
            
            // اختبار إنشاء Transport
            Transport transport = session.getTransport("smtp");
            System.out.println("✅ تم إنشاء Transport بنجاح");
            
            System.out.println("🎉 جميع اختبارات JavaMail نجحت!");
            
        } catch (Exception e) {
            System.err.println("❌ فشل اختبار JavaMail: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
