@echo off
echo ========================================
echo 🔧 إصلاح شامل لجميع مشاكل المظهر
echo Complete Theme Issues Fix
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/5] تشخيص مشاكل المظهر...
echo Theme Issues Diagnosis...
echo ========================================

echo تشغيل أداة التشخيص الشاملة...
java -cp "lib\*;." ThemeDiagnosticTool

echo.
echo [2/5] تجميع مدير المظاهر النهائي...
echo Compiling Final Theme Manager...
echo ========================================

javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\FinalThemeManager.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع FinalThemeManager بنجاح
) else (
    echo ❌ فشل في تجميع FinalThemeManager
    pause
    exit /b 1
)

echo.
echo [3/5] تجميع أداة الإصلاح الشاملة...
echo Compiling Complete Theme Fixer...
echo ========================================

javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CompleteThemeFixer.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CompleteThemeFixer بنجاح
) else (
    echo ❌ فشل في تجميع CompleteThemeFixer
    pause
    exit /b 1
)

echo.
echo [4/5] تشغيل أداة الإصلاح الشاملة...
echo Running Complete Theme Fixer...
echo ========================================

java -cp "lib\*;." CompleteThemeFixer

echo.
echo [5/5] تجميع وتشغيل نافذة اختبار المظاهر...
echo Compiling and Running Theme Test Window...
echo ========================================

javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\ThemeTestWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع ThemeTestWindow بنجاح
    echo تشغيل نافذة اختبار المظاهر...
    start java -cp "lib\*;." ThemeTestWindow
) else (
    echo ❌ فشل في تجميع ThemeTestWindow
)

echo.
echo ========================================
echo ✅ تم إكمال إصلاح جميع مشاكل المظهر!
echo Complete Theme Issues Fix Completed!
echo ========================================

echo.
echo 📋 ملخص الإصلاحات:
echo =================
echo • تم تشخيص جميع مشاكل المظهر
echo • تم إنشاء مدير مظاهر موحد (FinalThemeManager)
echo • تم إصلاح جميع الملفات لتستخدم المدير الموحد
echo • تم إنشاء نافذة اختبار المظاهر
echo • تم توحيد جميع إعدادات المظهر

echo.
echo 💡 للاستخدام:
echo =============
echo • استخدم FinalThemeManager.initializeDefaultTheme() في بداية التطبيق
echo • استخدم FinalThemeManager.getInstance().applyTheme() لتغيير المظهر
echo • جميع الإعدادات محفوظة في unified-theme.properties

echo.
echo 🎨 المظاهر المتاحة:
echo ==================
echo • FlatLaf Light (الافتراضي)
echo • FlatLaf Dark
echo • FlatLaf IntelliJ
echo • FlatLaf Darcula
echo • System Default
echo • Windows
echo • Metal
echo • Nimbus

echo.
echo 🧪 لاختبار المظاهر:
echo ==================
echo java -cp "lib\*;." ThemeTestWindow

echo.
echo 🔧 لإصلاح مظاهر النوافذ المفتوحة:
echo ================================
echo FinalThemeManager.fixAllWindowsThemes()

echo.
pause
