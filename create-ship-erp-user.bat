@echo off
echo ========================================
echo   CREATE SHIP_ERP USER SCRIPT
echo   سكريبت إنشاء مستخدم SHIP_ERP
echo ========================================

cd /d "d:\java\java"

echo.
echo [INFO] Starting SHIP_ERP user creation...
echo [INFO] بدء إنشاء مستخدم SHIP_ERP...
echo.

echo ========================================
echo    Method 1: Using SQL*Plus (Recommended)
echo    الطريقة الأولى: استخدام SQL*Plus (موصى بها)
echo ========================================

echo.
echo [1] Attempting to connect with SQL*Plus...
echo [1] محاولة الاتصال باستخدام SQL*Plus...

REM محاولة تشغيل SQL*Plus مع مستخدم SYSTEM
sqlplus system/oracle@localhost:1521/ORCL @create_ship_erp_user.sql

if %errorlevel% neq 0 (
    echo.
    echo [2] Trying with different SYSTEM password...
    echo [2] محاولة بكلمة مرور SYSTEM مختلفة...
    
    sqlplus system/sys@localhost:1521/ORCL @create_ship_erp_user.sql
    
    if %errorlevel% neq 0 (
        echo.
        echo [3] Trying with SYS user...
        echo [3] محاولة بمستخدم SYS...
        
        sqlplus sys/sys@localhost:1521/ORCL as sysdba @create_ship_erp_user.sql
        
        if %errorlevel% neq 0 (
            echo.
            echo ❌ SQL*Plus method failed
            echo ❌ فشلت طريقة SQL*Plus
            goto JAVA_METHOD
        )
    )
)

echo.
echo ✅ SQL*Plus method completed
echo ✅ تمت طريقة SQL*Plus
goto TEST_CONNECTION

:JAVA_METHOD
echo.
echo ========================================
echo    Method 2: Using Java Tool
echo    الطريقة الثانية: استخدام أداة Java
echo ========================================

echo.
echo [INFO] Compiling Java tool...
echo [INFO] تجميع أداة Java...

javac -encoding UTF-8 -cp "lib\ojdbc11.jar;lib\orai18n.jar" CreateShipERPUser.java

if %errorlevel% equ 0 (
    echo ✅ Compilation successful
    echo ✅ تم التجميع بنجاح
    
    echo.
    echo [INFO] Running Java tool...
    echo [INFO] تشغيل أداة Java...
    
    java -cp "lib\ojdbc11.jar;lib\orai18n.jar;." CreateShipERPUser
    
    if %errorlevel% equ 0 (
        echo ✅ Java method completed
        echo ✅ تمت طريقة Java
    ) else (
        echo ❌ Java method failed
        echo ❌ فشلت طريقة Java
    )
) else (
    echo ❌ Java compilation failed
    echo ❌ فشل تجميع Java
)

:TEST_CONNECTION
echo.
echo ========================================
echo    Testing SHIP_ERP Connection
echo    اختبار اتصال SHIP_ERP
echo ========================================

echo.
echo [INFO] Testing connection to SHIP_ERP...
echo [INFO] اختبار الاتصال بـ SHIP_ERP...

java -cp "lib\ojdbc11.jar;lib\orai18n.jar;." DatabaseConnectionTest

echo.
echo ========================================
echo    Manual Connection Instructions
echo    تعليمات الاتصال اليدوي
echo ========================================

echo.
echo If automatic creation failed, you can create the user manually:
echo إذا فشل الإنشاء التلقائي، يمكنك إنشاء المستخدم يدوياً:
echo.
echo 1. Connect to Oracle as SYSTEM or SYS:
echo    الاتصال بـ Oracle كـ SYSTEM أو SYS:
echo    sqlplus system/password@localhost:1521/ORCL
echo.
echo 2. Run the following commands:
echo    تشغيل الأوامر التالية:
echo    CREATE USER SHIP_ERP IDENTIFIED BY ship_erp_password;
echo    GRANT CONNECT, RESOURCE TO SHIP_ERP;
echo    GRANT CREATE SESSION TO SHIP_ERP;
echo    GRANT UNLIMITED TABLESPACE TO SHIP_ERP;
echo.
echo 3. Test connection:
echo    اختبار الاتصال:
echo    sqlplus SHIP_ERP/ship_erp_password@localhost:1521/ORCL
echo.

echo ========================================
echo    Alternative Database Setup
echo    إعداد قاعدة بيانات بديل
echo ========================================

echo.
echo You can also use the existing ias20251 user:
echo يمكنك أيضاً استخدام المستخدم الموجود ias20251:
echo.
echo Connection: ias20251/ys123@localhost:1521/ORCL
echo الاتصال: ias20251/ys123@localhost:1521/ORCL
echo.
echo This user already contains sample data and tables.
echo هذا المستخدم يحتوي بالفعل على بيانات وجداول تجريبية.
echo.

echo ========================================
echo    Next Steps
echo    الخطوات التالية
echo ========================================

echo.
echo 1. Update application.properties with correct database settings
echo    تحديث application.properties بإعدادات قاعدة البيانات الصحيحة
echo.
echo 2. Run the Ship ERP application:
echo    تشغيل تطبيق Ship ERP:
echo    start-system.bat
echo.
echo 3. Check database connection in the application
echo    فحص اتصال قاعدة البيانات في التطبيق
echo.

pause
