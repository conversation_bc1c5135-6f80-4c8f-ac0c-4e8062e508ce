@echo off
echo ========================================
echo   Theme Libraries Diagnostic
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Checking Java installation...
java -version
if %errorlevel% neq 0 (
    echo ERROR: Java not found or not working
    pause
    exit /b 1
)

echo.
echo [2] Checking lib directory...
if not exist "lib" (
    echo ERROR: lib directory not found
    pause
    exit /b 1
)

echo Found lib directory
dir lib\*.jar /b

echo.
echo [3] Checking for specific theme libraries...

echo Checking FlatLaf...
if exist "lib\flatlaf-*.jar" (
    echo [FOUND] FlatLaf library
    dir lib\flatlaf-*.jar /b
) else (
    echo [NOT FOUND] FlatLaf library
)

echo.
echo Checking JTattoo...
if exist "lib\jtattoo-*.jar" (
    echo [FOUND] JTattoo library
    dir lib\jtattoo-*.jar /b
) else (
    echo [NOT FOUND] JTattoo library
)

echo.
echo Checking IntelliJ Themes...
if exist "lib\*intellij*.jar" (
    echo [FOUND] IntelliJ Themes
    dir lib\*intellij*.jar /b
) else (
    echo [NOT FOUND] IntelliJ Themes
)

echo.
echo [4] Testing basic theme classes...

echo Testing System LookAndFeel...
java -cp "lib\*;." -c "javax.swing.UIManager.getSystemLookAndFeel()" 2>nul
if %errorlevel% equ 0 (
    echo [OK] System LookAndFeel available
) else (
    echo [ERROR] System LookAndFeel not available
)

echo Testing Metal LookAndFeel...
java -cp "lib\*;." -c "javax.swing.plaf.metal.MetalLookAndFeel" 2>nul
if %errorlevel% equ 0 (
    echo [OK] Metal LookAndFeel available
) else (
    echo [ERROR] Metal LookAndFeel not available
)

echo Testing Nimbus LookAndFeel...
java -cp "lib\*;." -c "javax.swing.plaf.nimbus.NimbusLookAndFeel" 2>nul
if %errorlevel% equ 0 (
    echo [OK] Nimbus LookAndFeel available
) else (
    echo [ERROR] Nimbus LookAndFeel not available
)

echo.
echo [5] Testing FlatLaf (if available)...
java -cp "lib\*;." -c "com.formdev.flatlaf.FlatLightLaf" 2>nul
if %errorlevel% equ 0 (
    echo [OK] FlatLaf Light available
) else (
    echo [NOT AVAILABLE] FlatLaf Light
)

echo.
echo [6] Testing JTattoo (if available)...
java -cp "lib\*;." -c "com.jtattoo.plaf.acryl.AcrylLookAndFeel" 2>nul
if %errorlevel% equ 0 (
    echo [OK] JTattoo Acryl available
) else (
    echo [NOT AVAILABLE] JTattoo Acryl
)

echo.
echo [7] Compilation test...
echo Testing WorkingThemeManager compilation...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\WorkingThemeManager.java 2>nul
if %errorlevel% equ 0 (
    echo [OK] WorkingThemeManager compiles successfully
) else (
    echo [ERROR] WorkingThemeManager compilation failed
)

echo Testing WorkingThemeWindow compilation...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\WorkingThemeWindow.java 2>nul
if %errorlevel% equ 0 (
    echo [OK] WorkingThemeWindow compiles successfully
) else (
    echo [ERROR] WorkingThemeWindow compilation failed
)

echo.
echo ========================================
echo   Diagnostic Complete
echo ========================================
echo.
echo RECOMMENDATIONS:
echo 1. If FlatLaf/JTattoo not found, the system will use basic themes only
echo 2. If compilation fails, check Java version and classpath
echo 3. Use start-working-theme-system.bat for guaranteed compatibility
echo.
pause
