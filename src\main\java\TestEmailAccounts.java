import java.sql.*;

/**
 * اختبار عرض حسابات البريد الإلكتروني من قاعدة البيانات
 * Test Email Accounts Display
 */
public class TestEmailAccounts {
    
    public static void main(String[] args) {
        System.out.println("🔍 اختبار عرض حسابات البريد الإلكتروني من قاعدة البيانات");
        System.out.println("📧 Testing Email Accounts Display from Database");
        System.out.println("=" + "=".repeat(60));
        
        Connection dbConnection = null;
        
        try {
            // محاولة الاتصال بقاعدة البيانات
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            dbConnection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح");
            
            // استعلام حسابات البريد الإلكتروني
            String sql = """
                SELECT ACCOUNT_ID, EMAIL_ADDRESS, DISPLAY_NAME, EMAIL_PROVIDER, 
                       SMTP_SERVER, IMAP_SERVER, IS_ACTIVE, CREATED_DATE
                FROM SHIP_ERP_EMAIL_ACCOUNTS 
                ORDER BY DISPLAY_NAME
                """;
            
            try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                int count = 0;
                System.out.println("\n📋 حسابات البريد الإلكتروني المتاحة:");
                System.out.println("-".repeat(80));
                
                while (rs.next()) {
                    count++;
                    int accountId = rs.getInt("ACCOUNT_ID");
                    String emailAddress = rs.getString("EMAIL_ADDRESS");
                    String displayName = rs.getString("DISPLAY_NAME");
                    String provider = rs.getString("EMAIL_PROVIDER");
                    String smtpServer = rs.getString("SMTP_SERVER");
                    String imapServer = rs.getString("IMAP_SERVER");
                    String isActive = rs.getString("IS_ACTIVE");
                    Timestamp createdDate = rs.getTimestamp("CREATED_DATE");
                    
                    System.out.printf("%d. 📧 %s (%s)%n", count, 
                        displayName != null ? displayName : "حساب بريد", 
                        emailAddress);
                    System.out.printf("   🏢 المزود: %s%n", provider != null ? provider : "غير محدد");
                    System.out.printf("   📤 SMTP: %s%n", smtpServer != null ? smtpServer : "غير محدد");
                    System.out.printf("   📥 IMAP: %s%n", imapServer != null ? imapServer : "غير محدد");
                    System.out.printf("   ✅ نشط: %s%n", "Y".equals(isActive) ? "نعم" : "لا");
                    System.out.printf("   📅 تاريخ الإنشاء: %s%n", createdDate != null ? createdDate.toString() : "غير محدد");
                    System.out.println();
                }
                
                if (count == 0) {
                    System.out.println("⚠️ لا توجد حسابات بريد إلكتروني في قاعدة البيانات");
                    System.out.println("💡 يمكنك إضافة حسابات جديدة من خلال نافذة إعدادات البريد الإلكتروني");
                } else {
                    System.out.println("✅ تم العثور على " + count + " حساب بريد إلكتروني");
                    System.out.println("🚀 يمكنك الآن فتح نافذة صندوق البريد الوارد لعرض هذه الحسابات");
                }
                
            } catch (SQLException e) {
                System.err.println("❌ خطأ في استعلام حسابات البريد: " + e.getMessage());
                e.printStackTrace();
            }
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
            System.out.println("💡 تأكد من:");
            System.out.println("   • تشغيل قاعدة البيانات Oracle");
            System.out.println("   • وجود المستخدم ship_erp");
            System.out.println("   • تنفيذ سكريبت إنشاء جداول النظام");
        } finally {
            // إغلاق الاتصال
            try {
                if (dbConnection != null && !dbConnection.isClosed()) {
                    dbConnection.close();
                    System.out.println("✅ تم إغلاق اتصال قاعدة البيانات");
                }
            } catch (SQLException e) {
                System.err.println("❌ خطأ في إغلاق اتصال قاعدة البيانات: " + e.getMessage());
            }
        }
        
        System.out.println("\n" + "=".repeat(60));
        System.out.println("🏁 انتهى اختبار عرض حسابات البريد الإلكتروني");
    }
}
