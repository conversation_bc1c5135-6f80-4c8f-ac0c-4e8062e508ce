@echo off
echo ========================================
echo   STARTING UI SETTINGS WINDOW
echo   تشغيل نافذة إعدادات الواجهة
echo ========================================

cd /d "d:\java\java"

echo.
echo [INFO] Compiling and starting UI Settings Window...
echo [INFO] تجميع وتشغيل نافذة إعدادات الواجهة...
echo.

REM تجميع الملفات
echo [1] Compiling files...
javac -encoding UTF-8 -cp "lib\*" EnhancedSettingsManager.java AdvancedUISettingsWindow.java

if %errorlevel% neq 0 (
    echo ❌ Compilation failed!
    echo ❌ فشل التجميع!
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo ✅ تم التجميع بنجاح!

echo.
echo [2] Starting UI Settings Window...
echo [2] تشغيل نافذة إعدادات الواجهة...

REM تشغيل النافذة
start "UI Settings" java -cp "lib\*;." -Djava.awt.headless=false -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA AdvancedUISettingsWindow

echo.
echo ✅ UI Settings Window started!
echo ✅ تم تشغيل نافذة إعدادات الواجهة!
echo.
echo The window should now be visible on your screen.
echo يجب أن تكون النافذة مرئية الآن على الشاشة.
echo.

pause
