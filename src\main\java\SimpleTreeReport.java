import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * تقرير مبسط لجدول شجرة الأنظمة
 */
public class SimpleTreeReport {
    
    public static void main(String[] args) {
        System.out.println("🚀 بدء فحص جدول شجرة الأنظمة...");
        System.out.println("==========================================");
        
        Connection conn = null;
        try {
            // الاتصال بقاعدة البيانات
            String url = "*************************************";
            String username = "SHIP_ERP";
            String password = "ship_erp_password";
            
            System.out.println("🔌 محاولة الاتصال بقاعدة البيانات...");
            conn = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح");
            
            // طباعة رأس التقرير
            printHeader();
            
            // فحص وجود الجدول
            checkTable(conn);
            
            // الإحصائيات العامة
            printStatistics(conn);
            
            // توزيع الأنواع
            printNodeTypes(conn);
            
            // فحص المشاكل
            checkIssues(conn);
            
            // عينة من الهيكل
            printSampleStructure(conn);
            
            System.out.println("\n✅ تم إكمال التقرير بنجاح!");
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في قاعدة البيانات: " + e.getMessage());
            
            // محاولة اتصال بديلة
            tryAlternativeConnection();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ عام: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                    System.out.println("🔌 تم إغلاق الاتصال");
                } catch (SQLException e) {
                    System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
                }
            }
        }
    }
    
    private static void printHeader() {
        System.out.println("\n📊 تقرير فحص جدول شجرة الأنظمة");
        System.out.println("==========================================");
        System.out.println("التاريخ: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        System.out.println("قاعدة البيانات: Oracle SHIP_ERP");
        System.out.println("الجدول: ERP_SYSTEM_TREE");
        System.out.println("==========================================");
    }
    
    private static void checkTable(Connection conn) throws SQLException {
        System.out.println("\n🔍 [1] فحص وجود الجدول:");
        
        String sql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'ERP_SYSTEM_TREE'";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next() && rs.getInt(1) > 0) {
                System.out.println("✅ الجدول ERP_SYSTEM_TREE موجود");
            } else {
                System.out.println("❌ الجدول ERP_SYSTEM_TREE غير موجود!");
                throw new SQLException("الجدول غير موجود");
            }
        }
    }
    
    private static void printStatistics(Connection conn) throws SQLException {
        System.out.println("\n📊 [2] الإحصائيات العامة:");
        
        // إجمالي العقد
        String sql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                System.out.println("📋 إجمالي العقد: " + rs.getInt(1));
            }
        }
        
        // العقد النشطة
        sql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE IS_ACTIVE = 'Y'";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                System.out.println("✅ العقد النشطة: " + rs.getInt(1));
            }
        }
        
        // العقد المرئية
        sql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE IS_VISIBLE = 'Y'";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                System.out.println("👁️ العقد المرئية: " + rs.getInt(1));
            }
        }
    }
    
    private static void printNodeTypes(Connection conn) throws SQLException {
        System.out.println("\n📋 [3] توزيع أنواع العقد:");
        
        String sql = "SELECT NODE_TYPE, COUNT(*) FROM ERP_SYSTEM_TREE GROUP BY NODE_TYPE ORDER BY COUNT(*) DESC";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String type = rs.getString(1);
                int count = rs.getInt(2);
                String icon = getIcon(type);
                System.out.println(icon + " " + type + ": " + count + " عقدة");
            }
        }
    }
    
    private static void checkIssues(Connection conn) throws SQLException {
        System.out.println("\n⚠️ [4] فحص المشاكل:");
        
        int issues = 0;
        
        // العقد اليتيمة
        String sql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE PARENT_ID IS NOT NULL AND PARENT_ID NOT IN (SELECT TREE_ID FROM ERP_SYSTEM_TREE)";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                int orphans = rs.getInt(1);
                if (orphans > 0) {
                    System.out.println("❌ عقد يتيمة: " + orphans);
                    issues += orphans;
                }
            }
        }
        
        // النوافذ بدون كلاس
        sql = "SELECT COUNT(*) FROM ERP_SYSTEM_TREE WHERE NODE_TYPE = 'WINDOW' AND (WINDOW_CLASS IS NULL OR TRIM(WINDOW_CLASS) = '')";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                int noClass = rs.getInt(1);
                if (noClass > 0) {
                    System.out.println("❌ نوافذ بدون كلاس: " + noClass);
                    issues += noClass;
                }
            }
        }
        
        if (issues == 0) {
            System.out.println("✅ لم يتم العثور على مشاكل");
        }
    }
    
    private static void printSampleStructure(Connection conn) throws SQLException {
        System.out.println("\n🌳 [5] عينة من الهيكل:");
        
        String sql = """
            SELECT TREE_ID, LPAD(' ', TREE_LEVEL * 2) || NODE_NAME_AR AS DISPLAY_NAME, NODE_TYPE
            FROM ERP_SYSTEM_TREE 
            WHERE ROWNUM <= 10
            ORDER BY TREE_ID
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                int id = rs.getInt(1);
                String name = rs.getString(2);
                String type = rs.getString(3);
                String icon = getIcon(type);
                
                System.out.println(String.format("[%d] %s %s (%s)", id, icon, name, type));
            }
        }
    }
    
    private static String getIcon(String nodeType) {
        if (nodeType == null) return "📄";
        return switch (nodeType) {
            case "CATEGORY" -> "📂";
            case "WINDOW" -> "🪟";
            case "TOOL" -> "🛠️";
            case "REPORT" -> "📊";
            default -> "📄";
        };
    }
    
    private static void tryAlternativeConnection() {
        System.out.println("\n🔄 محاولة اتصال بديلة...");
        
        String[] alternatives = {
            "***********************************",
            "****************************************"
        };
        
        for (String url : alternatives) {
            try {
                System.out.println("🔌 محاولة: " + url);
                Connection conn = DriverManager.getConnection(url, "SHIP_ERP", "ship_erp_password");
                System.out.println("✅ نجح الاتصال مع: " + url);
                conn.close();
                return;
            } catch (SQLException e) {
                System.out.println("❌ فشل: " + e.getMessage());
            }
        }
        
        System.out.println("\n💡 نصائح لحل مشكلة الاتصال:");
        System.out.println("• تأكد من تشغيل Oracle Database");
        System.out.println("• تحقق من صحة كلمة المرور");
        System.out.println("• تأكد من وجود المستخدم SHIP_ERP");
        System.out.println("• تحقق من إعدادات الشبكة والمنافذ");
    }
}
