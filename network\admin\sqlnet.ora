# SQLNET.ORA Network Configuration File
# Ship ERP System - Oracle Network Configuration
# تكوين شبكة Oracle لنظام إدارة الشحنات
# Generated on: 2025-07-18

# =============================================================================
# NAMING METHODS
# طرق التسمية
# =============================================================================
NAMES.DIRECTORY_PATH = (TNSNAMES, EZCONNECT, HOSTNAME)
NAMES.DEFAULT_DOMAIN = 

# =============================================================================
# SECURITY SETTINGS
# إعدادات الأمان
# =============================================================================
SQLNET.AUTHENTICATION_SERVICES = (NTS)
SQLNET.AUTHENTICATION_GSSAPI_SERVICE = oracle
SQLNET.FALLBACK_AUTHENTICATION = TRUE

# =============================================================================
# ENCRYPTION SETTINGS
# إعدادات التشفير
# =============================================================================
SQLNET.ENCRYPTION_CLIENT = ACCEPTED
SQLNET.ENCRYPTION_TYPES_CLIENT = (AES256, AES192, AES128, 3DES168, 3DES112, DES, RC4_256, RC4_128, RC4_56, RC4_40, DES40)
SQLNET.CRYPTO_CHECKSUM_CLIENT = ACCEPTED
SQLNET.CRYPTO_CHECKSUM_TYPES_CLIENT = (SHA256, SHA1, MD5)

# =============================================================================
# CONNECTION TIMEOUT SETTINGS
# إعدادات مهلة الاتصال
# =============================================================================
SQLNET.INBOUND_CONNECT_TIMEOUT = 60
SQLNET.OUTBOUND_CONNECT_TIMEOUT = 60
SQLNET.RECV_TIMEOUT = 30
SQLNET.SEND_TIMEOUT = 30

# =============================================================================
# TRACE AND LOGGING SETTINGS
# إعدادات التتبع والسجلات
# =============================================================================
TRACE_LEVEL_CLIENT = OFF
TRACE_DIRECTORY_CLIENT = E:\ship_erp\java\network\admin\trace
TRACE_FILE_CLIENT = sqlnet_client
TRACE_TIMESTAMP_CLIENT = ON
TRACE_UNIQUE_CLIENT = ON

LOG_DIRECTORY_CLIENT = E:\ship_erp\java\network\admin\log
LOG_FILE_CLIENT = sqlnet_client

# =============================================================================
# PERFORMANCE SETTINGS
# إعدادات الأداء
# =============================================================================
SQLNET.EXPIRE_TIME = 10
TCP.CONNECT_TIMEOUT = 60
TCP.NODELAY = YES
DISABLE_OOB = ON

# =============================================================================
# ARABIC AND UNICODE SUPPORT
# دعم العربية ويونيكود
# =============================================================================
NLS_LANG = ARABIC_SAUDI ARABIA.AL32UTF8
SQLNET.ORA_NLS_LANGUAGE = ARABIC
SQLNET.ORA_NLS_TERRITORY = SAUDI ARABIA
SQLNET.ORA_NLS_CHARACTERSET = AL32UTF8

# =============================================================================
# CONNECTION POOLING SETTINGS
# إعدادات تجميع الاتصالات
# =============================================================================
CONNECTION_POOLING = ON
CONNECTION_POOL_MIN_LIMIT = 2
CONNECTION_POOL_MAX_LIMIT = 10
CONNECTION_POOL_INCREMENT = 1
CONNECTION_POOL_TIMEOUT = 0

# =============================================================================
# ADVANCED SETTINGS
# الإعدادات المتقدمة
# =============================================================================
SQLNET.ALLOWED_LOGON_VERSION_CLIENT = 12
SQLNET.ALLOWED_LOGON_VERSION_SERVER = 12
BEQUEATH_DETACH = YES
AUTOMATIC_IPC = OFF

# =============================================================================
# ERROR HANDLING
# معالجة الأخطاء
# =============================================================================
SQLNET.SEND_BUF_SIZE = 32767
SQLNET.RECV_BUF_SIZE = 32767
BREAK_POLL_SKIP = 1000

# =============================================================================
# WALLET CONFIGURATION (for SSL)
# تكوين المحفظة (للـ SSL)
# =============================================================================
# WALLET_LOCATION = (SOURCE = (METHOD = FILE) (METHOD_DATA = (DIRECTORY = E:\ship_erp\java\network\admin\wallet)))
# SQLNET.WALLET_OVERRIDE = TRUE
# SSL_CLIENT_AUTHENTICATION = FALSE
# SSL_VERSION = 0

# =============================================================================
# KERBEROS CONFIGURATION
# تكوين Kerberos
# =============================================================================
# SQLNET.KERBEROS5_KEYTAB = E:\ship_erp\java\network\admin\krb5.keytab
# SQLNET.KERBEROS5_REALMS = E:\ship_erp\java\network\admin\krb5.conf
# SQLNET.KERBEROS5_CC_NAME = E:\ship_erp\java\network\admin\krb5cc

# =============================================================================
# LDAP CONFIGURATION
# تكوين LDAP
# =============================================================================
# NAMES.LDAP_HOST = ldap.company.com
# NAMES.LDAP_PORT = 389
# NAMES.LDAP_CONTEXT = dc=company,dc=com

# =============================================================================
# CONFIGURATION NOTES
# ملاحظات التكوين
# =============================================================================
#
# This file configures Oracle Net Services for the Ship ERP application.
# هذا الملف يكون خدمات Oracle Net لتطبيق إدارة الشحنات.
#
# Key Features Enabled:
# الميزات الرئيسية المفعلة:
# - Connection timeout protection
# - Arabic language support
# - Encryption support
# - Connection pooling
# - Comprehensive logging
#
# To enable SSL, uncomment the WALLET_LOCATION section and configure
# the wallet properly.
# لتفعيل SSL، قم بإلغاء التعليق على قسم WALLET_LOCATION وكون
# المحفظة بشكل صحيح.
#
# =============================================================================
