@echo off
echo Testing start-system.bat with JavaMail...
cd /d "e:\ship_erp\java"

echo Checking JavaMail libraries...
if exist "lib\javax.mail-1.6.2.jar" (
    echo ✅ javax.mail-1.6.2.jar found
) else (
    echo ❌ javax.mail-1.6.2.jar NOT found
    pause
    exit /b 1
)

if exist "lib\activation.jar" (
    echo ✅ activation.jar found
) else (
    echo ❌ activation.jar NOT found
    pause
    exit /b 1
)

echo Compiling CompleteEmailAccountsWindow with JavaMail...
javac -encoding UTF-8 -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\*;." -d . src\main\java\CompleteEmailAccountsWindow.java

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)

echo ✅ Compilation successful!

echo Testing email window...
java -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\*;." CompleteEmailAccountsWindow

pause
