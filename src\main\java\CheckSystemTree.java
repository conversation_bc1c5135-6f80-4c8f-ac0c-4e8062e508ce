import java.sql.*;

/**
 * فحص شجرة الأنظمة الحالية
 * Check Current System Tree
 */
public class CheckSystemTree {
    
    public static void main(String[] args) {
        System.out.println("🔍 فحص شجرة الأنظمة الحالية...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            displaySystemTree(connection);
            
            connection.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فحص شجرة الأنظمة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void displaySystemTree(Connection connection) throws SQLException {
        System.out.println("\n📊 شجرة الأنظمة الحالية:");
        System.out.println("========================================");
        
        // عرض الفئات الرئيسية
        String categoriesSQL = """
            SELECT TREE_ID, NODE_NAME_AR, NODE_NAME_EN, DISPLAY_ORDER, IS_ACTIVE
            FROM ERP_SYSTEM_TREE 
            WHERE PARENT_ID IS NULL AND NODE_TYPE = 'CATEGORY'
            ORDER BY DISPLAY_ORDER
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(categoriesSQL);
             ResultSet rs = stmt.executeQuery()) {
            
            int categoryCount = 0;
            while (rs.next()) {
                categoryCount++;
                int treeId = rs.getInt("TREE_ID");
                String nameAr = rs.getString("NODE_NAME_AR");
                String nameEn = rs.getString("NODE_NAME_EN");
                int order = rs.getInt("DISPLAY_ORDER");
                String isActive = rs.getString("IS_ACTIVE");
                
                String status = "Y".equals(isActive) ? "✅" : "❌";
                
                System.out.println(String.format("%s %d. %s (%s)", 
                    status, order, nameAr, nameEn));
                
                // عرض النوافذ الفرعية
                displaySubWindows(connection, treeId, "  ");
            }
            
            System.out.println("\n📈 إجمالي الفئات الرئيسية: " + categoryCount);
        }
        
        // عرض إحصائيات شاملة
        displayStatistics(connection);
    }
    
    private static void displaySubWindows(Connection connection, int parentId, String indent) throws SQLException {
        String windowsSQL = """
            SELECT TREE_ID, NODE_NAME_AR, NODE_NAME_EN, WINDOW_CLASS, DISPLAY_ORDER, IS_ACTIVE
            FROM ERP_SYSTEM_TREE 
            WHERE PARENT_ID = ? AND NODE_TYPE = 'WINDOW'
            ORDER BY DISPLAY_ORDER
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(windowsSQL)) {
            stmt.setInt(1, parentId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String nameAr = rs.getString("NODE_NAME_AR");
                    String nameEn = rs.getString("NODE_NAME_EN");
                    String windowClass = rs.getString("WINDOW_CLASS");
                    int order = rs.getInt("DISPLAY_ORDER");
                    String isActive = rs.getString("IS_ACTIVE");
                    
                    String status = "Y".equals(isActive) ? "✅" : "❌";
                    
                    System.out.println(String.format("%s%s %d. %s (%s)", 
                        indent, status, order, nameAr, nameEn));
                    
                    if (windowClass != null && !windowClass.isEmpty()) {
                        System.out.println(String.format("%s     🔧 Class: %s", indent, windowClass));
                    }
                }
            }
        }
    }
    
    private static void displayStatistics(Connection connection) throws SQLException {
        System.out.println("\n📊 إحصائيات شجرة الأنظمة:");
        System.out.println("========================================");
        
        // إحصائيات الفئات
        String categoriesCountSQL = """
            SELECT COUNT(*) as TOTAL_CATEGORIES
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_TYPE = 'CATEGORY' AND IS_ACTIVE = 'Y'
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(categoriesCountSQL);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                int totalCategories = rs.getInt("TOTAL_CATEGORIES");
                System.out.println("📁 إجمالي الفئات النشطة: " + totalCategories);
            }
        }
        
        // إحصائيات النوافذ
        String windowsCountSQL = """
            SELECT COUNT(*) as TOTAL_WINDOWS
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_TYPE = 'WINDOW' AND IS_ACTIVE = 'Y'
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(windowsCountSQL);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                int totalWindows = rs.getInt("TOTAL_WINDOWS");
                System.out.println("🪟 إجمالي النوافذ النشطة: " + totalWindows);
            }
        }
        
        // إحصائيات حسب الفئة
        String categoryStatsSQL = """
            SELECT 
                p.NODE_NAME_AR as CATEGORY_NAME,
                COUNT(c.TREE_ID) as WINDOW_COUNT
            FROM ERP_SYSTEM_TREE p
            LEFT JOIN ERP_SYSTEM_TREE c ON p.TREE_ID = c.PARENT_ID AND c.NODE_TYPE = 'WINDOW' AND c.IS_ACTIVE = 'Y'
            WHERE p.NODE_TYPE = 'CATEGORY' AND p.IS_ACTIVE = 'Y'
            GROUP BY p.TREE_ID, p.NODE_NAME_AR, p.DISPLAY_ORDER
            ORDER BY p.DISPLAY_ORDER
            """;
        
        System.out.println("\n📋 تفصيل النوافذ حسب الفئة:");
        try (PreparedStatement stmt = connection.prepareStatement(categoryStatsSQL);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String categoryName = rs.getString("CATEGORY_NAME");
                int windowCount = rs.getInt("WINDOW_COUNT");
                System.out.println(String.format("  📁 %s: %d نافذة", categoryName, windowCount));
            }
        }
        
        // البحث عن الفئات المفقودة المتوقعة
        System.out.println("\n🔍 البحث عن الفئات المتوقعة:");
        String[] expectedCategories = {
            "إدارة الشحنات",
            "إدارة العملاء", 
            "إدارة الموردين",
            "إدارة المخازن",
            "الحسابات والمالية",
            "إدارة الموظفين",
            "التقارير والإحصائيات",
            "الإعدادات العامة",
            "نظام إدارة البريد الإلكتروني"
        };
        
        for (String expectedCategory : expectedCategories) {
            checkCategoryExists(connection, expectedCategory);
        }
    }
    
    private static void checkCategoryExists(Connection connection, String categoryName) throws SQLException {
        String checkSQL = """
            SELECT COUNT(*) as COUNT, MAX(IS_ACTIVE) as IS_ACTIVE
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(checkSQL)) {
            stmt.setString(1, categoryName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    int count = rs.getInt("COUNT");
                    String isActive = rs.getString("IS_ACTIVE");
                    
                    if (count == 0) {
                        System.out.println("  ❌ مفقود: " + categoryName);
                    } else if ("N".equals(isActive)) {
                        System.out.println("  ⚠️ غير نشط: " + categoryName);
                    } else {
                        System.out.println("  ✅ موجود: " + categoryName);
                    }
                }
            }
        }
    }
}
