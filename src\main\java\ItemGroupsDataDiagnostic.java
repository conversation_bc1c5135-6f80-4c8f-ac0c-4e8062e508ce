import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

/**
 * أداة تشخيص بيانات مجموعات الأصناف
 * Item Groups Data Diagnostic Tool
 */
public class ItemGroupsDataDiagnostic {
    
    private Connection shipErpConnection;
    private Connection ias20251Connection;
    
    public static void main(String[] args) {
        System.out.println("🔍 أداة تشخيص بيانات مجموعات الأصناف");
        System.out.println("Item Groups Data Diagnostic Tool");
        System.out.println("==========================================");
        
        ItemGroupsDataDiagnostic diagnostic = new ItemGroupsDataDiagnostic();
        diagnostic.runDiagnostic();
    }
    
    public void runDiagnostic() {
        try {
            // الاتصال بقواعد البيانات
            connectToDatabases();
            
            // فحص الجداول والبيانات
            checkTables();
            
            // فحص البيانات في كل جدول
            checkTableData();
            
            // فحص مشاكل الترميز
            checkEncodingIssues();
            
            // فحص الفهارس والأداء
            checkPerformanceIssues();
            
            System.out.println("\n✅ تم إكمال التشخيص");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في التشخيص: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeConnections();
        }
    }
    
    private void connectToDatabases() throws Exception {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            shipErpConnection = tnsManager.getShipErpConnection();
            ias20251Connection = tnsManager.getIas20251Connection();
            
            System.out.println("✅ تم الاتصال بقواعد البيانات");
        } catch (Exception e) {
            System.err.println("❌ فشل الاتصال بقواعد البيانات: " + e.getMessage());
            throw e;
        }
    }
    
    private void checkTables() {
        System.out.println("\n📋 فحص الجداول المطلوبة:");
        System.out.println("==========================");
        
        // جداول SHIP_ERP
        String[] shipErpTables = {
            "ERP_GROUP_DETAILS",
            "ERP_MAINSUB_GRP_DTL", 
            "ERP_SUB_GRP_DTL",
            "ERP_ASSISTANT_GROUP",
            "ERP_DETAIL_GROUP"
        };
        
        System.out.println("\n🗄️ جداول SHIP_ERP:");
        for (String table : shipErpTables) {
            checkTableExists(table, shipErpConnection);
        }
        
        // جداول IAS20251
        String[] ias20251Tables = {
            "GROUP_DETAILS",
            "IAS_MAINSUB_GRP_DTL",
            "IAS_SUB_GRP_DTL", 
            "IAS_ASSISTANT_GROUP",
            "IAS_DETAIL_GROUP"
        };
        
        System.out.println("\n🗄️ جداول IAS20251:");
        for (String table : ias20251Tables) {
            checkTableExists(table, ias20251Connection);
        }
    }
    
    private void checkTableExists(String tableName, Connection connection) {
        try {
            String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE ROWNUM <= 1";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    System.out.println("✅ " + tableName + " - موجود");
                }
            }
        } catch (SQLException e) {
            System.out.println("❌ " + tableName + " - غير موجود أو خطأ: " + e.getMessage());
        }
    }
    
    private void checkTableData() {
        System.out.println("\n📊 فحص البيانات في الجداول:");
        System.out.println("=============================");
        
        // فحص بيانات SHIP_ERP
        checkTableDataCount("ERP_GROUP_DETAILS", shipErpConnection);
        checkTableDataCount("ERP_MAINSUB_GRP_DTL", shipErpConnection);
        checkTableDataCount("ERP_SUB_GRP_DTL", shipErpConnection);
        checkTableDataCount("ERP_ASSISTANT_GROUP", shipErpConnection);
        checkTableDataCount("ERP_DETAIL_GROUP", shipErpConnection);
        
        // عرض عينة من البيانات
        showSampleData();
    }
    
    private void checkTableDataCount(String tableName, Connection connection) {
        try {
            String sql = "SELECT COUNT(*) as TOTAL FROM " + tableName;
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    int count = rs.getInt("TOTAL");
                    System.out.println("📊 " + tableName + ": " + count + " سجل");
                    
                    if (count == 0) {
                        System.out.println("⚠️ الجدول " + tableName + " فارغ!");
                    }
                }
            }
        } catch (SQLException e) {
            System.out.println("❌ خطأ في فحص " + tableName + ": " + e.getMessage());
        }
    }
    
    private void showSampleData() {
        System.out.println("\n📋 عينة من البيانات:");
        System.out.println("===================");
        
        // عرض عينة من المجموعات الرئيسية
        showTableSample("ERP_GROUP_DETAILS", shipErpConnection, 5);
    }
    
    private void showTableSample(String tableName, Connection connection, int limit) {
        try {
            String sql = "SELECT * FROM " + tableName + " WHERE ROWNUM <= " + limit;
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                System.out.println("\n🔍 عينة من " + tableName + ":");
                
                // عرض أسماء الأعمدة
                System.out.print("الأعمدة: ");
                for (int i = 1; i <= columnCount; i++) {
                    System.out.print(metaData.getColumnName(i));
                    if (i < columnCount) System.out.print(" | ");
                }
                System.out.println();
                
                // عرض البيانات
                int rowCount = 0;
                while (rs.next() && rowCount < limit) {
                    System.out.print("السجل " + (rowCount + 1) + ": ");
                    for (int i = 1; i <= columnCount; i++) {
                        String value = rs.getString(i);
                        if (value != null && value.length() > 20) {
                            value = value.substring(0, 20) + "...";
                        }
                        System.out.print(value);
                        if (i < columnCount) System.out.print(" | ");
                    }
                    System.out.println();
                    rowCount++;
                }
                
                if (rowCount == 0) {
                    System.out.println("⚠️ لا توجد بيانات في " + tableName);
                }
                
            }
        } catch (SQLException e) {
            System.out.println("❌ خطأ في عرض عينة من " + tableName + ": " + e.getMessage());
        }
    }
    
    private void checkEncodingIssues() {
        System.out.println("\n🔤 فحص مشاكل الترميز:");
        System.out.println("=====================");
        
        try {
            String sql = "SELECT G_CODE, G_A_NAME, G_E_NAME FROM ERP_GROUP_DETAILS WHERE ROWNUM <= 3";
            try (PreparedStatement stmt = shipErpConnection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                while (rs.next()) {
                    String code = rs.getString("G_CODE");
                    String arabicName = rs.getString("G_A_NAME");
                    String englishName = rs.getString("G_E_NAME");
                    
                    System.out.println("📝 كود: " + code);
                    System.out.println("   العربي: " + (arabicName != null ? arabicName : "NULL"));
                    System.out.println("   الإنجليزي: " + (englishName != null ? englishName : "NULL"));
                    
                    // فحص الترميز العربي
                    if (arabicName != null && arabicName.contains("?")) {
                        System.out.println("⚠️ مشكلة ترميز محتملة في الاسم العربي");
                    }
                    
                    System.out.println();
                }
            }
        } catch (SQLException e) {
            System.out.println("❌ خطأ في فحص الترميز: " + e.getMessage());
        }
    }
    
    private void checkPerformanceIssues() {
        System.out.println("\n⚡ فحص مشاكل الأداء:");
        System.out.println("===================");
        
        // فحص الفهارس
        checkIndexes();
        
        // فحص أوقات الاستعلام
        checkQueryPerformance();
    }
    
    private void checkIndexes() {
        System.out.println("\n📇 فحص الفهارس:");
        
        try {
            String sql = """
                SELECT INDEX_NAME, TABLE_NAME, COLUMN_NAME 
                FROM USER_IND_COLUMNS 
                WHERE TABLE_NAME IN ('ERP_GROUP_DETAILS', 'ERP_MAINSUB_GRP_DTL', 'ERP_SUB_GRP_DTL')
                ORDER BY TABLE_NAME, INDEX_NAME
            """;
            
            try (PreparedStatement stmt = shipErpConnection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                int indexCount = 0;
                while (rs.next()) {
                    String indexName = rs.getString("INDEX_NAME");
                    String tableName = rs.getString("TABLE_NAME");
                    String columnName = rs.getString("COLUMN_NAME");
                    
                    System.out.println("📇 " + tableName + "." + columnName + " (" + indexName + ")");
                    indexCount++;
                }
                
                if (indexCount == 0) {
                    System.out.println("⚠️ لا توجد فهارس مخصصة");
                } else {
                    System.out.println("✅ تم العثور على " + indexCount + " فهرس");
                }
            }
        } catch (SQLException e) {
            System.out.println("❌ خطأ في فحص الفهارس: " + e.getMessage());
        }
    }
    
    private void checkQueryPerformance() {
        System.out.println("\n⏱️ فحص أداء الاستعلامات:");
        
        String[] queries = {
            "SELECT COUNT(*) FROM ERP_GROUP_DETAILS",
            "SELECT * FROM ERP_GROUP_DETAILS ORDER BY G_CODE",
            "SELECT * FROM ERP_MAINSUB_GRP_DTL ORDER BY G_CODE, MNG_CODE"
        };
        
        for (String query : queries) {
            long startTime = System.currentTimeMillis();
            
            try (PreparedStatement stmt = shipErpConnection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                // تنفيذ الاستعلام
                while (rs.next()) {
                    // قراءة النتائج
                }
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                
                System.out.println("⏱️ " + query.substring(0, Math.min(50, query.length())) + "... : " + duration + " ms");
                
                if (duration > 1000) {
                    System.out.println("⚠️ استعلام بطيء!");
                }
                
            } catch (SQLException e) {
                System.out.println("❌ خطأ في الاستعلام: " + e.getMessage());
            }
        }
    }
    
    private void closeConnections() {
        try {
            if (shipErpConnection != null && !shipErpConnection.isClosed()) {
                shipErpConnection.close();
            }
            if (ias20251Connection != null && !ias20251Connection.isClosed()) {
                ias20251Connection.close();
            }
            System.out.println("🔌 تم إغلاق الاتصالات");
        } catch (SQLException e) {
            System.err.println("خطأ في إغلاق الاتصالات: " + e.getMessage());
        }
    }
}
