import java.sql.*;
import java.util.Properties;

/**
 * إصلاح هيكل شجرة النظام
 * Fix System Tree Structure
 */
public class FixSystemTreeStructure {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔧 إصلاح هيكل شجرة النظام...");
            
            Connection connection = getConnection();
            
            // إصلاح الهيكل
            fixTreeStructure(connection);
            
            connection.close();
            
            System.out.println("✅ تم إصلاح هيكل شجرة النظام بنجاح!");
            
            // اختبار النتيجة
            testResult();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إصلاح هيكل شجرة النظام: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    private static void fixTreeStructure(Connection conn) throws SQLException {
        System.out.println("📁 الحصول على معرفات الفئات...");
        
        // الحصول على معرفات الفئات
        int itemsId = getCategoryId(conn, "إدارة الأصناف");
        int usersId = getCategoryId(conn, "إدارة المستخدمين");
        int settingsId = getCategoryId(conn, "الإعدادات العامة");
        int reportsId = getCategoryId(conn, "التقارير");
        int toolsId = getCategoryId(conn, "أدوات النظام");
        
        System.out.println("  إدارة الأصناف: " + itemsId);
        System.out.println("  إدارة المستخدمين: " + usersId);
        System.out.println("  الإعدادات العامة: " + settingsId);
        System.out.println("  التقارير: " + reportsId);
        System.out.println("  أدوات النظام: " + toolsId);
        
        // إصلاح نوافذ إدارة الأصناف
        System.out.println("\n📦 إصلاح نوافذ إدارة الأصناف...");
        moveWindow(conn, "بيانات الأصناف الحقيقية", itemsId, 2, 1);
        moveWindow(conn, "بيانات الأصناف الشاملة", itemsId, 2, 2);
        moveWindow(conn, "مجموعات الأصناف", itemsId, 2, 3);
        moveWindow(conn, "وحدات القياس", itemsId, 2, 4);
        
        // إصلاح نوافذ إدارة المستخدمين
        System.out.println("\n👥 إصلاح نوافذ إدارة المستخدمين...");
        moveWindow(conn, "إدارة المستخدمين", usersId, 2, 1);
        moveWindow(conn, "صلاحيات المستخدمين", usersId, 2, 2);
        moveWindow(conn, "مجموعات المستخدمين", usersId, 2, 3);
        
        // إصلاح نوافذ الإعدادات
        System.out.println("\n⚙️ إصلاح نوافذ الإعدادات...");
        moveWindow(conn, "الإعدادات العامة", settingsId, 2, 1);
        moveWindow(conn, "إعدادات قاعدة البيانات", settingsId, 2, 2);
        moveWindow(conn, "إعدادات الأمان", settingsId, 2, 3);
        moveWindow(conn, "تكوين النظام", settingsId, 2, 4);
        
        // إصلاح نوافذ التقارير
        System.out.println("\n📊 إصلاح نوافذ التقارير...");
        moveWindow(conn, "تقرير الأصناف", reportsId, 2, 1);
        moveWindow(conn, "تقرير المستخدمين", reportsId, 2, 2);
        moveWindow(conn, "تقرير النظام", reportsId, 2, 3);
        moveWindow(conn, "التقارير المخصصة", reportsId, 2, 4);
        
        // إصلاح أدوات النظام
        System.out.println("\n🔧 إصلاح أدوات النظام...");
        moveWindow(conn, "فحص النظام الشامل", toolsId, 2, 1);
        moveWindow(conn, "مراقب الأداء", toolsId, 2, 2);
        moveWindow(conn, "إدارة الاتصالات", toolsId, 2, 3);
        moveWindow(conn, "مدير الأمان", toolsId, 2, 4);
        moveWindow(conn, "مدير التكوين", toolsId, 2, 5);
        moveWindow(conn, "البحث المتقدم", toolsId, 2, 6);
        moveWindow(conn, "النسخ الاحتياطي والاستعادة", toolsId, 2, 7);
        moveWindow(conn, "سجل النظام", toolsId, 2, 8);
        moveWindow(conn, "مركز الإشعارات", toolsId, 2, 9);
        
        // ترتيب الفئات الرئيسية
        System.out.println("\n📋 ترتيب الفئات الرئيسية...");
        updateCategory(conn, "إدارة الأصناف", 1, 1);
        updateCategory(conn, "إدارة المستخدمين", 1, 2);
        updateCategory(conn, "الإعدادات العامة", 1, 3);
        updateCategory(conn, "التقارير", 1, 4);
        updateCategory(conn, "أدوات النظام", 1, 5);
        
        // تحديث العقدة الجذرية
        updateCategory(conn, "نظام إدارة الشحنات", 0, 1);
        
        System.out.println("\n💾 حفظ التغييرات...");
        // لا نستخدم commit لتجنب مشاكل autocommit
        
        System.out.println("✅ تم إصلاح الهيكل بنجاح");
    }
    
    private static int getCategoryId(Connection conn, String categoryName) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }
    
    private static void moveWindow(Connection conn, String windowName, int parentId, int level, int order) throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET PARENT_ID = ?, TREE_LEVEL = ?, DISPLAY_ORDER = ? WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            stmt.setInt(2, level);
            stmt.setInt(3, order);
            stmt.setString(4, windowName);
            
            int updated = stmt.executeUpdate();
            if (updated > 0) {
                System.out.println("  ✅ " + windowName + " -> الفئة " + parentId);
            } else {
                System.out.println("  ⚠️ " + windowName + " (لم يتم العثور عليه)");
            }
        }
    }
    
    private static void updateCategory(Connection conn, String categoryName, int level, int order) throws SQLException {
        String sql = "UPDATE ERP_SYSTEM_TREE SET TREE_LEVEL = ?, DISPLAY_ORDER = ? WHERE NODE_NAME_AR = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, level);
            stmt.setInt(2, order);
            stmt.setString(3, categoryName);
            
            int updated = stmt.executeUpdate();
            if (updated > 0) {
                System.out.println("  ✅ " + categoryName + " (مستوى " + level + ", ترتيب " + order + ")");
            }
        }
    }
    
    private static void testResult() {
        System.out.println("\n🔍 اختبار النتيجة النهائية...");
        
        try {
            SystemTreeManager manager = SystemTreeManager.getInstance();
            manager.printSystemTree();
            manager.close();
        } catch (Exception e) {
            System.err.println("❌ خطأ في اختبار النتيجة: " + e.getMessage());
        }
    }
}
