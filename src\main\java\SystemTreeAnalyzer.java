import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Font;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.table.AbstractTableModel;

/**
 * محلل شجرة الأنظمة - System Tree Analyzer
 * أداة شاملة لفحص وتحليل جدول شجرة الأنظمة في قاعدة البيانات
 */
public class SystemTreeAnalyzer extends JFrame {
    
    private Connection dbConnection;
    private Font arabicFont;
    private JTabbedPane tabbedPane;
    private JTextArea reportTextArea;
    private JTable treeTable;
    private JTable statisticsTable;
    private JLabel statusLabel;
    
    // بيانات التحليل
    private List<SystemTreeNode> allNodes;
    private Map<String, Integer> nodeTypeStats;
    private Map<Integer, Integer> levelStats;
    private List<String> analysisResults;
    
    public SystemTreeAnalyzer() {
        initializeDatabase();
        initializeComponents();
        setupLayout();
        performAnalysis();
        
        setTitle("📊 محلل شجرة الأنظمة - System Tree Analyzer");
        setSize(1400, 900);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تطبيق مظهر حديث
        try {
            UIManager.setLookAndFeel("com.formdev.flatlaf.FlatDarkLaf");
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
        }
    }
    
    /**
     * تهيئة الاتصال بقاعدة البيانات
     */
    private void initializeDatabase() {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            dbConnection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات لتحليل شجرة الأنظمة");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, 
                "خطأ في الاتصال بقاعدة البيانات:\n" + e.getMessage(),
                "خطأ في الاتصال", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * تهيئة مكونات الواجهة
     */
    private void initializeComponents() {
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        // تهيئة البيانات
        allNodes = new ArrayList<>();
        nodeTypeStats = new HashMap<>();
        levelStats = new HashMap<>();
        analysisResults = new ArrayList<>();
        
        // إنشاء التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicFont);
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تبويب التقرير النصي
        createReportTab();
        
        // تبويب جدول الشجرة
        createTreeTableTab();
        
        // تبويب الإحصائيات
        createStatisticsTab();
        
        // شريط الحالة
        statusLabel = new JLabel("جاري تحليل شجرة الأنظمة...");
        statusLabel.setFont(arabicFont);
        statusLabel.setHorizontalAlignment(SwingConstants.CENTER);
        statusLabel.setBorder(BorderFactory.createEtchedBorder());
    }
    
    /**
     * إنشاء تبويب التقرير النصي
     */
    private void createReportTab() {
        reportTextArea = new JTextArea();
        reportTextArea.setFont(new Font("Courier New", Font.PLAIN, 12));
        reportTextArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        reportTextArea.setEditable(false);
        reportTextArea.setBackground(new Color(248, 249, 250));
        
        JScrollPane scrollPane = new JScrollPane(reportTextArea);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JPanel reportPanel = new JPanel(new BorderLayout());
        reportPanel.add(scrollPane, BorderLayout.CENTER);
        
        // أزرار التحكم
        JPanel buttonPanel = new JPanel();
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JButton refreshButton = new JButton("🔄 تحديث التحليل");
        refreshButton.setFont(arabicFont);
        refreshButton.addActionListener(e -> performAnalysis());
        
        JButton exportButton = new JButton("📄 تصدير التقرير");
        exportButton.setFont(arabicFont);
        exportButton.addActionListener(e -> exportReport());
        
        JButton fixIssuesButton = new JButton("🔧 إصلاح المشاكل");
        fixIssuesButton.setFont(arabicFont);
        fixIssuesButton.addActionListener(e -> fixDetectedIssues());
        
        buttonPanel.add(refreshButton);
        buttonPanel.add(exportButton);
        buttonPanel.add(fixIssuesButton);
        
        reportPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        tabbedPane.addTab("📋 التقرير الشامل", reportPanel);
    }
    
    /**
     * إنشاء تبويب جدول الشجرة
     */
    private void createTreeTableTab() {
        treeTable = new JTable();
        treeTable.setFont(arabicFont);
        treeTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        treeTable.setRowHeight(25);
        
        JScrollPane scrollPane = new JScrollPane(treeTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        tabbedPane.addTab("🌳 هيكل الشجرة", scrollPane);
    }
    
    /**
     * إنشاء تبويب الإحصائيات
     */
    private void createStatisticsTab() {
        statisticsTable = new JTable();
        statisticsTable.setFont(arabicFont);
        statisticsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statisticsTable.setRowHeight(25);
        
        JScrollPane scrollPane = new JScrollPane(statisticsTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        tabbedPane.addTab("📊 الإحصائيات", scrollPane);
    }
    
    /**
     * إعداد تخطيط الواجهة
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);
        add(statusLabel, BorderLayout.SOUTH);
    }
    
    /**
     * تنفيذ التحليل الشامل
     */
    private void performAnalysis() {
        SwingUtilities.invokeLater(() -> {
            statusLabel.setText("جاري تحليل شجرة الأنظمة...");
            
            try {
                // تحميل البيانات
                loadSystemTreeData();
                
                // تحليل البيانات
                analyzeData();
                
                // إنشاء التقرير
                generateReport();
                
                // تحديث الجداول
                updateTables();
                
                statusLabel.setText("تم إكمال التحليل بنجاح - العقد: " + allNodes.size());
                
            } catch (Exception e) {
                statusLabel.setText("خطأ في التحليل: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    /**
     * تحميل بيانات شجرة الأنظمة
     */
    private void loadSystemTreeData() throws SQLException {
        if (dbConnection == null) {
            throw new SQLException("لا يوجد اتصال بقاعدة البيانات");
        }
        
        allNodes.clear();
        
        String sql = """
            SELECT 
                TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION,
                NODE_TYPE, WINDOW_CLASS, ICON_PATH, DISPLAY_ORDER, TREE_LEVEL,
                IS_ACTIVE, IS_VISIBLE, ACCESS_PERMISSIONS, CREATED_DATE, CREATED_BY,
                LAST_UPDATED, UPDATED_BY, VERSION_NUMBER
            FROM ERP_SYSTEM_TREE
            ORDER BY TREE_LEVEL, DISPLAY_ORDER
        """;
        
        try (PreparedStatement stmt = dbConnection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                SystemTreeNode node = new SystemTreeNode();
                node.treeId = rs.getInt("TREE_ID");
                node.parentId = rs.getObject("PARENT_ID") != null ? rs.getInt("PARENT_ID") : null;
                node.nodeNameAr = rs.getString("NODE_NAME_AR");
                node.nodeNameEn = rs.getString("NODE_NAME_EN");
                node.nodeDescription = rs.getString("NODE_DESCRIPTION");
                node.nodeType = rs.getString("NODE_TYPE");
                node.windowClass = rs.getString("WINDOW_CLASS");
                node.iconPath = rs.getString("ICON_PATH");
                node.displayOrder = rs.getInt("DISPLAY_ORDER");
                node.treeLevel = rs.getInt("TREE_LEVEL");
                node.isActive = "Y".equals(rs.getString("IS_ACTIVE"));
                node.isVisible = "Y".equals(rs.getString("IS_VISIBLE"));
                node.accessPermissions = rs.getString("ACCESS_PERMISSIONS");
                node.createdDate = rs.getTimestamp("CREATED_DATE");
                node.createdBy = rs.getString("CREATED_BY");
                node.lastUpdated = rs.getTimestamp("LAST_UPDATED");
                node.updatedBy = rs.getString("UPDATED_BY");
                node.versionNumber = rs.getInt("VERSION_NUMBER");
                
                allNodes.add(node);
            }
        }
        
        System.out.println("✅ تم تحميل " + allNodes.size() + " عقدة من شجرة الأنظمة");
    }
    
    /**
     * تحليل البيانات
     */
    private void analyzeData() {
        analysisResults.clear();
        nodeTypeStats.clear();
        levelStats.clear();
        
        // إحصائيات أنواع العقد
        for (SystemTreeNode node : allNodes) {
            nodeTypeStats.put(node.nodeType, nodeTypeStats.getOrDefault(node.nodeType, 0) + 1);
            levelStats.put(node.treeLevel, levelStats.getOrDefault(node.treeLevel, 0) + 1);
        }
        
        // فحص المشاكل
        checkForIssues();
        
        // تحليل الهيكل
        analyzeStructure();
    }
    
    /**
     * فحص المشاكل في الشجرة
     */
    private void checkForIssues() {
        analysisResults.add("🔍 فحص المشاكل في شجرة الأنظمة:");
        analysisResults.add("=====================================");
        
        int issuesFound = 0;
        
        // فحص العقد اليتيمة (بدون أب صحيح)
        for (SystemTreeNode node : allNodes) {
            if (node.parentId != null) {
                boolean parentExists = allNodes.stream()
                    .anyMatch(n -> n.treeId == node.parentId);
                if (!parentExists) {
                    analysisResults.add("❌ عقدة يتيمة: " + node.nodeNameAr + " (ID: " + node.treeId + ")");
                    issuesFound++;
                }
            }
        }
        
        // فحص العقد المكررة
        Map<String, Integer> nameCount = new HashMap<>();
        for (SystemTreeNode node : allNodes) {
            nameCount.put(node.nodeNameAr, nameCount.getOrDefault(node.nodeNameAr, 0) + 1);
        }
        
        for (Map.Entry<String, Integer> entry : nameCount.entrySet()) {
            if (entry.getValue() > 1) {
                analysisResults.add("⚠️ اسم مكرر: " + entry.getKey() + " (" + entry.getValue() + " مرات)");
                issuesFound++;
            }
        }
        
        // فحص النوافذ المفقودة
        for (SystemTreeNode node : allNodes) {
            if ("WINDOW".equals(node.nodeType) && 
                (node.windowClass == null || node.windowClass.trim().isEmpty())) {
                analysisResults.add("❌ نافذة بدون كلاس: " + node.nodeNameAr);
                issuesFound++;
            }
        }
        
        if (issuesFound == 0) {
            analysisResults.add("✅ لم يتم العثور على مشاكل في الهيكل");
        } else {
            analysisResults.add("⚠️ تم العثور على " + issuesFound + " مشكلة");
        }
        
        analysisResults.add("");
    }
    
    /**
     * تحليل هيكل الشجرة
     */
    private void analyzeStructure() {
        analysisResults.add("🌳 تحليل هيكل الشجرة:");
        analysisResults.add("====================");
        
        // إحصائيات عامة
        analysisResults.add("📊 الإحصائيات العامة:");
        analysisResults.add("• إجمالي العقد: " + allNodes.size());
        analysisResults.add("• العقد النشطة: " + allNodes.stream().mapToInt(n -> n.isActive ? 1 : 0).sum());
        analysisResults.add("• العقد المرئية: " + allNodes.stream().mapToInt(n -> n.isVisible ? 1 : 0).sum());
        
        // إحصائيات أنواع العقد
        analysisResults.add("\n📋 توزيع أنواع العقد:");
        for (Map.Entry<String, Integer> entry : nodeTypeStats.entrySet()) {
            analysisResults.add("• " + entry.getKey() + ": " + entry.getValue());
        }
        
        // إحصائيات المستويات
        analysisResults.add("\n📏 توزيع المستويات:");
        for (Map.Entry<Integer, Integer> entry : levelStats.entrySet()) {
            analysisResults.add("• المستوى " + entry.getKey() + ": " + entry.getValue() + " عقدة");
        }
        
        analysisResults.add("");
    }

    /**
     * إنشاء التقرير الشامل
     */
    private void generateReport() {
        StringBuilder report = new StringBuilder();

        // رأس التقرير
        report.append("📊 تقرير تحليل شجرة الأنظمة\n");
        report.append("System Tree Analysis Report\n");
        report.append("==========================================\n");
        report.append("تاريخ التقرير: ").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("\n");
        report.append("قاعدة البيانات: Oracle - SHIP_ERP\n");
        report.append("الجدول: ERP_SYSTEM_TREE\n\n");

        // إضافة نتائج التحليل
        for (String result : analysisResults) {
            report.append(result).append("\n");
        }

        // تفاصيل العقد
        report.append("📋 تفاصيل جميع العقد:\n");
        report.append("===================\n");

        for (SystemTreeNode node : allNodes) {
            String indent = "  ".repeat(node.treeLevel);
            report.append(String.format("%s[%d] %s (%s)\n",
                indent, node.treeId, node.nodeNameAr, node.nodeType));

            if (node.windowClass != null && !node.windowClass.trim().isEmpty()) {
                report.append(String.format("%s    الكلاس: %s\n", indent, node.windowClass));
            }

            if (node.nodeDescription != null && !node.nodeDescription.trim().isEmpty()) {
                report.append(String.format("%s    الوصف: %s\n", indent, node.nodeDescription));
            }

            report.append(String.format("%s    الحالة: %s | المرئية: %s | الترتيب: %d\n",
                indent,
                node.isActive ? "نشط" : "غير نشط",
                node.isVisible ? "مرئي" : "مخفي",
                node.displayOrder));

            if (node.createdDate != null) {
                report.append(String.format("%s    تاريخ الإنشاء: %s بواسطة: %s\n",
                    indent,
                    new SimpleDateFormat("yyyy-MM-dd").format(node.createdDate),
                    node.createdBy != null ? node.createdBy : "غير محدد"));
            }

            report.append("\n");
        }

        // توصيات التحسين
        report.append("💡 توصيات التحسين:\n");
        report.append("==================\n");
        generateRecommendations(report);

        reportTextArea.setText(report.toString());
        reportTextArea.setCaretPosition(0);
    }

    /**
     * إنشاء توصيات التحسين
     */
    private void generateRecommendations(StringBuilder report) {
        // فحص العقد غير المستخدمة
        long inactiveNodes = allNodes.stream().filter(n -> !n.isActive).count();
        if (inactiveNodes > 0) {
            report.append("• يوجد ").append(inactiveNodes).append(" عقدة غير نشطة - يُنصح بمراجعتها\n");
        }

        // فحص العقد بدون وصف
        long noDescriptionNodes = allNodes.stream()
            .filter(n -> n.nodeDescription == null || n.nodeDescription.trim().isEmpty())
            .count();
        if (noDescriptionNodes > 0) {
            report.append("• يوجد ").append(noDescriptionNodes).append(" عقدة بدون وصف - يُنصح بإضافة أوصاف\n");
        }

        // فحص العقد بدون أيقونات
        long noIconNodes = allNodes.stream()
            .filter(n -> n.iconPath == null || n.iconPath.trim().isEmpty())
            .count();
        if (noIconNodes > 0) {
            report.append("• يوجد ").append(noIconNodes).append(" عقدة بدون أيقونة - يُنصح بإضافة أيقونات\n");
        }

        // فحص التوزيع
        if (nodeTypeStats.getOrDefault("WINDOW", 0) < 5) {
            report.append("• عدد النوافذ قليل - يُنصح بإضافة المزيد من النوافذ\n");
        }

        if (levelStats.size() > 4) {
            report.append("• الشجرة عميقة جداً (").append(levelStats.size()).append(" مستويات) - يُنصح بتبسيط الهيكل\n");
        }

        report.append("• يُنصح بإجراء نسخ احتياطي دوري للجدول\n");
        report.append("• يُنصح بمراجعة الصلاحيات بانتظام\n");
    }

    /**
     * تحديث الجداول
     */
    private void updateTables() {
        // تحديث جدول الشجرة
        TreeTableModel treeModel = new TreeTableModel(allNodes);
        treeTable.setModel(treeModel);

        // تحديث جدول الإحصائيات
        StatisticsTableModel statsModel = new StatisticsTableModel(nodeTypeStats, levelStats);
        statisticsTable.setModel(statsModel);
    }

    /**
     * تصدير التقرير
     */
    private void exportReport() {
        try {
            String fileName = "SystemTreeReport_" +
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".txt";

            try (FileWriter writer = new FileWriter(fileName)) {
                writer.write(reportTextArea.getText());
            }

            JOptionPane.showMessageDialog(this,
                "تم تصدير التقرير بنجاح:\n" + fileName,
                "تصدير ناجح", JOptionPane.INFORMATION_MESSAGE);

        } catch (IOException e) {
            JOptionPane.showMessageDialog(this,
                "خطأ في تصدير التقرير:\n" + e.getMessage(),
                "خطأ في التصدير", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * إصلاح المشاكل المكتشفة
     */
    private void fixDetectedIssues() {
        int result = JOptionPane.showConfirmDialog(this,
            "هل تريد إصلاح المشاكل المكتشفة تلقائياً؟\n" +
            "سيتم:\n" +
            "• إصلاح العقد اليتيمة\n" +
            "• تحديث الترتيب\n" +
            "• إضافة أوصاف مفقودة",
            "تأكيد الإصلاح", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            try {
                performAutomaticFixes();
                JOptionPane.showMessageDialog(this,
                    "تم إصلاح المشاكل بنجاح!\nيُنصح بإعادة تشغيل التحليل.",
                    "إصلاح ناجح", JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(this,
                    "خطأ في الإصلاح:\n" + e.getMessage(),
                    "خطأ في الإصلاح", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * تنفيذ الإصلاحات التلقائية
     */
    private void performAutomaticFixes() throws SQLException {
        if (dbConnection == null) return;

        // إصلاح العقد اليتيمة - ربطها بالجذر
        String fixOrphansSQL = """
            UPDATE ERP_SYSTEM_TREE
            SET PARENT_ID = 1, TREE_LEVEL = 1
            WHERE PARENT_ID IS NOT NULL
            AND PARENT_ID NOT IN (SELECT TREE_ID FROM ERP_SYSTEM_TREE)
        """;

        try (PreparedStatement stmt = dbConnection.prepareStatement(fixOrphansSQL)) {
            int updated = stmt.executeUpdate();
            System.out.println("تم إصلاح " + updated + " عقدة يتيمة");
        }

        // تحديث المستويات
        String updateLevelsSQL = """
            UPDATE ERP_SYSTEM_TREE
            SET TREE_LEVEL = (
                SELECT LEVEL - 1
                FROM ERP_SYSTEM_TREE t2
                WHERE t2.TREE_ID = ERP_SYSTEM_TREE.TREE_ID
                START WITH t2.PARENT_ID IS NULL
                CONNECT BY PRIOR t2.TREE_ID = t2.PARENT_ID
            )
        """;

        try (PreparedStatement stmt = dbConnection.prepareStatement(updateLevelsSQL)) {
            stmt.executeUpdate();
            System.out.println("تم تحديث مستويات الشجرة");
        }

        dbConnection.commit();
    }

    // ===== الكلاسات المساعدة =====

    /**
     * كلاس بيانات عقدة الشجرة
     */
    public static class SystemTreeNode {
        public int treeId;
        public Integer parentId;
        public String nodeNameAr;
        public String nodeNameEn;
        public String nodeDescription;
        public String nodeType;
        public String windowClass;
        public String iconPath;
        public int displayOrder;
        public int treeLevel;
        public boolean isActive;
        public boolean isVisible;
        public String accessPermissions;
        public Date createdDate;
        public String createdBy;
        public Date lastUpdated;
        public String updatedBy;
        public int versionNumber;
    }

    /**
     * نموذج جدول الشجرة
     */
    private class TreeTableModel extends AbstractTableModel {
        private final String[] columnNames = {
            "المعرف", "الأب", "الاسم العربي", "النوع", "الكلاس", "المستوى", "الترتيب", "نشط", "مرئي"
        };
        private final List<SystemTreeNode> nodes;

        public TreeTableModel(List<SystemTreeNode> nodes) {
            this.nodes = nodes;
        }

        @Override
        public int getRowCount() {
            return nodes.size();
        }

        @Override
        public int getColumnCount() {
            return columnNames.length;
        }

        @Override
        public String getColumnName(int column) {
            return columnNames[column];
        }

        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            SystemTreeNode node = nodes.get(rowIndex);
            return switch (columnIndex) {
                case 0 -> node.treeId;
                case 1 -> node.parentId != null ? node.parentId : "جذر";
                case 2 -> node.nodeNameAr;
                case 3 -> node.nodeType;
                case 4 -> node.windowClass != null ? node.windowClass : "-";
                case 5 -> node.treeLevel;
                case 6 -> node.displayOrder;
                case 7 -> node.isActive ? "✓" : "✗";
                case 8 -> node.isVisible ? "✓" : "✗";
                default -> "";
            };
        }
    }

    /**
     * نموذج جدول الإحصائيات
     */
    private class StatisticsTableModel extends AbstractTableModel {
        private final String[] columnNames = {"النوع/المستوى", "العدد", "النسبة"};
        private final List<StatRow> rows;

        public StatisticsTableModel(Map<String, Integer> nodeTypeStats, Map<Integer, Integer> levelStats) {
            rows = new ArrayList<>();

            // إضافة إحصائيات الأنواع
            rows.add(new StatRow("=== أنواع العقد ===", "", ""));
            int totalNodes = allNodes.size();
            for (Map.Entry<String, Integer> entry : nodeTypeStats.entrySet()) {
                double percentage = (entry.getValue() * 100.0) / totalNodes;
                rows.add(new StatRow(entry.getKey(), entry.getValue().toString(),
                    String.format("%.1f%%", percentage)));
            }

            // إضافة إحصائيات المستويات
            rows.add(new StatRow("", "", ""));
            rows.add(new StatRow("=== المستويات ===", "", ""));
            for (Map.Entry<Integer, Integer> entry : levelStats.entrySet()) {
                double percentage = (entry.getValue() * 100.0) / totalNodes;
                rows.add(new StatRow("المستوى " + entry.getKey(), entry.getValue().toString(),
                    String.format("%.1f%%", percentage)));
            }
        }

        @Override
        public int getRowCount() {
            return rows.size();
        }

        @Override
        public int getColumnCount() {
            return columnNames.length;
        }

        @Override
        public String getColumnName(int column) {
            return columnNames[column];
        }

        @Override
        public Object getValueAt(int rowIndex, int columnIndex) {
            StatRow row = rows.get(rowIndex);
            return switch (columnIndex) {
                case 0 -> row.name;
                case 1 -> row.count;
                case 2 -> row.percentage;
                default -> "";
            };
        }

        private class StatRow {
            String name, count, percentage;

            StatRow(String name, String count, String percentage) {
                this.name = name;
                this.count = count;
                this.percentage = percentage;
            }
        }
    }

    /**
     * نقطة الدخول الرئيسية للاختبار
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel("com.formdev.flatlaf.FlatDarkLaf");
            } catch (Exception e) {
                System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
            }

            System.out.println("🚀 تشغيل محلل شجرة الأنظمة");
            new SystemTreeAnalyzer().setVisible(true);
        });
    }
}
