import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Vector;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;
import javax.swing.UIManager;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة الموردين الشاملة
 * Comprehensive Suppliers Management Window
 * 
 * تتضمن:
 * - إدارة بيانات الموردين الأساسية
 * - إدارة جهات الاتصال
 * - إدارة العناوين
 * - إدارة الفئات
 * - تقييم الموردين
 * - المرفقات والوثائق
 * - التقارير والإحصائيات
 */
public class SuppliersManagementWindow extends JFrame {
    
    private static final String DB_URL = "*************************************";
    private static final String DB_USER = "ship_erp";
    private static final String DB_PASSWORD = "ship_erp_password";
    
    // المكونات الرئيسية
    private Font arabicFont;
    private JTabbedPane mainTabbedPane;
    
    // تبويب الموردين الرئيسي (بنفس بنية V_DETAILS)
    private JTable suppliersTable;
    private DefaultTableModel suppliersTableModel;
    private JTextField codeField, nameField, nameEnField, descriptionField;
    private JTextField contactPersonField, phoneField, mobileField, emailField;
    private JTextField addressLine1Field, addressLine2Field, cityField, stateField;
    private JTextField postalCodeField, taxNumberField, commercialRegisterField;
    private JTextField referenceNumberField, tagsField, externalIdField;
    private JComboBox<String> typeCombo, categoryCombo, statusCombo, countryCombo;
    private JComboBox<String> isActiveCombo, isApprovedCombo, priorityCombo;
    private JTextArea notesArea, internalNotesArea;
    
    // تبويب جهات الاتصال
    private JTable contactsTable;
    private DefaultTableModel contactsTableModel;
    private JTextField contactNameField, contactTitleField, contactDepartmentField;
    private JTextField contactPhoneField, contactMobileField, contactEmailField;
    private JCheckBox isPrimaryContactCheckBox;
    
    // تبويب العناوين
    private JTable addressesTable;
    private DefaultTableModel addressesTableModel;
    private JComboBox<String> addressTypeCombo;
    private JTextField addrLine1Field, addrLine2Field, addrCityField;
    private JTextField addrStateField, addrPostalField, addrCountryField;
    private JCheckBox isPrimaryAddressCheckBox;
    
    // تبويب الفئات
    private JTable categoriesTable;
    private DefaultTableModel categoriesTableModel;
    private JComboBox<String> availableCategoriesCombo;
    
    // تبويب التقييم
    private JTable evaluationsTable;
    private DefaultTableModel evaluationsTableModel;
    private JComboBox<String> qualityRatingCombo, deliveryRatingCombo;
    private JComboBox<String> serviceRatingCombo, priceRatingCombo;
    private JTextArea evaluationCommentsArea;
    
    // متغيرات العمل
    private String selectedSupplierCode = "";
    
    public SuppliersManagementWindow() {
        // تطبيق مظهر النافذة ليتوافق مع التطبيق الرئيسي
        applyCurrentLookAndFeel();
        
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        initializeWindow();
        createTabbedInterface();
        loadInitialData();
        
        // تحديث المظهر بعد إنشاء المكونات
        SwingUtilities.invokeLater(() -> {
            SwingUtilities.updateComponentTreeUI(this);
            repaint();
        });
    }
    
    /**
     * تطبيق مظهر النافذة الحالي من التطبيق الرئيسي
     */
    private void applyCurrentLookAndFeel() {
        try {
            // الحصول على المظهر الحالي
            String currentLAF = UIManager.getLookAndFeel().getClass().getName();
            System.out.println("🎨 تطبيق المظهر الحالي على نافذة الموردين: " + currentLAF);
            
            // إعداد خصائص إضافية للمظهر
            setupLookAndFeelProperties();
            
        } catch (Exception e) {
            System.err.println("⚠️ تحذير: لم يتم تطبيق المظهر بشكل كامل: " + e.getMessage());
        }
    }
    
    /**
     * إعداد خصائص المظهر الإضافية
     */
    private void setupLookAndFeelProperties() {
        try {
            // إعدادات للمظهر المظلم
            if (isDarkTheme()) {
                setupDarkThemeProperties();
            } else {
                setupLightThemeProperties();
            }
            
            // إعدادات عامة للخطوط العربية
            setupArabicFontProperties();
            
        } catch (Exception e) {
            System.err.println("⚠️ تحذير في إعداد خصائص المظهر: " + e.getMessage());
        }
    }
    
    /**
     * فحص ما إذا كان المظهر الحالي مظلم
     */
    private boolean isDarkTheme() {
        String lafName = UIManager.getLookAndFeel().getClass().getName().toLowerCase();
        return lafName.contains("dark") || lafName.contains("flatlaf");
    }
    
    /**
     * إعداد خصائص المظهر المظلم
     */
    private void setupDarkThemeProperties() {
        // ألوان المظهر المظلم
        UIManager.put("Panel.background", new java.awt.Color(60, 63, 65));
        UIManager.put("TextField.background", new java.awt.Color(69, 73, 74));
        UIManager.put("TextField.foreground", new java.awt.Color(187, 187, 187));
        UIManager.put("ComboBox.background", new java.awt.Color(69, 73, 74));
        UIManager.put("ComboBox.foreground", new java.awt.Color(187, 187, 187));
        UIManager.put("Table.background", new java.awt.Color(69, 73, 74));
        UIManager.put("Table.foreground", new java.awt.Color(187, 187, 187));
        UIManager.put("Table.selectionBackground", new java.awt.Color(75, 110, 175));
        UIManager.put("Button.background", new java.awt.Color(75, 110, 175));
        UIManager.put("Button.foreground", new java.awt.Color(255, 255, 255));
    }
    
    /**
     * إعداد خصائص المظهر الفاتح
     */
    private void setupLightThemeProperties() {
        // ألوان المظهر الفاتح
        UIManager.put("Panel.background", new java.awt.Color(240, 240, 240));
        UIManager.put("TextField.background", new java.awt.Color(255, 255, 255));
        UIManager.put("TextField.foreground", new java.awt.Color(0, 0, 0));
        UIManager.put("ComboBox.background", new java.awt.Color(255, 255, 255));
        UIManager.put("ComboBox.foreground", new java.awt.Color(0, 0, 0));
        UIManager.put("Table.background", new java.awt.Color(255, 255, 255));
        UIManager.put("Table.foreground", new java.awt.Color(0, 0, 0));
        UIManager.put("Table.selectionBackground", new java.awt.Color(184, 207, 229));
        UIManager.put("Button.background", new java.awt.Color(225, 225, 225));
        UIManager.put("Button.foreground", new java.awt.Color(0, 0, 0));
    }
    
    /**
     * إعداد خصائص الخطوط العربية
     */
    private void setupArabicFontProperties() {
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);
        
        // تطبيق الخطوط العربية على جميع المكونات
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("ComboBox.font", arabicFont);
        UIManager.put("Table.font", arabicFont);
        UIManager.put("TableHeader.font", arabicBoldFont);
        UIManager.put("TabbedPane.font", arabicBoldFont);
        UIManager.put("CheckBox.font", arabicFont);
        UIManager.put("RadioButton.font", arabicFont);
        UIManager.put("TextArea.font", arabicFont);
    }
    
    private void initializeWindow() {
        setTitle("🏢 إدارة الموردين - Suppliers Management");
        setSize(1400, 900);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تطبيق المظهر الموحد
        try {
            FinalThemeManager.initializeDefaultTheme();
        } catch (Exception e) {
            System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
        }
        
        System.out.println("🏢 تم فتح نافذة إدارة الموردين الشاملة");
    }
    
    private void createTabbedInterface() {
        mainTabbedPane = new JTabbedPane();
        mainTabbedPane.setFont(new Font("Tahoma", Font.BOLD, 12));
        mainTabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إضافة التبويبات
        mainTabbedPane.addTab("📋 الموردين الرئيسي", createSuppliersMainTab());
        mainTabbedPane.addTab("👥 جهات الاتصال", createContactsTab());
        mainTabbedPane.addTab("📍 العناوين", createAddressesTab());
        mainTabbedPane.addTab("📂 الفئات", createCategoriesTab());
        mainTabbedPane.addTab("⭐ التقييم", createEvaluationsTab());
        mainTabbedPane.addTab("📊 التقارير", createReportsTab());
        
        add(mainTabbedPane, BorderLayout.CENTER);
    }
    
    private JPanel createSuppliersMainTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إنشاء جدول الموردين
        createSuppliersTable();
        JScrollPane tableScrollPane = new JScrollPane(suppliersTable);
        
        // إنشاء نموذج إدخال البيانات
        JPanel formPanel = createSuppliersFormPanel();
        
        // إنشاء لوحة الأزرار
        JPanel buttonsPanel = createSuppliersButtonsPanel();
        
        // تجميع المكونات
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.add(tableScrollPane, BorderLayout.CENTER);
        topPanel.add(buttonsPanel, BorderLayout.SOUTH);
        
        panel.add(topPanel, BorderLayout.CENTER);
        panel.add(formPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private void createSuppliersTable() {
        // إنشاء نموذج الجدول (بدون عمود ID)
        String[] columnNames = {
            "الكود", "الاسم", "الاسم (إنجليزي)", "النوع", "الوصف",
            "الفئة", "الحالة", "الأولوية", "جهة الاتصال", "الهاتف",
            "البريد الإلكتروني", "المدينة", "الدولة", "نشط", "معتمد"
        };
        
        suppliersTableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        suppliersTable = new JTable(suppliersTableModel);
        suppliersTable.setFont(arabicFont);
        suppliersTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        suppliersTable.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 12));
        suppliersTable.getTableHeader().setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        suppliersTable.setRowHeight(25);
        
        // إضافة مستمع للنقر على الجدول لتحميل البيانات
        suppliersTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                if (evt.getClickCount() == 1) {
                    loadSelectedSupplierData();
                }
            }
        });

        // إضافة listener لاختيار الصف
        suppliersTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedSupplierData();
            }
        });
    }

    private JPanel createSuppliersFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder("بيانات المورد"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // الصف الأول - الحقول الأساسية (التسميات على اليسار، الحقول على اليمين)
        gbc.gridy = 0;

        // كود المورد
        gbc.gridx = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("كود المورد:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.2;
        codeField = new JTextField();
        codeField.setFont(arabicFont);
        panel.add(codeField, gbc);

        // اسم المورد
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("اسم المورد:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        nameField = new JTextField();
        nameField.setFont(arabicFont);
        nameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(nameField, gbc);

        // الاسم الإنجليزي
        gbc.gridx = 4; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الاسم الإنجليزي:"), gbc);
        gbc.gridx = 5; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        nameEnField = new JTextField();
        nameEnField.setFont(arabicFont);
        nameEnField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        panel.add(nameEnField, gbc);

        // الصف الثاني - النوع والوصف والفئة
        gbc.gridy = 1;

        // نوع المورد
        gbc.gridx = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("نوع المورد:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.2;
        typeCombo = new JComboBox<>(new String[]{"VENDOR", "CONTRACTOR", "SERVICE_PROVIDER", "MANUFACTURER"});
        typeCombo.setFont(arabicFont);
        panel.add(typeCombo, gbc);

        // الوصف
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الوصف:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        descriptionField = new JTextField();
        descriptionField.setFont(arabicFont);
        descriptionField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(descriptionField, gbc);

        // الفئة
        gbc.gridx = 4; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الفئة:"), gbc);
        gbc.gridx = 5; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        categoryCombo = new JComboBox<>(new String[]{"RAW_MATERIALS", "IT_SERVICES", "EQUIPMENT", "CONSULTING", "OTHER"});
        categoryCombo.setFont(arabicFont);
        panel.add(categoryCombo, gbc);

        // الصف الثالث - الحالة والأولوية وجهة الاتصال
        gbc.gridy = 2;

        // الحالة
        gbc.gridx = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الحالة:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.2;
        statusCombo = new JComboBox<>(new String[]{"ACTIVE", "INACTIVE", "PENDING", "SUSPENDED"});
        statusCombo.setFont(arabicFont);
        panel.add(statusCombo, gbc);

        // الأولوية
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الأولوية:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        priorityCombo = new JComboBox<>(new String[]{"1", "2", "3", "4", "5"});
        priorityCombo.setFont(arabicFont);
        panel.add(priorityCombo, gbc);

        // جهة الاتصال
        gbc.gridx = 4; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("جهة الاتصال:"), gbc);
        gbc.gridx = 5; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        contactPersonField = new JTextField();
        contactPersonField.setFont(arabicFont);
        contactPersonField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(contactPersonField, gbc);

        // الصف الرابع - معلومات الاتصال
        gbc.gridy = 3;

        // الهاتف
        gbc.gridx = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الهاتف:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.2;
        phoneField = new JTextField();
        phoneField.setFont(arabicFont);
        phoneField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        panel.add(phoneField, gbc);

        // الجوال
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الجوال:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        mobileField = new JTextField();
        mobileField.setFont(arabicFont);
        mobileField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        panel.add(mobileField, gbc);

        // البريد الإلكتروني
        gbc.gridx = 4; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("البريد الإلكتروني:"), gbc);
        gbc.gridx = 5; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        emailField = new JTextField();
        emailField.setFont(arabicFont);
        emailField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        panel.add(emailField, gbc);

        // الصف الخامس - معلومات العنوان
        gbc.gridy = 4;

        // العنوان الأول
        gbc.gridx = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("العنوان الأول:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.2;
        addressLine1Field = new JTextField();
        addressLine1Field.setFont(arabicFont);
        addressLine1Field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(addressLine1Field, gbc);

        // المدينة
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("المدينة:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        cityField = new JTextField();
        cityField.setFont(arabicFont);
        cityField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(cityField, gbc);

        // الدولة
        gbc.gridx = 4; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الدولة:"), gbc);
        gbc.gridx = 5; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        countryCombo = new JComboBox<>(new String[]{"Saudi Arabia", "UAE", "Kuwait", "Qatar", "Bahrain", "Oman", "Other"});
        countryCombo.setFont(arabicFont);
        panel.add(countryCombo, gbc);

        // الصف السادس - المعلومات المالية والضريبية
        gbc.gridy = 5;

        // الرقم الضريبي
        gbc.gridx = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الرقم الضريبي:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.2;
        taxNumberField = new JTextField();
        taxNumberField.setFont(arabicFont);
        taxNumberField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        panel.add(taxNumberField, gbc);

        // السجل التجاري
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("السجل التجاري:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        commercialRegisterField = new JTextField();
        commercialRegisterField.setFont(arabicFont);
        commercialRegisterField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        panel.add(commercialRegisterField, gbc);

        // العلامات
        gbc.gridx = 4; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("العلامات:"), gbc);
        gbc.gridx = 5; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        tagsField = new JTextField();
        tagsField.setFont(arabicFont);
        tagsField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(tagsField, gbc);

        // الصف السابع - حالة المورد
        gbc.gridy = 6;

        // نشط
        gbc.gridx = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("نشط:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.2;
        isActiveCombo = new JComboBox<>(new String[]{"نعم", "لا"});
        isActiveCombo.setFont(arabicFont);
        panel.add(isActiveCombo, gbc);

        // معتمد
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("معتمد:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        isApprovedCombo = new JComboBox<>(new String[]{"نعم", "لا"});
        isApprovedCombo.setFont(arabicFont);
        panel.add(isApprovedCombo, gbc);

        // الصف الثامن - منطقة الملاحظات
        gbc.gridy = 7;
        gbc.gridx = 5; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الملاحظات:"), gbc);
        gbc.gridx = 0; gbc.gridwidth = 5; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 0.3;
        notesArea = new JTextArea(3, 50);
        notesArea.setFont(arabicFont);
        notesArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        notesArea.setLineWrap(true);
        notesArea.setWrapStyleWord(true);
        JScrollPane notesScrollPane = new JScrollPane(notesArea);
        notesScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(notesScrollPane, gbc);

        // إعادة تعيين gridwidth للصفوف التالية
        gbc.gridwidth = 1;
        gbc.weighty = 0;



        return panel;
    }

    private JPanel createSuppliersButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addButton = new JButton("إضافة مورد");
        addButton.setFont(arabicFont);
        addButton.addActionListener(e -> addSupplier());

        JButton updateButton = new JButton("تعديل مورد");
        updateButton.setFont(arabicFont);
        updateButton.addActionListener(e -> updateSupplier());

        JButton deleteButton = new JButton("حذف مورد");
        deleteButton.setFont(arabicFont);
        deleteButton.addActionListener(e -> deleteSupplier());

        JButton clearButton = new JButton("مسح الحقول");
        clearButton.setFont(arabicFont);
        clearButton.addActionListener(e -> clearSupplierFields());

        JButton refreshButton = new JButton("تحديث البيانات");
        refreshButton.setFont(arabicFont);
        refreshButton.addActionListener(e -> loadSuppliersData());

        JButton importButton = new JButton("استيراد من IAS20251");
        importButton.setFont(arabicFont);
        importButton.addActionListener(e -> importFromVDetails());

        panel.add(addButton);
        panel.add(updateButton);
        panel.add(deleteButton);
        panel.add(clearButton);
        panel.add(refreshButton);
        panel.add(importButton);

        return panel;
    }

    private JPanel createContactsTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إنشاء جدول جهات الاتصال
        createContactsTable();
        JScrollPane tableScrollPane = new JScrollPane(contactsTable);

        // إنشاء نموذج إدخال جهات الاتصال
        JPanel formPanel = createContactsFormPanel();

        // إنشاء لوحة أزرار جهات الاتصال
        JPanel buttonsPanel = createContactsButtonsPanel();

        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.add(tableScrollPane, BorderLayout.CENTER);
        topPanel.add(buttonsPanel, BorderLayout.SOUTH);

        panel.add(topPanel, BorderLayout.CENTER);
        panel.add(formPanel, BorderLayout.SOUTH);

        return panel;
    }

    private void createContactsTable() {
        String[] columnNames = {
            "المعرف", "اسم جهة الاتصال", "المسمى الوظيفي", "القسم",
            "الهاتف", "الجوال", "البريد الإلكتروني", "رئيسي", "نشط"
        };

        contactsTableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        contactsTable = new JTable(contactsTableModel);
        contactsTable.setFont(arabicFont);
        contactsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        contactsTable.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 12));
        contactsTable.getTableHeader().setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        contactsTable.setRowHeight(25);

        // إضافة listener لاختيار جهة الاتصال
        contactsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedContactData();
            }
        });
    }

    private void loadSelectedContactData() {
        int selectedRow = contactsTable.getSelectedRow();
        if (selectedRow != -1) {
            try {
                contactNameField.setText(contactsTableModel.getValueAt(selectedRow, 1).toString());

                Object title = contactsTableModel.getValueAt(selectedRow, 2);
                contactTitleField.setText(title != null ? title.toString() : "");

                Object department = contactsTableModel.getValueAt(selectedRow, 3);
                contactDepartmentField.setText(department != null ? department.toString() : "");

                Object phone = contactsTableModel.getValueAt(selectedRow, 4);
                contactPhoneField.setText(phone != null ? phone.toString() : "");

                Object mobile = contactsTableModel.getValueAt(selectedRow, 5);
                contactMobileField.setText(mobile != null ? mobile.toString() : "");

                Object email = contactsTableModel.getValueAt(selectedRow, 6);
                contactEmailField.setText(email != null ? email.toString() : "");

                Object isPrimary = contactsTableModel.getValueAt(selectedRow, 7);
                isPrimaryContactCheckBox.setSelected("نعم".equals(isPrimary));

            } catch (Exception ex) {
                JOptionPane.showMessageDialog(this, "خطأ في تحميل بيانات جهة الاتصال: " + ex.getMessage(),
                                            "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private JPanel createContactsFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder("بيانات جهة الاتصال"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // الصف الأول
        gbc.gridx = 3; gbc.gridy = 0;
        panel.add(new JLabel("اسم جهة الاتصال:"), gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        contactNameField = new JTextField();
        contactNameField.setFont(arabicFont);
        contactNameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(contactNameField, gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("المسمى الوظيفي:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        contactTitleField = new JTextField();
        contactTitleField.setFont(arabicFont);
        contactTitleField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(contactTitleField, gbc);

        // الصف الثاني
        gbc.gridy = 1;
        gbc.gridx = 3; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("القسم:"), gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        contactDepartmentField = new JTextField();
        contactDepartmentField.setFont(arabicFont);
        contactDepartmentField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(contactDepartmentField, gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الهاتف:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        contactPhoneField = new JTextField();
        contactPhoneField.setFont(arabicFont);
        contactPhoneField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        panel.add(contactPhoneField, gbc);

        // الصف الثالث
        gbc.gridy = 2;
        gbc.gridx = 3; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("الجوال:"), gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        contactMobileField = new JTextField();
        contactMobileField.setFont(arabicFont);
        contactMobileField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        panel.add(contactMobileField, gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("البريد الإلكتروني:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        contactEmailField = new JTextField();
        contactEmailField.setFont(arabicFont);
        contactEmailField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        panel.add(contactEmailField, gbc);

        // الصف الرابع
        gbc.gridy = 3;
        gbc.gridx = 3; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        isPrimaryContactCheckBox = new JCheckBox("جهة اتصال رئيسية");
        isPrimaryContactCheckBox.setFont(arabicFont);
        isPrimaryContactCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(isPrimaryContactCheckBox, gbc);

        return panel;
    }

    private JPanel createContactsButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addContactButton = new JButton("إضافة جهة اتصال");
        addContactButton.setFont(arabicFont);
        addContactButton.addActionListener(e -> addContact());

        JButton updateContactButton = new JButton("تعديل جهة اتصال");
        updateContactButton.setFont(arabicFont);
        updateContactButton.addActionListener(e -> updateContact());

        JButton deleteContactButton = new JButton("حذف جهة اتصال");
        deleteContactButton.setFont(arabicFont);
        deleteContactButton.addActionListener(e -> deleteContact());

        panel.add(addContactButton);
        panel.add(updateContactButton);
        panel.add(deleteContactButton);

        return panel;
    }

    // دوال إدارة جهات الاتصال
    private void loadContacts() {
        contactsTableModel.setRowCount(0);

        if (selectedSupplierCode == null || selectedSupplierCode.isEmpty()) {
            return;
        }

        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            String sql = """
                SELECT CONTACT_ID, CONTACT_NAME, POSITION_TITLE, DEPARTMENT,
                       PHONE, MOBILE, EMAIL, IS_PRIMARY, IS_ACTIVE
                FROM ERP_SUPPLIER_CONTACTS
                WHERE SUPPLIER_CODE = ? AND IS_ACTIVE = 'Y'
                ORDER BY IS_PRIMARY DESC, CONTACT_NAME
            """;

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, selectedSupplierCode);
                ResultSet rs = stmt.executeQuery();

                while (rs.next()) {
                    Vector<Object> row = new Vector<>();
                    row.add(rs.getInt("CONTACT_ID"));
                    row.add(rs.getString("CONTACT_NAME"));
                    row.add(rs.getString("POSITION_TITLE"));
                    row.add(rs.getString("DEPARTMENT"));
                    row.add(rs.getString("PHONE"));
                    row.add(rs.getString("MOBILE"));
                    row.add(rs.getString("EMAIL"));
                    row.add("Y".equals(rs.getString("IS_PRIMARY")) ? "نعم" : "لا");
                    row.add("Y".equals(rs.getString("IS_ACTIVE")) ? "نشط" : "غير نشط");

                    contactsTableModel.addRow(row);
                }
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل جهات الاتصال: " + e.getMessage(),
                                        "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void addContact() {
        if (selectedSupplierCode == null || selectedSupplierCode.isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار مورد أولاً", "تنبيه", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (contactNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم جهة الاتصال", "خطأ", JOptionPane.ERROR_MESSAGE);
            return;
        }

        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            conn.setAutoCommit(false);

            // إذا كانت جهة الاتصال رئيسية، قم بإلغاء الرئيسية من الأخريات
            if (isPrimaryContactCheckBox.isSelected()) {
                String updateSql = "UPDATE ERP_SUPPLIER_CONTACTS SET IS_PRIMARY = 'N' WHERE SUPPLIER_CODE = ?";
                try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
                    updateStmt.setString(1, selectedSupplierCode);
                    updateStmt.executeUpdate();
                }
            }

            String sql = """
                INSERT INTO ERP_SUPPLIER_CONTACTS (
                    CONTACT_ID, SUPPLIER_CODE, CONTACT_NAME, POSITION_TITLE, DEPARTMENT,
                    PHONE, MOBILE, EMAIL, IS_PRIMARY, IS_ACTIVE, CREATED_BY
                ) VALUES (
                    SEQ_SUPPLIER_CONTACTS.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, ?, 'Y', 'SYSTEM'
                )
            """;

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, selectedSupplierCode);
                stmt.setString(2, contactNameField.getText().trim());
                stmt.setString(3, contactTitleField.getText().trim().isEmpty() ? null : contactTitleField.getText().trim());
                stmt.setString(4, contactDepartmentField.getText().trim().isEmpty() ? null : contactDepartmentField.getText().trim());
                stmt.setString(5, contactPhoneField.getText().trim().isEmpty() ? null : contactPhoneField.getText().trim());
                stmt.setString(6, contactMobileField.getText().trim().isEmpty() ? null : contactMobileField.getText().trim());
                stmt.setString(7, contactEmailField.getText().trim().isEmpty() ? null : contactEmailField.getText().trim());
                stmt.setString(8, isPrimaryContactCheckBox.isSelected() ? "Y" : "N");

                stmt.executeUpdate();
                conn.commit();

                JOptionPane.showMessageDialog(this, "تم إضافة جهة الاتصال بنجاح", "نجح", JOptionPane.INFORMATION_MESSAGE);
                clearContactFields();
                loadContacts();
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في إضافة جهة الاتصال: " + e.getMessage(),
                                        "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void updateContact() {
        int selectedRow = contactsTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار جهة اتصال للتعديل", "تنبيه", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (contactNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم جهة الاتصال", "خطأ", JOptionPane.ERROR_MESSAGE);
            return;
        }

        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            conn.setAutoCommit(false);

            int contactId = (Integer) contactsTableModel.getValueAt(selectedRow, 0);

            // إذا كانت جهة الاتصال رئيسية، قم بإلغاء الرئيسية من الأخريات
            if (isPrimaryContactCheckBox.isSelected()) {
                String updateSql = "UPDATE ERP_SUPPLIER_CONTACTS SET IS_PRIMARY = 'N' WHERE SUPPLIER_CODE = ? AND CONTACT_ID != ?";
                try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
                    updateStmt.setString(1, selectedSupplierCode);
                    updateStmt.setInt(2, contactId);
                    updateStmt.executeUpdate();
                }
            }

            String sql = """
                UPDATE ERP_SUPPLIER_CONTACTS SET
                    CONTACT_NAME = ?, POSITION_TITLE = ?, DEPARTMENT = ?,
                    PHONE = ?, MOBILE = ?, EMAIL = ?, IS_PRIMARY = ?, LAST_UPDATED = SYSDATE, UPDATED_BY = 'SYSTEM'
                WHERE CONTACT_ID = ?
            """;

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, contactNameField.getText().trim());
                stmt.setString(2, contactTitleField.getText().trim().isEmpty() ? null : contactTitleField.getText().trim());
                stmt.setString(3, contactDepartmentField.getText().trim().isEmpty() ? null : contactDepartmentField.getText().trim());
                stmt.setString(4, contactPhoneField.getText().trim().isEmpty() ? null : contactPhoneField.getText().trim());
                stmt.setString(5, contactMobileField.getText().trim().isEmpty() ? null : contactMobileField.getText().trim());
                stmt.setString(6, contactEmailField.getText().trim().isEmpty() ? null : contactEmailField.getText().trim());
                stmt.setString(7, isPrimaryContactCheckBox.isSelected() ? "Y" : "N");
                stmt.setInt(8, contactId);

                stmt.executeUpdate();
                conn.commit();

                JOptionPane.showMessageDialog(this, "تم تعديل جهة الاتصال بنجاح", "نجح", JOptionPane.INFORMATION_MESSAGE);
                clearContactFields();
                loadContacts();
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تعديل جهة الاتصال: " + e.getMessage(),
                                        "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void deleteContact() {
        int selectedRow = contactsTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار جهة اتصال للحذف", "تنبيه", JOptionPane.WARNING_MESSAGE);
            return;
        }

        int result = JOptionPane.showConfirmDialog(this,
            "هل أنت متأكد من حذف جهة الاتصال المختارة؟",
            "تأكيد الحذف", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
                int contactId = (Integer) contactsTableModel.getValueAt(selectedRow, 0);

                String sql = "UPDATE ERP_SUPPLIER_CONTACTS SET IS_ACTIVE = 'N', LAST_UPDATED = SYSDATE, UPDATED_BY = 'SYSTEM' WHERE CONTACT_ID = ?";
                try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                    stmt.setInt(1, contactId);
                    stmt.executeUpdate();

                    JOptionPane.showMessageDialog(this, "تم حذف جهة الاتصال بنجاح", "نجح", JOptionPane.INFORMATION_MESSAGE);
                    clearContactFields();
                    loadContacts();
                }
            } catch (SQLException e) {
                JOptionPane.showMessageDialog(this, "خطأ في حذف جهة الاتصال: " + e.getMessage(),
                                            "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private void clearContactFields() {
        contactNameField.setText("");
        contactTitleField.setText("");
        contactDepartmentField.setText("");
        contactPhoneField.setText("");
        contactMobileField.setText("");
        contactEmailField.setText("");
        isPrimaryContactCheckBox.setSelected(false);
    }

    private JPanel createAddressesTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إنشاء جدول العناوين
        createAddressesTable();
        JScrollPane tableScrollPane = new JScrollPane(addressesTable);

        // إنشاء نموذج إدخال العناوين
        JPanel formPanel = createAddressesFormPanel();

        // إنشاء لوحة أزرار العناوين
        JPanel buttonsPanel = createAddressesButtonsPanel();

        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.add(tableScrollPane, BorderLayout.CENTER);
        topPanel.add(buttonsPanel, BorderLayout.SOUTH);

        panel.add(topPanel, BorderLayout.CENTER);
        panel.add(formPanel, BorderLayout.SOUTH);

        return panel;
    }

    private void createAddressesTable() {
        String[] columnNames = {
            "المعرف", "نوع العنوان", "العنوان الأول", "العنوان الثاني",
            "المدينة", "المنطقة", "الرمز البريدي", "الدولة", "رئيسي", "نشط"
        };

        addressesTableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        addressesTable = new JTable(addressesTableModel);
        addressesTable.setFont(arabicFont);
        addressesTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        addressesTable.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 12));
        addressesTable.getTableHeader().setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        addressesTable.setRowHeight(25);
    }

    private JPanel createAddressesFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder("بيانات العنوان"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // الصف الأول
        gbc.gridx = 3; gbc.gridy = 0;
        panel.add(new JLabel("نوع العنوان:"), gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0.3;
        addressTypeCombo = new JComboBox<>(new String[]{"MAIN", "BILLING", "SHIPPING", "WAREHOUSE", "OFFICE"});
        addressTypeCombo.setFont(arabicFont);
        panel.add(addressTypeCombo, gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        isPrimaryAddressCheckBox = new JCheckBox("عنوان رئيسي");
        isPrimaryAddressCheckBox.setFont(arabicFont);
        isPrimaryAddressCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(isPrimaryAddressCheckBox, gbc);

        return panel;
    }

    private JPanel createAddressesButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addAddressButton = new JButton("إضافة عنوان");
        addAddressButton.setFont(arabicFont);
        addAddressButton.addActionListener(e -> addAddress());

        JButton updateAddressButton = new JButton("تعديل عنوان");
        updateAddressButton.setFont(arabicFont);
        updateAddressButton.addActionListener(e -> updateAddress());

        JButton deleteAddressButton = new JButton("حذف عنوان");
        deleteAddressButton.setFont(arabicFont);
        deleteAddressButton.addActionListener(e -> deleteAddress());

        panel.add(addAddressButton);
        panel.add(updateAddressButton);
        panel.add(deleteAddressButton);

        return panel;
    }

    private JPanel createCategoriesTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel label = new JLabel("تبويب الفئات - قيد التطوير", JLabel.CENTER);
        label.setFont(new Font("Tahoma", Font.BOLD, 16));
        panel.add(label, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createEvaluationsTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel label = new JLabel("تبويب التقييم - قيد التطوير", JLabel.CENTER);
        label.setFont(new Font("Tahoma", Font.BOLD, 16));
        panel.add(label, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createReportsTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel label = new JLabel("تبويب التقارير - قيد التطوير", JLabel.CENTER);
        label.setFont(new Font("Tahoma", Font.BOLD, 16));
        panel.add(label, BorderLayout.CENTER);

        return panel;
    }

    // =====================================================
    // دوال تحميل البيانات الأولية
    // =====================================================

    private void loadInitialData() {
        // إنشاء الجداول إذا لم تكن موجودة
        createTablesIfNotExists();

        // تحميل البيانات
        loadSuppliersData();
    }

    private void createTablesIfNotExists() {
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {

            // تشغيل سكريبت إنشاء الجداول
            String createScript = """
                -- إنشاء جدول الموردين إذا لم يكن موجوداً
                BEGIN
                    EXECUTE IMMEDIATE 'CREATE TABLE ERP_SUPPLIERS (
                        SUPPLIER_ID NUMBER(10) PRIMARY KEY,
                        SUPPLIER_CODE VARCHAR2(20) UNIQUE NOT NULL,
                        SUPPLIER_NAME VARCHAR2(200) NOT NULL,
                        SUPPLIER_NAME_EN VARCHAR2(200),
                        SUPPLIER_TYPE VARCHAR2(50) DEFAULT ''VENDOR'',
                        CONTACT_PERSON VARCHAR2(100),
                        PHONE VARCHAR2(50),
                        MOBILE VARCHAR2(50),
                        EMAIL VARCHAR2(100),
                        CITY VARCHAR2(100),
                        COUNTRY VARCHAR2(100) DEFAULT ''Saudi Arabia'',
                        IS_ACTIVE VARCHAR2(1) DEFAULT ''Y'' CHECK (IS_ACTIVE IN (''Y'', ''N'')),
                        IS_APPROVED VARCHAR2(1) DEFAULT ''N'' CHECK (IS_APPROVED IN (''Y'', ''N'')),
                        NOTES CLOB,
                        CREATED_BY VARCHAR2(50) DEFAULT USER,
                        CREATED_DATE DATE DEFAULT SYSDATE
                    )';
                EXCEPTION
                    WHEN OTHERS THEN
                        IF SQLCODE != -955 THEN -- Table already exists
                            RAISE;
                        END IF;
                END;
                /

                -- إنشاء sequence إذا لم يكن موجوداً
                BEGIN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE ERP_SUPPLIERS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE';
                EXCEPTION
                    WHEN OTHERS THEN
                        IF SQLCODE != -955 THEN -- Sequence already exists
                            RAISE;
                        END IF;
                END;
                /
            """;

            // تنفيذ السكريبت
            try (PreparedStatement stmt = conn.prepareStatement(createScript)) {
                stmt.execute();
                System.out.println("✅ تم إنشاء جداول الموردين بنجاح");
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في إنشاء جداول الموردين: " + e.getMessage());
        }
    }

    // =====================================================
    // دوال إدارة الموردين الرئيسية
    // =====================================================

    private void loadSuppliersData() {
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {

            String sql = """
                SELECT
                    V_CODE, V_A_NAME, V_E_NAME, TYPE, DESCRIPTION,
                    CATEGORY, STATUS, PRIORITY, CONTACT_PERSON, PHONE, EMAIL,
                    CITY, COUNTRY,
                    CASE WHEN IS_ACTIVE = 'Y' THEN 'نعم' ELSE 'لا' END AS IS_ACTIVE,
                    CASE WHEN IS_APPROVED = 'Y' THEN 'نعم' ELSE 'لا' END AS IS_APPROVED
                FROM ERP_SUPPLIERS
                WHERE IS_DELETED = 'N'
                ORDER BY V_CODE
            """;

            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {

                suppliersTableModel.setRowCount(0);

                while (rs.next()) {
                    Vector<Object> row = new Vector<>();
                    row.add(rs.getString("V_CODE"));
                    row.add(rs.getString("V_A_NAME"));
                    row.add(rs.getString("V_E_NAME"));
                    row.add(rs.getString("TYPE"));
                    row.add(rs.getString("DESCRIPTION"));
                    row.add(rs.getString("CATEGORY"));
                    row.add(rs.getString("STATUS"));
                    row.add(rs.getObject("PRIORITY"));
                    row.add(rs.getString("CONTACT_PERSON"));
                    row.add(rs.getString("PHONE"));
                    row.add(rs.getString("EMAIL"));
                    row.add(rs.getString("CITY"));
                    row.add(rs.getString("COUNTRY"));
                    row.add(rs.getString("IS_ACTIVE"));
                    row.add(rs.getString("IS_APPROVED"));

                    suppliersTableModel.addRow(row);
                }

                System.out.println("✅ تم تحميل بيانات الموردين");
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل بيانات الموردين: " + e.getMessage());
        }
    }

    private void loadSelectedSupplierData() {
        int selectedRow = suppliersTable.getSelectedRow();
        if (selectedRow >= 0) {
            try {
                // استخدام CODE كمعرف بدلاً من ID
                String selectedCode = suppliersTableModel.getValueAt(selectedRow, 0).toString();

                codeField.setText(selectedCode);
                nameField.setText(suppliersTableModel.getValueAt(selectedRow, 1).toString());

                Object nameEn = suppliersTableModel.getValueAt(selectedRow, 2);
                nameEnField.setText(nameEn != null ? nameEn.toString() : "");

                Object type = suppliersTableModel.getValueAt(selectedRow, 3);
                if (type != null) typeCombo.setSelectedItem(type.toString());

                Object description = suppliersTableModel.getValueAt(selectedRow, 4);
                descriptionField.setText(description != null ? description.toString() : "");

                Object category = suppliersTableModel.getValueAt(selectedRow, 5);
                if (category != null) categoryCombo.setSelectedItem(category.toString());

                Object status = suppliersTableModel.getValueAt(selectedRow, 6);
                if (status != null) statusCombo.setSelectedItem(status.toString());

                Object priority = suppliersTableModel.getValueAt(selectedRow, 7);
                if (priority != null) priorityCombo.setSelectedItem(priority.toString());

                Object contactPerson = suppliersTableModel.getValueAt(selectedRow, 8);
                contactPersonField.setText(contactPerson != null ? contactPerson.toString() : "");

                Object phone = suppliersTableModel.getValueAt(selectedRow, 9);
                phoneField.setText(phone != null ? phone.toString() : "");

                Object email = suppliersTableModel.getValueAt(selectedRow, 10);
                emailField.setText(email != null ? email.toString() : "");

                Object city = suppliersTableModel.getValueAt(selectedRow, 11);
                cityField.setText(city != null ? city.toString() : "");

                Object country = suppliersTableModel.getValueAt(selectedRow, 12);
                if (country != null) countryCombo.setSelectedItem(country.toString());

                Object isActive = suppliersTableModel.getValueAt(selectedRow, 13);
                isActiveCombo.setSelectedItem("نعم".equals(isActive) ? "نعم" : "لا");

                Object isApproved = suppliersTableModel.getValueAt(selectedRow, 14);
                isApprovedCombo.setSelectedItem("نعم".equals(isApproved) ? "نعم" : "لا");

                selectedSupplierCode = selectedCode;
                System.out.println("✅ تم تحميل بيانات المورد المحدد: " + selectedSupplierCode);

                // تحميل جهات الاتصال للمورد المختار
                loadContacts();

            } catch (Exception e) {
                System.err.println("❌ خطأ في تحميل بيانات المورد المحدد: " + e.getMessage());
            }
        }
    }

    private void addSupplier() {
        if (codeField.getText().trim().isEmpty() || nameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كود المورد واسم المورد", "خطأ", JOptionPane.ERROR_MESSAGE);
            return;
        }

        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            conn.setAutoCommit(false);

            String sql = """
                INSERT INTO ERP_SUPPLIERS (
                    V_CODE, V_A_NAME, V_E_NAME, TYPE, DESCRIPTION, CATEGORY, STATUS, PRIORITY,
                    CONTACT_PERSON, PHONE, MOBILE, EMAIL, ADDRESS_LINE1, CITY, COUNTRY,
                    TAX_NUMBER, COMMERCIAL_REGISTER, TAGS, IS_ACTIVE, IS_APPROVED, NOTES
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            """;

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, codeField.getText().trim());
                stmt.setString(2, nameField.getText().trim());
                stmt.setString(3, nameEnField.getText().trim().isEmpty() ? null : nameEnField.getText().trim());
                stmt.setString(4, typeCombo.getSelectedItem().toString());
                stmt.setString(5, descriptionField.getText().trim().isEmpty() ? null : descriptionField.getText().trim());
                stmt.setString(6, categoryCombo.getSelectedItem().toString());
                stmt.setString(7, statusCombo.getSelectedItem().toString());
                stmt.setInt(8, Integer.parseInt(priorityCombo.getSelectedItem().toString()));
                stmt.setString(9, contactPersonField.getText().trim().isEmpty() ? null : contactPersonField.getText().trim());
                stmt.setString(10, phoneField.getText().trim().isEmpty() ? null : phoneField.getText().trim());
                stmt.setString(11, mobileField.getText().trim().isEmpty() ? null : mobileField.getText().trim());
                stmt.setString(12, emailField.getText().trim().isEmpty() ? null : emailField.getText().trim());
                stmt.setString(13, addressLine1Field.getText().trim().isEmpty() ? null : addressLine1Field.getText().trim());
                stmt.setString(14, cityField.getText().trim().isEmpty() ? null : cityField.getText().trim());
                stmt.setString(15, countryCombo.getSelectedItem().toString());
                stmt.setString(16, taxNumberField.getText().trim().isEmpty() ? null : taxNumberField.getText().trim());
                stmt.setString(17, commercialRegisterField.getText().trim().isEmpty() ? null : commercialRegisterField.getText().trim());
                stmt.setString(18, tagsField.getText().trim().isEmpty() ? null : tagsField.getText().trim());
                stmt.setString(19, "نعم".equals(isActiveCombo.getSelectedItem().toString()) ? "Y" : "N");
                stmt.setString(20, "نعم".equals(isApprovedCombo.getSelectedItem().toString()) ? "Y" : "N");
                stmt.setString(21, notesArea.getText().trim().isEmpty() ? null : notesArea.getText().trim());

                int rowsAffected = stmt.executeUpdate();
                if (rowsAffected > 0) {
                    conn.commit();
                    JOptionPane.showMessageDialog(this, "تم إضافة المورد بنجاح", "نجح", JOptionPane.INFORMATION_MESSAGE);
                    loadSuppliersData();
                    clearSupplierFields();
                } else {
                    conn.rollback();
                    JOptionPane.showMessageDialog(this, "فشل في إضافة المورد", "خطأ", JOptionPane.ERROR_MESSAGE);
                }
            } catch (SQLException e) {
                conn.rollback();
                throw e;
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في إضافة المورد: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "خطأ في إضافة المورد:\n" + e.getMessage(),
                "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void updateSupplier() {
        if (selectedSupplierCode == null || selectedSupplierCode.isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار مورد للتعديل", "تنبيه", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (codeField.getText().trim().isEmpty() || nameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كود المورد واسم المورد", "خطأ", JOptionPane.ERROR_MESSAGE);
            return;
        }

        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            conn.setAutoCommit(false);

            String sql = """
                UPDATE ERP_SUPPLIERS SET
                    V_A_NAME = ?, V_E_NAME = ?, TYPE = ?, DESCRIPTION = ?, CATEGORY = ?, STATUS = ?, PRIORITY = ?,
                    CONTACT_PERSON = ?, PHONE = ?, MOBILE = ?, EMAIL = ?, ADDRESS_LINE1 = ?, CITY = ?, COUNTRY = ?,
                    TAX_NUMBER = ?, COMMERCIAL_REGISTER = ?, TAGS = ?, IS_ACTIVE = ?, IS_APPROVED = ?, NOTES = ?
                WHERE V_CODE = ?
            """;

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, nameField.getText().trim());
                stmt.setString(2, nameEnField.getText().trim().isEmpty() ? null : nameEnField.getText().trim());
                stmt.setString(3, typeCombo.getSelectedItem().toString());
                stmt.setString(4, descriptionField.getText().trim().isEmpty() ? null : descriptionField.getText().trim());
                stmt.setString(5, categoryCombo.getSelectedItem().toString());
                stmt.setString(6, statusCombo.getSelectedItem().toString());
                stmt.setInt(7, Integer.parseInt(priorityCombo.getSelectedItem().toString()));
                stmt.setString(8, contactPersonField.getText().trim().isEmpty() ? null : contactPersonField.getText().trim());
                stmt.setString(9, phoneField.getText().trim().isEmpty() ? null : phoneField.getText().trim());
                stmt.setString(10, mobileField.getText().trim().isEmpty() ? null : mobileField.getText().trim());
                stmt.setString(11, emailField.getText().trim().isEmpty() ? null : emailField.getText().trim());
                stmt.setString(12, addressLine1Field.getText().trim().isEmpty() ? null : addressLine1Field.getText().trim());
                stmt.setString(13, cityField.getText().trim().isEmpty() ? null : cityField.getText().trim());
                stmt.setString(14, countryCombo.getSelectedItem().toString());
                stmt.setString(15, taxNumberField.getText().trim().isEmpty() ? null : taxNumberField.getText().trim());
                stmt.setString(16, commercialRegisterField.getText().trim().isEmpty() ? null : commercialRegisterField.getText().trim());
                stmt.setString(17, tagsField.getText().trim().isEmpty() ? null : tagsField.getText().trim());
                stmt.setString(18, "نعم".equals(isActiveCombo.getSelectedItem().toString()) ? "Y" : "N");
                stmt.setString(19, "نعم".equals(isApprovedCombo.getSelectedItem().toString()) ? "Y" : "N");
                stmt.setString(20, notesArea.getText().trim().isEmpty() ? null : notesArea.getText().trim());
                stmt.setString(21, codeField.getText().trim()); // استخدام CODE بدلاً من ID

                int rowsAffected = stmt.executeUpdate();
                if (rowsAffected > 0) {
                    conn.commit();
                    JOptionPane.showMessageDialog(this, "تم تعديل المورد بنجاح", "نجح", JOptionPane.INFORMATION_MESSAGE);
                    loadSuppliersData();
                } else {
                    conn.rollback();
                    JOptionPane.showMessageDialog(this, "لم يتم العثور على المورد للتعديل", "تحذير", JOptionPane.WARNING_MESSAGE);
                }
            } catch (SQLException e) {
                conn.rollback();
                throw e;
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تعديل المورد: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "خطأ في تعديل المورد:\n" + e.getMessage(),
                "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void deleteSupplier() {
        if (selectedSupplierCode.isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار مورد للحذف", "تنبيه", JOptionPane.WARNING_MESSAGE);
            return;
        }

        String supplierName = nameField.getText();
        int confirm = JOptionPane.showConfirmDialog(this,
            "هل أنت متأكد من حذف المورد:\n" + supplierName + "\n\nهذا الإجراء لا يمكن التراجع عنه.",
            "تأكيد الحذف",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE);

        if (confirm != JOptionPane.YES_OPTION) {
            return;
        }

        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            conn.setAutoCommit(false);

            String sql = "DELETE FROM ERP_SUPPLIERS WHERE V_CODE = ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, codeField.getText().trim());

                int rowsAffected = stmt.executeUpdate();
                if (rowsAffected > 0) {
                    conn.commit();
                    JOptionPane.showMessageDialog(this, "تم حذف المورد بنجاح", "نجح", JOptionPane.INFORMATION_MESSAGE);
                    loadSuppliersData();
                    clearSupplierFields();
                } else {
                    conn.rollback();
                    JOptionPane.showMessageDialog(this, "لم يتم العثور على المورد للحذف", "تحذير", JOptionPane.WARNING_MESSAGE);
                }
            } catch (SQLException e) {
                conn.rollback();
                throw e;
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في حذف المورد: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "خطأ في حذف المورد:\n" + e.getMessage(),
                "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void clearSupplierFields() {
        selectedSupplierCode = "";
        codeField.setText("");
        nameField.setText("");
        nameEnField.setText("");
        typeCombo.setSelectedIndex(0);
        descriptionField.setText("");
        categoryCombo.setSelectedIndex(0);
        statusCombo.setSelectedIndex(0);
        priorityCombo.setSelectedIndex(0);
        contactPersonField.setText("");
        phoneField.setText("");
        mobileField.setText("");
        emailField.setText("");
        addressLine1Field.setText("");
        cityField.setText("");
        countryCombo.setSelectedIndex(0);
        taxNumberField.setText("");
        commercialRegisterField.setText("");
        tagsField.setText("");
        isActiveCombo.setSelectedIndex(0);
        isApprovedCombo.setSelectedIndex(0);
        notesArea.setText("");
        contactPersonField.setText("");
        phoneField.setText("");
        mobileField.setText("");
        emailField.setText("");
        cityField.setText("");
        countryCombo.setSelectedIndex(0);
        isActiveCombo.setSelectedIndex(0);
        isApprovedCombo.setSelectedIndex(1);
        notesArea.setText("");
    }



    // =====================================================
    // دوال إدارة العناوين (مؤقتة)
    // =====================================================

    private void addAddress() {
        JOptionPane.showMessageDialog(this, "ميزة إضافة العناوين قيد التطوير", "قيد التطوير", JOptionPane.INFORMATION_MESSAGE);
    }

    private void updateAddress() {
        JOptionPane.showMessageDialog(this, "ميزة تعديل العناوين قيد التطوير", "قيد التطوير", JOptionPane.INFORMATION_MESSAGE);
    }

    private void deleteAddress() {
        JOptionPane.showMessageDialog(this, "ميزة حذف العناوين قيد التطوير", "قيد التطوير", JOptionPane.INFORMATION_MESSAGE);
    }

    // =====================================================
    // دالة استيراد البيانات من V_DETAILS
    // =====================================================

    private void importFromVDetails() {
        // نافذة إدخال بيانات الاتصال
        JDialog connectionDialog = new JDialog(this, "بيانات الاتصال بقاعدة IAS20251", true);
        connectionDialog.setSize(500, 300);
        connectionDialog.setLocationRelativeTo(this);
        connectionDialog.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        // حقول إدخال بيانات الاتصال
        JTextField userField = new JTextField("IAS20251", 15);
        JPasswordField passwordField = new JPasswordField("IAS20251", 15);
        JTextField hostField = new JTextField("localhost", 15);
        JTextField portField = new JTextField("1521", 15);
        JTextField sidField = new JTextField("ORCL", 15);

        userField.setFont(arabicFont);
        passwordField.setFont(arabicFont);
        hostField.setFont(arabicFont);
        portField.setFont(arabicFont);
        sidField.setFont(arabicFont);

        // إضافة المكونات
        gbc.gridx = 1; gbc.gridy = 0;
        mainPanel.add(new JLabel("اسم المستخدم:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(userField, gbc);

        gbc.gridx = 1; gbc.gridy = 1;
        mainPanel.add(new JLabel("كلمة المرور:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(passwordField, gbc);

        gbc.gridx = 1; gbc.gridy = 2;
        mainPanel.add(new JLabel("الخادم:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(hostField, gbc);

        gbc.gridx = 1; gbc.gridy = 3;
        mainPanel.add(new JLabel("المنفذ:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(portField, gbc);

        gbc.gridx = 1; gbc.gridy = 4;
        mainPanel.add(new JLabel("SID:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(sidField, gbc);

        // أزرار
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton connectButton = new JButton("اتصال واستيراد");
        JButton cancelButton = new JButton("إلغاء");

        connectButton.setFont(arabicFont);
        cancelButton.setFont(arabicFont);

        final boolean[] shouldProceed = {false};

        connectButton.addActionListener(e -> {
            shouldProceed[0] = true;
            connectionDialog.dispose();
        });

        cancelButton.addActionListener(e -> {
            shouldProceed[0] = false;
            connectionDialog.dispose();
        });

        buttonPanel.add(connectButton);
        buttonPanel.add(cancelButton);

        gbc.gridx = 0; gbc.gridy = 5; gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(buttonPanel, gbc);

        connectionDialog.add(mainPanel);
        connectionDialog.setVisible(true);

        if (!shouldProceed[0]) {
            return;
        }

        // الحصول على بيانات الاتصال
        String dbUser = userField.getText().trim();
        String dbPassword = new String(passwordField.getPassword());
        String dbHost = hostField.getText().trim();
        String dbPort = portField.getText().trim();
        String dbSid = sidField.getText().trim();

        // تأكيد العملية
        int confirm = JOptionPane.showConfirmDialog(this,
            "هل أنت متأكد من استيراد البيانات من جدول V_DETAILS؟\n\n" +
            "بيانات الاتصال:\n" +
            "• المستخدم: " + dbUser + "\n" +
            "• الخادم: " + dbHost + ":" + dbPort + "/" + dbSid + "\n\n" +
            "سيتم:\n" +
            "• الاتصال بقاعدة بيانات IAS20251\n" +
            "• قراءة جميع البيانات من جدول V_DETAILS\n" +
            "• نسخها إلى جدول ERP_SUPPLIERS\n" +
            "• تحديث الجدول الحالي\n\n" +
            "هذه العملية قد تستغرق بعض الوقت...",
            "تأكيد الاستيراد",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE);

        if (confirm != JOptionPane.YES_OPTION) {
            return;
        }

        // إظهار نافذة تقدم الاستيراد
        JDialog progressDialog = new JDialog(this, "استيراد البيانات من IAS20251", true);
        progressDialog.setSize(600, 400);
        progressDialog.setLocationRelativeTo(this);
        progressDialog.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel progressMainPanel = new JPanel(new BorderLayout());
        progressMainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // منطقة النص لعرض التقدم
        JTextArea progressArea = new JTextArea();
        progressArea.setFont(new Font("Tahoma", Font.PLAIN, 12));
        progressArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        progressArea.setEditable(false);
        progressArea.setBackground(getBackground());

        JScrollPane scrollPane = new JScrollPane(progressArea);
        progressMainPanel.add(scrollPane, BorderLayout.CENTER);

        // زر إغلاق
        JPanel progressButtonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        progressButtonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton closeButton = new JButton("إغلاق");
        closeButton.setFont(new Font("Tahoma", Font.PLAIN, 12));
        closeButton.setEnabled(false); // معطل حتى انتهاء الاستيراد
        closeButton.addActionListener(e -> progressDialog.dispose());
        progressButtonPanel.add(closeButton);

        progressMainPanel.add(progressButtonPanel, BorderLayout.SOUTH);
        progressDialog.add(progressMainPanel);

        // تشغيل الاستيراد في thread منفصل
        SwingWorker<Void, String> importWorker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                publish("🚀 بدء عملية استيراد البيانات من IAS20251...\n");
                publish("📊 الاتصال بقاعدة بيانات IAS20251...\n");
                Thread.sleep(1000);

                // معلومات الاتصال بقاعدة IAS20251 (من النافذة)
                String IAS_DB_URL = "jdbc:oracle:thin:@" + dbHost + ":" + dbPort + ":" + dbSid;
                String IAS_DB_USER = dbUser;
                String IAS_DB_PASSWORD = dbPassword;

                // معلومات الاتصال بقاعدة SHIP_ERP
                String ERP_DB_URL = "*************************************";
                String ERP_DB_USER = "ship_erp";
                String ERP_DB_PASSWORD = "ship_erp_password";

                try {
                    // الاتصال بقاعدة IAS20251
                    publish("🔗 الاتصال بقاعدة بيانات IAS20251...\n");
                    Connection iasConn = DriverManager.getConnection(IAS_DB_URL, IAS_DB_USER, IAS_DB_PASSWORD);
                    publish("✅ تم الاتصال بقاعدة IAS20251 بنجاح\n");

                    // الاتصال بقاعدة SHIP_ERP
                    publish("🔗 الاتصال بقاعدة بيانات SHIP_ERP...\n");
                    Connection erpConn = DriverManager.getConnection(ERP_DB_URL, ERP_DB_USER, ERP_DB_PASSWORD);
                    erpConn.setAutoCommit(false);
                    publish("✅ تم الاتصال بقاعدة SHIP_ERP بنجاح\n");

                    // فحص الأعمدة الموجودة في V_DETAILS أولاً
                    publish("🔍 فحص بنية جدول V_DETAILS...\n");
                    String checkColumnsSQL = """
                        SELECT COLUMN_NAME
                        FROM USER_TAB_COLUMNS
                        WHERE TABLE_NAME = 'V_DETAILS'
                        ORDER BY COLUMN_ID
                    """;

                    PreparedStatement checkStmt = iasConn.prepareStatement(checkColumnsSQL);
                    ResultSet columnsRs = checkStmt.executeQuery();

                    StringBuilder availableColumns = new StringBuilder();
                    java.util.List<String> columnsList = new java.util.ArrayList<>();

                    while (columnsRs.next()) {
                        String columnName = columnsRs.getString("COLUMN_NAME");
                        columnsList.add(columnName);
                        if (availableColumns.length() > 0) {
                            availableColumns.append(", ");
                        }
                        availableColumns.append(columnName);
                    }
                    columnsRs.close();
                    checkStmt.close();

                    publish("📋 الأعمدة الموجودة: " + columnsList.size() + " عمود\n");

                    // إنشاء استعلام ديناميكي بناءً على الأعمدة الموجودة (نسخ جميع البيانات)
                    String selectSQL = "SELECT " + availableColumns.toString() + " FROM V_DETAILS ORDER BY " +
                                     (columnsList.contains("ID") ? "ID" : columnsList.get(0));

                    publish("📖 قراءة البيانات من جدول V_DETAILS...\n");
                    PreparedStatement selectStmt = iasConn.prepareStatement(selectSQL);
                    ResultSet rs = selectStmt.executeQuery();

                    // فحص الأعمدة الموجودة في ERP_SUPPLIERS
                    String checkTargetSQL = """
                        SELECT COLUMN_NAME
                        FROM USER_TAB_COLUMNS
                        WHERE TABLE_NAME = 'ERP_SUPPLIERS'
                        ORDER BY COLUMN_ID
                    """;

                    PreparedStatement checkTargetStmt = erpConn.prepareStatement(checkTargetSQL);
                    ResultSet targetColumnsRs = checkTargetStmt.executeQuery();

                    java.util.List<String> targetColumnsList = new java.util.ArrayList<>();
                    while (targetColumnsRs.next()) {
                        targetColumnsList.add(targetColumnsRs.getString("COLUMN_NAME"));
                    }
                    targetColumnsRs.close();
                    checkTargetStmt.close();

                    // إنشاء قائمة الأعمدة المشتركة
                    java.util.List<String> commonColumns = new java.util.ArrayList<>();
                    for (String col : columnsList) {
                        if (targetColumnsList.contains(col)) {
                            commonColumns.add(col);
                        }
                    }

                    publish("🔄 سيتم نسخ " + commonColumns.size() + " عمود مشترك\n");
                    publish("📝 الأعمدة المشتركة: " + String.join(", ", commonColumns) + "\n");

                    // التحقق من وجود أعمدة مشتركة
                    if (commonColumns.isEmpty()) {
                        publish("❌ لا توجد أعمدة مشتركة للنسخ!\n");
                        return null;
                    }

                    // إنشاء استعلام الإدراج الديناميكي
                    StringBuilder insertColumns = new StringBuilder();
                    StringBuilder insertValues = new StringBuilder();

                    for (int i = 0; i < commonColumns.size(); i++) {
                        if (i > 0) {
                            insertColumns.append(", ");
                            insertValues.append(", ");
                        }
                        insertColumns.append(commonColumns.get(i));
                        insertValues.append("?");
                    }

                    String insertSQL = "INSERT INTO ERP_SUPPLIERS (" + insertColumns.toString() +
                                     ") VALUES (" + insertValues.toString() + ")";

                    publish("🔍 استعلام الإدراج: " + insertSQL + "\n");

                    // اختبار صحة الاستعلام
                    try {
                        PreparedStatement testStmt = erpConn.prepareStatement(insertSQL);
                        testStmt.close();
                        publish("✅ تم التحقق من صحة استعلام الإدراج\n");
                    } catch (SQLException e) {
                        publish("❌ خطأ في استعلام الإدراج: " + e.getMessage() + "\n");
                        return null;
                    }

                    PreparedStatement insertStmt = erpConn.prepareStatement(insertSQL);

                    int recordCount = 0;
                    int successCount = 0;
                    int errorCount = 0;

                    publish("📊 بدء نسخ البيانات...\n");

                    while (rs.next()) {
                        recordCount++;

                        try {
                            // نسخ القيم الأصلية كما هي من V_DETAILS
                            for (int i = 0; i < commonColumns.size(); i++) {
                                String columnName = commonColumns.get(i);
                                Object value = rs.getObject(columnName);
                                insertStmt.setObject(i + 1, value);
                            }

                            insertStmt.executeUpdate();
                            successCount++;

                            if (recordCount % 10 == 0) {
                                publish("📊 تم نسخ " + recordCount + " سجل...\n");
                            }

                        } catch (SQLException e) {
                            errorCount++;
                            publish("❌ خطأ في السجل " + recordCount + ": " + e.getErrorCode() + "-" + e.getMessage() + "\n");

                            // إذا كان الخطأ في الاستعلام نفسه، توقف
                            if (e.getErrorCode() == 928) { // ORA-00928
                                publish("🛑 خطأ في بناء الاستعلام - توقف الاستيراد\n");
                                break;
                            }
                        }
                    }

                    // تثبيت التغييرات
                    erpConn.commit();

                    // إغلاق الاتصالات
                    rs.close();
                    selectStmt.close();
                    insertStmt.close();
                    iasConn.close();
                    erpConn.close();

                    publish("\n🎉 تم الانتهاء من الاستيراد!\n");
                    publish("📊 إجمالي السجلات: " + recordCount + "\n");
                    publish("✅ تم نسخها بنجاح: " + successCount + "\n");
                    publish("❌ فشل في النسخ: " + errorCount + "\n");
                    publish("⏰ وقت الانتهاء: " + new java.util.Date() + "\n");

                } catch (SQLException e) {
                    publish("❌ خطأ في الاستيراد: " + e.getMessage() + "\n");
                    e.printStackTrace();
                }

                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    progressArea.append(message);
                    progressArea.setCaretPosition(progressArea.getDocument().getLength());
                }
            }

            @Override
            protected void done() {
                closeButton.setEnabled(true);
                closeButton.setText("إغلاق");

                // تحديث جدول الموردين
                SwingUtilities.invokeLater(() -> {
                    loadSuppliersData();
                });
            }
        };

        importWorker.execute();
        progressDialog.setVisible(true);
    }

    // =====================================================
    // دالة main للاختبار
    // =====================================================

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new SuppliersManagementWindow().setVisible(true);
                System.out.println("🏢 تم فتح نافذة إدارة الموردين الشاملة");

            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null,
                        "خطأ في فتح نافذة إدارة الموردين:\n" + e.getMessage(),
                        "خطأ - Error",
                        JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
