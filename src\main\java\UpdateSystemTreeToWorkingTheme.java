import java.sql.*;

/**
 * تحديث جدول النظام لاستخدام WorkingThemeWindow
 * Update System Tree to use WorkingThemeWindow
 */
public class UpdateSystemTreeToWorkingTheme {
    
    public static void main(String[] args) {
        System.out.println("🔄 تحديث جدول النظام لاستخدام WorkingThemeWindow...");
        
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();
            
            updateSystemTree(connection);
            
            connection.close();
            System.out.println("✅ تم تحديث جدول النظام بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحديث جدول النظام: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void updateSystemTree(Connection connection) throws SQLException {
        System.out.println("📋 البحث عن نوافذ المظاهر في جدول النظام...");
        
        // البحث عن جميع نوافذ المظاهر
        String searchSQL = """
            SELECT TREE_ID, NODE_NAME_AR, WINDOW_CLASS 
            FROM ERP_SYSTEM_TREE 
            WHERE NODE_NAME_AR LIKE '%مظهر%' 
               OR NODE_NAME_AR LIKE '%واجهة%' 
               OR WINDOW_CLASS LIKE '%Theme%'
            """;
        
        try (PreparedStatement searchStmt = connection.prepareStatement(searchSQL);
             ResultSet rs = searchStmt.executeQuery()) {
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                int treeId = rs.getInt("TREE_ID");
                String nodeName = rs.getString("NODE_NAME_AR");
                String currentClassName = rs.getString("WINDOW_CLASS");
                
                System.out.println("  وجدت نافذة: " + nodeName + " (ID: " + treeId + ")");
                System.out.println("  الكلاس الحالي: " + currentClassName);
                
                // تحديث اسم الكلاس إلى WorkingThemeWindow
                String updateSQL = """
                    UPDATE ERP_SYSTEM_TREE 
                    SET WINDOW_CLASS = ?, 
                        NODE_DESCRIPTION = ?, 
                        LAST_UPDATED = SYSDATE,
                        UPDATED_BY = 'SYSTEM'
                    WHERE TREE_ID = ?
                    """;
                
                try (PreparedStatement updateStmt = connection.prepareStatement(updateSQL)) {
                    updateStmt.setString(1, "WorkingThemeWindow");
                    updateStmt.setString(2, "نظام المظاهر العملي - يعمل مع جميع المكتبات المتاحة ويدعم المظاهر المخصصة");
                    updateStmt.setInt(3, treeId);
                    
                    int updated = updateStmt.executeUpdate();
                    
                    if (updated > 0) {
                        System.out.println("  ✅ تم تحديث الكلاس إلى: WorkingThemeWindow");
                    } else {
                        System.err.println("  ❌ فشل في تحديث الكلاس");
                    }
                }
            }
            
            if (!found) {
                System.out.println("  لم يتم العثور على نوافذ المظاهر، سيتم إنشاؤها...");
                createThemeWindowEntry(connection);
            }
        }
        
        // التحقق من النتيجة
        System.out.println("\n📋 التحقق من النتيجة:");
        try (PreparedStatement checkStmt = connection.prepareStatement(searchSQL);
             ResultSet rs = checkStmt.executeQuery()) {
            
            while (rs.next()) {
                String nodeName = rs.getString("NODE_NAME_AR");
                String className = rs.getString("WINDOW_CLASS");
                System.out.println("  " + nodeName + " -> " + className);
            }
        }
    }
    
    private static void createThemeWindowEntry(Connection connection) throws SQLException {
        System.out.println("📝 إنشاء إدخال جديد لنافذة المظاهر العملية...");
        
        // الحصول على معرف فئة الإعدادات العامة
        int settingsId = getCategoryId(connection, "الإعدادات العامة");
        
        if (settingsId == 0) {
            System.err.println("❌ لم يتم العثور على فئة الإعدادات العامة");
            return;
        }
        
        // الحصول على الترتيب التالي
        int nextOrder = getNextOrder(connection, settingsId);
        
        String insertSQL = """
            INSERT INTO ERP_SYSTEM_TREE 
            (TREE_ID, PARENT_ID, NODE_NAME_AR, NODE_NAME_EN, NODE_DESCRIPTION, NODE_TYPE, 
             WINDOW_CLASS, DISPLAY_ORDER, TREE_LEVEL, IS_ACTIVE, IS_VISIBLE, 
             CREATED_DATE, CREATED_BY, LAST_UPDATED, UPDATED_BY, VERSION_NUMBER)
            VALUES (ERP_SYSTEM_TREE_SEQ.NEXTVAL, ?, ?, ?, ?, 'WINDOW', ?, ?, 2, 'Y', 'Y', 
                    SYSDATE, 'SYSTEM', SYSDATE, 'SYSTEM', 1)
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
            stmt.setInt(1, settingsId);
            stmt.setString(2, "نظام المظاهر العملي");
            stmt.setString(3, "Working Theme System");
            stmt.setString(4, "نظام المظاهر العملي - يعمل مع جميع المكتبات المتاحة ويدعم المظاهر المخصصة والأساسية");
            stmt.setString(5, "WorkingThemeWindow");
            stmt.setInt(6, nextOrder);
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("  ✅ تم إنشاء إدخال نافذة المظاهر العملية بنجاح");
            } else {
                System.err.println("  ❌ فشل في إنشاء إدخال نافذة المظاهر");
            }
        }
    }
    
    private static int getCategoryId(Connection conn, String categoryName) throws SQLException {
        String sql = "SELECT TREE_ID FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND NODE_TYPE = 'CATEGORY'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, categoryName);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("TREE_ID");
                }
            }
        }
        
        return 0;
    }
    
    private static int getNextOrder(Connection conn, int parentId) throws SQLException {
        String sql = "SELECT NVL(MAX(DISPLAY_ORDER), 0) + 1 FROM ERP_SYSTEM_TREE WHERE PARENT_ID = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, parentId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        
        return 1;
    }
}
