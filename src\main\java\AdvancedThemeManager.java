import java.awt.Component;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.swing.JOptionPane;
import javax.swing.LookAndFeel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
// FlatLaf imports
import com.formdev.flatlaf.FlatDarculaLaf;
import com.formdev.flatlaf.FlatDarkLaf;
import com.formdev.flatlaf.FlatIntelliJLaf;
import com.formdev.flatlaf.FlatLightLaf;

/**
 * مدير المظاهر المتقدم والشامل Advanced and Comprehensive Theme Manager
 */
public class AdvancedThemeManager {

    private static AdvancedThemeManager instance;
    private Connection connection;
    private Map<String, ThemeInfo> availableThemes;
    private String currentTheme = "FlatLaf Light";

    // معلومات المظهر
    public static class ThemeInfo {
        public String name;
        public String displayName;
        public String category;
        public String className;
        public boolean isDark;
        public boolean isAvailable;
        public String description;
        public String author;
        public String version;

        public ThemeInfo(String name, String displayName, String category, String className,
                boolean isDark, String description, String author, String version) {
            this.name = name;
            this.displayName = displayName;
            this.category = category;
            this.className = className;
            this.isDark = isDark;
            this.description = description;
            this.author = author;
            this.version = version;
            this.isAvailable = checkAvailability();
        }

        private boolean checkAvailability() {
            try {
                if (className != null && !className.isEmpty()) {
                    Class.forName(className);
                    return true;
                }
                return true;
            } catch (ClassNotFoundException e) {
                System.err.println("Theme not available: " + name + " - " + e.getMessage());
                return false;
            }
        }
    }

    private AdvancedThemeManager() {
        initializeAllThemes();
        connectToDatabase();
    }

    public static AdvancedThemeManager getInstance() {
        if (instance == null) {
            instance = new AdvancedThemeManager();
        }
        return instance;
    }

    private void initializeAllThemes() {
        availableThemes = new LinkedHashMap<>();

        System.out.println("🎨 تهيئة جميع المظاهر المتاحة...");

        // FlatLaf Core Themes
        addTheme("FlatLaf Light", "FlatLaf فاتح", "FlatLaf Core",
                "com.formdev.flatlaf.FlatLightLaf", false, "مظهر فاتح حديث وأنيق من FlatLaf",
                "FormDev", "3.2.5");
        addTheme("FlatLaf Dark", "FlatLaf مظلم", "FlatLaf Core", "com.formdev.flatlaf.FlatDarkLaf",
                true, "مظهر مظلم حديث وأنيق من FlatLaf", "FormDev", "3.2.5");
        addTheme("FlatLaf IntelliJ", "FlatLaf IntelliJ", "FlatLaf Core",
                "com.formdev.flatlaf.FlatIntelliJLaf", false, "مظهر IntelliJ IDEA الكلاسيكي",
                "FormDev", "3.2.5");
        addTheme("FlatLaf Darcula", "FlatLaf Darcula", "FlatLaf Core",
                "com.formdev.flatlaf.FlatDarculaLaf", true, "مظهر Darcula المظلم الشهير", "FormDev",
                "3.2.5");

        // FlatLaf IntelliJ Themes
        addTheme("Arc Theme", "مظهر Arc", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatArcIJTheme", false,
                "مظهر Arc الحديث والأنيق", "Arc Team", "1.0");
        addTheme("Arc Dark Theme", "مظهر Arc المظلم", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatArcDarkIJTheme", true,
                "مظهر Arc المظلم الجميل", "Arc Team", "1.0");
        addTheme("Carbon Theme", "مظهر Carbon", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatCarbonIJTheme", true,
                "مظهر Carbon المظلم الأنيق", "Carbon Team", "1.0");
        addTheme("Cobalt 2 Theme", "مظهر Cobalt 2", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatCobalt2IJTheme", true,
                "مظهر Cobalt 2 الأزرق المميز", "Cobalt Team", "2.0");
        addTheme("Cyan Light Theme", "مظهر Cyan الفاتح", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatCyanLightIJTheme", false,
                "مظهر Cyan الفاتح المنعش", "Cyan Team", "1.0");
        addTheme("Dark Purple Theme", "مظهر البنفسجي المظلم", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatDarkPurpleIJTheme", true,
                "مظهر بنفسجي مظلم رائع", "Purple Team", "1.0");
        addTheme("Dracula Theme", "مظهر Dracula", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatDraculaIJTheme", true,
                "مظهر Dracula الشهير والمحبوب", "Dracula Team", "1.0");
        addTheme("Gradianto Dark Fuchsia", "مظهر Gradianto", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatGradiantoDarkFuchsiaIJTheme", true,
                "مظهر Gradianto بتدرجات جميلة", "Gradianto Team", "1.0");
        addTheme("Gray Theme", "المظهر الرمادي", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatGrayIJTheme", false,
                "مظهر رمادي هادئ ومريح", "Gray Team", "1.0");
        addTheme("Gruvbox Dark Hard", "مظهر Gruvbox", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatGruvboxDarkHardIJTheme", true,
                "مظهر Gruvbox المظلم المتباين", "Gruvbox Team", "1.0");
        addTheme("Hiberbee Dark Theme", "مظهر Hiberbee", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatHiberbeeDarkIJTheme", true,
                "مظهر Hiberbee المظلم الجميل", "Hiberbee Team", "1.0");
        addTheme("High Contrast Theme", "مظهر التباين العالي", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatHighContrastIJTheme", true,
                "مظهر عالي التباين للوضوح", "Contrast Team", "1.0");
        addTheme("Light Flat Theme", "المظهر الفاتح المسطح", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatLightFlatIJTheme", false,
                "مظهر فاتح مسطح وبسيط", "Light Team", "1.0");
        addTheme("Material Design Dark", "Material Design المظلم", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatMaterialDesignDarkIJTheme", true,
                "مظهر Material Design المظلم", "Material Team", "1.0");
        addTheme("Monocai Theme", "مظهر Monocai", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatMonocaiIJTheme", true,
                "مظهر Monocai الأحادي اللون", "Monocai Team", "1.0");
        addTheme("Nord Theme", "مظهر Nord", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatNordIJTheme", false,
                "مظهر Nord الاسكندنافي الهادئ", "Nord Team", "1.0");
        addTheme("One Dark Theme", "مظهر One Dark", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatOneDarkIJTheme", true,
                "مظهر One Dark الشهير من Atom", "One Dark Team", "1.0");
        addTheme("Solarized Dark Theme", "مظهر Solarized المظلم", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatSolarizedDarkIJTheme", true,
                "مظهر Solarized المظلم المتوازن", "Solarized Team", "1.0");
        addTheme("Solarized Light Theme", "مظهر Solarized الفاتح", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatSolarizedLightIJTheme", false,
                "مظهر Solarized الفاتح المتوازن", "Solarized Team", "1.0");
        addTheme("Spacegray Theme", "مظهر Spacegray", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatSpacegrayIJTheme", true,
                "مظهر Spacegray الفضائي", "Spacegray Team", "1.0");
        addTheme("Vuesion Theme", "مظهر Vuesion", "IntelliJ Themes",
                "com.formdev.flatlaf.intellijthemes.FlatVuesionIJTheme", false,
                "مظهر Vuesion الحديث والمتطور", "Vuesion Team", "1.0");

        // JTattoo Themes
        addJTattooThemes();

        // DarkLaf Themes (commented out - library issues)
        // addDarkLafThemes();

        // System Themes
        addSystemThemes();

        // عد المظاهر المتاحة
        long availableCount = availableThemes.values().stream()
                .mapToLong(theme -> theme.isAvailable ? 1 : 0).sum();

        System.out.println("✅ تم تهيئة " + availableThemes.size() + " مظهر، منها " + availableCount
                + " متاح للاستخدام");
    }

    private void addJTattooThemes() {
        // JTattoo Light Themes
        addTheme("Acryl", "مظهر Acryl", "JTattoo", "com.jtattoo.plaf.acryl.AcrylLookAndFeel", false,
                "مظهر Acryl الشفاف والأنيق", "JTattoo", "1.6.13");
        addTheme("Aero", "مظهر Aero", "JTattoo", "com.jtattoo.plaf.aero.AeroLookAndFeel", false,
                "مظهر Aero الحديث والمتطور", "JTattoo", "1.6.13");
        addTheme("Aluminium", "مظهر الألمنيوم", "JTattoo",
                "com.jtattoo.plaf.aluminium.AluminiumLookAndFeel", false,
                "مظهر معدني أنيق بلمسة ألمنيوم", "JTattoo", "1.6.13");
        addTheme("Bernstein", "مظهر Bernstein", "JTattoo",
                "com.jtattoo.plaf.bernstein.BernsteinLookAndFeel", false,
                "مظهر Bernstein الدافئ والجميل", "JTattoo", "1.6.13");
        addTheme("Fast", "مظهر Fast", "JTattoo", "com.jtattoo.plaf.fast.FastLookAndFeel", false,
                "مظهر سريع وبسيط للأداء العالي", "JTattoo", "1.6.13");
        addTheme("Luna", "مظهر Luna", "JTattoo", "com.jtattoo.plaf.luna.LunaLookAndFeel", false,
                "مظهر Luna الكلاسيكي والجميل", "JTattoo", "1.6.13");
        addTheme("McWin", "مظهر McWin", "JTattoo", "com.jtattoo.plaf.mcwin.McWinLookAndFeel", false,
                "مظهر McWin الحديث والمتطور", "JTattoo", "1.6.13");
        addTheme("Mint", "مظهر النعناع", "JTattoo", "com.jtattoo.plaf.mint.MintLookAndFeel", false,
                "مظهر النعناع المنعش والهادئ", "JTattoo", "1.6.13");
        addTheme("Smart", "مظهر Smart", "JTattoo", "com.jtattoo.plaf.smart.SmartLookAndFeel", false,
                "مظهر Smart الذكي والعملي", "JTattoo", "1.6.13");
        addTheme("Texture", "مظهر Texture", "JTattoo",
                "com.jtattoo.plaf.texture.TextureLookAndFeel", false, "مظهر Texture المحكم والمميز",
                "JTattoo", "1.6.13");

        // JTattoo Dark Themes
        addTheme("Graphite", "مظهر الجرافيت", "JTattoo",
                "com.jtattoo.plaf.graphite.GraphiteLookAndFeel", true,
                "مظهر الجرافيت المظلم والأنيق", "JTattoo", "1.6.13");
        addTheme("HiFi", "مظهر HiFi", "JTattoo", "com.jtattoo.plaf.hifi.HiFiLookAndFeel", true,
                "مظهر HiFi المتقدم والمميز", "JTattoo", "1.6.13");
        addTheme("Noire", "مظهر Noire", "JTattoo", "com.jtattoo.plaf.noire.NoireLookAndFeel", true,
                "مظهر Noire الأسود الأنيق", "JTattoo", "1.6.13");
    }

    private void addDarkLafThemes() {
        // DarkLaf Themes - مظاهر DarkLaf المتقدمة
        addTheme("DarkLaf", "مظهر DarkLaf", "DarkLaf", "com.github.weisj.darklaf.DarkLaf", true,
                "مظهر DarkLaf المظلم المتقدم", "DarkLaf Team", "3.0.2");
    }

    private void addSystemThemes() {
        // System Themes - مظاهر النظام
        addTheme("System Default", "مظهر النظام", "System", "", false, "مظهر النظام الافتراضي",
                "System", "Default");
        addTheme("Metal", "مظهر Metal", "System", "javax.swing.plaf.metal.MetalLookAndFeel", false,
                "مظهر Java Metal الكلاسيكي", "Oracle", "Java");
        addTheme("Nimbus", "مظهر Nimbus", "System", "javax.swing.plaf.nimbus.NimbusLookAndFeel",
                false, "مظهر Nimbus الحديث والجميل", "Oracle", "Java");
        addTheme("Windows", "مظهر Windows", "System",
                "com.sun.java.swing.plaf.windows.WindowsLookAndFeel", false, "مظهر Windows الأصلي",
                "Microsoft", "Windows");
        addTheme("GTK+", "مظهر GTK+", "System", "com.sun.java.swing.plaf.gtk.GTKLookAndFeel", false,
                "مظهر GTK+ لأنظمة Linux", "GTK Team", "Linux");
    }

    private void addTheme(String name, String displayName, String category, String className,
            boolean isDark, String description, String author, String version) {
        ThemeInfo theme = new ThemeInfo(name, displayName, category, className, isDark, description,
                author, version);
        availableThemes.put(name, theme);
    }

    private void connectToDatabase() {
        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            connection = tnsManager.getShipErpConnection();
            System.out.println("✅ تم الاتصال بقاعدة البيانات لمدير المظاهر المتقدم");
        } catch (Exception e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
        }
    }

    /**
     * تطبيق المظهر المحدد
     */
    public boolean applyTheme(String themeName, Component parentComponent) {
        try {
            ThemeInfo theme = availableThemes.get(themeName);
            if (theme == null) {
                System.err.println("❌ المظهر غير موجود: " + themeName);
                return false;
            }

            if (!theme.isAvailable) {
                System.err.println("❌ المظهر غير متاح: " + themeName);
                if (parentComponent != null) {
                    JOptionPane.showMessageDialog(parentComponent,
                            "المظهر غير متاح: " + theme.displayName + "\n"
                                    + "تأكد من وجود المكتبة المطلوبة: " + theme.className,
                            "مظهر غير متاح", JOptionPane.WARNING_MESSAGE);
                }
                return false;
            }

            System.out
                    .println("🎨 تطبيق المظهر: " + theme.displayName + " (" + theme.category + ")");

            boolean success = false;

            // تطبيق المظهر حسب النوع
            switch (theme.category) {
                case "FlatLaf Core":
                    success = applyFlatLafTheme(theme);
                    break;
                case "IntelliJ Themes":
                    success = applyIntelliJTheme(theme);
                    break;
                case "JTattoo":
                    success = applyJTattooTheme(theme);
                    break;
                // case "DarkLaf":
                // success = applyDarkLafTheme(theme);
                // break;
                case "System":
                    success = applySystemTheme(theme);
                    break;
                default:
                    success = applyGenericTheme(theme);
                    break;
            }

            if (success) {
                // تحديث جميع النوافذ
                updateAllWindows();

                // حفظ المظهر الحالي
                currentTheme = themeName;
                saveCurrentTheme(themeName);

                System.out.println("✅ تم تطبيق المظهر بنجاح: " + theme.displayName);

                // عرض رسالة تأكيد مفصلة
                if (parentComponent != null) {
                    SwingUtilities.invokeLater(() -> {
                        String message = String.format(
                                "تم تطبيق المظهر بنجاح!\n\n" + "الاسم: %s\n" + "الفئة: %s\n"
                                        + "المطور: %s\n" + "الإصدار: %s\n" + "النوع: %s\n\n"
                                        + "الوصف: %s",
                                theme.displayName, theme.category, theme.author, theme.version,
                                theme.isDark ? "مظلم" : "فاتح", theme.description);

                        JOptionPane.showMessageDialog(parentComponent, message, "تم تطبيق المظهر",
                                JOptionPane.INFORMATION_MESSAGE);
                    });
                }

                return true;
            } else {
                System.err.println("❌ فشل في تطبيق المظهر: " + theme.displayName);
                if (parentComponent != null) {
                    JOptionPane.showMessageDialog(parentComponent,
                            "فشل في تطبيق المظهر: " + theme.displayName + "\n"
                                    + "قد تكون المكتبة المطلوبة غير متاحة أو تالفة.",
                            "خطأ في تطبيق المظهر", JOptionPane.ERROR_MESSAGE);
                }
            }

        } catch (Exception e) {
            System.err.println("❌ خطأ في تطبيق المظهر: " + e.getMessage());
            e.printStackTrace();

            if (parentComponent != null) {
                JOptionPane.showMessageDialog(parentComponent,
                        "خطأ في تطبيق المظهر:\n" + e.getMessage(), "خطأ",
                        JOptionPane.ERROR_MESSAGE);
            }
        }

        return false;
    }

    private boolean applyFlatLafTheme(ThemeInfo theme) {
        try {
            switch (theme.name) {
                case "FlatLaf Light":
                    return FlatLightLaf.setup();
                case "FlatLaf Dark":
                    return FlatDarkLaf.setup();
                case "FlatLaf IntelliJ":
                    return FlatIntelliJLaf.setup();
                case "FlatLaf Darcula":
                    return FlatDarculaLaf.setup();
                default:
                    return false;
            }
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق FlatLaf: " + e.getMessage());
            return false;
        }
    }

    private boolean applyIntelliJTheme(ThemeInfo theme) {
        try {
            Class<?> themeClass = Class.forName(theme.className);
            LookAndFeel laf = (LookAndFeel) themeClass.getDeclaredConstructor().newInstance();
            UIManager.setLookAndFeel(laf);
            return true;
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق IntelliJ Theme: " + e.getMessage());
            return false;
        }
    }

    private boolean applyJTattooTheme(ThemeInfo theme) {
        try {
            Class<?> themeClass = Class.forName(theme.className);
            LookAndFeel laf = (LookAndFeel) themeClass.getDeclaredConstructor().newInstance();
            UIManager.setLookAndFeel(laf);
            return true;
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق JTattoo Theme: " + e.getMessage());
            return false;
        }
    }

    private boolean applyDarkLafTheme(ThemeInfo theme) {
        try {
            Class<?> themeClass = Class.forName(theme.className);
            LookAndFeel laf = (LookAndFeel) themeClass.getDeclaredConstructor().newInstance();
            UIManager.setLookAndFeel(laf);
            return true;
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق DarkLaf Theme: " + e.getMessage());
            return false;
        }
    }

    private boolean applySystemTheme(ThemeInfo theme) {
        try {
            switch (theme.name) {
                case "System Default":
                    String osName = System.getProperty("os.name").toLowerCase();
                    if (osName.contains("windows")) {
                        UIManager.setLookAndFeel(
                                "com.sun.java.swing.plaf.windows.WindowsLookAndFeel");
                    } else if (osName.contains("mac")) {
                        UIManager.setLookAndFeel("com.apple.laf.AquaLookAndFeel");
                    } else {
                        UIManager.setLookAndFeel("javax.swing.plaf.metal.MetalLookAndFeel");
                    }
                    return true;
                default:
                    UIManager.setLookAndFeel(theme.className);
                    return true;
            }
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق System Theme: " + e.getMessage());
            return false;
        }
    }

    private boolean applyGenericTheme(ThemeInfo theme) {
        try {
            UIManager.setLookAndFeel(theme.className);
            return true;
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق Generic Theme: " + e.getMessage());
            return false;
        }
    }

    private void updateAllWindows() {
        SwingUtilities.invokeLater(() -> {
            System.out.println("🔄 تحديث جميع النوافذ...");

            for (java.awt.Window window : java.awt.Window.getWindows()) {
                if (window.isDisplayable()) {
                    SwingUtilities.updateComponentTreeUI(window);
                    window.repaint();
                    window.revalidate();
                }
            }

            System.out.println("✅ تم تحديث جميع النوافذ");
        });
    }

    private void saveCurrentTheme(String themeName) {
        if (connection == null)
            return;

        try {
            // إزالة المظهر الحالي
            String resetSQL = "UPDATE ERP_COMPREHENSIVE_THEMES SET IS_CURRENT_THEME = 'N'";
            try (PreparedStatement resetStmt = connection.prepareStatement(resetSQL)) {
                resetStmt.executeUpdate();
            }

            // تعيين المظهر الجديد كحالي
            String updateSQL =
                    "UPDATE ERP_COMPREHENSIVE_THEMES SET IS_CURRENT_THEME = 'Y', LAST_APPLIED_DATE = SYSDATE WHERE THEME_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(updateSQL)) {
                stmt.setString(1, themeName);
                int updated = stmt.executeUpdate();
                if (updated > 0) {
                    System.out.println("💾 تم حفظ المظهر في قاعدة البيانات: " + themeName);
                } else {
                    System.out
                            .println("⚠️ لم يتم العثور على المظهر في قاعدة البيانات: " + themeName);
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في حفظ المظهر الحالي: " + e.getMessage());
        }
    }

    /**
     * الحصول على قائمة المظاهر المتاحة
     */
    public String[] getAvailableThemeNames() {
        return availableThemes.values().stream().filter(theme -> theme.isAvailable)
                .map(theme -> theme.name).toArray(String[]::new);
    }

    /**
     * الحصول على قائمة المظاهر حسب الفئة
     */
    public Map<String, List<ThemeInfo>> getThemesByCategory() {
        Map<String, List<ThemeInfo>> themesByCategory = new LinkedHashMap<>();

        for (ThemeInfo theme : availableThemes.values()) {
            if (theme.isAvailable) {
                themesByCategory.computeIfAbsent(theme.category, k -> new ArrayList<>()).add(theme);
            }
        }

        return themesByCategory;
    }

    /**
     * الحصول على معلومات المظهر
     */
    public ThemeInfo getThemeInfo(String themeName) {
        return availableThemes.get(themeName);
    }

    /**
     * الحصول على المظهر الحالي
     */
    public String getCurrentTheme() {
        return currentTheme;
    }

    /**
     * تحميل المظهر المحفوظ من قاعدة البيانات
     */
    public void loadSavedTheme() {
        if (connection == null)
            return;

        try {
            String sql =
                    "SELECT THEME_NAME FROM ERP_COMPREHENSIVE_THEMES WHERE IS_CURRENT_THEME = 'Y'";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                    ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    String savedTheme = rs.getString("THEME_NAME");
                    if (savedTheme != null && availableThemes.containsKey(savedTheme)) {
                        System.out.println("📂 تحميل المظهر المحفوظ: " + savedTheme);
                        currentTheme = savedTheme;
                        applyTheme(savedTheme, null);
                    }
                } else {
                    System.out.println("ℹ️ لا يوجد مظهر محفوظ، سيتم استخدام المظهر الافتراضي");
                }
            }
        } catch (SQLException e) {
            System.err.println("خطأ في تحميل المظهر المحفوظ: " + e.getMessage());
        }
    }

    /**
     * طباعة تقرير المظاهر المتاحة
     */
    public void printThemeReport() {
        System.out.println("\n📊 تقرير المظاهر المتاحة:");
        System.out.println("========================");

        Map<String, List<ThemeInfo>> themesByCategory = getThemesByCategory();

        for (Map.Entry<String, List<ThemeInfo>> entry : themesByCategory.entrySet()) {
            System.out
                    .println("\n🎨 " + entry.getKey() + " (" + entry.getValue().size() + " مظهر):");
            for (ThemeInfo theme : entry.getValue()) {
                System.out.println("  ✅ " + theme.displayName + " - " + theme.description);
            }
        }

        System.out.println("\n📈 إجمالي المظاهر المتاحة: " + availableThemes.values().stream()
                .mapToLong(theme -> theme.isAvailable ? 1 : 0).sum());
    }

    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
        }
    }
}
