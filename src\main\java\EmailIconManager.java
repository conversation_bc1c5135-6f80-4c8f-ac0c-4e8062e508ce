import java.awt.*;
import java.util.HashMap;
import java.util.Map;
import javax.swing.*;

/**
 * مدير الرموز لنظام البريد الإلكتروني
 * Email System Icons Manager
 */
public class EmailIconManager {
    
    // خريطة الرموز النصية العربية
    private static final Map<String, String> ICONS = new HashMap<>();
    
    static {
        // رموز القوائم الرئيسية
        ICONS.put("file", "ملف");
        ICONS.put("edit", "تحرير");
        ICONS.put("view", "عرض");
        ICONS.put("message", "رسالة");
        ICONS.put("tools", "أدوات");
        ICONS.put("help", "مساعدة");
        
        // رموز العمليات الأساسية
        ICONS.put("refresh", "تحديث");
        ICONS.put("sync", "مزامنة");
        ICONS.put("new", "جديد");
        ICONS.put("compose", "إنشاء");
        ICONS.put("reply", "رد");
        ICONS.put("forward", "تحويل");
        ICONS.put("delete", "حذف");
        ICONS.put("archive", "أرشفة");
        
        // رموز البحث والفلترة
        ICONS.put("search", "بحث");
        ICONS.put("filter", "فلتر");
        ICONS.put("sort", "ترتيب");
        ICONS.put("clear", "مسح");
        
        // رموز الحالة
        ICONS.put("read", "مقروء");
        ICONS.put("unread", "غير مقروء");
        ICONS.put("important", "مهم");
        ICONS.put("flag", "علامة");
        ICONS.put("spam", "مزعج");
        
        // رموز المرفقات
        ICONS.put("attachment", "مرفق");
        ICONS.put("download", "تحميل");
        ICONS.put("save", "حفظ");
        ICONS.put("open", "فتح");
        
        // رموز الطباعة والتصدير
        ICONS.put("print", "طباعة");
        ICONS.put("export", "تصدير");
        ICONS.put("import", "استيراد");
        
        // رموز الإعدادات
        ICONS.put("settings", "إعدادات");
        ICONS.put("preferences", "تفضيلات");
        ICONS.put("account", "حساب");
        ICONS.put("security", "أمان");
        
        // رموز التنقل
        ICONS.put("next", "التالي");
        ICONS.put("previous", "السابق");
        ICONS.put("first", "الأول");
        ICONS.put("last", "الأخير");
        
        // رموز الاتصال
        ICONS.put("connect", "اتصال");
        ICONS.put("disconnect", "قطع");
        ICONS.put("online", "متصل");
        ICONS.put("offline", "غير متصل");
        
        // رموز المجلدات
        ICONS.put("inbox", "وارد");
        ICONS.put("outbox", "صادر");
        ICONS.put("sent", "مرسل");
        ICONS.put("drafts", "مسودات");
        ICONS.put("trash", "سلة المهملات");
        ICONS.put("folder", "مجلد");
        
        // رموز التقارير والإحصائيات
        ICONS.put("statistics", "إحصائيات");
        ICONS.put("reports", "تقارير");
        ICONS.put("chart", "مخطط");
        ICONS.put("graph", "رسم بياني");
        
        // رموز النسخ الاحتياطي
        ICONS.put("backup", "نسخ احتياطي");
        ICONS.put("restore", "استعادة");
        ICONS.put("sync_all", "مزامنة الكل");
        
        // رموز المساعدة
        ICONS.put("manual", "دليل");
        ICONS.put("tutorial", "تعليمي");
        ICONS.put("about", "حول");
        ICONS.put("info", "معلومات");
        
        // رموز الوقت
        ICONS.put("today", "اليوم");
        ICONS.put("yesterday", "أمس");
        ICONS.put("week", "أسبوع");
        ICONS.put("month", "شهر");
        
        // رموز العمليات المتقدمة
        ICONS.put("copy", "نسخ");
        ICONS.put("cut", "قص");
        ICONS.put("paste", "لصق");
        ICONS.put("select_all", "تحديد الكل");
        ICONS.put("undo", "تراجع");
        ICONS.put("redo", "إعادة");
        
        // رموز التشفير والأمان
        ICONS.put("encrypt", "تشفير");
        ICONS.put("decrypt", "فك التشفير");
        ICONS.put("signature", "توقيع");
        ICONS.put("certificate", "شهادة");
        
        // رموز الشبكة
        ICONS.put("server", "خادم");
        ICONS.put("client", "عميل");
        ICONS.put("protocol", "بروتوكول");
        ICONS.put("port", "منفذ");
    }
    
    /**
     * الحصول على رمز نصي
     */
    public static String getIcon(String iconName) {
        return ICONS.getOrDefault(iconName, iconName);
    }
    
    /**
     * إنشاء زر مع رمز نصي
     */
    public static JButton createButton(String iconName, String tooltip, Runnable action) {
        String iconText = getIcon(iconName);
        JButton button = new JButton(iconText);
        button.setToolTipText(tooltip);
        button.setFont(new Font("Tahoma", Font.BOLD, 11));
        button.setPreferredSize(new Dimension(80, 30));
        button.setFocusPainted(false);
        
        // تحسين المظهر
        button.setBackground(new Color(245, 245, 245));
        button.setBorder(BorderFactory.createRaisedBevelBorder());
        
        // إضافة الحدث
        if (action != null) {
            button.addActionListener(e -> action.run());
        }
        
        // تأثير hover
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(225, 225, 225));
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(245, 245, 245));
            }
        });
        
        return button;
    }
    
    /**
     * إنشاء عنصر قائمة مع رمز
     */
    public static JMenuItem createMenuItem(String iconName, String text, String tooltip, Runnable action) {
        String iconText = getIcon(iconName);
        JMenuItem menuItem = new JMenuItem(iconText + " " + text);
        menuItem.setToolTipText(tooltip);
        menuItem.setFont(new Font("Tahoma", Font.PLAIN, 12));
        
        if (action != null) {
            menuItem.addActionListener(e -> action.run());
        }
        
        return menuItem;
    }
    
    /**
     * إنشاء تسمية مع رمز
     */
    public static JLabel createLabel(String iconName, String text) {
        String iconText = getIcon(iconName);
        JLabel label = new JLabel(iconText + " " + text);
        label.setFont(new Font("Tahoma", Font.PLAIN, 12));
        return label;
    }
    
    /**
     * تطبيق رمز على زر موجود
     */
    public static void applyIcon(AbstractButton button, String iconName) {
        String iconText = getIcon(iconName);
        String currentText = button.getText();
        
        if (currentText != null && !currentText.isEmpty()) {
            // إزالة الرمز القديم إذا وجد
            if (currentText.contains(" ")) {
                String[] parts = currentText.split(" ", 2);
                if (parts.length > 1) {
                    button.setText(iconText + " " + parts[1]);
                } else {
                    button.setText(iconText + " " + currentText);
                }
            } else {
                button.setText(iconText + " " + currentText);
            }
        } else {
            button.setText(iconText);
        }
    }
    
    /**
     * إنشاء أيقونة ملونة
     */
    public static JLabel createColoredIcon(String iconName, Color color) {
        String iconText = getIcon(iconName);
        JLabel icon = new JLabel(iconText);
        icon.setForeground(color);
        icon.setFont(new Font("Tahoma", Font.BOLD, 14));
        return icon;
    }
    
    /**
     * إنشاء زر شريط أدوات محسن
     */
    public static JButton createToolbarButton(String iconName, String tooltip, Runnable action) {
        JButton button = createButton(iconName, tooltip, action);
        button.setPreferredSize(new Dimension(70, 28));
        button.setFont(new Font("Tahoma", Font.BOLD, 10));
        return button;
    }
}
