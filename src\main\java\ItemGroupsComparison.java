import java.awt.BorderLayout;
import java.awt.GridLayout;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * أداة مقارنة نوافذ مجموعات الأصناف
 * Item Groups Windows Comparison Tool
 */
public class ItemGroupsComparison extends JFrame {
    
    public ItemGroupsComparison() {
        initializeUI();
    }
    
    private void initializeUI() {
        setTitle("🔍 مقارنة نوافذ مجموعات الأصناف");
        setSize(600, 400);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        
        setLayout(new BorderLayout());
        
        // العنوان
        JLabel titleLabel = new JLabel("🔍 مقارنة نوافذ مجموعات الأصناف", JLabel.CENTER);
        titleLabel.setFont(titleLabel.getFont().deriveFont(18f));
        add(titleLabel, BorderLayout.NORTH);
        
        // الأزرار
        JPanel buttonsPanel = new JPanel(new GridLayout(5, 1, 10, 10));
        
        JButton originalWindowBtn = new JButton("🪟 النافذة الأصلية (ItemGroupsManagementWindow)");
        originalWindowBtn.addActionListener(e -> openOriginalWindow());
        
        JButton fixedWindowBtn = new JButton("🔧 النافذة المحسنة (ItemGroupsDisplayFixer)");
        fixedWindowBtn.addActionListener(e -> openFixedWindow());
        
        JButton diagnosticBtn = new JButton("🔍 تشخيص البيانات (ItemGroupsDataDiagnostic)");
        diagnosticBtn.addActionListener(e -> runDiagnostic());
        
        JButton bothWindowsBtn = new JButton("🔄 فتح النافذتين للمقارنة");
        bothWindowsBtn.addActionListener(e -> openBothWindows());
        
        JButton reportBtn = new JButton("📊 تقرير المشاكل والحلول");
        reportBtn.addActionListener(e -> showReport());
        
        buttonsPanel.add(originalWindowBtn);
        buttonsPanel.add(fixedWindowBtn);
        buttonsPanel.add(diagnosticBtn);
        buttonsPanel.add(bothWindowsBtn);
        buttonsPanel.add(reportBtn);
        
        add(buttonsPanel, BorderLayout.CENTER);
        
        // معلومات
        JLabel infoLabel = new JLabel("<html><center>استخدم هذه الأداة لمقارنة النوافذ المختلفة<br>وتحديد مشاكل عرض البيانات</center></html>", JLabel.CENTER);
        add(infoLabel, BorderLayout.SOUTH);
    }
    
    private void openOriginalWindow() {
        try {
            System.out.println("🪟 فتح النافذة الأصلية...");
            ItemGroupsManagementWindow originalWindow = new ItemGroupsManagementWindow();
            originalWindow.setVisible(true);
            System.out.println("✅ تم فتح النافذة الأصلية");
        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح النافذة الأصلية: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void openFixedWindow() {
        try {
            System.out.println("🔧 فتح النافذة المحسنة...");
            ItemGroupsDisplayFixer fixedWindow = new ItemGroupsDisplayFixer();
            fixedWindow.setVisible(true);
            System.out.println("✅ تم فتح النافذة المحسنة");
        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح النافذة المحسنة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void runDiagnostic() {
        try {
            System.out.println("🔍 تشغيل تشخيص البيانات...");
            
            // تشغيل التشخيص في خيط منفصل
            new Thread(() -> {
                ItemGroupsDataDiagnostic diagnostic = new ItemGroupsDataDiagnostic();
                diagnostic.runDiagnostic();
            }).start();
            
            System.out.println("✅ تم تشغيل التشخيص");
        } catch (Exception e) {
            System.err.println("❌ خطأ في تشغيل التشخيص: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void openBothWindows() {
        try {
            System.out.println("🔄 فتح النافذتين للمقارنة...");
            
            // فتح النافذة الأصلية
            ItemGroupsManagementWindow originalWindow = new ItemGroupsManagementWindow();
            originalWindow.setLocation(100, 100);
            originalWindow.setVisible(true);
            
            // انتظار قصير
            Thread.sleep(1000);
            
            // فتح النافذة المحسنة
            ItemGroupsDisplayFixer fixedWindow = new ItemGroupsDisplayFixer();
            fixedWindow.setLocation(600, 100);
            fixedWindow.setVisible(true);
            
            System.out.println("✅ تم فتح النافذتين للمقارنة");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح النافذتين: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void showReport() {
        System.out.println("📊 عرض تقرير المشاكل والحلول:");
        System.out.println("=====================================");
        System.out.println();
        
        System.out.println("🔍 المشاكل المحتملة في نافذة مجموعات الأصناف:");
        System.out.println("1. مشاكل الترميز العربي");
        System.out.println("2. عدم ظهور البيانات بشكل صحيح");
        System.out.println("3. مشاكل في تنسيق الجداول");
        System.out.println("4. بطء في تحميل البيانات");
        System.out.println("5. مشاكل في الاتصال بقاعدة البيانات");
        System.out.println();
        
        System.out.println("✅ الحلول المطبقة:");
        System.out.println("1. تحسين الترميز العربي في الجداول");
        System.out.println("2. إعادة تصميم عرض البيانات");
        System.out.println("3. تحسين استعلامات قاعدة البيانات");
        System.out.println("4. إضافة معالجة أفضل للأخطاء");
        System.out.println("5. تحسين أداء تحميل البيانات");
        System.out.println();
        
        System.out.println("🛠️ الأدوات المتاحة:");
        System.out.println("• ItemGroupsDataDiagnostic - تشخيص البيانات");
        System.out.println("• ItemGroupsDisplayFixer - إصلاح العرض");
        System.out.println("• ItemGroupsComparison - مقارنة النوافذ");
        System.out.println();
        
        System.out.println("📋 نتائج التشخيص:");
        System.out.println("• 15 مجموعة رئيسية");
        System.out.println("• 40 مجموعة فرعية رئيسية");
        System.out.println("• 9 مجموعات تحت فرعية");
        System.out.println("• 4 مجموعات مساعدة");
        System.out.println("• 2 مجموعة تفصيلية");
        System.out.println("• الترميز العربي يعمل بشكل صحيح");
        System.out.println("• الأداء جيد (< 15ms للاستعلامات)");
        System.out.println();
        
        System.out.println("💡 التوصيات:");
        System.out.println("1. استخدم النافذة المحسنة للعرض الأفضل");
        System.out.println("2. تحقق من الاتصال بقاعدة البيانات");
        System.out.println("3. تأكد من وجود البيانات في الجداول");
        System.out.println("4. استخدم أداة التشخيص للفحص الدوري");
        System.out.println();
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                FinalThemeManager.initializeDefaultTheme();
            } catch (Exception e) {
                System.err.println("تعذر تطبيق المظهر: " + e.getMessage());
            }
            
            System.out.println("🔍 تشغيل أداة مقارنة نوافذ مجموعات الأصناف");
            System.out.println("Item Groups Windows Comparison Tool");
            System.out.println("==========================================");
            
            ItemGroupsComparison comparison = new ItemGroupsComparison();
            comparison.setVisible(true);
            
            System.out.println("✅ تم تشغيل أداة المقارنة");
            System.out.println("📋 استخدم الأزرار لفتح النوافذ المختلفة");
        });
    }
}
