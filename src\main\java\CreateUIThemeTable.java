import java.sql.*;
import java.util.Properties;

/**
 * إنشاء جدول إعدادات الواجهة والمظهر والألوان
 * Create UI Theme and Appearance Settings Table
 */
public class CreateUIThemeTable {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔄 إنشاء جدول إعدادات الواجهة والمظهر...");
            
            Connection connection = getConnection();
            
            // إنشاء الجدول
            createUIThemeTable(connection);
            
            // إدراج البيانات الأساسية
            insertBasicThemeSettings(connection);
            
            // إنشاء الفهارس والـ Views
            createIndexesAndViews(connection);
            
            connection.close();
            
            System.out.println("✅ تم إنشاء جدول إعدادات الواجهة والمظهر بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء جدول إعدادات الواجهة: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.J2EE13Compliant", "true");
            
            String url = "*************************************";
            return DriverManager.getConnection(url, props);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("Oracle JDBC Driver not found", e);
        }
    }
    
    private static void createUIThemeTable(Connection conn) throws SQLException {
        System.out.println("📊 إنشاء جدول ERP_UI_THEME_SETTINGS...");
        
        // حذف الجدول إذا كان موجوداً
        try {
            conn.createStatement().execute("DROP TABLE ERP_UI_THEME_SETTINGS CASCADE CONSTRAINTS");
            System.out.println("⚠️ تم حذف الجدول الموجود");
        } catch (SQLException e) {
            // تجاهل الخطأ إذا لم يكن الجدول موجوداً
        }
        
        // حذف Sequence إذا كان موجوداً
        try {
            conn.createStatement().execute("DROP SEQUENCE ERP_UI_THEME_SEQ");
        } catch (SQLException e) {
            // تجاهل الخطأ
        }
        
        // إنشاء Sequence
        String createSeq = "CREATE SEQUENCE ERP_UI_THEME_SEQ START WITH 1 INCREMENT BY 1";
        conn.createStatement().execute(createSeq);
        
        // إنشاء الجدول
        String createTable = """
            CREATE TABLE ERP_UI_THEME_SETTINGS (
                SETTING_ID NUMBER(10) PRIMARY KEY,
                SETTING_NAME VARCHAR2(100) NOT NULL UNIQUE,
                SETTING_NAME_AR NVARCHAR2(200) NOT NULL,
                SETTING_NAME_EN VARCHAR2(200) NOT NULL,
                SETTING_DESCRIPTION NVARCHAR2(500),
                SETTING_VALUE NVARCHAR2(2000),
                DEFAULT_VALUE NVARCHAR2(2000),
                SETTING_TYPE VARCHAR2(50) DEFAULT 'STRING' NOT NULL,
                SETTING_CATEGORY VARCHAR2(50) DEFAULT 'GENERAL' NOT NULL,
                SETTING_GROUP VARCHAR2(100),
                COLOR_VALUE VARCHAR2(20),
                RGB_RED NUMBER(3),
                RGB_GREEN NUMBER(3),
                RGB_BLUE NUMBER(3),
                ALPHA_VALUE NUMBER(3) DEFAULT 255,
                HEX_COLOR VARCHAR2(10),
                FONT_FAMILY VARCHAR2(100),
                FONT_SIZE NUMBER(3),
                FONT_STYLE VARCHAR2(20),
                FONT_WEIGHT VARCHAR2(20),
                THEME_NAME VARCHAR2(100),
                LOOK_AND_FEEL VARCHAR2(200),
                IS_CUSTOM_THEME CHAR(1) DEFAULT 'N',
                IS_DARK_MODE CHAR(1) DEFAULT 'N',
                PRIORITY_LEVEL NUMBER(1) DEFAULT 2,
                IS_REQUIRED CHAR(1) DEFAULT 'N',
                IS_READONLY CHAR(1) DEFAULT 'N',
                IS_ACTIVE CHAR(1) DEFAULT 'Y',
                IS_VISIBLE CHAR(1) DEFAULT 'Y',
                APPLIES_TO VARCHAR2(100),
                CSS_CLASS VARCHAR2(200),
                CSS_STYLE CLOB,
                PREVIEW_IMAGE VARCHAR2(500),
                DISPLAY_ORDER NUMBER(5) DEFAULT 0,
                HELP_TEXT NVARCHAR2(1000),
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50) DEFAULT USER,
                LAST_UPDATED DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(50) DEFAULT USER,
                VERSION_NUMBER NUMBER(10) DEFAULT 1,
                NOTES NVARCHAR2(1000)
            )
        """;
        
        conn.createStatement().execute(createTable);
        System.out.println("✅ تم إنشاء جدول ERP_UI_THEME_SETTINGS");
        
        // إنشاء Triggers
        createTriggers(conn);
    }
    
    private static void createTriggers(Connection conn) throws SQLException {
        System.out.println("🔧 إنشاء Triggers...");
        
        // Trigger لتحديث التاريخ والإصدار
        String updateTrigger = """
            CREATE OR REPLACE TRIGGER TRG_UI_THEME_UPDATE
                BEFORE UPDATE ON ERP_UI_THEME_SETTINGS
                FOR EACH ROW
            BEGIN
                :NEW.LAST_UPDATED := SYSDATE;
                :NEW.UPDATED_BY := USER;
                :NEW.VERSION_NUMBER := :OLD.VERSION_NUMBER + 1;
            END;
        """;
        conn.createStatement().execute(updateTrigger);
        
        // Trigger لتعيين المعرف الفريد
        String idTrigger = """
            CREATE OR REPLACE TRIGGER TRG_UI_THEME_ID
                BEFORE INSERT ON ERP_UI_THEME_SETTINGS
                FOR EACH ROW
                WHEN (NEW.SETTING_ID IS NULL)
            BEGIN
                :NEW.SETTING_ID := ERP_UI_THEME_SEQ.NEXTVAL;
            END;
        """;
        conn.createStatement().execute(idTrigger);
        
        // Trigger لتحديث HEX_COLOR تلقائياً
        String colorTrigger = """
            CREATE OR REPLACE TRIGGER TRG_UI_THEME_COLOR
                BEFORE INSERT OR UPDATE ON ERP_UI_THEME_SETTINGS
                FOR EACH ROW
            BEGIN
                IF :NEW.RGB_RED IS NOT NULL AND :NEW.RGB_GREEN IS NOT NULL AND :NEW.RGB_BLUE IS NOT NULL THEN
                    :NEW.HEX_COLOR := '#' || 
                        LPAD(TO_CHAR(:NEW.RGB_RED, 'XX'), 2, '0') ||
                        LPAD(TO_CHAR(:NEW.RGB_GREEN, 'XX'), 2, '0') ||
                        LPAD(TO_CHAR(:NEW.RGB_BLUE, 'XX'), 2, '0');
                END IF;
            END;
        """;
        conn.createStatement().execute(colorTrigger);
        
        System.out.println("✅ تم إنشاء Triggers");
    }
    
    private static void insertBasicThemeSettings(Connection conn) throws SQLException {
        System.out.println("📝 إدراج إعدادات المظهر الأساسية...");
        
        String[][] basicSettings = {
            // إعدادات المظهر العامة
            {"MAIN_THEME", "المظهر الرئيسي", "Main Theme", "المظهر الرئيسي للتطبيق", "FlatLaf Light", "THEME", "APPEARANCE", "FlatLaf Light", "1", "Y", "1"},
            {"DARK_MODE", "الوضع المظلم", "Dark Mode", "تفعيل الوضع المظلم", "false", "BOOLEAN", "APPEARANCE", "false", "2", "N", "2"},
            {"CUSTOM_THEME", "مظهر مخصص", "Custom Theme", "استخدام مظهر مخصص", "false", "BOOLEAN", "APPEARANCE", "false", "3", "N", "3"},
            
            // ألوان الواجهة الرئيسية
            {"PRIMARY_COLOR", "اللون الأساسي", "Primary Color", "اللون الأساسي للواجهة", "#2196F3", "COLOR", "COLORS", "#2196F3", "1", "Y", "4"},
            {"SECONDARY_COLOR", "اللون الثانوي", "Secondary Color", "اللون الثانوي للواجهة", "#FFC107", "COLOR", "COLORS", "#FFC107", "2", "Y", "5"},
            {"ACCENT_COLOR", "لون التمييز", "Accent Color", "لون التمييز والتركيز", "#FF5722", "COLOR", "COLORS", "#FF5722", "2", "Y", "6"},
            {"BACKGROUND_COLOR", "لون الخلفية", "Background Color", "لون خلفية التطبيق", "#FFFFFF", "COLOR", "COLORS", "#FFFFFF", "1", "Y", "7"},
            {"TEXT_COLOR", "لون النص", "Text Color", "لون النص الأساسي", "#212121", "COLOR", "COLORS", "#212121", "1", "Y", "8"},
            
            // ألوان الأزرار
            {"BUTTON_BG_COLOR", "لون خلفية الأزرار", "Button Background", "لون خلفية الأزرار", "#2196F3", "COLOR", "BUTTONS", "#2196F3", "1", "Y", "9"},
            {"BUTTON_TEXT_COLOR", "لون نص الأزرار", "Button Text Color", "لون نص الأزرار", "#FFFFFF", "COLOR", "BUTTONS", "#FFFFFF", "1", "Y", "10"},
            {"BUTTON_HOVER_COLOR", "لون الأزرار عند التمرير", "Button Hover Color", "لون الأزرار عند التمرير", "#1976D2", "COLOR", "BUTTONS", "#1976D2", "2", "Y", "11"},
            
            // إعدادات الخطوط
            {"MAIN_FONT_FAMILY", "خط النص الرئيسي", "Main Font Family", "عائلة الخط الرئيسي", "Tahoma", "FONT", "FONTS", "Tahoma", "1", "Y", "12"},
            {"MAIN_FONT_SIZE", "حجم الخط الرئيسي", "Main Font Size", "حجم الخط الرئيسي", "12", "NUMBER", "FONTS", "12", "1", "Y", "13"},
            {"HEADER_FONT_SIZE", "حجم خط العناوين", "Header Font Size", "حجم خط العناوين", "14", "NUMBER", "FONTS", "14", "2", "Y", "14"},
            {"BUTTON_FONT_SIZE", "حجم خط الأزرار", "Button Font Size", "حجم خط الأزرار", "12", "NUMBER", "FONTS", "12", "2", "Y", "15"},
            
            // إعدادات الجداول
            {"TABLE_HEADER_COLOR", "لون رأس الجدول", "Table Header Color", "لون خلفية رأس الجدول", "#E3F2FD", "COLOR", "TABLES", "#E3F2FD", "2", "Y", "16"},
            {"TABLE_ROW_COLOR", "لون صفوف الجدول", "Table Row Color", "لون صفوف الجدول", "#FFFFFF", "COLOR", "TABLES", "#FFFFFF", "2", "Y", "17"},
            {"TABLE_ALT_ROW_COLOR", "لون الصفوف البديلة", "Alternate Row Color", "لون الصفوف البديلة في الجدول", "#F5F5F5", "COLOR", "TABLES", "#F5F5F5", "2", "Y", "18"},
            
            // إعدادات القوائم
            {"MENU_BG_COLOR", "لون خلفية القائمة", "Menu Background", "لون خلفية القائمة الرئيسية", "#FAFAFA", "COLOR", "MENUS", "#FAFAFA", "2", "Y", "19"},
            {"MENU_TEXT_COLOR", "لون نص القائمة", "Menu Text Color", "لون نص القائمة", "#424242", "COLOR", "MENUS", "#424242", "2", "Y", "20"},
            {"MENU_HOVER_COLOR", "لون القائمة عند التمرير", "Menu Hover Color", "لون القائمة عند التمرير", "#E0E0E0", "COLOR", "MENUS", "#E0E0E0", "2", "Y", "21"}
        };
        
        String sql = """
            INSERT INTO ERP_UI_THEME_SETTINGS (
                SETTING_NAME, SETTING_NAME_AR, SETTING_NAME_EN, SETTING_DESCRIPTION,
                SETTING_VALUE, DEFAULT_VALUE, SETTING_TYPE, SETTING_CATEGORY, SETTING_GROUP,
                PRIORITY_LEVEL, IS_REQUIRED, DISPLAY_ORDER
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            for (String[] setting : basicSettings) {
                stmt.setString(1, setting[0]);  // SETTING_NAME
                stmt.setString(2, setting[1]);  // SETTING_NAME_AR
                stmt.setString(3, setting[2]);  // SETTING_NAME_EN
                stmt.setString(4, setting[3]);  // SETTING_DESCRIPTION
                stmt.setString(5, setting[4]);  // SETTING_VALUE
                stmt.setString(6, setting[7]);  // DEFAULT_VALUE
                stmt.setString(7, setting[5]);  // SETTING_TYPE
                stmt.setString(8, setting[6]);  // SETTING_CATEGORY
                stmt.setString(9, setting[6]);  // SETTING_GROUP
                stmt.setInt(10, Integer.parseInt(setting[8]));  // PRIORITY_LEVEL
                stmt.setString(11, setting[9]); // IS_REQUIRED
                stmt.setInt(12, Integer.parseInt(setting[10])); // DISPLAY_ORDER
                
                stmt.executeUpdate();
                System.out.println("  ✅ " + setting[1]);
            }
        }
        
        System.out.println("✅ تم إدراج " + basicSettings.length + " إعداد أساسي");
    }
    
    private static void createIndexesAndViews(Connection conn) throws SQLException {
        System.out.println("🔍 إنشاء الفهارس والـ Views...");
        
        // إنشاء الفهارس
        String[] indexes = {
            "CREATE INDEX IDX_UI_THEME_NAME ON ERP_UI_THEME_SETTINGS(SETTING_NAME)",
            "CREATE INDEX IDX_UI_THEME_CATEGORY ON ERP_UI_THEME_SETTINGS(SETTING_CATEGORY)",
            "CREATE INDEX IDX_UI_THEME_GROUP ON ERP_UI_THEME_SETTINGS(SETTING_GROUP)",
            "CREATE INDEX IDX_UI_THEME_TYPE ON ERP_UI_THEME_SETTINGS(SETTING_TYPE)",
            "CREATE INDEX IDX_UI_THEME_ACTIVE ON ERP_UI_THEME_SETTINGS(IS_ACTIVE)"
        };
        
        for (String sql : indexes) {
            try {
                conn.createStatement().execute(sql);
            } catch (SQLException e) {
                System.err.println("⚠️ تحذير في إنشاء فهرس: " + e.getMessage());
            }
        }
        
        // إنشاء View
        String createView = """
            CREATE OR REPLACE VIEW VW_UI_THEME_ORGANIZED AS
            SELECT 
                SETTING_ID, SETTING_NAME, SETTING_NAME_AR, SETTING_NAME_EN,
                SETTING_DESCRIPTION, SETTING_VALUE, DEFAULT_VALUE,
                SETTING_TYPE, SETTING_CATEGORY, SETTING_GROUP,
                COLOR_VALUE, HEX_COLOR, FONT_FAMILY, FONT_SIZE,
                THEME_NAME, LOOK_AND_FEEL, IS_DARK_MODE,
                PRIORITY_LEVEL, IS_REQUIRED, IS_ACTIVE, DISPLAY_ORDER,
                CASE SETTING_CATEGORY
                    WHEN 'APPEARANCE' THEN 'إعدادات المظهر'
                    WHEN 'COLORS' THEN 'إعدادات الألوان'
                    WHEN 'FONTS' THEN 'إعدادات الخطوط'
                    WHEN 'BUTTONS' THEN 'إعدادات الأزرار'
                    WHEN 'TABLES' THEN 'إعدادات الجداول'
                    WHEN 'MENUS' THEN 'إعدادات القوائم'
                    ELSE SETTING_CATEGORY
                END AS CATEGORY_NAME_AR,
                CASE PRIORITY_LEVEL
                    WHEN 1 THEN 'عالي'
                    WHEN 2 THEN 'متوسط'
                    WHEN 3 THEN 'منخفض'
                    ELSE 'غير محدد'
                END AS PRIORITY_NAME_AR
            FROM ERP_UI_THEME_SETTINGS
            WHERE IS_ACTIVE = 'Y'
            ORDER BY SETTING_CATEGORY, SETTING_GROUP, DISPLAY_ORDER
        """;
        
        conn.createStatement().execute(createView);
        System.out.println("✅ تم إنشاء الفهارس والـ Views");
    }
}
