@echo off
echo ========================================
echo    TNS ENVIRONMENT SETUP - SIMPLE
echo    اعداد بيئة TNS - مبسط
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Setting up TNS environment...
echo.

echo [1] Checking TNS files...

if exist "network\admin\tnsnames.ora" (
    echo OK: tnsnames.ora found
) else (
    echo WARNING: tnsnames.ora missing
)

if exist "network\admin\sqlnet.ora" (
    echo OK: sqlnet.ora found
) else (
    echo WARNING: sqlnet.ora missing
)

echo.
echo [2] Setting environment variables...

set TNS_ADMIN=%CD%\network\admin
set ORACLE_NET_TNS_ADMIN=%TNS_ADMIN%
set JAVA_TNS_OPTS=-Doracle.net.tns_admin="%TNS_ADMIN%" -Doracle.jdbc.defaultNChar=true -Dfile.encoding=UTF-8

echo TNS_ADMIN: %TNS_ADMIN%
echo JAVA_TNS_OPTS: %JAVA_TNS_OPTS%

echo.
echo [3] Creating directories...

if not exist "network\admin\trace" mkdir network\admin\trace
if not exist "network\admin\log" mkdir network\admin\log

echo OK: Directories created

echo.
echo [4] Creating environment script...

echo @echo off > set-tns-env.bat
echo set TNS_ADMIN=%CD%\network\admin >> set-tns-env.bat
echo set ORACLE_NET_TNS_ADMIN=%%TNS_ADMIN%% >> set-tns-env.bat
echo set JAVA_TNS_OPTS=-Doracle.net.tns_admin="%%TNS_ADMIN%%" -Doracle.jdbc.defaultNChar=true -Dfile.encoding=UTF-8 >> set-tns-env.bat
echo echo TNS Environment configured >> set-tns-env.bat

echo OK: Environment script created: set-tns-env.bat

echo.
echo [5] Testing TNS setup...

if exist "TNSConnectionManager.class" (
    echo Testing TNS connections...
    java %JAVA_TNS_OPTS% -cp . TNSConnectionManager
) else (
    echo INFO: TNSConnectionManager not compiled yet
    echo To compile: javac -d . src\main\java\TNSConnectionManager.java
)

echo.
echo ========================================
echo    SETUP COMPLETED
echo    اكتمل الاعداد
echo ========================================

echo.
echo TNS Configuration Summary:
echo - TNS_ADMIN: %TNS_ADMIN%
echo - Files: tnsnames.ora, sqlnet.ora, listener.ora
echo - Directories: trace, log
echo - Environment script: set-tns-env.bat
echo.
echo Available TNS Names:
echo - ORCL, SHIP_ERP, IAS20251
echo - SHIP_ERP_DEV, SHIP_ERP_TEST
echo - SHIP_ERP_HA, SHIP_ERP_SSL
echo.
echo Usage in Java:
echo - **************************
echo - **************************
echo.
echo [SUCCESS] TNS environment setup completed!
echo.

pause
