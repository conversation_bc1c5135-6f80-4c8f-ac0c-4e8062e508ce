/**
 * اختبار المظاهر المتاحة
 * Test Available Themes
 */
public class TestAvailableThemes {
    
    public static void main(String[] args) {
        System.out.println("🔍 فحص المظاهر المتاحة...");
        
        try {
            AdvancedThemeManager themeManager = AdvancedThemeManager.getInstance();
            
            // طباعة تقرير المظاهر
            themeManager.printThemeReport();
            
            // فحص مكتبات محددة
            System.out.println("\n🔍 فحص مكتبات محددة:");
            
            // فحص FlatLaf Core
            testClass("com.formdev.flatlaf.FlatLightLaf", "FlatLaf Light");
            testClass("com.formdev.flatlaf.FlatDarkLaf", "FlatLaf Dark");
            testClass("com.formdev.flatlaf.FlatIntelliJLaf", "FlatLaf IntelliJ");
            testClass("com.formdev.flatlaf.FlatDarculaLaf", "FlatLaf Darcula");
            
            // فحص IntelliJ Themes
            System.out.println("\n📋 فحص IntelliJ Themes:");
            testClass("com.formdev.flatlaf.intellijthemes.FlatArcIJTheme", "Arc Theme");
            testClass("com.formdev.flatlaf.intellijthemes.FlatDraculaIJTheme", "Dracula Theme");
            testClass("com.formdev.flatlaf.intellijthemes.FlatOneDarkIJTheme", "One Dark Theme");
            testClass("com.formdev.flatlaf.intellijthemes.FlatNordIJTheme", "Nord Theme");
            testClass("com.formdev.flatlaf.intellijthemes.FlatSolarizedLightIJTheme", "Solarized Light");
            
            // فحص JTattoo
            System.out.println("\n📋 فحص JTattoo Themes:");
            testClass("com.jtattoo.plaf.acryl.AcrylLookAndFeel", "Acryl");
            testClass("com.jtattoo.plaf.graphite.GraphiteLookAndFeel", "Graphite");
            testClass("com.jtattoo.plaf.mint.MintLookAndFeel", "Mint");
            testClass("com.jtattoo.plaf.noire.NoireLookAndFeel", "Noire");
            
            // فحص System Themes
            System.out.println("\n📋 فحص System Themes:");
            testClass("javax.swing.plaf.metal.MetalLookAndFeel", "Metal");
            testClass("javax.swing.plaf.nimbus.NimbusLookAndFeel", "Nimbus");
            
            System.out.println("\n✅ انتهى فحص المظاهر");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في فحص المظاهر: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testClass(String className, String themeName) {
        try {
            Class.forName(className);
            System.out.println("  ✅ " + themeName + " - متاح");
        } catch (ClassNotFoundException e) {
            System.out.println("  ❌ " + themeName + " - غير متاح (" + className + ")");
        }
    }
}
