@echo off
echo ========================================
echo   Test Company Settings Window
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Compiling AdvancedCompanySettingsWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\AdvancedCompanySettingsWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile AdvancedCompanySettingsWindow
    pause
    exit /b 1
)

echo.
echo [2] Testing AdvancedCompanySettingsWindow...
java -cp "lib\*;." AdvancedCompanySettingsWindow

echo.
echo Test completed
pause
