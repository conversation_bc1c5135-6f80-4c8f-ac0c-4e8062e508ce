import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenu;
import javax.swing.JMenuBar;
import javax.swing.JMenuItem;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.SwingUtilities;

/**
 * تطبيق Ship ERP مع الشجرة المطوية
 * Ship ERP Application with Collapsed Tree
 */
public class CollapsedShipERP extends JFrame {
    
    private CollapsedTreeMenuPanel treeMenuPanel;
    private JTextField searchField;
    private Font arabicFont;
    
    public CollapsedShipERP() {
        arabicFont = new Font("Ta<PERSON>a", Font.PLAIN, 12);
        initializeApplication();
    }
    
    private void initializeApplication() {
        setTitle("🚢 نظام إدارة الشحنات - Ship ERP System (شجرة مطوية)");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        
        // تطبيق اتجاه اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تطبيق المظهر الموحد
        FinalThemeManager.initializeDefaultTheme();
        
        setupLayout();
        setupMenuBar();
        
        System.out.println("✅ تم تشغيل نظام Ship ERP مع الشجرة المطوية بنجاح!");
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة العلوية - شريط الأدوات
        JPanel topPanel = createTopPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // الشجرة المطوية في الجانب الأيمن
        treeMenuPanel = new CollapsedTreeMenuPanel(this);
        add(treeMenuPanel, BorderLayout.EAST);
        
        // المنطقة الوسطى - منطقة العمل
        JPanel centerPanel = createCenterPanel();
        add(centerPanel, BorderLayout.CENTER);
        
        // اللوحة السفلى - شريط الحالة
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // العنوان
        JLabel titleLabel = new JLabel("🚢 نظام إدارة الشحنات - شجرة الأنظمة المطوية", JLabel.CENTER);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        panel.add(titleLabel, BorderLayout.NORTH);
        
        // شريط الأدوات
        JPanel toolbarPanel = createToolbarPanel();
        panel.add(toolbarPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createToolbarPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // البحث في الشجرة
        JLabel searchLabel = new JLabel("🔍 البحث في الشجرة:");
        searchLabel.setFont(arabicFont);
        
        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.addActionListener(e -> performTreeSearch());
        
        JButton searchButton = new JButton("بحث");
        searchButton.setFont(arabicFont);
        searchButton.addActionListener(e -> performTreeSearch());
        
        JButton clearButton = new JButton("مسح");
        clearButton.setFont(arabicFont);
        clearButton.addActionListener(e -> clearTreeSearch());
        
        // أزرار التحكم في الشجرة
        JButton collapseButton = new JButton("🔽 طي الشجرة");
        collapseButton.setFont(arabicFont);
        collapseButton.addActionListener(e -> treeMenuPanel.collapseAllNodes());
        
        JButton expandMainButton = new JButton("📂 توسيع الرئيسية");
        expandMainButton.setFont(arabicFont);
        expandMainButton.addActionListener(e -> treeMenuPanel.expandMainNodes());
        
        JButton expandAllButton = new JButton("🔼 توسيع الكل");
        expandAllButton.setFont(arabicFont);
        expandAllButton.addActionListener(e -> treeMenuPanel.expandAllNodes());
        
        // إضافة المكونات
        panel.add(searchLabel);
        panel.add(searchField);
        panel.add(searchButton);
        panel.add(clearButton);
        panel.add(new JLabel(" | "));
        panel.add(collapseButton);
        panel.add(expandMainButton);
        panel.add(expandAllButton);
        
        return panel;
    }
    
    private JPanel createCenterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // منطقة العمل الرئيسية
        JLabel workAreaLabel = new JLabel(
            "<html><div style='text-align: center;'>" +
            "<h2>🏢 منطقة العمل الرئيسية</h2>" +
            "<p>مرحباً بك في نظام إدارة الشحنات</p>" +
            "<p>استخدم الشجرة على اليمين للتنقل بين الأنظمة</p>" +
            "<p>الشجرة تظهر في وضع التجميع (مطوية) افتراضياً</p>" +
            "<br>" +
            "<h3>📋 الميزات:</h3>" +
            "<ul style='text-align: right;'>" +
            "<li>🔽 الشجرة مطوية افتراضياً لتوفير المساحة</li>" +
            "<li>🖱️ نقر مزدوج على العقد لتوسيعها/طيها</li>" +
            "<li>🔍 البحث في الشجرة</li>" +
            "<li>📂 أزرار للتحكم في حالة الشجرة</li>" +
            "<li>🔄 تحديث الشجرة من قاعدة البيانات</li>" +
            "</ul>" +
            "</div></html>",
            JLabel.CENTER
        );
        workAreaLabel.setFont(arabicFont);
        workAreaLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        panel.add(workAreaLabel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel statusLabel = new JLabel("✅ النظام جاهز - الشجرة في وضع التجميع");
        statusLabel.setFont(arabicFont);
        panel.add(statusLabel);
        
        return panel;
    }
    
    private void setupMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        menuBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // قائمة الملف
        JMenu fileMenu = new JMenu("ملف");
        fileMenu.setFont(arabicFont);
        fileMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JMenuItem exitItem = new JMenuItem("خروج");
        exitItem.setFont(arabicFont);
        exitItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        exitItem.addActionListener(e -> System.exit(0));
        fileMenu.add(exitItem);
        
        // قائمة العرض
        JMenu viewMenu = new JMenu("عرض");
        viewMenu.setFont(arabicFont);
        viewMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JMenuItem collapseTreeItem = new JMenuItem("🔽 طي الشجرة");
        collapseTreeItem.setFont(arabicFont);
        collapseTreeItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        collapseTreeItem.addActionListener(e -> treeMenuPanel.collapseAllNodes());
        viewMenu.add(collapseTreeItem);
        
        JMenuItem expandMainItem = new JMenuItem("📂 توسيع العقد الرئيسية");
        expandMainItem.setFont(arabicFont);
        expandMainItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        expandMainItem.addActionListener(e -> treeMenuPanel.expandMainNodes());
        viewMenu.add(expandMainItem);
        
        JMenuItem expandAllItem = new JMenuItem("🔼 توسيع جميع العقد");
        expandAllItem.setFont(arabicFont);
        expandAllItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        expandAllItem.addActionListener(e -> treeMenuPanel.expandAllNodes());
        viewMenu.add(expandAllItem);
        
        viewMenu.addSeparator();
        
        JMenuItem refreshTreeItem = new JMenuItem("🔄 تحديث الشجرة");
        refreshTreeItem.setFont(arabicFont);
        refreshTreeItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        refreshTreeItem.addActionListener(e -> treeMenuPanel.refreshTree());
        viewMenu.add(refreshTreeItem);
        
        // قائمة الأدوات
        JMenu toolsMenu = new JMenu("أدوات");
        toolsMenu.setFont(arabicFont);
        toolsMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JMenuItem themeSettingsItem = new JMenuItem("🎨 إعدادات المظهر");
        themeSettingsItem.setFont(arabicFont);
        themeSettingsItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        themeSettingsItem.addActionListener(e -> openThemeSettings());
        toolsMenu.add(themeSettingsItem);
        
        // قائمة المساعدة
        JMenu helpMenu = new JMenu("مساعدة");
        helpMenu.setFont(arabicFont);
        helpMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JMenuItem aboutItem = new JMenuItem("حول البرنامج");
        aboutItem.setFont(arabicFont);
        aboutItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);
        
        // إضافة القوائم
        menuBar.add(fileMenu);
        menuBar.add(viewMenu);
        menuBar.add(toolsMenu);
        menuBar.add(helpMenu);
        
        setJMenuBar(menuBar);
    }
    
    private void performTreeSearch() {
        String searchText = searchField.getText().trim();
        if (!searchText.isEmpty()) {
            treeMenuPanel.searchInTree(searchText);
            System.out.println("🔍 البحث في الشجرة عن: " + searchText);
        }
    }
    
    private void clearTreeSearch() {
        searchField.setText("");
        treeMenuPanel.collapseAllNodes();
        System.out.println("❌ تم مسح البحث وإعادة طي الشجرة");
    }
    
    private void openThemeSettings() {
        try {
            SwingUtilities.invokeLater(() -> {
                ThemeTestWindow themeWindow = new ThemeTestWindow();
                themeWindow.setVisible(true);
            });
        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح إعدادات المظهر: " + e.getMessage());
        }
    }
    
    private void showAboutDialog() {
        javax.swing.JOptionPane.showMessageDialog(this,
            "🚢 نظام إدارة الشحنات\n" +
            "Ship ERP System\n\n" +
            "الإصدار: 1.0\n" +
            "الميزة: شجرة الأنظمة المطوية\n\n" +
            "تطوير: فريق التطوير\n" +
            "التاريخ: " + java.time.LocalDate.now(),
            "حول البرنامج",
            javax.swing.JOptionPane.INFORMATION_MESSAGE);
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("🚢 تشغيل نظام Ship ERP مع الشجرة المطوية");
                System.out.println("Ship ERP with Collapsed Tree Starting...");
                System.out.println("==========================================");
                
                CollapsedShipERP app = new CollapsedShipERP();
                app.setVisible(true);
                
                System.out.println("✅ تم تشغيل النظام بنجاح");
                System.out.println();
                System.out.println("📋 الميزات الجديدة:");
                System.out.println("• 🔽 الشجرة تظهر مطوية افتراضياً");
                System.out.println("• 🖱️ نقر مزدوج لتوسيع/طي العقد");
                System.out.println("• 🔍 البحث في الشجرة");
                System.out.println("• 📂 أزرار التحكم في الشجرة");
                System.out.println("• 🔄 تحديث من قاعدة البيانات");
                
            } catch (Exception e) {
                e.printStackTrace();
                javax.swing.JOptionPane.showMessageDialog(null,
                        "خطأ في تشغيل النظام:\n" + e.getMessage(),
                        "خطأ - Error",
                        javax.swing.JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
