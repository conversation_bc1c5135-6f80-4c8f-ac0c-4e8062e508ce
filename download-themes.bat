@echo off
cd /d "e:\ship_erp\java\lib"

echo Downloading FlatLaf Core...
curl -L -o flatlaf-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar"

echo Downloading FlatLaf Extras...
curl -L -o flatlaf-extras-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar"

echo Downloading FlatLaf IntelliJ Themes...
curl -L -o flatlaf-intellij-themes-3.2.5.jar "https://repo1.maven.org/maven2/com/formdev/flatlaf-intellij-themes/3.2.5/flatlaf-intellij-themes-3.2.5.jar"

echo Downloading Substance Look and Feel...
curl -L -o substance-8.0.02.jar "https://repo1.maven.org/maven2/org/pushingpixels/substance/8.0.02/substance-8.0.02.jar"

echo Downloading Trident Animation...
curl -L -o trident-1.5.00.jar "https://repo1.maven.org/maven2/org/pushingpixels/trident/1.5.00/trident-1.5.00.jar"

echo Downloading JTattoo...
curl -L -o jtattoo-1.6.13.jar "https://repo1.maven.org/maven2/com/jtattoo/JTattoo/1.6.13/JTattoo-1.6.13.jar"

echo Downloading JSON Processing...
curl -L -o json-20230227.jar "https://repo1.maven.org/maven2/org/json/json/20230227/json-20230227.jar"

echo Download completed!
dir /b *.jar | findstr -i "flat\|substance\|tattoo\|json"

pause
