import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.FlowLayout;
import java.awt.Font;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.SwingUtilities;

/**
 * اختبار الشجرة المطوية
 * Collapsed Tree Test
 */
public class CollapsedTreeTest extends JFrame {
    
    private CollapsedTreeMenuPanel treePanel;
    private JTextField searchField;
    private Font arabicFont;
    
    public CollapsedTreeTest() {
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        initializeUI();
    }
    
    private void initializeUI() {
        setTitle("🌳 اختبار الشجرة المطوية - Collapsed Tree Test");
        setSize(1000, 700);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        setLayout(new BorderLayout());
        
        // اللوحة العلوية - أدوات التحكم
        JPanel topPanel = createTopPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // الشجرة المطوية
        treePanel = new CollapsedTreeMenuPanel(this);
        add(treePanel, BorderLayout.CENTER);
        
        // اللوحة السفلى - معلومات
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // العنوان
        JLabel titleLabel = new JLabel("🌳 شجرة الأنظمة في وضع التجميع (مطوية)", JLabel.CENTER);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        panel.add(titleLabel, BorderLayout.NORTH);
        
        // أدوات التحكم
        JPanel controlsPanel = createControlsPanel();
        panel.add(controlsPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createControlsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // البحث
        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);
        
        searchField = new JTextField(15);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.addActionListener(e -> performSearch());
        
        JButton searchButton = new JButton("🔍 بحث");
        searchButton.setFont(arabicFont);
        searchButton.addActionListener(e -> performSearch());
        
        JButton clearButton = new JButton("❌ مسح");
        clearButton.setFont(arabicFont);
        clearButton.addActionListener(e -> clearSearch());
        
        // أزرار التحكم في الشجرة
        JButton collapseButton = new JButton("🔽 طي الكل");
        collapseButton.setFont(arabicFont);
        collapseButton.addActionListener(e -> treePanel.collapseAllNodes());
        
        JButton expandMainButton = new JButton("📂 توسيع الرئيسية");
        expandMainButton.setFont(arabicFont);
        expandMainButton.addActionListener(e -> treePanel.expandMainNodes());
        
        JButton expandAllButton = new JButton("🔼 توسيع الكل");
        expandAllButton.setFont(arabicFont);
        expandAllButton.addActionListener(e -> treePanel.expandAllNodes());
        
        JButton refreshButton = new JButton("🔄 تحديث");
        refreshButton.setFont(arabicFont);
        refreshButton.addActionListener(e -> treePanel.refreshTree());
        
        // إضافة المكونات
        panel.add(searchLabel);
        panel.add(searchField);
        panel.add(searchButton);
        panel.add(clearButton);
        panel.add(new JLabel(" | "));
        panel.add(collapseButton);
        panel.add(expandMainButton);
        panel.add(expandAllButton);
        panel.add(refreshButton);
        
        return panel;
    }
    
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel infoLabel = new JLabel("💡 نقر مزدوج على العقد لتوسيعها/طيها | نقر مزدوج على العناصر لفتحها");
        infoLabel.setFont(arabicFont);
        panel.add(infoLabel);
        
        return panel;
    }
    
    private void performSearch() {
        String searchText = searchField.getText().trim();
        if (!searchText.isEmpty()) {
            treePanel.searchInTree(searchText);
            System.out.println("🔍 البحث عن: " + searchText);
        }
    }
    
    private void clearSearch() {
        searchField.setText("");
        treePanel.collapseAllNodes();
        System.out.println("❌ تم مسح البحث وإعادة طي الشجرة");
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق المظهر الموحد
                FinalThemeManager.initializeDefaultTheme();
                
                System.out.println("🌳 تشغيل اختبار الشجرة المطوية");
                System.out.println("Collapsed Tree Test Starting...");
                System.out.println("==========================================");
                
                CollapsedTreeTest testWindow = new CollapsedTreeTest();
                testWindow.setVisible(true);
                
                System.out.println("✅ تم تشغيل نافذة اختبار الشجرة المطوية");
                System.out.println();
                System.out.println("📋 الميزات المتاحة:");
                System.out.println("• الشجرة تظهر في وضع التجميع (مطوية) افتراضياً");
                System.out.println("• نقر مزدوج على العقد لتوسيعها/طيها");
                System.out.println("• نقر مزدوج على العناصر لفتحها");
                System.out.println("• البحث في الشجرة");
                System.out.println("• أزرار للتحكم في حالة الشجرة");
                System.out.println("• تحديث الشجرة من قاعدة البيانات");
                
            } catch (Exception e) {
                e.printStackTrace();
                javax.swing.JOptionPane.showMessageDialog(null,
                        "خطأ في تشغيل اختبار الشجرة المطوية:\n" + e.getMessage(),
                        "خطأ - Error",
                        javax.swing.JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
