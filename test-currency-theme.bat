@echo off
echo ========================================
echo 🎨 اختبار مظهر نافذة إدارة العملات
echo Testing Currency Management Window Theme
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/3] تجميع النافذة المحدثة...
echo Compiling Updated Window...
echo ========================================

echo تجميع CurrencyManagementWindow مع دعم المظهر...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CurrencyManagementWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع النافذة بنجاح
) else (
    echo ❌ فشل في تجميع النافذة
    pause
    exit /b 1
)

echo.
echo [2/3] اختبار المظهر...
echo Testing Theme...
echo ========================================

echo.
echo اختر طريقة الاختبار:
echo 1. تشغيل النافذة مباشرة (مظهر افتراضي)
echo 2. تشغيل عبر SimpleShipERP (مظهر التطبيق)
echo 3. كلاهما للمقارنة
echo.

set /p choice="أدخل اختيارك (1-3): "

echo.
if "%choice%"=="1" (
    echo تشغيل النافذة مباشرة...
    start "Currency Management - Default Theme" java -cp "lib\*;." CurrencyManagementWindow
    echo ✅ تم تشغيل النافذة مباشرة
) else if "%choice%"=="2" (
    echo تشغيل عبر SimpleShipERP...
    start "Simple Ship ERP - Theme Test" java -cp "lib\*;." SimpleShipERP
    echo ✅ تم تشغيل SimpleShipERP
    echo.
    echo 📋 للاختبار:
    echo 1. انتقل إلى "الإعدادات والإدارة"
    echo 2. افتح "الإعدادات العامة"
    echo 3. انقر على "إعدادات العملة"
    echo 4. لاحظ توافق المظهر مع التطبيق الرئيسي
) else if "%choice%"=="3" (
    echo تشغيل كلا الاختبارين للمقارنة...
    
    echo 1. تشغيل النافذة مباشرة...
    start "Currency Management - Default Theme" java -cp "lib\*;." CurrencyManagementWindow
    
    timeout /t 2 /nobreak >nul
    
    echo 2. تشغيل عبر SimpleShipERP...
    start "Simple Ship ERP - Theme Test" java -cp "lib\*;." SimpleShipERP
    
    echo ✅ تم تشغيل كلا الاختبارين
    echo 📊 قارن بين المظهرين
) else (
    echo ❌ اختيار غير صحيح، سيتم تشغيل النافذة مباشرة
    start "Currency Management - Default Theme" java -cp "lib\*;." CurrencyManagementWindow
)

echo.
echo [3/3] معلومات إصلاح المظهر...
echo Theme Fix Information...
echo ========================================

echo.
echo 🎨 الإصلاحات المطبقة:
echo ====================
echo ✅ تطبيق المظهر الحالي من التطبيق الرئيسي
echo ✅ فحص نوع المظهر (مظلم/فاتح)
echo ✅ إعداد ألوان متوافقة مع المظهر
echo ✅ تطبيق خطوط عربية موحدة
echo ✅ تحديث المكونات بعد الإنشاء

echo.
echo 🔧 الدوال المضافة:
echo ==================
echo • applyCurrentLookAndFeel() - تطبيق المظهر الحالي
echo • setupLookAndFeelProperties() - إعداد خصائص المظهر
echo • isDarkTheme() - فحص نوع المظهر
echo • setupDarkThemeProperties() - إعداد المظهر المظلم
echo • setupLightThemeProperties() - إعداد المظهر الفاتح
echo • setupArabicFontProperties() - إعداد الخطوط العربية

echo.
echo 🎯 النتائج المتوقعة:
echo ===================
echo ✅ النافذة تتبع مظهر التطبيق الرئيسي
echo ✅ الألوان متناسقة مع المظهر المحدد
echo ✅ الخطوط العربية واضحة ومتسقة
echo ✅ الأزرار والحقول بنفس نمط التطبيق
echo ✅ الجداول والقوائم متوافقة مع المظهر

echo.
echo 🌈 أنواع المظاهر المدعومة:
echo ===========================
echo • المظهر الافتراضي (Metal)
echo • المظهر الفاتح (Light Theme)
echo • المظهر المظلم (Dark Theme)
echo • FlatLaf (إذا كان متاحاً)
echo • Nimbus
echo • System Look and Feel

echo.
echo 🔍 كيفية التحقق من التوافق:
echo ============================
echo 1. افتح التطبيق الرئيسي (SimpleShipERP)
echo 2. لاحظ مظهر النوافذ والألوان
echo 3. افتح نافذة إدارة العملات
echo 4. تأكد من توافق الألوان والخطوط
echo 5. جرب التبديل بين التبويبات
echo 6. تحقق من مظهر الأزرار والحقول

echo.
echo 🎨 خصائص المظهر المطبقة:
echo =========================

echo.
echo 🌙 المظهر المظلم:
echo • خلفية رمادية داكنة (60, 63, 65)
echo • حقول نص داكنة (69, 73, 74)
echo • نص فاتح (187, 187, 187)
echo • أزرار زرقاء (75, 110, 175)
echo • تحديد أزرق (75, 110, 175)

echo.
echo ☀️ المظهر الفاتح:
echo • خلفية فاتحة (240, 240, 240)
echo • حقول نص بيضاء (255, 255, 255)
echo • نص أسود (0, 0, 0)
echo • أزرار رمادية فاتحة (225, 225, 225)
echo • تحديد أزرق فاتح (184, 207, 229)

echo.
echo 📝 الخطوط العربية:
echo ==================
echo • الخط الأساسي: Tahoma 12pt
echo • الخط العريض: Tahoma Bold 12pt
echo • دعم كامل للنصوص العربية
echo • اتجاه النص من اليمين لليسار
echo • تطبيق على جميع المكونات

echo.
echo 💡 نصائح للاستخدام:
echo ===================
echo • النافذة تتكيف تلقائياً مع مظهر التطبيق
echo • لا حاجة لإعدادات إضافية
echo • المظهر يطبق عند فتح النافذة
echo • التحديث يحدث تلقائياً
echo • متوافق مع جميع أنواع المظاهر

echo.
echo 🔧 للمطورين:
echo =============
echo • الكود قابل للتوسع لمظاهر جديدة
echo • إعدادات منفصلة لكل نوع مظهر
echo • دعم كامل لـ UIManager
echo • تحديث ديناميكي للمكونات
echo • معالجة الأخطاء المتقدمة

echo.
echo ========================================
echo ✅ تم إصلاح مظهر النافذة بنجاح!
echo Window Theme Successfully Fixed!
echo ========================================

echo.
pause
