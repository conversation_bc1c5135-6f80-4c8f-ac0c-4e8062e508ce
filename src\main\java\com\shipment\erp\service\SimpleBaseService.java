package com.shipment.erp.service;

import com.shipment.erp.model.BaseEntity;
import java.util.List;
import java.util.Optional;

/**
 * خدمة أساسية مبسطة لجميع الكيانات
 * Simple Base Service for all entities
 */
public interface SimpleBaseService<T extends BaseEntity> {
    
    /**
     * حفظ كيان جديد أو تحديث موجود
     */
    T save(T entity);
    
    /**
     * البحث عن كيان بالمعرف
     */
    Optional<T> findById(Long id);
    
    /**
     * الحصول على جميع الكيانات
     */
    List<T> findAll();
    
    /**
     * حذف كيان بالمعرف
     */
    void delete(Long id);
    
    /**
     * حذف كيان
     */
    void delete(T entity);
    
    /**
     * التحقق من وجود كيان بالمعرف
     */
    boolean existsById(Long id);
    
    /**
     * الحصول على عدد الكيانات
     */
    long count();
    
    /**
     * البحث عن كيانات متعددة بالمعرفات
     */
    List<T> findAllById(Iterable<Long> ids);
    
    /**
     * حفظ قائمة من الكيانات
     */
    List<T> saveAll(Iterable<T> entities);
    
    /**
     * حذف جميع الكيانات
     */
    void deleteAll();
    
    /**
     * حذف قائمة من الكيانات
     */
    void deleteAll(Iterable<T> entities);
    
    /**
     * حذف كيانات بالمعرفات
     */
    void deleteAllById(Iterable<Long> ids);
}
