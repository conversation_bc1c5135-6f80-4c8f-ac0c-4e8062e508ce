package com.shipment.erp.view;

import com.shipment.erp.model.AuditLog;

import javax.swing.*;
import java.awt.*;
import java.text.SimpleDateFormat;

/**
 * نافذة تفاصيل سجل التدقيق
 * Audit Log Details Dialog
 */
public class AuditLogDetailsDialog extends JDialog {
    
    private Font arabicFont;
    private AuditLog auditLog;
    
    public AuditLogDetailsDialog(Dialog parent, AuditLog auditLog) {
        super(parent, "تفاصيل سجل التدقيق", true);
        
        this.auditLog = auditLog;
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        initializeComponents();
        setupLayout();
        
        setSize(600, 500);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }
    
    private void initializeComponents() {
        // لا توجد مكونات خاصة للتهيئة
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // معلومات أساسية
        JPanel infoPanel = createInfoPanel();
        
        // تفاصيل التغييرات
        JPanel changesPanel = createChangesPanel();
        
        // أزرار التحكم
        JPanel buttonsPanel = createButtonsPanel();
        
        mainPanel.add(infoPanel, BorderLayout.NORTH);
        mainPanel.add(changesPanel, BorderLayout.CENTER);
        
        add(mainPanel, BorderLayout.CENTER);
        add(buttonsPanel, BorderLayout.SOUTH);
    }
    
    private JPanel createInfoPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("المعلومات الأساسية"));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;
        
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        int row = 0;
        
        // رقم السجل
        gbc.gridx = 1; gbc.gridy = row;
        panel.add(createLabel("رقم السجل:"), gbc);
        gbc.gridx = 0;
        panel.add(createValueLabel(auditLog.getId().toString()), gbc);
        row++;
        
        // المستخدم
        gbc.gridx = 1; gbc.gridy = row;
        panel.add(createLabel("المستخدم:"), gbc);
        gbc.gridx = 0;
        panel.add(createValueLabel("المستخدم " + auditLog.getUserId()), gbc); // TODO: Get actual username
        row++;
        
        // العملية
        gbc.gridx = 1; gbc.gridy = row;
        panel.add(createLabel("العملية:"), gbc);
        gbc.gridx = 0;
        panel.add(createValueLabel(auditLog.getAction()), gbc);
        row++;
        
        // الجدول
        gbc.gridx = 1; gbc.gridy = row;
        panel.add(createLabel("الجدول:"), gbc);
        gbc.gridx = 0;
        panel.add(createValueLabel(auditLog.getTableName()), gbc);
        row++;
        
        // رقم السجل المتأثر
        gbc.gridx = 1; gbc.gridy = row;
        panel.add(createLabel("رقم السجل المتأثر:"), gbc);
        gbc.gridx = 0;
        panel.add(createValueLabel(auditLog.getRecordId() != null ? auditLog.getRecordId().toString() : "غير محدد"), gbc);
        row++;
        
        // عنوان IP
        gbc.gridx = 1; gbc.gridy = row;
        panel.add(createLabel("عنوان IP:"), gbc);
        gbc.gridx = 0;
        panel.add(createValueLabel(auditLog.getIpAddress()), gbc);
        row++;
        
        // متصفح المستخدم
        gbc.gridx = 1; gbc.gridy = row;
        panel.add(createLabel("متصفح المستخدم:"), gbc);
        gbc.gridx = 0;
        JLabel userAgentLabel = createValueLabel(auditLog.getUserAgent());
        userAgentLabel.setToolTipText(auditLog.getUserAgent()); // عرض النص الكامل عند التمرير
        panel.add(userAgentLabel, gbc);
        row++;
        
        // التاريخ والوقت
        gbc.gridx = 1; gbc.gridy = row;
        panel.add(createLabel("التاريخ والوقت:"), gbc);
        gbc.gridx = 0;
        panel.add(createValueLabel(auditLog.getActionDate() != null ? dateFormat.format(auditLog.getActionDate()) : "غير محدد"), gbc);
        row++;
        
        return panel;
    }
    
    private JPanel createChangesPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("تفاصيل التغييرات"));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.setFont(arabicFont);
        
        // تبويب القيم القديمة
        if (auditLog.getOldValues() != null && !auditLog.getOldValues().trim().isEmpty()) {
            JTextArea oldValuesArea = new JTextArea(auditLog.getOldValues());
            oldValuesArea.setFont(new Font("Courier New", Font.PLAIN, 11));
            oldValuesArea.setEditable(false);
            oldValuesArea.setLineWrap(true);
            oldValuesArea.setWrapStyleWord(true);
            oldValuesArea.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
            
            JScrollPane oldScrollPane = new JScrollPane(oldValuesArea);
            oldScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            tabbedPane.addTab("القيم القديمة", oldScrollPane);
        }
        
        // تبويب القيم الجديدة
        if (auditLog.getNewValues() != null && !auditLog.getNewValues().trim().isEmpty()) {
            JTextArea newValuesArea = new JTextArea(auditLog.getNewValues());
            newValuesArea.setFont(new Font("Courier New", Font.PLAIN, 11));
            newValuesArea.setEditable(false);
            newValuesArea.setLineWrap(true);
            newValuesArea.setWrapStyleWord(true);
            newValuesArea.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
            
            JScrollPane newScrollPane = new JScrollPane(newValuesArea);
            newScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            tabbedPane.addTab("القيم الجديدة", newScrollPane);
        }
        
        // إذا لم توجد تغييرات
        if (tabbedPane.getTabCount() == 0) {
            JLabel noChangesLabel = new JLabel("لا توجد تفاصيل تغييرات لهذه العملية", SwingConstants.CENTER);
            noChangesLabel.setFont(arabicFont);
            noChangesLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            panel.add(noChangesLabel, BorderLayout.CENTER);
        } else {
            panel.add(tabbedPane, BorderLayout.CENTER);
        }
        
        return panel;
    }
    
    private JPanel createButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JButton closeButton = new JButton("إغلاق");
        closeButton.setFont(arabicFont);
        closeButton.setPreferredSize(new Dimension(100, 30));
        closeButton.addActionListener(e -> dispose());
        
        JButton copyButton = new JButton("نسخ التفاصيل");
        copyButton.setFont(arabicFont);
        copyButton.setPreferredSize(new Dimension(120, 30));
        copyButton.addActionListener(e -> copyDetailsToClipboard());
        
        panel.add(copyButton);
        panel.add(closeButton);
        
        return panel;
    }
    
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }
    
    private JLabel createValueLabel(String text) {
        JLabel label = new JLabel(text != null ? text : "غير محدد");
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        label.setForeground(Color.BLUE);
        return label;
    }
    
    private void copyDetailsToClipboard() {
        StringBuilder details = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        details.append("تفاصيل سجل التدقيق\n");
        details.append("==================\n");
        details.append("رقم السجل: ").append(auditLog.getId()).append("\n");
        details.append("المستخدم: ").append("المستخدم ").append(auditLog.getUserId()).append("\n");
        details.append("العملية: ").append(auditLog.getAction()).append("\n");
        details.append("الجدول: ").append(auditLog.getTableName()).append("\n");
        details.append("رقم السجل المتأثر: ").append(auditLog.getRecordId() != null ? auditLog.getRecordId() : "غير محدد").append("\n");
        details.append("عنوان IP: ").append(auditLog.getIpAddress()).append("\n");
        details.append("متصفح المستخدم: ").append(auditLog.getUserAgent()).append("\n");
        details.append("التاريخ والوقت: ").append(auditLog.getActionDate() != null ? dateFormat.format(auditLog.getActionDate()) : "غير محدد").append("\n");
        
        if (auditLog.getOldValues() != null && !auditLog.getOldValues().trim().isEmpty()) {
            details.append("\nالقيم القديمة:\n").append(auditLog.getOldValues()).append("\n");
        }
        
        if (auditLog.getNewValues() != null && !auditLog.getNewValues().trim().isEmpty()) {
            details.append("\nالقيم الجديدة:\n").append(auditLog.getNewValues()).append("\n");
        }
        
        // نسخ إلى الحافظة
        java.awt.datatransfer.StringSelection stringSelection = new java.awt.datatransfer.StringSelection(details.toString());
        java.awt.datatransfer.Clipboard clipboard = java.awt.Toolkit.getDefaultToolkit().getSystemClipboard();
        clipboard.setContents(stringSelection, null);
        
        JOptionPane.showMessageDialog(this, "تم نسخ التفاصيل إلى الحافظة", "نجح النسخ", JOptionPane.INFORMATION_MESSAGE);
    }
}
