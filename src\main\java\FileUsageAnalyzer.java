import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * أداة تحليل استخدام الملفات في المشروع
 * File Usage Analyzer Tool
 */
public class FileUsageAnalyzer {
    
    private static final String REPORT_FILE = "FILE_USAGE_ANALYSIS_REPORT.md";
    private static PrintWriter report;
    
    // قوائم الملفات
    private static Set<String> javaFiles = new HashSet<>();
    private static Set<String> classFiles = new HashSet<>();
    private static Set<String> batFiles = new HashSet<>();
    private static Set<String> otherFiles = new HashSet<>();
    
    // قوائم الاستخدام
    private static Map<String, Set<String>> fileUsage = new HashMap<>();
    private static Set<String> usedFiles = new HashSet<>();
    private static Set<String> unusedFiles = new HashSet<>();
    private static Set<String> coreFiles = new HashSet<>();
    
    public static void main(String[] args) {
        try {
            initializeReport();
            initializeCoreFiles();
            
            System.out.println("🔍 بدء تحليل استخدام الملفات...");
            report.println("# تقرير تحليل استخدام الملفات");
            report.println("## File Usage Analysis Report");
            report.println("**التاريخ:** " + new Date());
            report.println("**الهدف:** تحديد الملفات غير المستخدمة للتنظيف الآمن");
            report.println("\n---\n");
            
            // 1. جمع جميع الملفات
            collectAllFiles();
            
            // 2. تحليل الاستخدام
            analyzeFileUsage();
            
            // 3. تحديد الملفات المستخدمة
            identifyUsedFiles();
            
            // 4. تحديد الملفات غير المستخدمة
            identifyUnusedFiles();
            
            // 5. تصنيف الملفات
            categorizeFiles();
            
            // 6. إنشاء التوصيات
            generateRecommendations();
            
            // 7. إنشاء سكريپت التنظيف الآمن
            generateCleanupScript();
            
            System.out.println("✅ تم إنجاز تحليل استخدام الملفات بنجاح!");
            System.out.println("📋 تقرير مفصل متاح في: " + REPORT_FILE);
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحليل الملفات: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (report != null) {
                report.close();
            }
        }
    }
    
    private static void initializeReport() throws IOException {
        report = new PrintWriter(new FileWriter(REPORT_FILE, false), true);
    }
    
    private static void initializeCoreFiles() {
        // الملفات الأساسية التي لا يجب حذفها أبداً
        coreFiles.add("DatabaseConfig.java");
        coreFiles.add("TreeMenuPanel.java");
        coreFiles.add("EnhancedMainWindow.java");
        coreFiles.add("CompleteOracleSystemTest.java");
        coreFiles.add("SettingsManager.java");
        coreFiles.add("UIUtils.java");
        coreFiles.add("ItemData.java");
        coreFiles.add("User.java");
        coreFiles.add("MeasurementUnitsWindow.java");
        coreFiles.add("RealItemDataWindow.java");
        coreFiles.add("ComprehensiveItemDataWindow.java");
        coreFiles.add("ItemGroupsManagementWindow.java");
        coreFiles.add("UserManagementWindow.java");
        coreFiles.add("GeneralSettingsWindow.java");
        
        // ملفات التشغيل الأساسية
        coreFiles.add("start-system.bat");
        coreFiles.add("quick-start.bat");
        coreFiles.add("dev-start.bat");
        
        // المكتبات الأساسية
        coreFiles.add("ojdbc11.jar");
        coreFiles.add("orai18n.jar");
        coreFiles.add("commons-logging-1.2.jar");
    }
    
    private static void collectAllFiles() {
        report.println("## 1. جمع جميع الملفات");
        report.println("### File Collection\n");
        
        try {
            // جمع ملفات Java
            Files.walk(Paths.get("src"))
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".java"))
                    .forEach(path -> javaFiles.add(path.getFileName().toString()));
            
            // جمع ملفات Class
            Files.walk(Paths.get("."))
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".class"))
                    .forEach(path -> classFiles.add(path.getFileName().toString()));
            
            // جمع ملفات Batch
            Files.walk(Paths.get("."))
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".bat"))
                    .forEach(path -> batFiles.add(path.getFileName().toString()));
            
            // جمع الملفات الأخرى
            Files.walk(Paths.get("."))
                    .filter(Files::isRegularFile)
                    .filter(path -> !path.toString().endsWith(".java"))
                    .filter(path -> !path.toString().endsWith(".class"))
                    .filter(path -> !path.toString().endsWith(".bat"))
                    .filter(path -> !path.toString().contains("lib"))
                    .filter(path -> !path.toString().contains(".git"))
                    .forEach(path -> otherFiles.add(path.getFileName().toString()));
            
            report.println("### إحصائيات الملفات:");
            report.println("- **ملفات Java:** " + javaFiles.size());
            report.println("- **ملفات Class:** " + classFiles.size());
            report.println("- **ملفات Batch:** " + batFiles.size());
            report.println("- **ملفات أخرى:** " + otherFiles.size());
            report.println("- **إجمالي الملفات:** " + (javaFiles.size() + classFiles.size() + batFiles.size() + otherFiles.size()));
            
        } catch (Exception e) {
            report.println("❌ خطأ في جمع الملفات: " + e.getMessage());
        }
        
        report.println("\n---\n");
    }
    
    private static void analyzeFileUsage() {
        report.println("## 2. تحليل استخدام الملفات");
        report.println("### File Usage Analysis\n");
        
        try {
            // تحليل ملفات Java للبحث عن الاستيراد والاستخدام
            for (String javaFile : javaFiles) {
                Path filePath = findJavaFile(javaFile);
                if (filePath != null) {
                    analyzeJavaFile(filePath);
                }
            }
            
            // تحليل ملفات Batch للبحث عن الاستخدام
            for (String batFile : batFiles) {
                Path filePath = Paths.get(batFile);
                if (Files.exists(filePath)) {
                    analyzeBatchFile(filePath);
                }
            }
            
            report.println("### نتائج التحليل:");
            report.println("- **ملفات تم تحليلها:** " + fileUsage.size());
            report.println("- **علاقات الاستخدام:** " + fileUsage.values().stream().mapToInt(Set::size).sum());
            
        } catch (Exception e) {
            report.println("❌ خطأ في تحليل الاستخدام: " + e.getMessage());
        }
        
        report.println("\n---\n");
    }
    
    private static Path findJavaFile(String fileName) {
        try {
            return Files.walk(Paths.get("src"))
                    .filter(Files::isRegularFile)
                    .filter(path -> path.getFileName().toString().equals(fileName))
                    .findFirst()
                    .orElse(null);
        } catch (Exception e) {
            return null;
        }
    }
    
    private static void analyzeJavaFile(Path filePath) {
        try {
            List<String> lines = Files.readAllLines(filePath);
            String fileName = filePath.getFileName().toString();
            Set<String> usedInThisFile = new HashSet<>();
            
            for (String line : lines) {
                // البحث عن import statements
                if (line.trim().startsWith("import")) {
                    String importedClass = extractClassName(line);
                    if (importedClass != null) {
                        usedInThisFile.add(importedClass + ".java");
                    }
                }
                
                // البحث عن استخدام الكلاسات
                for (String javaFile : javaFiles) {
                    String className = javaFile.replace(".java", "");
                    if (line.contains(className) && !fileName.equals(javaFile)) {
                        usedInThisFile.add(javaFile);
                    }
                }
            }
            
            fileUsage.put(fileName, usedInThisFile);
            
        } catch (Exception e) {
            System.err.println("خطأ في تحليل ملف: " + filePath);
        }
    }
    
    private static void analyzeBatchFile(Path filePath) {
        try {
            List<String> lines = Files.readAllLines(filePath);
            String fileName = filePath.getFileName().toString();
            Set<String> usedInThisFile = new HashSet<>();
            
            for (String line : lines) {
                // البحث عن استخدام ملفات Java
                for (String javaFile : javaFiles) {
                    String className = javaFile.replace(".java", "");
                    if (line.contains(className)) {
                        usedInThisFile.add(javaFile);
                    }
                }
                
                // البحث عن استخدام ملفات batch أخرى
                for (String batFile : batFiles) {
                    if (line.contains(batFile) && !fileName.equals(batFile)) {
                        usedInThisFile.add(batFile);
                    }
                }
            }
            
            fileUsage.put(fileName, usedInThisFile);
            
        } catch (Exception e) {
            System.err.println("خطأ في تحليل ملف batch: " + filePath);
        }
    }
    
    private static String extractClassName(String importLine) {
        try {
            String[] parts = importLine.split("\\s+");
            if (parts.length >= 2) {
                String fullClassName = parts[1].replace(";", "");
                String[] classParts = fullClassName.split("\\.");
                return classParts[classParts.length - 1];
            }
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
        return null;
    }
    
    private static void identifyUsedFiles() {
        report.println("## 3. تحديد الملفات المستخدمة");
        report.println("### Used Files Identification\n");
        
        // إضافة الملفات الأساسية
        usedFiles.addAll(coreFiles);
        
        // إضافة الملفات المستخدمة من التحليل
        for (Set<String> used : fileUsage.values()) {
            usedFiles.addAll(used);
        }
        
        // إضافة الملفات التي تستخدم ملفات أخرى
        usedFiles.addAll(fileUsage.keySet());
        
        report.println("### الملفات المستخدمة:");
        List<String> sortedUsedFiles = new ArrayList<>(usedFiles);
        Collections.sort(sortedUsedFiles);
        
        for (String file : sortedUsedFiles) {
            if (coreFiles.contains(file)) {
                report.println("- ✅ **" + file + "** (ملف أساسي)");
            } else {
                report.println("- ✅ **" + file + "** (مستخدم)");
            }
        }
        
        report.println("\n**إجمالي الملفات المستخدمة:** " + usedFiles.size());
        report.println("\n---\n");
    }
    
    private static void identifyUnusedFiles() {
        report.println("## 4. تحديد الملفات غير المستخدمة");
        report.println("### Unused Files Identification\n");
        
        // جمع جميع الملفات
        Set<String> allFiles = new HashSet<>();
        allFiles.addAll(javaFiles);
        allFiles.addAll(batFiles);
        allFiles.addAll(otherFiles);
        
        // تحديد الملفات غير المستخدمة
        for (String file : allFiles) {
            if (!usedFiles.contains(file) && !coreFiles.contains(file)) {
                unusedFiles.add(file);
            }
        }
        
        report.println("### الملفات غير المستخدمة:");
        if (unusedFiles.isEmpty()) {
            report.println("✅ **لا توجد ملفات غير مستخدمة!**");
        } else {
            List<String> sortedUnusedFiles = new ArrayList<>(unusedFiles);
            Collections.sort(sortedUnusedFiles);
            
            for (String file : sortedUnusedFiles) {
                report.println("- ⚠️ **" + file + "** (غير مستخدم)");
            }
        }
        
        report.println("\n**إجمالي الملفات غير المستخدمة:** " + unusedFiles.size());
        report.println("\n---\n");
    }
    
    private static void categorizeFiles() {
        report.println("## 5. تصنيف الملفات");
        report.println("### File Categorization\n");
        
        // تصنيف الملفات حسب النوع والاستخدام
        Map<String, List<String>> categories = new HashMap<>();
        categories.put("ملفات أساسية (لا تحذف)", new ArrayList<>());
        categories.put("ملفات مستخدمة", new ArrayList<>());
        categories.put("ملفات غير مستخدمة (آمنة للحذف)", new ArrayList<>());
        categories.put("ملفات مشكوك فيها", new ArrayList<>());
        
        // تصنيف جميع الملفات
        Set<String> allFiles = new HashSet<>();
        allFiles.addAll(javaFiles);
        allFiles.addAll(batFiles);
        allFiles.addAll(otherFiles);
        
        for (String file : allFiles) {
            if (coreFiles.contains(file)) {
                categories.get("ملفات أساسية (لا تحذف)").add(file);
            } else if (usedFiles.contains(file)) {
                categories.get("ملفات مستخدمة").add(file);
            } else if (unusedFiles.contains(file)) {
                if (isSafeToDelete(file)) {
                    categories.get("ملفات غير مستخدمة (آمنة للحذف)").add(file);
                } else {
                    categories.get("ملفات مشكوك فيها").add(file);
                }
            }
        }
        
        // طباعة التصنيفات
        for (Map.Entry<String, List<String>> entry : categories.entrySet()) {
            report.println("### " + entry.getKey() + ":");
            List<String> files = entry.getValue();
            Collections.sort(files);
            
            if (files.isEmpty()) {
                report.println("- لا توجد ملفات في هذا التصنيف");
            } else {
                for (String file : files) {
                    report.println("- " + file);
                }
            }
            report.println();
        }
        
        report.println("---\n");
    }
    
    private static boolean isSafeToDelete(String fileName) {
        // قواعد الأمان للحذف
        if (fileName.endsWith(".java")) {
            // ملفات Java آمنة للحذف إذا لم تكن مستخدمة
            return true;
        }
        
        if (fileName.endsWith(".class")) {
            // ملفات Class آمنة للحذف (يمكن إعادة تجميعها)
            return true;
        }
        
        if (fileName.endsWith(".bat")) {
            // ملفات Batch تحتاج فحص دقيق
            return !fileName.contains("start") && !fileName.contains("run");
        }
        
        // الملفات الأخرى تحتاج فحص يدوي
        return false;
    }
    
    private static void generateRecommendations() {
        report.println("## 6. التوصيات");
        report.println("### Recommendations\n");
        
        report.println("### ✅ آمن للحذف:");
        if (unusedFiles.isEmpty()) {
            report.println("- لا توجد ملفات غير مستخدمة للحذف");
        } else {
            for (String file : unusedFiles) {
                if (isSafeToDelete(file)) {
                    report.println("- **" + file + "** - آمن للحذف");
                }
            }
        }
        
        report.println("\n### ⚠️ يحتاج مراجعة:");
        for (String file : unusedFiles) {
            if (!isSafeToDelete(file)) {
                report.println("- **" + file + "** - يحتاج مراجعة يدوية");
            }
        }
        
        report.println("\n### 🚫 لا تحذف أبداً:");
        List<String> sortedCoreFiles = new ArrayList<>(coreFiles);
        Collections.sort(sortedCoreFiles);
        for (String file : sortedCoreFiles) {
            report.println("- **" + file + "** - ملف أساسي");
        }
        
        report.println("\n---\n");
    }
    
    private static void generateCleanupScript() {
        report.println("## 7. سكريپت التنظيف الآمن");
        report.println("### Safe Cleanup Script\n");
        
        try {
            PrintWriter cleanupScript = new PrintWriter(new FileWriter("safe-cleanup.bat", false), true);
            
            cleanupScript.println("@echo off");
            cleanupScript.println("echo ========================================");
            cleanupScript.println("echo    SAFE CLEANUP SCRIPT");
            cleanupScript.println("echo    سكريپت التنظيف الآمن");
            cleanupScript.println("echo ========================================");
            cleanupScript.println();
            cleanupScript.println("echo [WARNING] This script will delete unused files!");
            cleanupScript.println("echo [تحذير] هذا السكريپت سيحذف الملفات غير المستخدمة!");
            cleanupScript.println("echo.");
            cleanupScript.println("pause");
            cleanupScript.println();
            
            boolean hasFilesToDelete = false;
            for (String file : unusedFiles) {
                if (isSafeToDelete(file)) {
                    cleanupScript.println("if exist \"" + file + "\" (");
                    cleanupScript.println("    echo Deleting: " + file);
                    cleanupScript.println("    del /q \"" + file + "\"");
                    cleanupScript.println(")");
                    hasFilesToDelete = true;
                }
            }
            
            if (!hasFilesToDelete) {
                cleanupScript.println("echo No files to delete - system is already clean!");
                cleanupScript.println("echo لا توجد ملفات للحذف - النظام نظيف بالفعل!");
            }
            
            cleanupScript.println();
            cleanupScript.println("echo Cleanup completed!");
            cleanupScript.println("echo تم التنظيف بنجاح!");
            cleanupScript.println("pause");
            
            cleanupScript.close();
            
            report.println("✅ **تم إنشاء سكريپت التنظيف الآمن:** `safe-cleanup.bat`");
            
        } catch (Exception e) {
            report.println("❌ خطأ في إنشاء سكريپت التنظيف: " + e.getMessage());
        }
        
        report.println("\n---\n");
    }
}
