@echo off
echo ========================================
echo 🌳 تشغيل التطبيق مع الشجرة المطوية
echo Running App with Collapsed Tree
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/3] تجميع الملفات...
echo Compiling Files...
echo ========================================

echo تجميع CollapsedTreeMenuPanel...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CollapsedTreeMenuPanel.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CollapsedTreeMenuPanel بنجاح
) else (
    echo ❌ فشل في تجميع CollapsedTreeMenuPanel
    pause
    exit /b 1
)

echo تجميع CollapsedTreeTest...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CollapsedTreeTest.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CollapsedTreeTest بنجاح
) else (
    echo ❌ فشل في تجميع CollapsedTreeTest
    pause
    exit /b 1
)

echo تجميع CollapsedShipERP...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CollapsedShipERP.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع CollapsedShipERP بنجاح
) else (
    echo ❌ فشل في تجميع CollapsedShipERP
    pause
    exit /b 1
)

echo.
echo [2/3] اختيار التطبيق للتشغيل...
echo Choose Application to Run...
echo ========================================

echo.
echo اختر التطبيق الذي تريد تشغيله:
echo 1. اختبار الشجرة المطوية (CollapsedTreeTest)
echo 2. نظام Ship ERP مع الشجرة المطوية (CollapsedShipERP)
echo 3. تشغيل كلاهما
echo.

set /p choice="أدخل اختيارك (1-3): "

echo.
echo [3/3] تشغيل التطبيق...
echo Running Application...
echo ========================================

if "%choice%"=="1" (
    echo تشغيل اختبار الشجرة المطوية...
    start "Collapsed Tree Test" java -cp "lib\*;." CollapsedTreeTest
    echo ✅ تم تشغيل اختبار الشجرة المطوية
) else if "%choice%"=="2" (
    echo تشغيل نظام Ship ERP مع الشجرة المطوية...
    start "Collapsed Ship ERP" java -cp "lib\*;." CollapsedShipERP
    echo ✅ تم تشغيل نظام Ship ERP مع الشجرة المطوية
) else if "%choice%"=="3" (
    echo تشغيل كلا التطبيقين...
    start "Collapsed Tree Test" java -cp "lib\*;." CollapsedTreeTest
    timeout /t 2 /nobreak >nul
    start "Collapsed Ship ERP" java -cp "lib\*;." CollapsedShipERP
    echo ✅ تم تشغيل كلا التطبيقين
) else (
    echo ❌ اختيار غير صحيح، سيتم تشغيل الاختبار افتراضياً
    start "Collapsed Tree Test" java -cp "lib\*;." CollapsedTreeTest
)

echo.
echo ========================================
echo ✅ تم تشغيل التطبيق بنجاح!
echo Application Started Successfully!
echo ========================================

echo.
echo 📋 ميزات الشجرة المطوية:
echo ==========================
echo • 🔽 الشجرة تظهر مطوية افتراضياً لتوفير المساحة
echo • 🖱️ نقر مزدوج على العقد لتوسيعها/طيها
echo • 🔍 البحث في الشجرة
echo • 📂 أزرار للتحكم في حالة الشجرة:
echo   - طي الكل
echo   - توسيع العقد الرئيسية
echo   - توسيع الكل
echo • 🔄 تحديث الشجرة من قاعدة البيانات
echo • 🎨 دعم كامل للعربية مع RTL

echo.
echo 💡 كيفية الاستخدام:
echo ==================
echo • نقر مزدوج على العقد لتوسيعها/طيها
echo • نقر مزدوج على العناصر لفتحها
echo • استخدم مربع البحث للبحث في الشجرة
echo • استخدم الأزرار للتحكم في حالة الشجرة

echo.
echo 🔧 للتطوير:
echo ============
echo • استخدم CollapsedTreeMenuPanel بدلاً من TreeMenuPanel
echo • الشجرة تبدأ مطوية تلقائياً
echo • يمكن التحكم في حالة الشجرة برمجياً

echo.
pause
