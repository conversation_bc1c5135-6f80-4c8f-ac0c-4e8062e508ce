import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Scanner;

/**
 * اختبار الاتصال بقاعدة البيانات Oracle Oracle Database Connection Test
 */
public class DatabaseConnectionTest {

    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   اختبار الاتصال بقاعدة البيانات Oracle");
        System.out.println("   Oracle Database Connection Test");
        System.out.println("====================================");
        System.out.println();

        // فحص مكتبة Oracle JDBC
        if (!checkOracleJDBCDriver()) {
            System.err.println("❌ مكتبة Oracle JDBC غير موجودة!");
            System.err.println("يرجى تشغيل LibraryDownloader أولاً");
            return;
        }

        Scanner scanner = new Scanner(System.in);

        // الحصول على بيانات الاتصال من المستخدم
        System.out.println("أدخل بيانات الاتصال:");
        System.out.println();

        System.out.print("عنوان الخادم (localhost): ");
        String host = scanner.nextLine().trim();
        if (host.isEmpty())
            host = "localhost";

        System.out.print("المنفذ (1521): ");
        String port = scanner.nextLine().trim();
        if (port.isEmpty())
            port = "1521";

        System.out.print("اسم الخدمة (orcl): ");
        String serviceName = scanner.nextLine().trim();
        if (serviceName.isEmpty())
            serviceName = "orcl";

        System.out.print("اسم المستخدم (ship_erp): ");
        String username = scanner.nextLine().trim();
        if (username.isEmpty())
            username = "ship_erp";

        System.out.print("كلمة المرور (ship_erp_password): ");
        String password = scanner.nextLine().trim();
        if (password.isEmpty())
            password = "ship_erp_password";

        System.out.println();
        System.out.println("====================================");
        System.out.println("   بدء اختبار الاتصال");
        System.out.println("====================================");

        // اختبار الاتصال
        testConnection(host, port, serviceName, username, password);

        scanner.close();
    }

    /**
     * فحص وجود مكتبة Oracle JDBC
     */
    private static boolean checkOracleJDBCDriver() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            System.out.println("✅ مكتبة Oracle JDBC موجودة ومحملة");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    private static void testConnection(String host, String port, String serviceName,
            String username, String password) {
        String url = String.format("**************************", host, port, serviceName);

        System.out.println("رابط الاتصال: " + url);
        System.out.println("المستخدم: " + username);
        System.out.println();

        Connection connection = null;

        try {
            System.out.println("🔄 جاري الاتصال...");

            // إنشاء الاتصال
            connection = DriverManager.getConnection(url, username, password);

            System.out.println("✅ نجح الاتصال!");
            System.out.println();

            // الحصول على معلومات قاعدة البيانات
            DatabaseMetaData metaData = connection.getMetaData();

            System.out.println("====================================");
            System.out.println("   معلومات قاعدة البيانات");
            System.out.println("====================================");
            System.out.println("اسم قاعدة البيانات: " + metaData.getDatabaseProductName());
            System.out.println("إصدار قاعدة البيانات: " + metaData.getDatabaseProductVersion());
            System.out.println("اسم التعريف: " + metaData.getDriverName());
            System.out.println("إصدار التعريف: " + metaData.getDriverVersion());
            System.out.println("المستخدم الحالي: " + metaData.getUserName());
            System.out.println();

            // اختبار استعلام بسيط
            System.out.println("🔄 اختبار استعلام بسيط...");
            try (Statement stmt = connection.createStatement();
                    ResultSet rs = stmt.executeQuery("SELECT SYSDATE FROM DUAL")) {

                if (rs.next()) {
                    System.out.println("✅ الاستعلام نجح!");
                    System.out.println("التاريخ والوقت الحالي: " + rs.getTimestamp(1));
                }
            }
            System.out.println();

            // البحث عن جداول الأصناف
            System.out.println("🔍 البحث عن جداول الأصناف...");
            searchForItemTables(connection);

        } catch (SQLException e) {
            System.err.println("❌ فشل الاتصال!");
            System.err.println("رمز الخطأ: " + e.getErrorCode());
            System.err.println("رسالة الخطأ: " + e.getMessage());
            System.err.println();

            // اقتراحات لحل المشاكل الشائعة
            suggestSolutions(e);

        } finally {
            // إغلاق الاتصال
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("🔒 تم إغلاق الاتصال");
                } catch (SQLException e) {
                    System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
                }
            }
        }
    }

    /**
     * البحث عن جداول الأصناف
     */
    private static void searchForItemTables(Connection connection) throws SQLException {
        String query = """
                    SELECT table_name, num_rows
                    FROM user_tables
                    WHERE table_name LIKE '%ITEM%'
                       OR table_name LIKE '%PRODUCT%'
                       OR table_name LIKE '%INVENTORY%'
                    ORDER BY table_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            boolean found = false;
            System.out.println("الجداول المتاحة للأصناف:");
            System.out.println("-------------------------");

            while (rs.next()) {
                String tableName = rs.getString("table_name");
                Object numRows = rs.getObject("num_rows");

                System.out.printf("📋 %s", tableName);
                if (numRows != null) {
                    System.out.printf(" (%s سجل)", numRows);
                }
                System.out.println();

                found = true;
            }

            if (!found) {
                System.out.println("❌ لم يتم العثور على جداول أصناف");
                System.out.println("تأكد من وجود جداول تحتوي على ITEM أو PRODUCT في اسمها");
            } else {
                System.out.println("✅ تم العثور على جداول أصناف");
            }

        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن الجداول: " + e.getMessage());
        }
    }

    /**
     * اقتراح حلول للمشاكل الشائعة
     */
    private static void suggestSolutions(SQLException e) {
        System.out.println("====================================");
        System.out.println("   اقتراحات لحل المشكلة");
        System.out.println("====================================");

        int errorCode = e.getErrorCode();
        String message = e.getMessage().toLowerCase();

        if (errorCode == 17002 || message.contains("io exception")
                || message.contains("connection refused")) {
            System.out.println("🔧 مشكلة في الاتصال بالخادم:");
            System.out.println("   • تأكد من أن خادم Oracle يعمل");
            System.out.println("   • تحقق من عنوان الخادم والمنفذ");
            System.out.println("   • تأكد من عدم وجود جدار حماية يحجب الاتصال");

        } else if (errorCode == 1017 || message.contains("invalid username/password")) {
            System.out.println("🔧 مشكلة في اسم المستخدم أو كلمة المرور:");
            System.out.println("   • تحقق من صحة اسم المستخدم وكلمة المرور");
            System.out.println("   • تأكد من أن الحساب غير مقفل");
            System.out.println("   • جرب الاتصال باستخدام SQL*Plus أولاً");

        } else if (errorCode == 12505 || message.contains("invalid sid")) {
            System.out.println("🔧 مشكلة في اسم الخدمة:");
            System.out.println("   • تحقق من صحة اسم الخدمة (SID)");
            System.out.println("   • جرب استخدام XE أو ORCL");
            System.out.println("   • تأكد من أن الخدمة تعمل");

        } else if (message.contains("listener")) {
            System.out.println("🔧 مشكلة في Oracle Listener:");
            System.out.println("   • تأكد من تشغيل Oracle Listener");
            System.out.println("   • تحقق من إعدادات listener.ora");
            System.out.println("   • جرب إعادة تشغيل Listener");

        } else {
            System.out.println("🔧 نصائح عامة:");
            System.out.println("   • تأكد من تثبيت Oracle Database بشكل صحيح");
            System.out.println("   • تحقق من متغيرات البيئة (ORACLE_HOME, PATH)");
            System.out.println("   • راجع سجلات Oracle للمزيد من التفاصيل");
        }

        System.out.println();
        System.out.println("💡 للمساعدة الإضافية:");
        System.out.println("   • راجع دليل تثبيت Oracle Database");
        System.out.println("   • تحقق من حالة خدمات Oracle في Windows Services");
        System.out.println("   • استخدم Oracle SQL Developer للاختبار");
    }
}
