@echo off
echo ========================================
echo 🛡️ اختبار الاستيراد الآمن من NULL
echo Testing NULL-Safe Import
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/3] التحقق من الجداول...
echo Checking Tables...
echo ========================================

echo التحقق من جدول ERP_SUPPLIERS...
sqlplus -s ship_erp/ship_erp_password@localhost:1521/ORCL << EOF
SELECT 'ERP_SUPPLIERS: ' || COUNT(*) || ' records' FROM ERP_SUPPLIERS;
EXIT;
EOF

echo.
echo [2/3] تجميع النافذة المحدثة...
echo Compiling Updated Window...
echo ========================================

echo تجميع SuppliersManagementWindow مع معالجة NULL...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\SuppliersManagementWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع النافذة بنجاح
) else (
    echo ❌ فشل في تجميع النافذة
    pause
    exit /b 1
)

echo.
echo [3/3] تشغيل النافذة...
echo Running Window...
echo ========================================

echo تشغيل النافذة مع الاستيراد الآمن...
start "NULL-Safe Import - Suppliers Management" java -cp "lib\*;." SuppliersManagementWindow

echo.
echo ========================================
echo 🛡️ الاستيراد الآمن من NULL جاهز!
echo NULL-Safe Import Ready!
echo ========================================

echo.
echo 🎯 المشاكل المحلولة:
echo ===================

echo.
echo ❌ المشكلة السابقة:
echo • ORA-01400: لا يجوز إدراج NULL في ID
echo • فشل في استيراد السجلات
echo • توقف العملية عند أول خطأ

echo.
echo ✅ الحل المطبق:
echo • فلترة السجلات التي تحتوي على NULL في ID
echo • إنشاء ID تلقائي للسجلات المفقودة
echo • معالجة الحقول المطلوبة الأخرى
echo • استمرار العملية رغم الأخطاء

echo.
echo 🔧 الميزات الجديدة:
echo ==================

echo.
echo 🛡️ معالجة NULL الذكية:
echo ----------------------
echo ✅ فلترة WHERE ID IS NOT NULL
echo ✅ إنشاء ID تلقائي من Sequence
echo ✅ قيم افتراضية للحقول المطلوبة
echo ✅ معالجة الأعمدة المفقودة

echo.
echo 🔄 قيم افتراضية ذكية:
echo ---------------------
echo • ID: من ERP_SUPPLIERS_SEQ.NEXTVAL
echo • CODE: IMP_[رقم السجل]
echo • NAME: مورد مستورد [رقم السجل]
echo • باقي الحقول: NULL مسموح

echo.
echo 📊 تقارير محسنة:
echo ----------------
echo ✅ عدد السجلات المفلترة
echo ✅ عدد السجلات المنسوخة بنجاح
echo ✅ تفاصيل الأخطاء المتبقية
echo ✅ إحصائيات شاملة

echo.
echo 📋 خطوات الاستخدام:
echo ===================

echo.
echo 1️⃣ فتح النافذة:
echo • تم تشغيل النافذة تلقائياً
echo • ابحث عن زر "استيراد من IAS20251"

echo.
echo 2️⃣ إدخال بيانات الاتصال:
echo • انقر على زر الاستيراد
echo • أدخل بيانات الاتصال الصحيحة
echo • تأكد من صحة المستخدم وكلمة المرور

echo.
echo 3️⃣ مراقبة العملية:
echo • راقب فحص الأعمدة
echo • تحقق من فلترة NULL
echo • راقب إنشاء القيم التلقائية
echo • راجع التقرير النهائي

echo.
echo 🎉 النتائج المتوقعة:
echo ====================

echo.
echo ✅ نجح الاستيراد:
echo • لا توجد أخطاء ORA-01400
echo • تم إنشاء ID تلقائي للسجلات
echo • نسخ ناجح للبيانات الصالحة
echo • تقرير شامل عن العملية

echo.
echo 📈 إحصائيات محتملة:
echo • إجمالي السجلات في V_DETAILS: X
echo • السجلات المفلترة (NULL ID): Y
echo • السجلات المنسوخة بنجاح: Z
echo • الأخطاء المتبقية: 0 (متوقع)

echo.
echo 🔧 حل المشاكل:
echo ===============

echo.
echo ❌ إذا ظهرت أخطاء أخرى:
echo • تحقق من أنواع البيانات
echo • راجع القيود على الجدول
echo • تأكد من مساحة قاعدة البيانات
echo • راجع صلاحيات المستخدم

echo.
echo ❌ إذا لم تظهر بيانات:
echo • تحقق من وجود بيانات في V_DETAILS
echo • تأكد من عدم فلترة جميع السجلات
echo • راجع شروط WHERE في الاستعلام
echo • تحقق من الأعمدة المشتركة

echo.
echo 💡 نصائح مهمة:
echo ===============

echo.
echo 🎯 للحصول على أفضل النتائج:
echo • تأكد من وجود بيانات صالحة في V_DETAILS
echo • راجع بنية الجدول قبل الاستيراد
echo • اعمل نسخة احتياطية قبل البدء
echo • راقب استهلاك الذاكرة أثناء العملية

echo.
echo 🔍 للتحقق من النتائج:
echo • افحص جدول ERP_SUPPLIERS بعد الاستيراد
echo • تحقق من القيم المنشأة تلقائياً
echo • راجع البيانات المنسوخة
echo • قارن مع البيانات الأصلية

echo.
echo 🚀 ابدأ الآن:
echo ============
echo 1. افتح النافذة (تم تشغيلها)
echo 2. انقر "استيراد من IAS20251"
echo 3. أدخل بيانات الاتصال
echo 4. راقب العملية الآمنة
echo 5. راجع النتائج النهائية

echo.
echo 🎊 متوقع: استيراد ناجح بدون أخطاء NULL!
echo.
pause
