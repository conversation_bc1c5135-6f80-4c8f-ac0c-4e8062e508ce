import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * مستكشف جداول قاعدة بيانات Ship ERP Ship ERP Database Tables Explorer
 */
public class ShipErpTablesExplorer {

    public static void main(String[] args) {
        System.out.println("🚢 مستكشف جداول قاعدة بيانات Ship ERP");
        System.out.println("========================================");

        try {
            TNSConnectionManager tnsManager = TNSConnectionManager.getInstance();
            Connection connection = tnsManager.getShipErpConnection();

            exploreShipErpTables(connection);

            connection.close();
            System.out.println("✅ انتهى استكشاف قاعدة بيانات Ship ERP");

        } catch (Exception e) {
            System.err.println("❌ خطأ في استكشاف قاعدة بيانات Ship ERP: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void exploreShipErpTables(Connection connection) throws SQLException {
        System.out.println("\n📋 استكشاف جداول Ship ERP...");

        // الحصول على جميع الجداول في schema الحالي
        String query = """
                SELECT table_name, num_rows, last_analyzed
                FROM user_tables
                ORDER BY table_name
                """;

        Map<String, List<TableInfo>> categorizedTables = new HashMap<>();
        int totalTables = 0;
        long totalRows = 0;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                String tableName = rs.getString("table_name");
                int numRows = rs.getInt("num_rows");
                java.sql.Date lastAnalyzed = rs.getDate("last_analyzed");

                // الحصول على العدد الفعلي للصفوف
                int actualRows = getActualRowCount(connection, tableName);

                String category = categorizeShipErpTable(tableName);
                TableInfo tableInfo = new TableInfo(tableName, actualRows, numRows, lastAnalyzed);

                categorizedTables.computeIfAbsent(category, k -> new ArrayList<>()).add(tableInfo);
                totalTables++;
                totalRows += actualRows;
            }
        }

        System.out.println("✅ تم العثور على " + totalTables + " جدول في Ship ERP");

        // عرض الجداول مصنفة
        displayShipErpTables(categorizedTables);

        // إحصائيات مفصلة
        displayShipErpStatistics(categorizedTables, totalTables, totalRows);

        // عرض هيكل الجداول المهمة
        displayImportantTableStructures(connection, categorizedTables);
    }

    private static String categorizeShipErpTable(String tableName) {
        String upperName = tableName.toUpperCase();

        // جداول النظام والإعدادات
        if (upperName.startsWith("ERP_SYSTEM") || upperName.contains("SYSTEM")
                || upperName.contains("CONFIG") || upperName.contains("SETTINGS")) {
            return "🔧 جداول النظام والإعدادات";
        }

        // جداول المظاهر والواجهة
        if (upperName.contains("THEME") || upperName.contains("UI_")
                || upperName.startsWith("ERP_UI") || upperName.contains("COMPLETE_THEME")) {
            return "🎨 جداول المظاهر والواجهة";
        }

        // جداول الشحن والحاويات
        if (upperName.contains("SHIP") || upperName.contains("VESSEL")
                || upperName.contains("CONTAINER") || upperName.contains("CARGO")
                || upperName.contains("VOYAGE") || upperName.contains("PORT")) {
            return "🚢 جداول الشحن والحاويات";
        }

        // جداول الأصناف والمخزون
        if (upperName.contains("ITEM") || upperName.contains("STOCK")
                || upperName.contains("INVENTORY") || upperName.contains("PRODUCT")
                || upperName.contains("CATEGORY") || upperName.contains("UNIT")) {
            return "📦 جداول الأصناف والمخزون";
        }

        // جداول العملاء والموردين
        if (upperName.contains("CUSTOMER") || upperName.contains("SUPPLIER")
                || upperName.contains("VENDOR") || upperName.contains("CLIENT")) {
            return "👥 جداول العملاء والموردين";
        }

        // جداول المالية والمحاسبة
        if (upperName.contains("ACCOUNT") || upperName.contains("FINANCE")
                || upperName.contains("PAYMENT") || upperName.contains("INVOICE")
                || upperName.contains("COST") || upperName.contains("PRICE")) {
            return "💰 جداول المالية والمحاسبة";
        }

        // جداول الموظفين والمستخدمين
        if (upperName.contains("EMPLOYEE") || upperName.contains("USER")
                || upperName.contains("STAFF") || upperName.contains("ROLE")) {
            return "👨‍💼 جداول الموظفين والمستخدمين";
        }

        // جداول الشركات والفروع
        if (upperName.contains("COMPANY") || upperName.contains("BRANCH")
                || upperName.contains("LOCATION") || upperName.contains("OFFICE")) {
            return "🏢 جداول الشركات والفروع";
        }

        // جداول التقارير والسجلات
        if (upperName.contains("REPORT") || upperName.contains("LOG") || upperName.contains("AUDIT")
                || upperName.contains("HISTORY")) {
            return "📊 جداول التقارير والسجلات";
        }

        // جداول الجمارك والوثائق
        if (upperName.contains("CUSTOMS") || upperName.contains("DOCUMENT")
                || upperName.contains("CERTIFICATE") || upperName.contains("PERMIT")) {
            return "📋 جداول الجمارك والوثائق";
        }

        return "📋 جداول أخرى";
    }

    private static void displayShipErpTables(Map<String, List<TableInfo>> categorizedTables) {
        System.out.println("\n📊 جداول Ship ERP مصنفة حسب الفئة:");
        System.out.println("========================================");

        for (Map.Entry<String, List<TableInfo>> entry : categorizedTables.entrySet()) {
            String category = entry.getKey();
            List<TableInfo> tables = entry.getValue();

            System.out.println("\n" + category + " (" + tables.size() + " جدول):");

            // ترتيب الجداول حسب عدد الصفوف
            tables.sort((a, b) -> Integer.compare(b.actualRows, a.actualRows));

            for (TableInfo table : tables) {
                String status = table.actualRows > 0 ? "✅" : "⚪";
                String rowsText = String.format("%,d", table.actualRows);
                System.out.println("  " + status + " " + table.name + " (" + rowsText + " صف)");
            }
        }
    }

    private static void displayShipErpStatistics(Map<String, List<TableInfo>> categorizedTables,
            int totalTables, long totalRows) {
        System.out.println("\n📈 إحصائيات Ship ERP:");
        System.out.println("========================================");

        int tablesWithData = 0;
        for (List<TableInfo> tables : categorizedTables.values()) {
            for (TableInfo table : tables) {
                if (table.actualRows > 0) {
                    tablesWithData++;
                }
            }
        }

        System.out.println("📊 إجمالي الجداول: " + totalTables);
        System.out.println("📊 الجداول التي تحتوي على بيانات: " + tablesWithData);
        System.out.println("📊 الجداول الفارغة: " + (totalTables - tablesWithData));
        System.out.println("📊 إجمالي الصفوف: " + String.format("%,d", totalRows));

        if (totalTables > 0) {
            System.out.println("📊 متوسط الصفوف لكل جدول: "
                    + String.format("%.1f", (double) totalRows / totalTables));
        }

        // أكبر الجداول
        System.out.println("\n🔝 أكبر 10 جداول في Ship ERP:");
        List<TableInfo> allTables = new ArrayList<>();
        for (List<TableInfo> tables : categorizedTables.values()) {
            allTables.addAll(tables);
        }

        allTables.sort((a, b) -> Integer.compare(b.actualRows, a.actualRows));
        for (int i = 0; i < Math.min(10, allTables.size()); i++) {
            TableInfo table = allTables.get(i);
            if (table.actualRows > 0) {
                System.out.println("  " + (i + 1) + ". " + table.name + " - "
                        + String.format("%,d", table.actualRows) + " صف");
            }
        }
    }

    private static void displayImportantTableStructures(Connection connection,
            Map<String, List<TableInfo>> categorizedTables) {
        System.out.println("\n🔍 هيكل الجداول المهمة:");
        System.out.println("========================================");

        // البحث عن الجداول المهمة
        String[] importantTables = {"ERP_SYSTEM_TREE", "ERP_UI_THEME_SETTINGS",
                "ERP_COMPLETE_THEMES", "COMPANIES", "BRANCHES"};

        for (String tableName : importantTables) {
            showTableStructure(connection, tableName);
        }
    }

    private static void showTableStructure(Connection connection, String tableName) {
        try {
            String query = """
                    SELECT column_name, data_type, data_length, nullable, data_default
                    FROM user_tab_columns
                    WHERE table_name = ?
                    ORDER BY column_id
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(query)) {
                stmt.setString(1, tableName.toUpperCase());

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        System.out.println("\n📋 جدول: " + tableName);
                        System.out.println("  الأعمدة:");

                        do {
                            String columnName = rs.getString("column_name");
                            String dataType = rs.getString("data_type");
                            int dataLength = rs.getInt("data_length");
                            String nullable = rs.getString("nullable");
                            String defaultValue = rs.getString("data_default");

                            String typeInfo = dataType;
                            if (dataLength > 0 && !dataType.equals("DATE")
                                    && !dataType.equals("NUMBER")) {
                                typeInfo += "(" + dataLength + ")";
                            }

                            String nullInfo = "Y".equals(nullable) ? "NULL" : "NOT NULL";
                            String defaultInfo =
                                    defaultValue != null ? " DEFAULT " + defaultValue : "";

                            System.out.println("    - " + columnName + " " + typeInfo + " "
                                    + nullInfo + defaultInfo);

                        } while (rs.next());
                    }
                }
            }
        } catch (SQLException e) {
            // الجدول غير موجود
        }
    }

    private static int getActualRowCount(Connection connection, String tableName) {
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
            int count = 0;
            if (rs.next()) {
                count = rs.getInt(1);
            }
            rs.close();
            stmt.close();
            return count;
        } catch (SQLException e) {
            return 0;
        }
    }

    static class TableInfo {
        String name;
        int actualRows;
        int statisticsRows;
        java.sql.Date lastAnalyzed;

        TableInfo(String name, int actualRows, int statisticsRows, java.sql.Date lastAnalyzed) {
            this.name = name;
            this.actualRows = actualRows;
            this.statisticsRows = statisticsRows;
            this.lastAnalyzed = lastAnalyzed;
        }
    }
}
