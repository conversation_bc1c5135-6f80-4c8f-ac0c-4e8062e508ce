Write-Host "========================================" -ForegroundColor Green
Write-Host "  COMPREHENSIVE DOWNLOAD RETRY" -ForegroundColor Green
Write-Host "  إعادة التنزيل الشاملة" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Set-Location "e:\ship_erp\java\lib"

# Function to download with extensive retry logic
function Download-Comprehensive {
    param(
        [string]$Name,
        [array]$UrlList,
        [string]$OutputFile,
        [int]$MinSize = 10000
    )
    
    Write-Host "`n[$Name]" -ForegroundColor White
    Write-Host "Target file: $OutputFile" -ForegroundColor Gray
    
    # Remove existing small/corrupted file
    if (Test-Path $OutputFile) {
        $existingSize = (Get-Item $OutputFile).Length
        if ($existingSize -lt $MinSize) {
            Write-Host "Removing corrupted file ($existingSize bytes)" -ForegroundColor Yellow
            Remove-Item $OutputFile -Force
        } else {
            Write-Host "File already exists and valid ($existingSize bytes)" -ForegroundColor Green
            return $true
        }
    }
    
    foreach ($url in $UrlList) {
        try {
            Write-Host "  Trying: $url" -ForegroundColor Cyan
            
            # Use different methods
            $methods = @(
                { Invoke-WebRequest -Uri $url -OutFile $OutputFile -TimeoutSec 120 -UserAgent "Mozilla/5.0" },
                { Invoke-RestMethod -Uri $url -OutFile $OutputFile -TimeoutSec 120 },
                { (New-Object System.Net.WebClient).DownloadFile($url, $OutputFile) }
            )
            
            foreach ($method in $methods) {
                try {
                    & $method
                    
                    if (Test-Path $OutputFile) {
                        $size = (Get-Item $OutputFile).Length
                        if ($size -gt $MinSize) {
                            Write-Host "    SUCCESS: $OutputFile ($size bytes)" -ForegroundColor Green
                            return $true
                        } else {
                            Write-Host "    File too small: $size bytes" -ForegroundColor Yellow
                            Remove-Item $OutputFile -Force -ErrorAction SilentlyContinue
                        }
                    }
                }
                catch {
                    Write-Host "    Method failed: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
        catch {
            Write-Host "    URL failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Start-Sleep -Seconds 1
    }
    
    Write-Host "    ALL ATTEMPTS FAILED for $Name" -ForegroundColor Red
    return $false
}

# Comprehensive library definitions with multiple sources
$libraries = @(
    @{
        Name = "FlatLaf Core"
        File = "flatlaf-3.2.5.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar",
            "https://repo1.maven.org/maven2/com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar",
            "https://central.sonatype.com/artifact/com.formdev/flatlaf/3.2.5/jar",
            "https://mvnrepository.com/artifact/com.formdev/flatlaf/3.2.5"
        )
    },
    @{
        Name = "FlatLaf Extras"
        File = "flatlaf-extras-3.2.5.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar",
            "https://repo1.maven.org/maven2/com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar",
            "https://central.sonatype.com/artifact/com.formdev/flatlaf-extras/3.2.5/jar"
        )
    },
    @{
        Name = "FlatLaf IntelliJ Themes"
        File = "flatlaf-intellij-themes-3.2.5.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-intellij-themes/3.2.5/flatlaf-intellij-themes-3.2.5.jar",
            "https://repo1.maven.org/maven2/com/formdev/flatlaf-intellij-themes/3.2.5/flatlaf-intellij-themes-3.2.5.jar",
            "https://central.sonatype.com/artifact/com.formdev/flatlaf-intellij-themes/3.2.5/jar"
        )
    },
    @{
        Name = "JTattoo Look and Feel"
        File = "jtattoo-1.6.13.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=com/jtattoo/JTattoo/1.6.13/JTattoo-1.6.13.jar",
            "https://repo1.maven.org/maven2/com/jtattoo/JTattoo/1.6.13/JTattoo-1.6.13.jar",
            "https://central.sonatype.com/artifact/com.jtattoo/JTattoo/1.6.13/jar",
            "https://search.maven.org/remotecontent?filepath=com/jtattoo/JTattoo/1.6.12/JTattoo-1.6.12.jar"
        )
    },
    @{
        Name = "Substance Look and Feel"
        File = "substance-8.0.02.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=org/pushingpixels/substance/8.0.02/substance-8.0.02.jar",
            "https://repo1.maven.org/maven2/org/pushingpixels/substance/8.0.02/substance-8.0.02.jar",
            "https://search.maven.org/remotecontent?filepath=org/pushingpixels/substance/7.3.04/substance-7.3.04.jar",
            "https://repo1.maven.org/maven2/org/pushingpixels/substance/7.3.04/substance-7.3.04.jar"
        )
    },
    @{
        Name = "Trident Animation"
        File = "trident-1.5.00.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=org/pushingpixels/trident/1.5.00/trident-1.5.00.jar",
            "https://repo1.maven.org/maven2/org/pushingpixels/trident/1.5.00/trident-1.5.00.jar",
            "https://search.maven.org/remotecontent?filepath=org/pushingpixels/trident/1.4.00/trident-1.4.00.jar",
            "https://repo1.maven.org/maven2/org/pushingpixels/trident/1.4.00/trident-1.4.00.jar"
        )
    },
    @{
        Name = "DarkLaf Core"
        File = "darklaf-core-3.0.2.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=com/github/weisj/darklaf-core/3.0.2/darklaf-core-3.0.2.jar",
            "https://repo1.maven.org/maven2/com/github/weisj/darklaf-core/3.0.2/darklaf-core-3.0.2.jar",
            "https://search.maven.org/remotecontent?filepath=com/github/weisj/darklaf-core/2.7.3/darklaf-core-2.7.3.jar"
        )
    },
    @{
        Name = "DarkLaf Theme"
        File = "darklaf-theme-3.0.2.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=com/github/weisj/darklaf-theme/3.0.2/darklaf-theme-3.0.2.jar",
            "https://repo1.maven.org/maven2/com/github/weisj/darklaf-theme/3.0.2/darklaf-theme-3.0.2.jar",
            "https://search.maven.org/remotecontent?filepath=com/github/weisj/darklaf-theme/2.7.3/darklaf-theme-2.7.3.jar"
        )
    },
    @{
        Name = "Material UI Swing"
        File = "material-ui-swing-1.1.4.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=io/github/vincenzopalazzo/material-ui-swing/1.1.4/material-ui-swing-1.1.4.jar",
            "https://repo1.maven.org/maven2/io/github/vincenzopalazzo/material-ui-swing/1.1.4/material-ui-swing-1.1.4.jar",
            "https://search.maven.org/remotecontent?filepath=io/github/vincenzopalazzo/material-ui-swing/1.1.2/material-ui-swing-1.1.2.jar"
        )
    },
    @{
        Name = "WebLaF Core"
        File = "weblaf-core-2.2.1.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=com/weblookandfeel/weblaf-core/2.2.1/weblaf-core-2.2.1.jar",
            "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-core/2.2.1/weblaf-core-2.2.1.jar",
            "https://search.maven.org/remotecontent?filepath=com/weblookandfeel/weblaf-core/2.1.0/weblaf-core-2.1.0.jar",
            "https://search.maven.org/remotecontent?filepath=com/weblookandfeel/weblaf-core/2.0.3/weblaf-core-2.0.3.jar"
        )
    },
    @{
        Name = "WebLaF UI"
        File = "weblaf-ui-2.2.1.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=com/weblookandfeel/weblaf-ui/2.2.1/weblaf-ui-2.2.1.jar",
            "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-ui/2.2.1/weblaf-ui-2.2.1.jar",
            "https://search.maven.org/remotecontent?filepath=com/weblookandfeel/weblaf-ui/2.1.0/weblaf-ui-2.1.0.jar",
            "https://search.maven.org/remotecontent?filepath=com/weblookandfeel/weblaf-ui/2.0.3/weblaf-ui-2.0.3.jar"
        )
    },
    @{
        Name = "Synthetica Look and Feel"
        File = "synthetica-2.30.0.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=de/javasoft/synthetica/2.30.0/synthetica-2.30.0.jar",
            "https://repo1.maven.org/maven2/de/javasoft/synthetica/2.30.0/synthetica-2.30.0.jar",
            "https://search.maven.org/remotecontent?filepath=de/javasoft/synthetica/2.29.0/synthetica-2.29.0.jar"
        )
    },
    @{
        Name = "BeautyEye Look and Feel"
        File = "beautyeye-3.7.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=org/jb2011/beautyeye/3.7/beautyeye-3.7.jar",
            "https://repo1.maven.org/maven2/org/jb2011/beautyeye/3.7/beautyeye-3.7.jar",
            "https://search.maven.org/remotecontent?filepath=org/jb2011/beautyeye/3.6/beautyeye-3.6.jar"
        )
    },
    @{
        Name = "Seaglass Look and Feel"
        File = "seaglass-0.2.1.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=com/seaglasslookandfeel/seaglasslookandfeel/0.2.1/seaglasslookandfeel-0.2.1.jar",
            "https://repo1.maven.org/maven2/com/seaglasslookandfeel/seaglasslookandfeel/0.2.1/seaglasslookandfeel-0.2.1.jar",
            "https://search.maven.org/remotecontent?filepath=com/seaglasslookandfeel/seaglasslookandfeel/0.2/seaglasslookandfeel-0.2.jar"
        )
    },
    @{
        Name = "JSON Processing"
        File = "json-20230227.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=org/json/json/20230227/json-20230227.jar",
            "https://repo1.maven.org/maven2/org/json/json/20230227/json-20230227.jar",
            "https://search.maven.org/remotecontent?filepath=org/json/json/20220924/json-20220924.jar"
        )
    },
    @{
        Name = "Apache Commons IO"
        File = "commons-io-2.11.0.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=commons-io/commons-io/2.11.0/commons-io-2.11.0.jar",
            "https://repo1.maven.org/maven2/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar",
            "https://search.maven.org/remotecontent?filepath=commons-io/commons-io/2.10.0/commons-io-2.10.0.jar"
        )
    },
    @{
        Name = "Apache Commons Lang"
        File = "commons-lang3-3.12.0.jar"
        Urls = @(
            "https://search.maven.org/remotecontent?filepath=org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar",
            "https://repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar",
            "https://search.maven.org/remotecontent?filepath=org/apache/commons/commons-lang3/3.11/commons-lang3-3.11.jar"
        )
    }
)

$successCount = 0
$failCount = 0
$totalLibraries = $libraries.Count

Write-Host "Starting comprehensive download of $totalLibraries libraries..." -ForegroundColor White
Write-Host "============================================================" -ForegroundColor White

foreach ($lib in $libraries) {
    $result = Download-Comprehensive -Name $lib.Name -UrlList $lib.Urls -OutputFile $lib.File
    if ($result) {
        $successCount++
    } else {
        $failCount++
    }
    
    # Progress indicator
    $progress = [math]::Round(($successCount + $failCount) / $totalLibraries * 100, 1)
    Write-Host "Progress: $progress% ($($successCount + $failCount)/$totalLibraries)" -ForegroundColor Gray
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "  COMPREHENSIVE DOWNLOAD RESULTS" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "Total Libraries Attempted: $totalLibraries" -ForegroundColor White
Write-Host "Successfully Downloaded: $successCount" -ForegroundColor Green
Write-Host "Failed Downloads: $failCount" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round($successCount / $totalLibraries * 100, 1))%" -ForegroundColor Cyan

Write-Host "`n[VERIFICATION]" -ForegroundColor Yellow
Write-Host "Verifying all downloaded files..." -ForegroundColor Yellow

$verifiedCount = 0
foreach ($lib in $libraries) {
    if (Test-Path $lib.File) {
        $size = (Get-Item $lib.File).Length
        if ($size -gt 10000) {
            Write-Host "  ✓ $($lib.Name): $($lib.File) ($size bytes)" -ForegroundColor Green
            $verifiedCount++
        } else {
            Write-Host "  ✗ $($lib.Name): $($lib.File) (TOO SMALL: $size bytes)" -ForegroundColor Red
        }
    } else {
        Write-Host "  ✗ $($lib.Name): $($lib.File) (MISSING)" -ForegroundColor Red
    }
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "  FINAL SUMMARY" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "Libraries Available: $verifiedCount/$totalLibraries" -ForegroundColor White

if ($verifiedCount -ge ($totalLibraries * 0.7)) {
    Write-Host "`n🎉 EXCELLENT! Most libraries downloaded successfully!" -ForegroundColor Green
    Write-Host "🎉 ممتاز! تم تحميل معظم المكتبات بنجاح!" -ForegroundColor Green
} elseif ($verifiedCount -ge ($totalLibraries * 0.5)) {
    Write-Host "`n✅ GOOD! Sufficient libraries for basic functionality!" -ForegroundColor Yellow
    Write-Host "✅ جيد! مكتبات كافية للوظائف الأساسية!" -ForegroundColor Yellow
} else {
    Write-Host "`n⚠️ LIMITED! Some functionality may be restricted!" -ForegroundColor Red
    Write-Host "⚠️ محدود! قد تكون بعض الوظائف مقيدة!" -ForegroundColor Red
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
