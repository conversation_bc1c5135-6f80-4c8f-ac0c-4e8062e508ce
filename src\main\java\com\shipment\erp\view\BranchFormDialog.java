package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dialog;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import com.shipment.erp.model.Branch;
import com.shipment.erp.model.Company;

/**
 * نموذج إدخال/تعديل بيانات الفرع Branch Form Dialog
 */
public class BranchFormDialog extends JDialog {

    private Font arabicFont;
    private Branch branch;
    private boolean confirmed = false;
    private List<Company> companiesList;

    // حقول الإدخال
    private JComboBox<Company> companyCombo;
    private JTextField codeField;
    private JTextField nameField;
    private JTextField nameEnField;
    private JTextArea addressArea;
    private JTextField managerNameField;
    private JTextField phoneField;
    private JTextField emailField;
    private JCheckBox activeCheckBox;

    public BranchFormDialog(Dialog parent, String title, Branch branch,
            List<Company> companiesList) {
        super(parent, title, true);

        this.branch = branch;
        this.companiesList = companiesList;
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeComponents();
        setupLayout();
        loadData();

        setSize(600, 600);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }

    private void initializeComponents() {
        // قائمة الشركات
        companyCombo = new JComboBox<>();
        companyCombo.setFont(arabicFont);
        companyCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تحميل الشركات
        for (Company company : companiesList) {
            companyCombo.addItem(company);
        }

        // كود الفرع
        codeField = new JTextField(20);
        codeField.setFont(arabicFont);
        codeField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        // اسم الفرع بالعربية
        nameField = new JTextField(30);
        nameField.setFont(arabicFont);
        nameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // اسم الفرع بالإنجليزية
        nameEnField = new JTextField(30);
        nameEnField.setFont(arabicFont);
        nameEnField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        // العنوان
        addressArea = new JTextArea(3, 30);
        addressArea.setFont(arabicFont);
        addressArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        addressArea.setLineWrap(true);
        addressArea.setWrapStyleWord(true);

        // اسم المدير
        managerNameField = new JTextField(30);
        managerNameField.setFont(arabicFont);
        managerNameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الهاتف
        phoneField = new JTextField(20);
        phoneField.setFont(arabicFont);
        phoneField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        // البريد الإلكتروني
        emailField = new JTextField(30);
        emailField.setFont(arabicFont);
        emailField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);

        // الحالة
        activeCheckBox = new JCheckBox("نشط");
        activeCheckBox.setFont(arabicFont);
        activeCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        activeCheckBox.setSelected(true);
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // الشركة
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الشركة:*"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(companyCombo, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // كود الفرع
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("كود الفرع:*"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(codeField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // اسم الفرع بالعربية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("اسم الفرع (عربي):*"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(nameField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // اسم الفرع بالإنجليزية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("اسم الفرع (إنجليزي):"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(nameEnField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // العنوان
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("العنوان:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.BOTH;
        mainPanel.add(new JScrollPane(addressArea), gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // اسم المدير
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("اسم المدير:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(managerNameField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // الهاتف
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الهاتف:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(phoneField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // البريد الإلكتروني
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("البريد الإلكتروني:"), gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(emailField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;

        // الحالة
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الحالة:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(activeCheckBox, gbc);
        row++;

        // أزرار التحكم
        JPanel buttonsPanel = createButtonsPanel();

        add(mainPanel, BorderLayout.CENTER);
        add(buttonsPanel, BorderLayout.SOUTH);
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    private JPanel createButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton saveButton = new JButton("حفظ");
        saveButton.setFont(arabicFont);
        saveButton.setPreferredSize(new Dimension(100, 30));
        saveButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (validateInput()) {
                    saveData();
                    confirmed = true;
                    dispose();
                }
            }
        });

        JButton cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.setPreferredSize(new Dimension(100, 30));
        cancelButton.addActionListener(e -> dispose());

        panel.add(saveButton);
        panel.add(cancelButton);

        return panel;
    }

    private void loadData() {
        if (branch != null) {
            // تحديد الشركة
            for (int i = 0; i < companyCombo.getItemCount(); i++) {
                Company company = companyCombo.getItemAt(i);
                if (company.getId().equals(branch.getCompany().getId())) {
                    companyCombo.setSelectedIndex(i);
                    break;
                }
            }

            codeField.setText(branch.getCode());
            nameField.setText(branch.getName());
            nameEnField.setText(branch.getNameEn());
            addressArea.setText(branch.getAddress());
            managerNameField.setText(branch.getManagerName());
            phoneField.setText(branch.getPhone());
            emailField.setText(branch.getEmail());
            activeCheckBox.setSelected(branch.getIsActive());
        }
    }

    private boolean validateInput() {
        if (companyCombo.getSelectedItem() == null) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار الشركة", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            companyCombo.requestFocus();
            return false;
        }

        if (codeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كود الفرع", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }

        if (nameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم الفرع", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            nameField.requestFocus();
            return false;
        }

        String email = emailField.getText().trim();
        if (!email.isEmpty() && !isValidEmail(email)) {
            JOptionPane.showMessageDialog(this, "البريد الإلكتروني غير صحيح", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            emailField.requestFocus();
            return false;
        }

        return true;
    }

    private boolean isValidEmail(String email) {
        return email.matches("^[A-Za-z0-9+_.-]+@(.+)$");
    }

    private void saveData() {
        if (branch == null) {
            branch = new Branch();
        }

        Company selectedCompany = (Company) companyCombo.getSelectedItem();
        branch.setCompany(selectedCompany);
        branch.setCode(codeField.getText().trim());
        branch.setName(nameField.getText().trim());
        branch.setNameEn(nameEnField.getText().trim());
        branch.setAddress(addressArea.getText().trim());
        branch.setManagerName(managerNameField.getText().trim());
        branch.setPhone(phoneField.getText().trim());
        branch.setEmail(emailField.getText().trim());
        branch.setIsActive(activeCheckBox.isSelected());
    }

    public Branch getBranch() {
        return branch;
    }

    public boolean isConfirmed() {
        return confirmed;
    }
}
