@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 📧 نافذة صندوق البريد الوارد المتقدم
echo    Advanced Email Inbox Window
echo ================================================
echo.

echo 🔨 تجميع النافذة...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\EmailInboxWindow.java

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ خطأ في التجميع!
    echo    Compilation Error!
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح!
echo    Compilation Successful!
echo.

echo 🚀 تشغيل النافذة...
echo    Starting Email Inbox Window...
echo.

java -cp "lib\*;." EmailInboxWindow

echo.
echo 📧 تم إغلاق نافذة صندوق البريد الوارد
echo    Email Inbox Window Closed
echo.
pause
