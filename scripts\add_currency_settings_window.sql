-- =====================================================
-- إضافة نافذة إعدادات العملة إلى شجرة الأنظمة
-- Add Currency Settings Window to System Tree
-- =====================================================

PROMPT '💰 إضافة نافذة إعدادات العملة إلى شجرة الأنظمة'
PROMPT 'Adding Currency Settings Window to System Tree'
PROMPT '================================================='

-- تعيين التنسيق للعرض
SET PAGESIZE 50
SET LINESIZE 120
SET SERVEROUTPUT ON

PROMPT
PROMPT '[1] فحص الهيكل الحالي...'
PROMPT '========================'

-- فحص فئة الإعدادات العامة
SELECT 
    TREE_ID,
    NODE_NAME_AR,
    NODE_TYPE,
    DISPLAY_ORDER
FROM ERP_SYSTEM_TREE 
WHERE NODE_NAME_AR = 'الإعدادات العامة'
AND NODE_TYPE = 'CATEGORY';

PROMPT
PROMPT '[2] فحص النوافذ الموجودة في الإعدادات العامة...'
PROMPT '=============================================='

-- عرض النوافذ الحالية في فئة الإعدادات العامة
SELECT 
    TREE_ID,
    LPAD(' ', 4) || NODE_NAME_AR AS WINDOW_NAME,
    NODE_TYPE,
    DISPLAY_ORDER,
    WINDOW_CLASS
FROM ERP_SYSTEM_TREE 
WHERE PARENT_ID = (
    SELECT TREE_ID 
    FROM ERP_SYSTEM_TREE 
    WHERE NODE_NAME_AR = 'الإعدادات العامة'
)
ORDER BY DISPLAY_ORDER;

PROMPT
PROMPT '[3] فحص وجود نافذة إعدادات العملة...'
PROMPT '===================================='

-- فحص وجود النافذة مسبقاً
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '⚠️ النافذة موجودة مسبقاً'
        ELSE '✅ يمكن إضافة النافذة'
    END AS STATUS
FROM ERP_SYSTEM_TREE 
WHERE NODE_NAME_AR = 'إعدادات العملة' 
OR NODE_NAME_EN = 'Currency Settings';

PROMPT
PROMPT '[4] إضافة نافذة إعدادات العملة...'
PROMPT '================================='

-- إضافة النافذة الجديدة
INSERT INTO ERP_SYSTEM_TREE (
    TREE_ID,
    PARENT_ID,
    NODE_NAME_AR,
    NODE_NAME_EN,
    NODE_DESCRIPTION,
    NODE_TYPE,
    WINDOW_CLASS,
    ICON_PATH,
    DISPLAY_ORDER,
    TREE_LEVEL,
    IS_ACTIVE,
    IS_VISIBLE,
    ACCESS_PERMISSIONS,
    ADDITIONAL_INFO,
    CREATED_DATE,
    CREATED_BY,
    LAST_UPDATED,
    UPDATED_BY,
    VERSION_NUMBER
)
SELECT 
    (SELECT NVL(MAX(TREE_ID), 0) + 1 FROM ERP_SYSTEM_TREE) AS TREE_ID,
    gs.TREE_ID AS PARENT_ID,
    'إعدادات العملة' AS NODE_NAME_AR,
    'Currency Settings' AS NODE_NAME_EN,
    'إعدادات وإدارة العملات المستخدمة في النظام - قيد التطوير' AS NODE_DESCRIPTION,
    'WINDOW' AS NODE_TYPE,
    'CurrencySettingsWindow' AS WINDOW_CLASS,
    'icons/currency.png' AS ICON_PATH,
    COALESCE(
        (SELECT MAX(DISPLAY_ORDER) + 1 
         FROM ERP_SYSTEM_TREE 
         WHERE PARENT_ID = gs.TREE_ID), 
        1
    ) AS DISPLAY_ORDER,
    2 AS TREE_LEVEL,
    'Y' AS IS_ACTIVE,
    'Y' AS IS_VISIBLE,
    'ADMIN,SETTINGS_MANAGER' AS ACCESS_PERMISSIONS,
    'STATUS:UNDER_DEVELOPMENT;PRIORITY:MEDIUM;MODULE:SETTINGS;VERSION:1.0' AS ADDITIONAL_INFO,
    SYSDATE AS CREATED_DATE,
    USER AS CREATED_BY,
    SYSDATE AS LAST_UPDATED,
    USER AS UPDATED_BY,
    1 AS VERSION_NUMBER
FROM ERP_SYSTEM_TREE gs
WHERE gs.NODE_NAME_AR = 'الإعدادات العامة'
AND gs.NODE_TYPE = 'CATEGORY'
AND NOT EXISTS (
    SELECT 1 FROM ERP_SYSTEM_TREE 
    WHERE NODE_NAME_AR = 'إعدادات العملة'
);

-- التحقق من نجاح الإدراج
PROMPT
PROMPT '[5] التحقق من نجاح الإضافة...'
PROMPT '============================='

DECLARE
    v_count NUMBER;
    v_tree_id NUMBER;
    v_parent_id NUMBER;
    v_display_order NUMBER;
BEGIN
    -- فحص وجود النافذة
    SELECT COUNT(*), MAX(TREE_ID)
    INTO v_count, v_tree_id
    FROM ERP_SYSTEM_TREE 
    WHERE NODE_NAME_AR = 'إعدادات العملة';
    
    IF v_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('✅ تم إضافة نافذة إعدادات العملة بنجاح');
        DBMS_OUTPUT.PUT_LINE('🆔 معرف العقدة: ' || v_tree_id);
        
        -- الحصول على تفاصيل إضافية
        SELECT PARENT_ID, DISPLAY_ORDER
        INTO v_parent_id, v_display_order
        FROM ERP_SYSTEM_TREE 
        WHERE TREE_ID = v_tree_id;
        
        DBMS_OUTPUT.PUT_LINE('👨‍👩‍👧‍👦 معرف الأب: ' || v_parent_id);
        DBMS_OUTPUT.PUT_LINE('📊 ترتيب العرض: ' || v_display_order);
        DBMS_OUTPUT.PUT_LINE('🏷️ الحالة: قيد التطوير');
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ فشل في إضافة النافذة');
    END IF;
END;
/

PROMPT
PROMPT '[6] عرض الهيكل المحدث...'
PROMPT '========================'

-- عرض الهيكل المحدث لفئة الإعدادات العامة
SELECT 
    TREE_ID,
    LPAD(' ', (TREE_LEVEL - 1) * 4) || NODE_NAME_AR AS TREE_STRUCTURE,
    NODE_TYPE,
    DISPLAY_ORDER,
    CASE 
        WHEN ADDITIONAL_INFO LIKE '%UNDER_DEVELOPMENT%' THEN '🚧 قيد التطوير'
        WHEN IS_ACTIVE = 'Y' THEN '✅ نشط'
        ELSE '❌ غير نشط'
    END AS STATUS,
    WINDOW_CLASS
FROM ERP_SYSTEM_TREE
WHERE TREE_ID IN (
    -- فئة الإعدادات العامة
    SELECT TREE_ID FROM ERP_SYSTEM_TREE 
    WHERE NODE_NAME_AR = 'الإعدادات العامة'
    UNION
    -- جميع النوافذ تحت الإعدادات العامة
    SELECT TREE_ID FROM ERP_SYSTEM_TREE 
    WHERE PARENT_ID = (
        SELECT TREE_ID FROM ERP_SYSTEM_TREE 
        WHERE NODE_NAME_AR = 'الإعدادات العامة'
    )
)
ORDER BY TREE_LEVEL, DISPLAY_ORDER;

PROMPT
PROMPT '[7] تفاصيل نافذة إعدادات العملة...'
PROMPT '=================================='

-- عرض تفاصيل النافذة المضافة
SELECT 
    TREE_ID AS "معرف العقدة",
    NODE_NAME_AR AS "الاسم العربي",
    NODE_NAME_EN AS "الاسم الإنجليزي",
    NODE_DESCRIPTION AS "الوصف",
    WINDOW_CLASS AS "كلاس النافذة",
    DISPLAY_ORDER AS "ترتيب العرض",
    ACCESS_PERMISSIONS AS "الصلاحيات",
    TO_CHAR(CREATED_DATE, 'YYYY-MM-DD HH24:MI:SS') AS "تاريخ الإنشاء"
FROM ERP_SYSTEM_TREE 
WHERE NODE_NAME_AR = 'إعدادات العملة';

PROMPT
PROMPT '[8] إحصائيات الشجرة المحدثة...'
PROMPT '=============================='

-- إحصائيات عامة
SELECT 
    '📊 إجمالي العقد' AS STATISTIC,
    COUNT(*) AS VALUE
FROM ERP_SYSTEM_TREE
UNION ALL
SELECT 
    '📱 النوافذ',
    COUNT(*)
FROM ERP_SYSTEM_TREE 
WHERE NODE_TYPE = 'WINDOW'
UNION ALL
SELECT 
    '📂 الفئات',
    COUNT(*)
FROM ERP_SYSTEM_TREE 
WHERE NODE_TYPE = 'CATEGORY'
UNION ALL
SELECT 
    '🚧 قيد التطوير',
    COUNT(*)
FROM ERP_SYSTEM_TREE 
WHERE ADDITIONAL_INFO LIKE '%UNDER_DEVELOPMENT%';

PROMPT
PROMPT '[9] حفظ التغييرات...'
PROMPT '==================='

-- حفظ التغييرات
COMMIT;

PROMPT '✅ تم حفظ التغييرات بنجاح'

PROMPT
PROMPT '[10] ملخص العملية...'
PROMPT '===================='

DECLARE
    v_currency_exists NUMBER;
    v_general_settings_id NUMBER;
    v_total_windows NUMBER;
BEGIN
    -- فحص وجود نافذة إعدادات العملة
    SELECT COUNT(*) INTO v_currency_exists
    FROM ERP_SYSTEM_TREE 
    WHERE NODE_NAME_AR = 'إعدادات العملة';
    
    -- الحصول على معرف فئة الإعدادات العامة
    SELECT TREE_ID INTO v_general_settings_id
    FROM ERP_SYSTEM_TREE 
    WHERE NODE_NAME_AR = 'الإعدادات العامة';
    
    -- عدد النوافذ في الإعدادات العامة
    SELECT COUNT(*) INTO v_total_windows
    FROM ERP_SYSTEM_TREE 
    WHERE PARENT_ID = v_general_settings_id
    AND NODE_TYPE = 'WINDOW';
    
    DBMS_OUTPUT.PUT_LINE('📋 ملخص العملية:');
    DBMS_OUTPUT.PUT_LINE('================');
    
    IF v_currency_exists > 0 THEN
        DBMS_OUTPUT.PUT_LINE('✅ تم إضافة نافذة إعدادات العملة بنجاح');
        DBMS_OUTPUT.PUT_LINE('📍 الموقع: تحت فئة الإعدادات العامة (ID: ' || v_general_settings_id || ')');
        DBMS_OUTPUT.PUT_LINE('📊 إجمالي النوافذ في الفئة: ' || v_total_windows);
        DBMS_OUTPUT.PUT_LINE('🏷️ الحالة: قيد التطوير');
        DBMS_OUTPUT.PUT_LINE('💻 كلاس النافذة: CurrencySettingsWindow');
        DBMS_OUTPUT.PUT_LINE('🔐 الصلاحيات: ADMIN, SETTINGS_MANAGER');
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ فشل في إضافة النافذة');
    END IF;
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('💡 للوصول للنافذة:');
    DBMS_OUTPUT.PUT_LINE('• افتح شجرة الأنظمة');
    DBMS_OUTPUT.PUT_LINE('• انتقل إلى الإعدادات العامة');
    DBMS_OUTPUT.PUT_LINE('• ابحث عن إعدادات العملة');
    DBMS_OUTPUT.PUT_LINE('• النافذة تظهر بحالة "قيد التطوير"');
END;
/

PROMPT
PROMPT '🎉 انتهت عملية إضافة نافذة إعدادات العملة'
PROMPT '========================================'

-- إعادة تعيين الإعدادات
SET PAGESIZE 14
SET LINESIZE 80
SET SERVEROUTPUT OFF
