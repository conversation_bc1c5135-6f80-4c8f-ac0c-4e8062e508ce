@echo off
echo ========================================
echo   DATABASE BACKUP SYSTEM
echo   نظام النسخ الاحتياطي لقاعدة البيانات
echo ========================================

cd /d "d:\java\java"

REM إنشاء مجلد النسخ الاحتياطي
if not exist "backups" mkdir backups
if not exist "backups\daily" mkdir backups\daily
if not exist "backups\weekly" mkdir backups\weekly
if not exist "backups\monthly" mkdir backups\monthly

REM الحصول على التاريخ والوقت
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%%MM%%DD%" & set "timestamp=%HH%%Min%%Sec%"
set "fullstamp=%datestamp%_%timestamp%"

echo.
echo [INFO] Starting database backup...
echo [INFO] بدء النسخ الاحتياطي لقاعدة البيانات...
echo [INFO] Timestamp: %fullstamp%
echo.

echo ========================================
echo    Method 1: Oracle Data Pump Export
echo    الطريقة الأولى: Oracle Data Pump Export
echo ========================================

echo.
echo [1] Creating Data Pump export for ias20251...
echo [1] إنشاء Data Pump export لـ ias20251...

REM تصدير مخطط ias20251
expdp ias20251/ys123@localhost:1521/ORCL ^
    schemas=ias20251 ^
    directory=DATA_PUMP_DIR ^
    dumpfile=ias20251_backup_%fullstamp%.dmp ^
    logfile=ias20251_backup_%fullstamp%.log ^
    compression=all

if %errorlevel% equ 0 (
    echo ✅ Data Pump export completed successfully
    echo ✅ تم تصدير Data Pump بنجاح
) else (
    echo ❌ Data Pump export failed, trying alternative method
    echo ❌ فشل تصدير Data Pump، جاري المحاولة بطريقة بديلة
    goto SQL_EXPORT
)

goto COPY_FILES

:SQL_EXPORT
echo.
echo ========================================
echo    Method 2: SQL Export
echo    الطريقة الثانية: تصدير SQL
echo ========================================

echo.
echo [2] Creating SQL export for ias20251...
echo [2] إنشاء تصدير SQL لـ ias20251...

REM إنشاء سكريبت التصدير
echo SPOOL backups\daily\ias20251_data_%fullstamp%.sql > temp_export.sql
echo SET PAGESIZE 0 >> temp_export.sql
echo SET FEEDBACK OFF >> temp_export.sql
echo SET HEADING OFF >> temp_export.sql
echo. >> temp_export.sql
echo -- Database Export for ias20251 >> temp_export.sql
echo -- Generated on %date% %time% >> temp_export.sql
echo. >> temp_export.sql
echo -- Table: IAS_ITM_MST >> temp_export.sql
echo SELECT 'INSERT INTO IAS_ITM_MST VALUES (''' ^|^| ITM_CD ^|^| ''',''' ^|^| ITM_NM ^|^| ''',''' ^|^| ITM_NM_AR ^|^| ''',''' ^|^| ITM_DESC ^|^| ''',''' ^|^| ITM_GRP_CD ^|^| ''',''' ^|^| ITM_SUB_GRP_CD ^|^| ''',''' ^|^| ITM_UNIT ^|^| ''',' ^|^| ITM_PRICE ^|^| ',' ^|^| ITM_COST ^|^| ',''' ^|^| ITM_STATUS ^|^| ''',''' ^|^| CREATED_BY ^|^| ''',''' ^|^| TO_CHAR(CREATED_DATE,'YYYY-MM-DD HH24:MI:SS') ^|^| ''');' FROM IAS_ITM_MST; >> temp_export.sql
echo. >> temp_export.sql
echo -- Table: IAS_ITM_DTL >> temp_export.sql
echo SELECT 'INSERT INTO IAS_ITM_DTL VALUES (''' ^|^| ITM_CD ^|^| ''',''' ^|^| ITM_DTL_CD ^|^| ''',''' ^|^| ITM_DTL_NM ^|^| ''',''' ^|^| ITM_DTL_NM_AR ^|^| ''',''' ^|^| ITM_DTL_DESC ^|^| ''',' ^|^| ITM_DTL_QTY ^|^| ',''' ^|^| ITM_DTL_UNIT ^|^| ''',' ^|^| ITM_DTL_PRICE ^|^| ',''' ^|^| ITM_DTL_STATUS ^|^| ''',''' ^|^| CREATED_BY ^|^| ''',''' ^|^| TO_CHAR(CREATED_DATE,'YYYY-MM-DD HH24:MI:SS') ^|^| ''');' FROM IAS_ITM_DTL; >> temp_export.sql
echo. >> temp_export.sql
echo SPOOL OFF >> temp_export.sql
echo EXIT; >> temp_export.sql

REM تشغيل التصدير
sqlplus ias20251/ys123@localhost:1521/ORCL @temp_export.sql

if %errorlevel% equ 0 (
    echo ✅ SQL export completed successfully
    echo ✅ تم التصدير SQL بنجاح
    del temp_export.sql
) else (
    echo ❌ SQL export failed, creating manual backup
    echo ❌ فشل التصدير SQL، إنشاء نسخة احتياطية يدوية
    goto MANUAL_BACKUP
)

goto COPY_FILES

:MANUAL_BACKUP
echo.
echo ========================================
echo    Method 3: Manual File Backup
echo    الطريقة الثالثة: النسخ الاحتياطي اليدوي
echo ========================================

echo.
echo [3] Creating manual backup...
echo [3] إنشاء نسخة احتياطية يدوية...

REM نسخ ملفات التكوين
copy "ship_erp_settings.properties" "backups\daily\ship_erp_settings_%fullstamp%.properties" >nul 2>&1
copy "src\main\resources\application.properties" "backups\daily\application_%fullstamp%.properties" >nul 2>&1
copy "network\admin\tnsnames.ora" "backups\daily\tnsnames_%fullstamp%.ora" >nul 2>&1

REM نسخ سكريبتات قاعدة البيانات
copy "scripts\*.sql" "backups\daily\" >nul 2>&1

echo ✅ Manual backup completed
echo ✅ تم النسخ الاحتياطي اليدوي

:COPY_FILES
echo.
echo ========================================
echo    File Organization
echo    تنظيم الملفات
echo ========================================

echo.
echo [INFO] Organizing backup files...
echo [INFO] تنظيم ملفات النسخ الاحتياطي...

REM نسخ إلى المجلدات المناسبة حسب التاريخ
set /a day_of_week=%date:~0,3%
set /a day_of_month=%DD%

REM نسخة أسبوعية (كل يوم أحد)
if "%date:~0,3%"=="Sun" (
    echo Creating weekly backup...
    echo إنشاء نسخة احتياطية أسبوعية...
    if exist "backups\daily\*_%fullstamp%.*" (
        copy "backups\daily\*_%fullstamp%.*" "backups\weekly\" >nul 2>&1
    )
)

REM نسخة شهرية (اليوم الأول من الشهر)
if "%DD%"=="01" (
    echo Creating monthly backup...
    echo إنشاء نسخة احتياطية شهرية...
    if exist "backups\daily\*_%fullstamp%.*" (
        copy "backups\daily\*_%fullstamp%.*" "backups\monthly\" >nul 2>&1
    )
)

echo.
echo ========================================
echo    Backup Verification
echo    التحقق من النسخ الاحتياطي
echo ========================================

echo.
echo [INFO] Verifying backup files...
echo [INFO] التحقق من ملفات النسخ الاحتياطي...

REM عرض ملفات النسخ الاحتياطي المنشأة
echo.
echo Daily backups created today:
echo النسخ الاحتياطية اليومية المنشأة اليوم:
dir "backups\daily\*%datestamp%*" /b 2>nul

echo.
echo Weekly backups:
echo النسخ الاحتياطية الأسبوعية:
dir "backups\weekly\*" /b 2>nul | findstr /i "%YYYY%%MM%"

echo.
echo Monthly backups:
echo النسخ الاحتياطية الشهرية:
dir "backups\monthly\*" /b 2>nul | findstr /i "%YYYY%"

echo.
echo ========================================
echo    Cleanup Old Backups
echo    تنظيف النسخ الاحتياطية القديمة
echo ========================================

echo.
echo [INFO] Cleaning up old backup files...
echo [INFO] تنظيف ملفات النسخ الاحتياطية القديمة...

REM حذف النسخ اليومية الأقدم من 30 يوم
forfiles /p "backups\daily" /s /m *.* /d -30 /c "cmd /c del @path" 2>nul

REM حذف النسخ الأسبوعية الأقدم من 12 أسبوع
forfiles /p "backups\weekly" /s /m *.* /d -84 /c "cmd /c del @path" 2>nul

REM حذف النسخ الشهرية الأقدم من 12 شهر
forfiles /p "backups\monthly" /s /m *.* /d -365 /c "cmd /c del @path" 2>nul

echo ✅ Cleanup completed
echo ✅ تم التنظيف

echo.
echo ========================================
echo    Backup Summary
echo    ملخص النسخ الاحتياطي
echo ========================================

echo.
echo Backup completed on: %date% %time%
echo تم النسخ الاحتياطي في: %date% %time%
echo.
echo Backup location: d:\java\java\backups\
echo موقع النسخ الاحتياطي: d:\java\java\backups\
echo.

REM حساب حجم النسخ الاحتياطية
for /f "tokens=3" %%a in ('dir "backups" /s /-c ^| findstr /i "bytes"') do set backup_size=%%a
echo Total backup size: %backup_size% bytes
echo الحجم الإجمالي للنسخ الاحتياطية: %backup_size% بايت
echo.

echo ========================================
echo    Automated Backup Setup
echo    إعداد النسخ الاحتياطي التلقائي
echo ========================================

echo.
echo To setup automated daily backups:
echo لإعداد النسخ الاحتياطية اليومية التلقائية:
echo.
echo 1. Open Task Scheduler (taskschd.msc)
echo    افتح مجدول المهام (taskschd.msc)
echo.
echo 2. Create Basic Task with these settings:
echo    أنشئ مهمة أساسية بهذه الإعدادات:
echo    - Name: Ship ERP Database Backup
echo    - Trigger: Daily at 2:00 AM
echo    - Action: Start Program
echo    - Program: %~dp0DatabaseBackupSystem.bat
echo.
echo 3. Test the scheduled task
echo    اختبر المهمة المجدولة
echo.

echo ========================================
echo    BACKUP SYSTEM COMPLETED
echo    تم الانتهاء من نظام النسخ الاحتياطي
echo ========================================

echo.
echo ✅ Database backup system setup completed!
echo ✅ تم الانتهاء من إعداد نظام النسخ الاحتياطي!
echo.

pause
