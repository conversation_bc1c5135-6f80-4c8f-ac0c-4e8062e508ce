@echo off
echo ========================================
echo 🏢 تشغيل نافذة إدارة الموردين الشاملة
echo Running Comprehensive Suppliers Management
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/4] إنشاء جداول الموردين...
echo Creating Suppliers Tables...
echo ========================================

echo تنفيذ سكريبت إنشاء جداول الموردين...
sqlplus ship_erp/ship_erp_password@localhost:1521/ORCL @create-suppliers-table.sql
if %errorlevel% equ 0 (
    echo ✅ تم إنشاء جداول الموردين بنجاح
) else (
    echo ⚠️ تحذير: قد تكون الجداول موجودة مسبقاً
)

echo.
echo [2/4] تجميع نافذة إدارة الموردين...
echo Compiling Suppliers Management Window...
echo ========================================

echo تجميع SuppliersManagementWindow...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\SuppliersManagementWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع نافذة إدارة الموردين بنجاح
) else (
    echo ❌ فشل في تجميع نافذة إدارة الموردين
    pause
    exit /b 1
)

echo.
echo [3/4] تشغيل نافذة إدارة الموردين...
echo Running Suppliers Management Window...
echo ========================================

echo تشغيل النافذة...
start "Suppliers Management Window" java -cp "lib\*;." SuppliersManagementWindow

echo.
echo [4/4] معلومات النافذة...
echo Window Information...
echo ========================================

echo.
echo 🏢 نافذة إدارة الموردين الشاملة
echo ================================

echo.
echo 📋 الميزات المتاحة:
echo ==================
echo ✅ إدارة بيانات الموردين الأساسية
echo   • إضافة موردين جدد
echo   • تعديل بيانات الموردين
echo   • حذف الموردين
echo   • البحث والتصفية

echo.
echo ✅ إدارة جهات الاتصال (قيد التطوير)
echo   • إضافة جهات اتصال متعددة لكل مورد
echo   • تحديد جهة الاتصال الرئيسية
echo   • معلومات الاتصال التفصيلية

echo.
echo ✅ إدارة العناوين (قيد التطوير)
echo   • عناوين متعددة لكل مورد
echo   • أنواع عناوين مختلفة
echo   • تحديد العنوان الرئيسي

echo.
echo ✅ إدارة الفئات (قيد التطوير)
echo   • تصنيف الموردين حسب الفئات
echo   • فئات هرمية
echo   • ربط متعدد بالفئات

echo.
echo ✅ تقييم الموردين (قيد التطوير)
echo   • تقييم الجودة والخدمة
echo   • تقييم الأسعار والتسليم
echo   • تاريخ التقييمات

echo.
echo ✅ التقارير والإحصائيات (قيد التطوير)
echo   • تقارير شاملة عن الموردين
echo   • إحصائيات الأداء
echo   • تحليل البيانات

echo.
echo 🗂️ بنية قاعدة البيانات:
echo =========================
echo • ERP_SUPPLIERS - الجدول الرئيسي للموردين
echo • ERP_SUPPLIER_CATEGORIES - فئات الموردين
echo • ERP_SUPPLIER_CATEGORY_MAPPING - ربط الموردين بالفئات
echo • ERP_SUPPLIER_CONTACTS - جهات اتصال الموردين
echo • ERP_SUPPLIER_ADDRESSES - عناوين الموردين
echo • ERP_SUPPLIER_ATTACHMENTS - مرفقات الموردين
echo • ERP_SUPPLIER_EVALUATIONS - تقييمات الموردين

echo.
echo 📊 الحقول الرئيسية:
echo ===================
echo • كود المورد (فريد)
echo • اسم المورد (عربي وإنجليزي)
echo • نوع المورد (مورد، مقاول، مقدم خدمة، مصنع)
echo • معلومات الاتصال (هاتف، جوال، بريد إلكتروني)
echo • العنوان (مدينة، دولة)
echo • حالة النشاط والاعتماد
echo • الملاحظات

echo.
echo 🎯 كيفية الاستخدام:
echo ==================

echo.
echo 📝 إضافة مورد جديد:
echo -------------------
echo 1. املأ كود المورد (مطلوب)
echo 2. املأ اسم المورد (مطلوب)
echo 3. املأ باقي البيانات (اختيارية)
echo 4. انقر "إضافة مورد"

echo.
echo ✏️ تعديل مورد موجود:
echo --------------------
echo 1. انقر على المورد في الجدول
echo 2. ستظهر بياناته في النموذج
echo 3. عدل البيانات المطلوبة
echo 4. انقر "تعديل مورد"

echo.
echo 🗑️ حذف مورد:
echo -------------
echo 1. انقر على المورد في الجدول
echo 2. انقر "حذف مورد"
echo 3. أكد عملية الحذف

echo.
echo 🔄 تحديث البيانات:
echo ------------------
echo • انقر "تحديث البيانات" لإعادة تحميل الجدول
echo • انقر "مسح الحقول" لمسح النموذج

echo.
echo 🎨 المظهر والتصميم:
echo ==================
echo ✅ واجهة عربية كاملة
echo ✅ دعم النصوص من اليمين لليسار
echo ✅ خطوط عربية واضحة
echo ✅ تصميم متجاوب ومتوافق
echo ✅ ألوان متناسقة مع النظام

echo.
echo 🔧 المتطلبات التقنية:
echo =====================
echo • Java 8 أو أحدث
echo • Oracle Database 11g أو أحدث
echo • JDBC Driver for Oracle
echo • قاعدة بيانات SHIP_ERP

echo.
echo 📞 الدعم الفني:
echo ===============
echo • تحقق من اتصال قاعدة البيانات
echo • تأكد من وجود المستخدم ship_erp
echo • راجع رسائل وحدة التحكم للأخطاء
echo • تحقق من صحة بيانات الاتصال

echo.
echo 🚀 التطوير المستقبلي:
echo =====================
echo • إكمال تبويبات جهات الاتصال والعناوين
echo • تطوير نظام الفئات والتصنيف
echo • إضافة نظام التقييم والمراجعة
echo • تطوير التقارير والإحصائيات المتقدمة
echo • إضافة نظام المرفقات والوثائق
echo • تطوير واجهة البحث المتقدم

echo.
echo 💡 نصائح للاستخدام:
echo ===================
echo • استخدم أكواد موردين واضحة ومنطقية
echo • املأ جميع البيانات المتاحة للاستفادة القصوى
echo • راجع البيانات قبل الحفظ
echo • استخدم الملاحظات لمعلومات إضافية
echo • حدث البيانات بانتظام

echo.
echo 🎉 مميزات خاصة:
echo ================
echo ✅ تطبيق تلقائي لمظهر النظام
echo ✅ إنشاء تلقائي للجداول المطلوبة
echo ✅ معالجة شاملة للأخطاء
echo ✅ رسائل تأكيد واضحة
echo ✅ تحديث فوري للبيانات
echo ✅ واجهة سهلة الاستخدام

echo.
echo ========================================
echo ✅ تم تشغيل نافذة إدارة الموردين!
echo Suppliers Management Window Launched!
echo ========================================

echo.
echo 📋 للوصول من التطبيق الرئيسي:
echo ==============================
echo 1. افتح SimpleShipERP
echo 2. انتقل إلى "إدارة الموردين"
echo 3. انقر على "بيانات الموردين"

echo.
pause
