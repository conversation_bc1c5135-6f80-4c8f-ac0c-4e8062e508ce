import java.io.*;
import java.nio.file.*;
import java.sql.*;
import java.util.*;
import java.util.regex.Pattern;

/**
 * فحص شامل ومفصل للنظام بالكامل
 * Comprehensive System Audit Tool
 */
public class ComprehensiveSystemAudit {
    
    private static final String REPORT_FILE = "COMPREHENSIVE_SYSTEM_AUDIT_REPORT.md";
    private static PrintWriter report;
    private static int totalIssues = 0;
    private static int criticalIssues = 0;
    private static int warningIssues = 0;
    private static int infoIssues = 0;
    
    public static void main(String[] args) {
        try {
            initializeReport();
            
            System.out.println("🔍 بدء الفحص الشامل للنظام...");
            report.println("# تقرير الفحص الشامل للنظام");
            report.println("## Comprehensive System Audit Report");
            report.println("**التاريخ:** " + new Date());
            report.println("**الهدف:** فحص شامل لجاهزية النظام للعمل والتطوير");
            report.println("\n---\n");
            
            // 1. فحص بنية المشروع
            auditProjectStructure();
            
            // 2. فحص المكتبات والتبعيات
            auditLibrariesAndDependencies();
            
            // 3. فحص ملفات المصدر الأساسية
            auditCoreSourceFiles();
            
            // 4. فحص قواعد البيانات والاتصال
            auditDatabaseConnectivity();
            
            // 5. فحص النوافذ والواجهات
            auditUserInterfaces();
            
            // 6. فحص التكامل والاستيراد
            auditSystemIntegration();
            
            // 7. فحص الإعدادات والتكوين
            auditConfigurationFiles();
            
            // 8. فحص سكريپتات التشغيل
            auditExecutionScripts();
            
            // 9. تقييم الجودة والأمان
            auditCodeQualityAndSecurity();
            
            // 10. توصيات التطوير
            generateDevelopmentRecommendations();
            
            // الخلاصة النهائية
            generateFinalSummary();
            
            System.out.println("✅ تم إنجاز الفحص الشامل بنجاح!");
            System.out.println("📋 تقرير مفصل متاح في: " + REPORT_FILE);
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في الفحص الشامل: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (report != null) {
                report.close();
            }
        }
    }
    
    private static void initializeReport() throws IOException {
        report = new PrintWriter(new FileWriter(REPORT_FILE, false), true);
    }
    
    private static void auditProjectStructure() {
        report.println("## 1. فحص بنية المشروع");
        report.println("### Project Structure Audit\n");
        
        try {
            // فحص المجلدات الأساسية
            String[] requiredDirs = {"src", "src/main", "src/main/java", "lib", "scripts"};
            for (String dir : requiredDirs) {
                Path path = Paths.get(dir);
                if (Files.exists(path)) {
                    report.println("✅ **" + dir + "** - موجود");
                } else {
                    report.println("❌ **" + dir + "** - مفقود");
                    logIssue("CRITICAL", "مجلد أساسي مفقود: " + dir);
                }
            }
            
            // فحص ملفات التكوين
            String[] configFiles = {"pom.xml", "settings.xml", "ship_erp_settings.properties"};
            report.println("\n### ملفات التكوين:");
            for (String file : configFiles) {
                Path path = Paths.get(file);
                if (Files.exists(path)) {
                    report.println("✅ **" + file + "** - موجود");
                } else {
                    report.println("⚠️ **" + file + "** - مفقود");
                    logIssue("WARNING", "ملف تكوين مفقود: " + file);
                }
            }
            
            // إحصائيات الملفات
            long javaFiles = countFiles("src/main/java", ".java");
            long classFiles = countFiles(".", ".class");
            long batFiles = countFiles(".", ".bat");
            
            report.println("\n### إحصائيات الملفات:");
            report.println("- **ملفات Java:** " + javaFiles);
            report.println("- **ملفات Class:** " + classFiles);
            report.println("- **ملفات Batch:** " + batFiles);
            
            if (javaFiles == 0) {
                logIssue("CRITICAL", "لا توجد ملفات Java في المشروع");
            }
            
        } catch (Exception e) {
            logIssue("CRITICAL", "خطأ في فحص بنية المشروع: " + e.getMessage());
        }
        
        report.println("\n---\n");
    }
    
    private static void auditLibrariesAndDependencies() {
        report.println("## 2. فحص المكتبات والتبعيات");
        report.println("### Libraries and Dependencies Audit\n");
        
        try {
            // فحص مجلد lib
            Path libDir = Paths.get("lib");
            if (Files.exists(libDir)) {
                report.println("✅ **مجلد lib** موجود\n");
                
                // المكتبات الأساسية المطلوبة
                String[] requiredLibs = {
                    "ojdbc11.jar", "orai18n.jar", "commons-logging-1.2.jar"
                };
                
                report.println("### المكتبات الأساسية:");
                for (String lib : requiredLibs) {
                    Path libPath = libDir.resolve(lib);
                    if (Files.exists(libPath)) {
                        long size = Files.size(libPath);
                        report.println("✅ **" + lib + "** - موجود (" + formatFileSize(size) + ")");
                    } else {
                        report.println("❌ **" + lib + "** - مفقود");
                        logIssue("CRITICAL", "مكتبة أساسية مفقودة: " + lib);
                    }
                }
                
                // عد جميع المكتبات
                long totalLibs = countFiles("lib", ".jar");
                report.println("\n**إجمالي المكتبات:** " + totalLibs);
                
            } else {
                report.println("❌ **مجلد lib** غير موجود");
                logIssue("CRITICAL", "مجلد المكتبات غير موجود");
            }
            
        } catch (Exception e) {
            logIssue("CRITICAL", "خطأ في فحص المكتبات: " + e.getMessage());
        }
        
        report.println("\n---\n");
    }
    
    private static void auditCoreSourceFiles() {
        report.println("## 3. فحص ملفات المصدر الأساسية");
        report.println("### Core Source Files Audit\n");
        
        try {
            // الملفات الأساسية المطلوبة
            String[] coreFiles = {
                "DatabaseConfig.java",
                "TreeMenuPanel.java", 
                "EnhancedMainWindow.java",
                "CompleteOracleSystemTest.java",
                "SettingsManager.java",
                "UIUtils.java"
            };
            
            report.println("### الملفات الأساسية:");
            for (String file : coreFiles) {
                Path filePath = Paths.get("src/main/java/" + file);
                if (Files.exists(filePath)) {
                    long size = Files.size(filePath);
                    report.println("✅ **" + file + "** - موجود (" + formatFileSize(size) + ")");
                    
                    // فحص محتوى الملف
                    auditJavaFile(filePath);
                    
                } else {
                    report.println("❌ **" + file + "** - مفقود");
                    logIssue("CRITICAL", "ملف أساسي مفقود: " + file);
                }
            }
            
            // فحص النوافذ الرئيسية
            String[] windowFiles = {
                "RealItemDataWindow.java",
                "ComprehensiveItemDataWindow.java", 
                "ItemGroupsManagementWindow.java",
                "MeasurementUnitsWindow.java",
                "UserManagementWindow.java",
                "GeneralSettingsWindow.java"
            };
            
            report.println("\n### نوافذ النظام:");
            for (String file : windowFiles) {
                Path filePath = Paths.get("src/main/java/" + file);
                if (Files.exists(filePath)) {
                    report.println("✅ **" + file + "** - موجود");
                } else {
                    report.println("⚠️ **" + file + "** - مفقود");
                    logIssue("WARNING", "نافذة مفقودة: " + file);
                }
            }
            
        } catch (Exception e) {
            logIssue("CRITICAL", "خطأ في فحص ملفات المصدر: " + e.getMessage());
        }
        
        report.println("\n---\n");
    }
    
    private static void auditJavaFile(Path filePath) {
        try {
            List<String> lines = Files.readAllLines(filePath);
            int lineCount = lines.size();
            
            // فحص أساسي للملف
            boolean hasPackage = lines.stream().anyMatch(line -> line.trim().startsWith("package"));
            boolean hasImports = lines.stream().anyMatch(line -> line.trim().startsWith("import"));
            boolean hasClass = lines.stream().anyMatch(line -> line.contains("class "));
            
            if (lineCount < 10) {
                logIssue("WARNING", filePath.getFileName() + " - ملف صغير جداً (" + lineCount + " سطر)");
            }
            
            if (!hasClass) {
                logIssue("WARNING", filePath.getFileName() + " - لا يحتوي على تعريف class");
            }
            
        } catch (Exception e) {
            logIssue("WARNING", "خطأ في فحص ملف: " + filePath.getFileName());
        }
    }
    
    private static void auditDatabaseConnectivity() {
        report.println("## 4. فحص قواعد البيانات والاتصال");
        report.println("### Database Connectivity Audit\n");
        
        try {
            // محاولة تحميل Oracle JDBC Driver
            try {
                Class.forName("oracle.jdbc.OracleDriver");
                report.println("✅ **Oracle JDBC Driver** - محمل بنجاح");
            } catch (ClassNotFoundException e) {
                report.println("❌ **Oracle JDBC Driver** - غير متاح");
                logIssue("CRITICAL", "Oracle JDBC Driver غير متاح");
            }
            
            // اختبار الاتصال بقواعد البيانات
            testDatabaseConnection("SHIP_ERP", "ship_erp_password");
            testDatabaseConnection("ias20251", "ys123");
            
        } catch (Exception e) {
            logIssue("CRITICAL", "خطأ في فحص قواعد البيانات: " + e.getMessage());
        }
        
        report.println("\n---\n");
    }
    
    private static void testDatabaseConnection(String user, String password) {
        try {
            Properties props = new Properties();
            props.setProperty("user", user);
            props.setProperty("password", password);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            String url = "*************************************";
            
            try (Connection conn = DriverManager.getConnection(url, props)) {
                report.println("✅ **اتصال " + user + "** - ناجح");
                
                // فحص الجداول المهمة
                checkImportantTables(conn, user);
                
            }
        } catch (SQLException e) {
            report.println("❌ **اتصال " + user + "** - فاشل: " + e.getMessage());
            logIssue("CRITICAL", "فشل الاتصال بقاعدة البيانات: " + user);
        }
    }
    
    private static void checkImportantTables(Connection conn, String schema) {
        String[] tables;
        if ("SHIP_ERP".equalsIgnoreCase(schema)) {
            tables = new String[]{"IAS_ITM_MST", "IAS_ITM_DTL", "ERP_MEASUREMENT", 
                                "ERP_SUB_GRP_DTL", "ERP_ASSISTANT_GROUP", "ERP_DETAIL_GROUP"};
        } else {
            tables = new String[]{"IAS_ITM_MST", "IAS_ITM_DTL", "MEASUREMENT", 
                                "IAS_SUB_GRP_DTL", "IAS_ASSISTANT_GROUP", "IAS_DETAIL_GROUP"};
        }
        
        for (String table : tables) {
            try (PreparedStatement stmt = conn.prepareStatement(
                    "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = ?")) {
                stmt.setString(1, table);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        report.println("  ✅ جدول " + table + " موجود");
                    } else {
                        report.println("  ❌ جدول " + table + " مفقود");
                        logIssue("WARNING", "جدول مفقود في " + schema + ": " + table);
                    }
                }
            } catch (SQLException e) {
                logIssue("WARNING", "خطأ في فحص جدول " + table + " في " + schema);
            }
        }
    }
    
    private static void auditUserInterfaces() {
        report.println("## 5. فحص النوافذ والواجهات");
        report.println("### User Interfaces Audit\n");
        
        // سيتم إضافة المزيد من التفاصيل
        report.println("🔄 فحص النوافذ والواجهات قيد التطوير...\n");
        report.println("---\n");
    }
    
    private static void auditSystemIntegration() {
        report.println("## 6. فحص التكامل والاستيراد");
        report.println("### System Integration Audit\n");
        
        // سيتم إضافة المزيد من التفاصيل
        report.println("🔄 فحص التكامل والاستيراد قيد التطوير...\n");
        report.println("---\n");
    }
    
    private static void auditConfigurationFiles() {
        report.println("## 7. فحص الإعدادات والتكوين");
        report.println("### Configuration Files Audit\n");
        
        // سيتم إضافة المزيد من التفاصيل
        report.println("🔄 فحص الإعدادات والتكوين قيد التطوير...\n");
        report.println("---\n");
    }
    
    private static void auditExecutionScripts() {
        report.println("## 8. فحص سكريپتات التشغيل");
        report.println("### Execution Scripts Audit\n");
        
        // سيتم إضافة المزيد من التفاصيل
        report.println("🔄 فحص سكريپتات التشغيل قيد التطوير...\n");
        report.println("---\n");
    }
    
    private static void auditCodeQualityAndSecurity() {
        report.println("## 9. تقييم الجودة والأمان");
        report.println("### Code Quality and Security Assessment\n");
        
        // سيتم إضافة المزيد من التفاصيل
        report.println("🔄 تقييم الجودة والأمان قيد التطوير...\n");
        report.println("---\n");
    }
    
    private static void generateDevelopmentRecommendations() {
        report.println("## 10. توصيات التطوير");
        report.println("### Development Recommendations\n");
        
        // سيتم إضافة المزيد من التفاصيل
        report.println("🔄 توصيات التطوير قيد التطوير...\n");
        report.println("---\n");
    }
    
    private static void generateFinalSummary() {
        report.println("## الخلاصة النهائية");
        report.println("### Final Summary\n");
        
        report.println("### إحصائيات الفحص:");
        report.println("- **إجمالي المشاكل:** " + totalIssues);
        report.println("- **مشاكل حرجة:** " + criticalIssues);
        report.println("- **تحذيرات:** " + warningIssues);
        report.println("- **معلومات:** " + infoIssues);
        
        String status;
        if (criticalIssues == 0 && warningIssues <= 3) {
            status = "✅ **النظام جاهز للعمل والتطوير**";
        } else if (criticalIssues <= 2) {
            status = "⚠️ **النظام يحتاج إصلاحات بسيطة**";
        } else {
            status = "❌ **النظام يحتاج إصلاحات جوهرية**";
        }
        
        report.println("\n### التقييم العام:");
        report.println(status);
        
        report.println("\n**تاريخ الفحص:** " + new Date());
        report.println("**أداة الفحص:** ComprehensiveSystemAudit v1.0");
    }
    
    private static void logIssue(String level, String message) {
        totalIssues++;
        switch (level) {
            case "CRITICAL": criticalIssues++; break;
            case "WARNING": warningIssues++; break;
            case "INFO": infoIssues++; break;
        }
        System.out.println("[" + level + "] " + message);
    }
    
    private static long countFiles(String directory, String extension) {
        try {
            return Files.walk(Paths.get(directory))
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(extension))
                    .count();
        } catch (Exception e) {
            return 0;
        }
    }
    
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return (bytes / 1024) + " KB";
        return (bytes / (1024 * 1024)) + " MB";
    }
}
