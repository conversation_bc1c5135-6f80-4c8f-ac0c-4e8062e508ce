@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 🎨 إعداد الأيقونات الشامل لشجرة الأنظمة
echo    Complete Icons Setup for System Tree
echo ================================================
echo.

cd /d "d:\java\java"

echo 🔍 فحص المتطلبات...
echo.

echo [1] فحص المكتبات المطلوبة...
if not exist "lib\ojdbc11.jar" (
    echo ❌ ojdbc11.jar مفقود!
    pause
    exit /b 1
)

echo ✅ جميع المكتبات متوفرة
echo.

echo [2] فحص الملفات المطلوبة...
if not exist "src\main\java\SystemTreeIconGenerator.java" (
    echo ❌ SystemTreeIconGenerator.java مفقود!
    pause
    exit /b 1
)

if not exist "src\main\java\IconPathUpdater.java" (
    echo ❌ IconPathUpdater.java مفقود!
    pause
    exit /b 1
)

echo ✅ جميع الملفات متوفرة
echo.

echo [3] تجميع أدوات الأيقونات...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\SystemTreeIconGenerator.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع SystemTreeIconGenerator
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\IconPathUpdater.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع IconPathUpdater
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح
echo.

echo ================================================
echo 🎨 الخطوة 1: إنشاء ملفات الأيقونات
echo ================================================
echo.

java -cp "lib\*;." SystemTreeIconGenerator

echo.
echo ================================================
echo 🔄 الخطوة 2: تحديث مسارات الأيقونات في قاعدة البيانات
echo ================================================
echo.

java -cp "lib\*;." IconPathUpdater

echo.
echo ================================================
echo ✅ تم إكمال إعداد الأيقونات بنجاح!
echo ================================================
echo.

echo 📋 ملخص ما تم إنجازه:
echo • تم إنشاء 9 ملفات أيقونات في resources/icons/
echo • تم تحديث 91 مسار أيقونة في قاعدة البيانات
echo • تم ربط كل نوع عقدة بالأيقونة المناسبة
echo.

echo 🎨 الأيقونات المنشأة:
echo • folder.png - للفئات والمجلدات
echo • window.png - للنوافذ التطبيقية
echo • tool.png - للأدوات المساعدة
echo • report.png - للتقارير
echo • user.png - لإدارة المستخدمين
echo • settings.png - للإعدادات
echo • email.png - للبريد الإلكتروني
echo • database.png - لقاعدة البيانات
echo • default.png - الأيقونة الافتراضية
echo.

echo 📁 موقع الأيقونات: resources/icons/
echo 🗄️ قاعدة البيانات: ERP_SYSTEM_TREE.ICON_PATH
echo.

echo 💡 لرؤية الأيقونات في التطبيق:
echo • أعد تشغيل نظام إدارة الشحنات
echo • افتح أي نافذة تحتوي على شجرة الأنظمة
echo • ستظهر الأيقونات بجانب أسماء العقد
echo.

echo 🔧 للتخصيص الإضافي:
echo • يمكن تعديل الأيقونات في resources/icons/
echo • يمكن إضافة أيقونات جديدة وتحديث قاعدة البيانات
echo • استخدم IconPathUpdater لتحديث المسارات
echo.

pause
