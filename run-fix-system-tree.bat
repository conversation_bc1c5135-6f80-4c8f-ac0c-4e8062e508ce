@echo off
echo ========================================
echo    FIX SYSTEM TREE STRUCTURE
echo    اصلاح هيكل شجرة النظام
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Running SQL script to fix system tree structure...
echo [معلومات] تشغيل سكريپت SQL لإصلاح هيكل شجرة النظام...
echo.

REM اعداد البيئة
call set-tns-env.bat

echo [1] Connecting to database and running fix script...

REM تشغيل سكريپت SQL
sqlplus -S SHIP_ERP/ship_erp_password@SHIP_ERP @scripts\fix_system_tree_structure.sql

if errorlevel 1 (
    echo ERROR: SQL script execution failed
    pause
    exit /b 1
)

echo OK: SQL script executed successfully

echo.
echo [2] Testing the result with SystemTreeManager...

java %JAVA_TNS_OPTS% -cp "lib\ojdbc11.jar;lib\orai18n.jar;." SystemTreeManager

echo.
echo ========================================
echo    FIX COMPLETED
echo    اكتمل الاصلاح
echo ========================================

echo.
echo [SUCCESS] System tree structure has been fixed!
echo [نجح] تم اصلاح هيكل شجرة النظام!
echo.
echo Tree structure:
echo هيكل الشجرة:
echo - نظام إدارة الشحنات
echo   ├── إدارة الأصناف
echo   │   ├── بيانات الأصناف الحقيقية
echo   │   ├── بيانات الأصناف الشاملة
echo   │   ├── مجموعات الأصناف
echo   │   └── وحدات القياس
echo   ├── إدارة المستخدمين
echo   │   ├── إدارة المستخدمين
echo   │   ├── صلاحيات المستخدمين
echo   │   └── مجموعات المستخدمين
echo   ├── الإعدادات العامة
echo   │   ├── الإعدادات العامة
echo   │   ├── إعدادات قاعدة البيانات
echo   │   ├── إعدادات الأمان
echo   │   └── تكوين النظام
echo   ├── التقارير
echo   │   ├── تقرير الأصناف
echo   │   ├── تقرير المستخدمين
echo   │   ├── تقرير النظام
echo   │   └── التقارير المخصصة
echo   └── أدوات النظام
echo       ├── فحص النظام الشامل
echo       ├── مراقب الأداء
echo       ├── إدارة الاتصالات
echo       ├── مدير الأمان
echo       ├── مدير التكوين
echo       ├── البحث المتقدم
echo       ├── النسخ الاحتياطي والاستعادة
echo       ├── سجل النظام
echo       └── مركز الإشعارات
echo.

pause
