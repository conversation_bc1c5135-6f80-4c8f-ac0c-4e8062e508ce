import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.GridLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Vector;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.JToolBar;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة بيانات الأصناف الحقيقية مبنية كلياً على الجداول الحقيقية IAS_ITM_MST و IAS_ITM_DTL الأبعاد:
 * 1377×782 بكسل
 */
public class RealItemDataWindow extends JFrame {

    // الأبعاد المطلوبة بالضبط
    private static final int WINDOW_WIDTH = 1377;
    private static final int WINDOW_HEIGHT = 782;

    // اتصال قاعدة البيانات
    private Connection connection;

    // مكونات الواجهة الرئيسية
    private JTabbedPane mainTabbedPane;
    private JTable itemsTable;
    private DefaultTableModel itemsTableModel;
    private JTable detailsTable;
    private DefaultTableModel detailsTableModel;

    // حقول IAS_ITM_MST - مطابقة للجدول الحقيقي
    private JTextField iCodeField; // I_CODE
    private JTextField iNameField; // I_NAME
    private JTextField iENameField; // I_E_NAME
    private JTextArea iDescArea; // I_DESC
    private JTextField gCodeField; // G_CODE
    private JTextField mngCodeField; // MNG_CODE
    private JTextField subgCodeField; // SUBG_CODE
    private JTextField itemSizeField; // ITEM_SIZE
    private JComboBox<String> itemTypeCombo; // ITEM_TYPE
    private JTextField primaryCostField; // PRIMARY_COST
    private JTextField initPrimaryCostField; // INIT_PRIMARY_COST
    private JTextField alterCodeField; // ALTER_CODE
    private JTextField manfCodeField; // MANF_CODE
    private JTextField vCodeField; // V_CODE

    // خيارات IAS_ITM_MST
    private JCheckBox inactiveCheckBox; // INACTIVE
    private JCheckBox serviceItmCheckBox; // SERVICE_ITM
    private JCheckBox cashSaleCheckBox; // CASH_SALE
    private JCheckBox noReturnSaleCheckBox; // NO_RETURN_SALE
    private JCheckBox kitItmCheckBox; // KIT_ITM
    private JCheckBox useExpDateCheckBox; // USE_EXP_DATE
    private JCheckBox useBatchNoCheckBox; // USE_BATCH_NO
    private JCheckBox useSerialNoCheckBox; // USE_SERIALNO

    // حقول IAS_ITM_DTL - مطابقة للجدول الحقيقي
    private JTextField itmUntField; // ITM_UNT
    private JTextField pSizeField; // P_SIZE
    private JTextField itmUntLDscField; // ITM_UNT_L_DSC
    private JTextField itmUntFDscField; // ITM_UNT_F_DSC
    private JTextField barcodeField; // BARCODE
    private JCheckBox mainUnitCheckBox; // MAIN_UNIT
    private JCheckBox saleUnitCheckBox; // SALE_UNIT
    private JCheckBox purUnitCheckBox; // PUR_UNIT
    private JCheckBox stockUnitCheckBox; // STOCK_UNIT

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new RealItemDataWindow().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ في بدء النافذة: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    public RealItemDataWindow() throws Exception {
        initializeDatabase();
        initializeGUI();
        loadData();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    private void initializeDatabase() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");

        // إعداد خصائص Oracle لدعم الترميز العربي
        java.util.Properties props = new java.util.Properties();
        props.setProperty("user", "ship_erp");
        props.setProperty("password", "ship_erp_password");
        props.setProperty("oracle.jdbc.defaultNChar", "true");
        props.setProperty("oracle.jdbc.J2EE13Compliant", "true");

        connection = DriverManager.getConnection("*************************************", props);

        // التأكد من وجود الجداول الحقيقية
        ensureRealTablesExist();
    }

    /**
     * التأكد من وجود الجداول الحقيقية
     */
    private void ensureRealTablesExist() throws SQLException {
        Statement stmt = connection.createStatement();

        try {
            // فحص وجود الجداول الحقيقية
            ResultSet rs = stmt.executeQuery(
                    "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'IAS_ITM_MST'");
            rs.next();
            if (rs.getInt(1) == 0) {
                // إنشاء الجداول الحقيقية
                // CreateExactTables.main(new String[] {}); // TODO: Implement table creation
                System.out.println("✅ تم إنشاء الجداول الحقيقية");
            }
            rs.close();
        } catch (Exception e) {
            System.out.println("⚠️ تحذير في فحص الجداول: " + e.getMessage());
        }

        stmt.close();
    }

    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeGUI() {
        setTitle("نافذة بيانات الأصناف الحقيقية - IAS_ITM_MST & IAS_ITM_DTL");
        setSize(WINDOW_WIDTH, WINDOW_HEIGHT);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        // إعداد الخط العربي
        Font arabicFont = new Font("Arial", Font.PLAIN, 12);
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);

        // إنشاء التبويبات الرئيسية
        mainTabbedPane = new JTabbedPane();

        // تبويب قائمة الأصناف
        createItemsListTab();

        // تبويب بيانات IAS_ITM_MST
        createIasMstTab();

        // تبويب بيانات IAS_ITM_DTL
        createIasDtlTab();

        // تبويب الإحصائيات
        createStatisticsTab();

        add(mainTabbedPane, BorderLayout.CENTER);

        // شريط الأدوات
        createToolbar();

        // شريط الحالة
        createStatusBar();
    }

    /**
     * إنشاء تبويب قائمة الأصناف
     */
    private void createItemsListTab() {
        JPanel itemsPanel = new JPanel(new BorderLayout());

        // شريط البحث
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.setBorder(BorderFactory.createTitledBorder("البحث في الأصناف"));

        JTextField searchField = new JTextField(20);
        JButton searchButton = new JButton("بحث");
        JComboBox<String> filterCombo = new JComboBox<>(
                new String[] {"جميع الأصناف", "الأصناف النشطة", "الأصناف غير النشطة",
                        "أصناف الخدمة", "أصناف البيع النقدي", "أصناف المجموعات"});

        searchPanel.add(new JLabel("البحث:"));
        searchPanel.add(searchField);
        searchPanel.add(searchButton);
        searchPanel.add(new JLabel("الفلتر:"));
        searchPanel.add(filterCombo);

        itemsPanel.add(searchPanel, BorderLayout.NORTH);

        // جدول الأصناف - مطابق للحقول الحقيقية مع التسميات الصحيحة
        String[] columns = {"كود الصنف", "اسم الصنف", "الاسم الإنجليزي", "كود المجموعة",
                "تسعير الأصناف", "نوع الصنف", "الحالة"};

        itemsTableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        itemsTable = new JTable(itemsTableModel);
        itemsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        itemsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedItemData();
            }
        });

        JScrollPane itemsScrollPane = new JScrollPane(itemsTable);
        itemsScrollPane.setPreferredSize(new Dimension(WINDOW_WIDTH - 50, 400));

        itemsPanel.add(itemsScrollPane, BorderLayout.CENTER);

        // أزرار العمليات
        JPanel buttonsPanel = new JPanel(new FlowLayout());
        JButton addButton = new JButton("إضافة صنف جديد");
        JButton editButton = new JButton("تعديل");
        JButton deleteButton = new JButton("حذف");
        JButton refreshButton = new JButton("تحديث");
        JButton importButton = new JButton("استيراد من IAS20251");

        addButton.addActionListener(e -> addNewItem());
        editButton.addActionListener(e -> editSelectedItem());
        deleteButton.addActionListener(e -> deleteSelectedItem());
        refreshButton.addActionListener(e -> loadItemsData());
        importButton.addActionListener(e -> importFromIAS20251());

        buttonsPanel.add(addButton);
        buttonsPanel.add(editButton);
        buttonsPanel.add(deleteButton);
        buttonsPanel.add(refreshButton);
        buttonsPanel.add(importButton);

        itemsPanel.add(buttonsPanel, BorderLayout.SOUTH);

        mainTabbedPane.addTab("قائمة الأصناف", itemsPanel);
    }

    /**
     * إنشاء تبويب بيانات IAS_ITM_MST
     */
    private void createIasMstTab() {
        JPanel mstPanel = new JPanel(new BorderLayout());

        // لوحة البيانات الأساسية
        JPanel basicDataPanel = new JPanel(new GridBagLayout());
        basicDataPanel
                .setBorder(BorderFactory.createTitledBorder("البيانات الأساسية - IAS_ITM_MST"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // الصف الأول
        gbc.gridx = 0;
        gbc.gridy = 0;
        basicDataPanel.add(new JLabel("كود الصنف (I_CODE):"), gbc);
        gbc.gridx = 1;
        iCodeField = new JTextField(15);
        basicDataPanel.add(iCodeField, gbc);

        gbc.gridx = 2;
        basicDataPanel.add(new JLabel("اسم الصنف (I_NAME):"), gbc);
        gbc.gridx = 3;
        iNameField = new JTextField(20);
        basicDataPanel.add(iNameField, gbc);

        // الصف الثاني
        gbc.gridx = 0;
        gbc.gridy = 1;
        basicDataPanel.add(new JLabel("الاسم الإنجليزي (I_E_NAME):"), gbc);
        gbc.gridx = 1;
        iENameField = new JTextField(15);
        basicDataPanel.add(iENameField, gbc);

        gbc.gridx = 2;
        basicDataPanel.add(new JLabel("كود المجموعة (G_CODE):"), gbc);
        gbc.gridx = 3;
        gCodeField = new JTextField(20);
        basicDataPanel.add(gCodeField, gbc);

        // الصف الثالث
        gbc.gridx = 0;
        gbc.gridy = 2;
        basicDataPanel.add(new JLabel("المجموعة الفرعية (MNG_CODE):"), gbc);
        gbc.gridx = 1;
        mngCodeField = new JTextField(15);
        basicDataPanel.add(mngCodeField, gbc);

        gbc.gridx = 2;
        basicDataPanel.add(new JLabel("المجموعة الفرعية 2 (SUBG_CODE):"), gbc);
        gbc.gridx = 3;
        subgCodeField = new JTextField(20);
        basicDataPanel.add(subgCodeField, gbc);

        // الصف الرابع
        gbc.gridx = 0;
        gbc.gridy = 3;
        basicDataPanel.add(new JLabel("نوع الصنف (ITEM_TYPE):"), gbc);
        gbc.gridx = 1;
        itemTypeCombo = new JComboBox<>(new String[] {"1-عادي", "2-خدمة", "3-مجموعة", "4-مكون"});
        basicDataPanel.add(itemTypeCombo, gbc);

        gbc.gridx = 2;
        basicDataPanel.add(new JLabel("حجم الصنف (ITEM_SIZE):"), gbc);
        gbc.gridx = 3;
        itemSizeField = new JTextField(20);
        itemSizeField.setText("1");
        basicDataPanel.add(itemSizeField, gbc);

        // الصف الخامس - الوصف
        gbc.gridx = 0;
        gbc.gridy = 4;
        basicDataPanel.add(new JLabel("وصف الصنف (I_DESC):"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        iDescArea = new JTextArea(3, 50);
        iDescArea.setLineWrap(true);
        iDescArea.setWrapStyleWord(true);
        JScrollPane descScrollPane = new JScrollPane(iDescArea);
        basicDataPanel.add(descScrollPane, gbc);
        gbc.gridwidth = 1; // إعادة تعيين

        mstPanel.add(basicDataPanel, BorderLayout.NORTH);

        // لوحة التكاليف والأكواد
        JPanel costsPanel = new JPanel(new GridBagLayout());
        costsPanel.setBorder(BorderFactory.createTitledBorder("التكاليف والأكواد"));

        gbc.gridx = 0;
        gbc.gridy = 0;
        costsPanel.add(new JLabel("تسعير الأصناف (PRIMARY_COST):"), gbc);
        gbc.gridx = 1;
        primaryCostField = new JTextField(10);
        primaryCostField.setText("0");
        costsPanel.add(primaryCostField, gbc);

        gbc.gridx = 2;
        costsPanel.add(new JLabel("التكلفة الأولية (INIT_PRIMARY_COST):"), gbc);
        gbc.gridx = 3;
        initPrimaryCostField = new JTextField(10);
        initPrimaryCostField.setText("0");
        costsPanel.add(initPrimaryCostField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 1;
        costsPanel.add(new JLabel("الكود البديل (ALTER_CODE):"), gbc);
        gbc.gridx = 1;
        alterCodeField = new JTextField(10);
        costsPanel.add(alterCodeField, gbc);

        gbc.gridx = 2;
        costsPanel.add(new JLabel("كود المصنع (MANF_CODE):"), gbc);
        gbc.gridx = 3;
        manfCodeField = new JTextField(10);
        costsPanel.add(manfCodeField, gbc);

        gbc.gridx = 0;
        gbc.gridy = 2;
        costsPanel.add(new JLabel("كود المورد (V_CODE):"), gbc);
        gbc.gridx = 1;
        vCodeField = new JTextField(10);
        costsPanel.add(vCodeField, gbc);

        mstPanel.add(costsPanel, BorderLayout.CENTER);

        // لوحة الخيارات
        JPanel optionsPanel = new JPanel(new GridLayout(2, 4, 5, 5));
        optionsPanel.setBorder(BorderFactory.createTitledBorder("الخيارات"));

        inactiveCheckBox = new JCheckBox("غير نشط (INACTIVE)");
        serviceItmCheckBox = new JCheckBox("صنف خدمة (SERVICE_ITM)");
        cashSaleCheckBox = new JCheckBox("بيع نقدي (CASH_SALE)");
        noReturnSaleCheckBox = new JCheckBox("عدم إرجاع (NO_RETURN_SALE)");
        kitItmCheckBox = new JCheckBox("صنف مجموعة (KIT_ITM)");
        useExpDateCheckBox = new JCheckBox("استخدام تاريخ انتهاء (USE_EXP_DATE)");
        useBatchNoCheckBox = new JCheckBox("استخدام رقم دفعة (USE_BATCH_NO)");
        useSerialNoCheckBox = new JCheckBox("استخدام رقم تسلسلي (USE_SERIALNO)");

        optionsPanel.add(inactiveCheckBox);
        optionsPanel.add(serviceItmCheckBox);
        optionsPanel.add(cashSaleCheckBox);
        optionsPanel.add(noReturnSaleCheckBox);
        optionsPanel.add(kitItmCheckBox);
        optionsPanel.add(useExpDateCheckBox);
        optionsPanel.add(useBatchNoCheckBox);
        optionsPanel.add(useSerialNoCheckBox);

        mstPanel.add(optionsPanel, BorderLayout.SOUTH);

        mainTabbedPane.addTab("بيانات IAS_ITM_MST", mstPanel);
    }

    /**
     * إنشاء تبويب بيانات IAS_ITM_DTL
     */
    private void createIasDtlTab() {
        JPanel dtlPanel = new JPanel(new BorderLayout());

        // لوحة بيانات الوحدات
        JPanel unitsPanel = new JPanel(new GridBagLayout());
        unitsPanel.setBorder(BorderFactory.createTitledBorder("بيانات الوحدات - IAS_ITM_DTL"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // الصف الأول
        gbc.gridx = 0;
        gbc.gridy = 0;
        unitsPanel.add(new JLabel("وحدة الصنف (ITM_UNT):"), gbc);
        gbc.gridx = 1;
        itmUntField = new JTextField(15);
        unitsPanel.add(itmUntField, gbc);

        gbc.gridx = 2;
        unitsPanel.add(new JLabel("حجم العبوة (P_SIZE):"), gbc);
        gbc.gridx = 3;
        pSizeField = new JTextField(10);
        pSizeField.setText("1");
        unitsPanel.add(pSizeField, gbc);

        // الصف الثاني
        gbc.gridx = 0;
        gbc.gridy = 1;
        unitsPanel.add(new JLabel("وصف الوحدة عربي (ITM_UNT_L_DSC):"), gbc);
        gbc.gridx = 1;
        itmUntLDscField = new JTextField(20);
        unitsPanel.add(itmUntLDscField, gbc);

        gbc.gridx = 2;
        unitsPanel.add(new JLabel("وصف الوحدة إنجليزي (ITM_UNT_F_DSC):"), gbc);
        gbc.gridx = 3;
        itmUntFDscField = new JTextField(20);
        unitsPanel.add(itmUntFDscField, gbc);

        // الصف الثالث
        gbc.gridx = 0;
        gbc.gridy = 2;
        unitsPanel.add(new JLabel("الباركود (BARCODE):"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        barcodeField = new JTextField(30);
        unitsPanel.add(barcodeField, gbc);
        gbc.gridwidth = 1;

        dtlPanel.add(unitsPanel, BorderLayout.NORTH);

        // لوحة خيارات الوحدات
        JPanel unitOptionsPanel = new JPanel(new GridLayout(2, 3, 10, 10));
        unitOptionsPanel.setBorder(BorderFactory.createTitledBorder("خيارات الوحدات"));

        mainUnitCheckBox = new JCheckBox("الوحدة الرئيسية (MAIN_UNIT)");
        saleUnitCheckBox = new JCheckBox("وحدة البيع (SALE_UNIT)");
        purUnitCheckBox = new JCheckBox("وحدة الشراء (PUR_UNIT)");
        stockUnitCheckBox = new JCheckBox("وحدة المخزون (STOCK_UNIT)");

        unitOptionsPanel.add(mainUnitCheckBox);
        unitOptionsPanel.add(saleUnitCheckBox);
        unitOptionsPanel.add(purUnitCheckBox);
        unitOptionsPanel.add(stockUnitCheckBox);

        dtlPanel.add(unitOptionsPanel, BorderLayout.CENTER);

        // جدول تفاصيل الوحدات
        String[] dtlColumns =
                {"وحدة الصنف", "حجم العبوة", "وصف الوحدة", "الباركود", "وحدة رئيسية", "وحدة بيع"};
        detailsTableModel = new DefaultTableModel(dtlColumns, 0);
        detailsTable = new JTable(detailsTableModel);

        JScrollPane dtlScrollPane = new JScrollPane(detailsTable);
        dtlScrollPane.setPreferredSize(new Dimension(WINDOW_WIDTH - 50, 200));
        dtlScrollPane.setBorder(BorderFactory.createTitledBorder("تفاصيل الوحدات الموجودة"));

        dtlPanel.add(dtlScrollPane, BorderLayout.SOUTH);

        mainTabbedPane.addTab("بيانات IAS_ITM_DTL", dtlPanel);
    }

    /**
     * إنشاء تبويب الإحصائيات
     */
    private void createStatisticsTab() {
        JPanel statsPanel = new JPanel(new GridLayout(2, 2, 10, 10));
        statsPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // بطاقات الإحصائيات
        JPanel totalItemsCard = createStatCard("إجمالي الأصناف", "0", Color.BLUE);
        JPanel activeItemsCard = createStatCard("الأصناف النشطة", "0", Color.GREEN);
        JPanel serviceItemsCard = createStatCard("أصناف الخدمة", "0", Color.ORANGE);
        JPanel unitsCard = createStatCard("إجمالي الوحدات", "0", Color.MAGENTA);

        statsPanel.add(totalItemsCard);
        statsPanel.add(activeItemsCard);
        statsPanel.add(serviceItemsCard);
        statsPanel.add(unitsCard);

        mainTabbedPane.addTab("الإحصائيات", statsPanel);
    }

    /**
     * إنشاء بطاقة إحصائية
     */
    private JPanel createStatCard(String title, String value, Color color) {
        JPanel card = new JPanel(new BorderLayout());
        card.setBorder(BorderFactory.createLineBorder(color, 2));
        card.setBackground(Color.WHITE);

        JLabel titleLabel = new JLabel(title, SwingConstants.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 14));
        titleLabel.setForeground(color);

        JLabel valueLabel = new JLabel(value, SwingConstants.CENTER);
        valueLabel.setFont(new Font("Arial", Font.BOLD, 24));
        valueLabel.setForeground(color);

        card.add(titleLabel, BorderLayout.NORTH);
        card.add(valueLabel, BorderLayout.CENTER);

        return card;
    }

    /**
     * إنشاء شريط الأدوات
     */
    private void createToolbar() {
        JToolBar toolbar = new JToolBar();
        toolbar.setFloatable(false);

        JButton saveButton = new JButton("حفظ");
        JButton newButton = new JButton("جديد");
        JButton deleteButton = new JButton("حذف");
        JButton importButton = new JButton("استيراد");
        JButton exportButton = new JButton("تصدير");

        saveButton.addActionListener(e -> saveCurrentItem());
        newButton.addActionListener(e -> clearForm());
        importButton.addActionListener(e -> importFromIAS20251());

        toolbar.add(newButton);
        toolbar.add(saveButton);
        toolbar.add(deleteButton);
        toolbar.addSeparator();
        toolbar.add(importButton);
        toolbar.add(exportButton);

        add(toolbar, BorderLayout.NORTH);
    }

    /**
     * إنشاء شريط الحالة
     */
    private void createStatusBar() {
        JPanel statusBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusBar.setBorder(BorderFactory.createLoweredBevelBorder());

        JLabel statusLabel = new JLabel("جاهز - متصل بالجداول الحقيقية IAS_ITM_MST & IAS_ITM_DTL");
        statusBar.add(statusLabel);

        add(statusBar, BorderLayout.SOUTH);
    }

    /**
     * تحميل البيانات
     */
    private void loadData() {
        loadItemsData();
        loadStatistics();
    }

    /**
     * تحميل بيانات الأصناف من الجداول الحقيقية
     */
    private void loadItemsData() {
        try {
            itemsTableModel.setRowCount(0);

            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery("""
                        SELECT I_CODE, I_NAME, I_E_NAME, G_CODE, PRIMARY_COST, ITEM_TYPE,
                               CASE WHEN INACTIVE = 0 THEN 'نشط' ELSE 'غير نشط' END AS STATUS
                        FROM IAS_ITM_MST
                        ORDER BY I_NAME
                    """);

            while (rs.next()) {
                Vector<Object> row = new Vector<>();
                row.add(rs.getString("I_CODE"));
                row.add(rs.getString("I_NAME"));
                row.add(rs.getString("I_E_NAME"));
                row.add(rs.getString("G_CODE"));
                row.add(rs.getDouble("PRIMARY_COST"));

                int itemType = rs.getInt("ITEM_TYPE");
                String itemTypeStr = switch (itemType) {
                    case 1 -> "عادي";
                    case 2 -> "خدمة";
                    case 3 -> "مجموعة";
                    case 4 -> "مكون";
                    default -> "غير محدد";
                };
                row.add(itemTypeStr);
                row.add(rs.getString("STATUS"));

                itemsTableModel.addRow(row);
            }

            rs.close();
            stmt.close();

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل الأصناف: " + e.getMessage());
        }
    }

    /**
     * تحميل الإحصائيات
     */
    private void loadStatistics() {
        try {
            Statement stmt = connection.createStatement();

            // إجمالي الأصناف
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM IAS_ITM_MST");
            rs.next();
            int totalItems = rs.getInt(1);
            rs.close();

            // الأصناف النشطة
            rs = stmt.executeQuery("SELECT COUNT(*) FROM IAS_ITM_MST WHERE INACTIVE = 0");
            rs.next();
            int activeItems = rs.getInt(1);
            rs.close();

            // أصناف الخدمة
            rs = stmt.executeQuery("SELECT COUNT(*) FROM IAS_ITM_MST WHERE SERVICE_ITM = 1");
            rs.next();
            int serviceItems = rs.getInt(1);
            rs.close();

            // إجمالي الوحدات
            rs = stmt.executeQuery("SELECT COUNT(*) FROM IAS_ITM_DTL");
            rs.next();
            int totalUnits = rs.getInt(1);
            rs.close();

            stmt.close();

            // تحديث البطاقات (سيتم تطويرها لاحقاً)
            System.out.println("📊 الإحصائيات:");
            System.out.println("  - إجمالي الأصناف: " + totalItems);
            System.out.println("  - الأصناف النشطة: " + activeItems);
            System.out.println("  - أصناف الخدمة: " + serviceItems);
            System.out.println("  - إجمالي الوحدات: " + totalUnits);

        } catch (SQLException e) {
            System.out.println("⚠️ خطأ في تحميل الإحصائيات: " + e.getMessage());
        }
    }

    /**
     * تحميل بيانات الصنف المحدد
     */
    private void loadSelectedItemData() {
        int selectedRow = itemsTable.getSelectedRow();
        if (selectedRow >= 0) {
            String itemCode = (String) itemsTableModel.getValueAt(selectedRow, 0);
            loadItemData(itemCode);
        }
    }

    /**
     * تحميل بيانات صنف محدد من الجداول الحقيقية
     */
    private void loadItemData(String itemCode) {
        try {
            // تحميل بيانات IAS_ITM_MST
            PreparedStatement stmt =
                    connection.prepareStatement("SELECT * FROM IAS_ITM_MST WHERE I_CODE = ?");
            stmt.setString(1, itemCode);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                iCodeField.setText(rs.getString("I_CODE"));
                iNameField.setText(rs.getString("I_NAME"));
                iENameField.setText(rs.getString("I_E_NAME"));
                iDescArea.setText(rs.getString("I_DESC"));
                gCodeField.setText(rs.getString("G_CODE"));
                mngCodeField.setText(rs.getString("MNG_CODE"));
                subgCodeField.setText(rs.getString("SUBG_CODE"));
                itemSizeField.setText(String.valueOf(rs.getDouble("ITEM_SIZE")));

                int itemType = rs.getInt("ITEM_TYPE");
                if (itemType > 0 && itemType <= 4) {
                    itemTypeCombo.setSelectedIndex(itemType - 1);
                }

                primaryCostField.setText(String.valueOf(rs.getDouble("PRIMARY_COST")));
                initPrimaryCostField.setText(String.valueOf(rs.getDouble("INIT_PRIMARY_COST")));
                alterCodeField.setText(rs.getString("ALTER_CODE"));
                manfCodeField.setText(rs.getString("MANF_CODE"));
                vCodeField.setText(rs.getString("V_CODE"));

                // الخيارات
                inactiveCheckBox.setSelected(rs.getInt("INACTIVE") == 1);
                serviceItmCheckBox.setSelected(rs.getInt("SERVICE_ITM") == 1);
                cashSaleCheckBox.setSelected(rs.getInt("CASH_SALE") == 1);
                noReturnSaleCheckBox.setSelected(rs.getInt("NO_RETURN_SALE") == 1);
                kitItmCheckBox.setSelected(rs.getInt("KIT_ITM") == 1);
                useExpDateCheckBox.setSelected(rs.getInt("USE_EXP_DATE") == 1);
                useBatchNoCheckBox.setSelected(rs.getInt("USE_BATCH_NO") == 1);
                useSerialNoCheckBox.setSelected(rs.getInt("USE_SERIALNO") == 1);
            }

            rs.close();
            stmt.close();

            // تحميل بيانات IAS_ITM_DTL
            loadItemDetails(itemCode);

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل بيانات الصنف: " + e.getMessage());
        }
    }

    /**
     * تحميل تفاصيل الوحدات من IAS_ITM_DTL
     */
    private void loadItemDetails(String itemCode) {
        try {
            detailsTableModel.setRowCount(0);

            PreparedStatement stmt = connection.prepareStatement(
                    "SELECT * FROM IAS_ITM_DTL WHERE I_CODE = ? ORDER BY ITM_UNT");
            stmt.setString(1, itemCode);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                Vector<Object> row = new Vector<>();
                row.add(rs.getString("ITM_UNT"));
                row.add(rs.getDouble("P_SIZE"));
                row.add(rs.getString("ITM_UNT_L_DSC"));
                row.add(rs.getString("BARCODE"));
                row.add(rs.getInt("MAIN_UNIT") == 1 ? "نعم" : "لا");
                row.add(rs.getInt("SALE_UNIT") == 1 ? "نعم" : "لا");

                detailsTableModel.addRow(row);
            }

            rs.close();
            stmt.close();

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل تفاصيل الوحدات: " + e.getMessage());
        }
    }

    /**
     * إضافة صنف جديد
     */
    private void addNewItem() {
        clearForm();
        mainTabbedPane.setSelectedIndex(1); // الانتقال لتبويب IAS_ITM_MST
        iCodeField.requestFocus();
    }

    /**
     * تعديل الصنف المحدد
     */
    private void editSelectedItem() {
        int selectedRow = itemsTable.getSelectedRow();
        if (selectedRow >= 0) {
            String itemCode = (String) itemsTableModel.getValueAt(selectedRow, 0);
            loadItemData(itemCode);
            mainTabbedPane.setSelectedIndex(1); // الانتقال لتبويب IAS_ITM_MST
        } else {
            JOptionPane.showMessageDialog(this, "يرجى اختيار صنف للتعديل");
        }
    }

    /**
     * حذف الصنف المحدد
     */
    private void deleteSelectedItem() {
        int selectedRow = itemsTable.getSelectedRow();
        if (selectedRow >= 0) {
            String itemCode = (String) itemsTableModel.getValueAt(selectedRow, 0);
            String itemName = (String) itemsTableModel.getValueAt(selectedRow, 1);

            int result = JOptionPane.showConfirmDialog(this,
                    "هل أنت متأكد من حذف الصنف: " + itemCode + " - " + itemName + "؟",
                    "تأكيد الحذف", JOptionPane.YES_NO_OPTION);

            if (result == JOptionPane.YES_OPTION) {
                try {
                    // حذف من IAS_ITM_DTL أولاً (المفتاح الخارجي)
                    PreparedStatement stmt =
                            connection.prepareStatement("DELETE FROM IAS_ITM_DTL WHERE I_CODE = ?");
                    stmt.setString(1, itemCode);
                    stmt.executeUpdate();
                    stmt.close();

                    // حذف من IAS_ITM_MST
                    stmt = connection.prepareStatement("DELETE FROM IAS_ITM_MST WHERE I_CODE = ?");
                    stmt.setString(1, itemCode);
                    int deleted = stmt.executeUpdate();
                    stmt.close();

                    if (deleted > 0) {
                        JOptionPane.showMessageDialog(this, "تم حذف الصنف بنجاح");
                        loadItemsData();
                        clearForm();
                    }

                } catch (SQLException e) {
                    JOptionPane.showMessageDialog(this, "خطأ في حذف الصنف: " + e.getMessage());
                }
            }
        } else {
            JOptionPane.showMessageDialog(this, "يرجى اختيار صنف للحذف");
        }
    }

    /**
     * حفظ الصنف الحالي
     */
    private void saveCurrentItem() {
        try {
            String iCode = iCodeField.getText().trim();
            String iName = iNameField.getText().trim();

            if (iCode.isEmpty() || iName.isEmpty()) {
                JOptionPane.showMessageDialog(this, "يرجى إدخال كود الصنف والاسم العربي");
                return;
            }

            // فحص وجود الصنف
            PreparedStatement checkStmt = connection
                    .prepareStatement("SELECT COUNT(*) FROM IAS_ITM_MST WHERE I_CODE = ?");
            checkStmt.setString(1, iCode);
            ResultSet rs = checkStmt.executeQuery();
            rs.next();
            boolean exists = rs.getInt(1) > 0;
            rs.close();
            checkStmt.close();

            if (exists) {
                // تحديث
                updateItem(iCode);
            } else {
                // إدراج جديد
                insertNewItem(iCode);
            }

            loadItemsData();
            JOptionPane.showMessageDialog(this, "تم حفظ الصنف بنجاح");

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في حفظ الصنف: " + e.getMessage());
        }
    }

    /**
     * إدراج صنف جديد
     */
    private void insertNewItem(String iCode) throws SQLException {
        String sql =
                """
                            INSERT INTO IAS_ITM_MST (
                                I_CODE, I_NAME, I_E_NAME, I_DESC, G_CODE, MNG_CODE, SUBG_CODE,
                                ITEM_SIZE, ITEM_TYPE, PRIMARY_COST, INIT_PRIMARY_COST,
                                ALTER_CODE, MANF_CODE, V_CODE, INACTIVE, SERVICE_ITM, CASH_SALE,
                                NO_RETURN_SALE, KIT_ITM, USE_EXP_DATE, USE_BATCH_NO, USE_SERIALNO,
                                AD_U_ID, AD_DATE
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, SYSDATE)
                        """;

        PreparedStatement stmt = connection.prepareStatement(sql);
        stmt.setString(1, iCode);
        stmt.setString(2, iNameField.getText().trim());
        stmt.setString(3, iENameField.getText().trim());
        stmt.setString(4, iDescArea.getText().trim());
        stmt.setString(5, gCodeField.getText().trim());
        stmt.setString(6, mngCodeField.getText().trim());
        stmt.setString(7, subgCodeField.getText().trim());
        stmt.setDouble(8, Double
                .parseDouble(itemSizeField.getText().isEmpty() ? "1" : itemSizeField.getText()));
        stmt.setInt(9, itemTypeCombo.getSelectedIndex() + 1);
        stmt.setDouble(10, Double.parseDouble(
                primaryCostField.getText().isEmpty() ? "0" : primaryCostField.getText()));
        stmt.setDouble(11, Double.parseDouble(
                initPrimaryCostField.getText().isEmpty() ? "0" : initPrimaryCostField.getText()));
        stmt.setString(12, alterCodeField.getText().trim());
        stmt.setString(13, manfCodeField.getText().trim());
        stmt.setString(14, vCodeField.getText().trim());
        stmt.setInt(15, inactiveCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(16, serviceItmCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(17, cashSaleCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(18, noReturnSaleCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(19, kitItmCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(20, useExpDateCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(21, useBatchNoCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(22, useSerialNoCheckBox.isSelected() ? 1 : 0);

        stmt.executeUpdate();
        stmt.close();
    }

    /**
     * تحديث صنف موجود
     */
    private void updateItem(String iCode) throws SQLException {
        String sql =
                """
                            UPDATE IAS_ITM_MST SET
                                I_NAME = ?, I_E_NAME = ?, I_DESC = ?, G_CODE = ?, MNG_CODE = ?, SUBG_CODE = ?,
                                ITEM_SIZE = ?, ITEM_TYPE = ?, PRIMARY_COST = ?, INIT_PRIMARY_COST = ?,
                                ALTER_CODE = ?, MANF_CODE = ?, V_CODE = ?, INACTIVE = ?, SERVICE_ITM = ?,
                                CASH_SALE = ?, NO_RETURN_SALE = ?, KIT_ITM = ?, USE_EXP_DATE = ?,
                                USE_BATCH_NO = ?, USE_SERIALNO = ?, UP_U_ID = 1, UP_DATE = SYSDATE, UP_CNT = NVL(UP_CNT, 0) + 1
                            WHERE I_CODE = ?
                        """;

        PreparedStatement stmt = connection.prepareStatement(sql);
        stmt.setString(1, iNameField.getText().trim());
        stmt.setString(2, iENameField.getText().trim());
        stmt.setString(3, iDescArea.getText().trim());
        stmt.setString(4, gCodeField.getText().trim());
        stmt.setString(5, mngCodeField.getText().trim());
        stmt.setString(6, subgCodeField.getText().trim());
        stmt.setDouble(7, Double
                .parseDouble(itemSizeField.getText().isEmpty() ? "1" : itemSizeField.getText()));
        stmt.setInt(8, itemTypeCombo.getSelectedIndex() + 1);
        stmt.setDouble(9, Double.parseDouble(
                primaryCostField.getText().isEmpty() ? "0" : primaryCostField.getText()));
        stmt.setDouble(10, Double.parseDouble(
                initPrimaryCostField.getText().isEmpty() ? "0" : initPrimaryCostField.getText()));
        stmt.setString(11, alterCodeField.getText().trim());
        stmt.setString(12, manfCodeField.getText().trim());
        stmt.setString(13, vCodeField.getText().trim());
        stmt.setInt(14, inactiveCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(15, serviceItmCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(16, cashSaleCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(17, noReturnSaleCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(18, kitItmCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(19, useExpDateCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(20, useBatchNoCheckBox.isSelected() ? 1 : 0);
        stmt.setInt(21, useSerialNoCheckBox.isSelected() ? 1 : 0);
        stmt.setString(22, iCode);

        stmt.executeUpdate();
        stmt.close();
    }

    /**
     * مسح النموذج
     */
    private void clearForm() {
        iCodeField.setText("");
        iNameField.setText("");
        iENameField.setText("");
        iDescArea.setText("");
        gCodeField.setText("");
        mngCodeField.setText("");
        subgCodeField.setText("");
        itemSizeField.setText("1");
        itemTypeCombo.setSelectedIndex(0);
        primaryCostField.setText("0");
        initPrimaryCostField.setText("0");
        alterCodeField.setText("");
        manfCodeField.setText("");
        vCodeField.setText("");

        // مسح الخيارات
        inactiveCheckBox.setSelected(false);
        serviceItmCheckBox.setSelected(false);
        cashSaleCheckBox.setSelected(false);
        noReturnSaleCheckBox.setSelected(false);
        kitItmCheckBox.setSelected(false);
        useExpDateCheckBox.setSelected(false);
        useBatchNoCheckBox.setSelected(false);
        useSerialNoCheckBox.setSelected(false);

        // مسح حقول IAS_ITM_DTL
        itmUntField.setText("");
        pSizeField.setText("1");
        itmUntLDscField.setText("");
        itmUntFDscField.setText("");
        barcodeField.setText("");
        mainUnitCheckBox.setSelected(false);
        saleUnitCheckBox.setSelected(false);
        purUnitCheckBox.setSelected(false);
        stockUnitCheckBox.setSelected(false);

        detailsTableModel.setRowCount(0);
    }

    /**
     * استيراد البيانات من IAS20251
     */
    private void importFromIAS20251() {
        JOptionPane.showMessageDialog(this, "وظيفة الاستيراد من IAS20251 ستتم إضافتها لاحقاً\n"
                + "النافذة الحالية تتعامل مع الجداول الحقيقية IAS_ITM_MST و IAS_ITM_DTL");
    }
}
