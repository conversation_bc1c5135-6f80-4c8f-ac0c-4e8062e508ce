import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GraphicsEnvironment;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.Window;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import javax.swing.BorderFactory;
import javax.swing.DefaultListCellRenderer;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JColorChooser;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JList;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JSlider;
import javax.swing.JSpinner;
import javax.swing.JTabbedPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SpinnerNumberModel;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * نافذة إعدادات الواجهة والمظهر المتقدمة
 * Advanced UI and Theme Settings Window
 */
public class AdvancedUISettingsWindow extends JDialog {
    
    // مكونات الواجهة
    private JComboBox<ThemeInfo> themeComboBox;
    private JComboBox<String> fontFamilyCombo;
    private JSpinner fontSizeSpinner;
    private JCheckBox enableAnimationsCheck;
    private JCheckBox enableSoundsCheck;
    private JCheckBox enableTooltipsCheck;
    private JCheckBox rtlSupportCheck;
    private JSlider transparencySlider;
    private JColorChooser accentColorChooser;
    private JPanel previewPanel;
    private JTextArea logArea;
    
    // إعدادات قاعدة البيانات
    private Connection connection;
    private final String DB_URL = "*************************************";
    private final String DB_USER = "ias20251";
    private final String DB_PASSWORD = "ys123";
    
    // قائمة الثيمات المتاحة
    private List<ThemeInfo> availableThemes;
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new AdvancedUISettingsWindow(null).setVisible(true);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
    
    public AdvancedUISettingsWindow(JFrame parent) {
        super(parent, "إعدادات الواجهة والمظهر المتقدمة - Advanced UI Settings", true);
        
        initializeDatabase();
        initializeThemes();
        initializeComponents();
        layoutComponents();
        loadCurrentSettings();
        setupEventHandlers();
        
        setSize(900, 700);
        setLocationRelativeTo(parent);
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    }
    
    /**
     * تهيئة اتصال قاعدة البيانات
     */
    private void initializeDatabase() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Properties props = new Properties();
            props.setProperty("user", DB_USER);
            props.setProperty("password", DB_PASSWORD);
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            connection = DriverManager.getConnection(DB_URL, props);
            
            // إنشاء جدول الإعدادات إذا لم يكن موجوداً
            createSettingsTableIfNotExists();
            
            logMessage("✅ تم الاتصال بقاعدة البيانات بنجاح");
            
        } catch (Exception e) {
            logMessage("❌ فشل الاتصال بقاعدة البيانات: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * إنشاء جدول الإعدادات
     */
    private void createSettingsTableIfNotExists() {
        try {
            String createTableSQL = """
                CREATE TABLE UI_SETTINGS (
                    SETTING_ID VARCHAR2(50) PRIMARY KEY,
                    SETTING_VALUE NVARCHAR2(500),
                    SETTING_TYPE VARCHAR2(20),
                    DESCRIPTION NVARCHAR2(200),
                    CREATED_DATE DATE DEFAULT SYSDATE,
                    MODIFIED_DATE DATE DEFAULT SYSDATE,
                    CREATED_BY VARCHAR2(50) DEFAULT USER
                )
                """;
            
            try (PreparedStatement stmt = connection.prepareStatement(createTableSQL)) {
                stmt.execute();
                logMessage("✅ تم إنشاء جدول UI_SETTINGS");
            }
            
        } catch (SQLException e) {
            if (e.getErrorCode() != 955) { // Table already exists
                logMessage("⚠️ خطأ في إنشاء جدول الإعدادات: " + e.getMessage());
            }
        }
    }
    
    /**
     * تهيئة قائمة الثيمات المتاحة
     */
    private void initializeThemes() {
        availableThemes = new ArrayList<>();
        
        // الثيمات الافتراضية
        availableThemes.add(new ThemeInfo("النظام الافتراضي", "javax.swing.plaf.system.SystemLookAndFeel", "System"));
        availableThemes.add(new ThemeInfo("Metal", "javax.swing.plaf.metal.MetalLookAndFeel", "Built-in"));
        availableThemes.add(new ThemeInfo("Nimbus", "javax.swing.plaf.nimbus.NimbusLookAndFeel", "Built-in"));
        
        // FlatLaf Themes
        addThemeIfAvailable("FlatLaf فاتح", "com.formdev.flatlaf.FlatLightLaf", "FlatLaf");
        addThemeIfAvailable("FlatLaf مظلم", "com.formdev.flatlaf.FlatDarkLaf", "FlatLaf");
        addThemeIfAvailable("FlatLaf IntelliJ", "com.formdev.flatlaf.FlatIntelliJLaf", "FlatLaf");
        addThemeIfAvailable("FlatLaf Darcula", "com.formdev.flatlaf.FlatDarculaLaf", "FlatLaf");
        
        // Material UI
        addThemeIfAvailable("Material Light", "mdlaf.MaterialLookAndFeel", "Material");
        
        // JTattoo Themes
        addThemeIfAvailable("JTattoo Acryl", "com.jtattoo.plaf.acryl.AcrylLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo Aero", "com.jtattoo.plaf.aero.AeroLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo Aluminium", "com.jtattoo.plaf.aluminium.AluminiumLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo Bernstein", "com.jtattoo.plaf.bernstein.BernsteinLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo Fast", "com.jtattoo.plaf.fast.FastLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo HiFi", "com.jtattoo.plaf.hifi.HiFiLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo Luna", "com.jtattoo.plaf.luna.LunaLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo McWin", "com.jtattoo.plaf.mcwin.McWinLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo Mint", "com.jtattoo.plaf.mint.MintLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo Noire", "com.jtattoo.plaf.noire.NoireLookAndFeel", "JTattoo");
        addThemeIfAvailable("JTattoo Smart", "com.jtattoo.plaf.smart.SmartLookAndFeel", "JTattoo");
        
        // DarkLaf
        addThemeIfAvailable("DarkLaf", "com.github.weisj.darklaf.DarkLaf", "DarkLaf");
        
        // SeaGlass
        addThemeIfAvailable("SeaGlass", "com.seaglasslookandfeel.SeaGlassLookAndFeel", "SeaGlass");
        
        logMessage("تم تحميل " + availableThemes.size() + " ثيم متاح");
    }
    
    /**
     * إضافة ثيم إذا كان متاحاً
     */
    private void addThemeIfAvailable(String name, String className, String category) {
        try {
            Class.forName(className);
            availableThemes.add(new ThemeInfo(name, className, category));
            logMessage("✅ تم العثور على: " + name);
        } catch (ClassNotFoundException e) {
            logMessage("❌ غير متاح: " + name);
        } catch (NoClassDefFoundError e) {
            logMessage("❌ مكتبة مفقودة لـ: " + name + " - " + e.getMessage());
        } catch (Exception e) {
            logMessage("❌ خطأ في تحميل: " + name + " - " + e.getMessage());
        }
    }
    
    /**
     * تهيئة مكونات الواجهة
     */
    private void initializeComponents() {
        // قائمة الثيمات
        themeComboBox = new JComboBox<>(availableThemes.toArray(new ThemeInfo[0]));
        themeComboBox.setRenderer(new ThemeComboRenderer());
        
        // إعدادات الخطوط
        String[] fonts = GraphicsEnvironment.getLocalGraphicsEnvironment().getAvailableFontFamilyNames();
        fontFamilyCombo = new JComboBox<>(fonts);
        fontSizeSpinner = new JSpinner(new SpinnerNumberModel(12, 8, 72, 1));
        
        // خيارات الواجهة
        enableAnimationsCheck = new JCheckBox("تفعيل الحركات والانتقالات");
        enableSoundsCheck = new JCheckBox("تفعيل الأصوات");
        enableTooltipsCheck = new JCheckBox("تفعيل التلميحات");
        rtlSupportCheck = new JCheckBox("دعم الكتابة من اليمين لليسار");
        
        // شريط الشفافية
        transparencySlider = new JSlider(0, 100, 100);
        transparencySlider.setMajorTickSpacing(25);
        transparencySlider.setPaintTicks(true);
        transparencySlider.setPaintLabels(true);
        
        // اختيار لون التمييز
        accentColorChooser = new JColorChooser(new Color(0, 120, 215));
        
        // منطقة المعاينة
        previewPanel = createPreviewPanel();
        
        // منطقة السجل
        logArea = new JTextArea(8, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        logArea.setBackground(new Color(248, 248, 248));
    }
    
    /**
     * إنشاء منطقة المعاينة
     */
    private JPanel createPreviewPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("معاينة المظهر - Theme Preview"));
        panel.setPreferredSize(new Dimension(400, 200));
        
        // مكونات المعاينة
        JPanel demoPanel = new JPanel(new FlowLayout());
        demoPanel.add(new JButton("زر تجريبي"));
        demoPanel.add(new JTextField("نص تجريبي", 15));
        demoPanel.add(new JCheckBox("خيار تجريبي", true));
        
        JComboBox<String> demoCombo = new JComboBox<>(new String[]{"خيار 1", "خيار 2", "خيار 3"});
        demoPanel.add(demoCombo);
        
        JProgressBar demoProgress = new JProgressBar(0, 100);
        demoProgress.setValue(65);
        demoProgress.setStringPainted(true);
        demoPanel.add(demoProgress);
        
        panel.add(demoPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * تخطيط المكونات
     */
    private void layoutComponents() {
        setLayout(new BorderLayout());
        
        // الجزء الرئيسي - تبويبات
        JTabbedPane tabbedPane = new JTabbedPane();
        
        // تبويب الثيمات
        tabbedPane.addTab("الثيمات", createThemeTab());
        
        // تبويب الخطوط
        tabbedPane.addTab("الخطوط", createFontTab());
        
        // تبويب الواجهة
        tabbedPane.addTab("الواجهة", createInterfaceTab());
        
        // تبويب الألوان
        tabbedPane.addTab("الألوان", createColorTab());
        
        // تبويب المعاينة
        tabbedPane.addTab("المعاينة", createPreviewTab());
        
        add(tabbedPane, BorderLayout.CENTER);
        
        // الجزء السفلي - الأزرار والسجل
        add(createBottomPanel(), BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء تبويب الثيمات
     */
    private JPanel createThemeTab() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;
        
        // اختيار الثيم
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("اختر الثيم:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(themeComboBox, gbc);
        
        // زر تطبيق فوري
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE;
        JButton applyButton = new JButton("تطبيق فوري");
        applyButton.addActionListener(e -> applyThemeImmediately());
        panel.add(applyButton, gbc);
        
        // معلومات الثيم
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 3;
        gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0;
        
        JTextArea themeInfoArea = new JTextArea(10, 40);
        themeInfoArea.setEditable(false);
        themeInfoArea.setText(getThemeInfo());
        themeInfoArea.setBorder(BorderFactory.createTitledBorder("معلومات الثيمات المتاحة"));
        
        panel.add(new JScrollPane(themeInfoArea), gbc);
        
        return panel;
    }
    
    /**
     * الحصول على معلومات الثيمات
     */
    private String getThemeInfo() {
        StringBuilder info = new StringBuilder();
        info.append("الثيمات المتاحة في النظام:\n\n");
        
        String currentCategory = "";
        for (ThemeInfo theme : availableThemes) {
            if (!theme.category.equals(currentCategory)) {
                currentCategory = theme.category;
                info.append("=== ").append(currentCategory).append(" ===\n");
            }
            info.append("• ").append(theme.name).append("\n");
        }
        
        info.append("\nإجمالي الثيمات: ").append(availableThemes.size()).append(" ثيم");
        
        return info.toString();
    }
    
    /**
     * تسجيل رسالة في السجل
     */
    private void logMessage(String message) {
        if (logArea != null) {
            String timestamp = java.time.LocalTime.now().toString().substring(0, 8);
            String logEntry = "[" + timestamp + "] " + message + "\n";
            logArea.append(logEntry);
            logArea.setCaretPosition(logArea.getDocument().getLength());
        }
        System.out.println(message);
    }
    
    /**
     * فئة معلومات الثيم
     */
    private static class ThemeInfo {
        String name;
        String className;
        String category;
        
        ThemeInfo(String name, String className, String category) {
            this.name = name;
            this.className = className;
            this.category = category;
        }
        
        @Override
        public String toString() {
            return name;
        }
    }
    
    /**
     * إنشاء تبويب الخطوط
     */
    private JPanel createFontTab() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;

        // عائلة الخط
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("عائلة الخط:"), gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(fontFamilyCombo, gbc);

        // حجم الخط
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("حجم الخط:"), gbc);

        gbc.gridx = 1;
        panel.add(fontSizeSpinner, gbc);

        // معاينة الخط
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0;

        JTextArea fontPreview = new JTextArea("هذا نص تجريبي للخط العربي\nThis is a sample text for English font\n1234567890");
        fontPreview.setEditable(false);
        fontPreview.setBorder(BorderFactory.createTitledBorder("معاينة الخط"));

        panel.add(new JScrollPane(fontPreview), gbc);

        return panel;
    }

    /**
     * إنشاء تبويب الواجهة
     */
    private JPanel createInterfaceTab() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.WEST;

        // خيارات الواجهة
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(enableAnimationsCheck, gbc);

        gbc.gridy = 1;
        panel.add(enableSoundsCheck, gbc);

        gbc.gridy = 2;
        panel.add(enableTooltipsCheck, gbc);

        gbc.gridy = 3;
        panel.add(rtlSupportCheck, gbc);

        // شريط الشفافية
        gbc.gridy = 4;
        panel.add(new JLabel("مستوى الشفافية:"), gbc);

        gbc.gridy = 5; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(transparencySlider, gbc);

        return panel;
    }

    /**
     * إنشاء تبويب الألوان
     */
    private JPanel createColorTab() {
        JPanel panel = new JPanel(new BorderLayout());

        JLabel label = new JLabel("لون التمييز الأساسي:", SwingConstants.CENTER);
        panel.add(label, BorderLayout.NORTH);

        panel.add(accentColorChooser, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء تبويب المعاينة
     */
    private JPanel createPreviewTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.add(previewPanel, BorderLayout.CENTER);

        JButton refreshButton = new JButton("تحديث المعاينة");
        refreshButton.addActionListener(e -> updatePreview());

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(refreshButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إنشاء الجزء السفلي
     */
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // منطقة السجل
        JScrollPane logScrollPane = new JScrollPane(logArea);
        logScrollPane.setBorder(BorderFactory.createTitledBorder("سجل الأحداث"));
        logScrollPane.setPreferredSize(new Dimension(0, 120));
        panel.add(logScrollPane, BorderLayout.CENTER);

        // أزرار التحكم
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton saveButton = new JButton("حفظ الإعدادات");
        saveButton.addActionListener(e -> saveSettings());
        buttonPanel.add(saveButton);

        JButton loadButton = new JButton("تحميل الإعدادات");
        loadButton.addActionListener(e -> loadCurrentSettings());
        buttonPanel.add(loadButton);

        JButton resetButton = new JButton("إعادة تعيين");
        resetButton.addActionListener(e -> resetToDefaults());
        buttonPanel.add(resetButton);

        JButton closeButton = new JButton("إغلاق");
        closeButton.addActionListener(e -> dispose());
        buttonPanel.add(closeButton);

        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        // تحديث المعاينة عند تغيير الثيم
        themeComboBox.addActionListener(e -> updatePreview());

        // تحديث معاينة الخط
        fontFamilyCombo.addActionListener(e -> updateFontPreview());
        fontSizeSpinner.addChangeListener(e -> updateFontPreview());

        // تحديث الشفافية
        transparencySlider.addChangeListener(e -> updateTransparency());
    }

    /**
     * تطبيق الثيم فوراً
     */
    private void applyThemeImmediately() {
        ThemeInfo selected = (ThemeInfo) themeComboBox.getSelectedItem();
        if (selected == null) return;

        try {
            logMessage("تطبيق الثيم: " + selected.name);

            UIManager.setLookAndFeel(selected.className);
            SwingUtilities.updateComponentTreeUI(this);

            // تحديث جميع النوافذ المفتوحة
            for (Window window : Window.getWindows()) {
                SwingUtilities.updateComponentTreeUI(window);
            }

            logMessage("✅ تم تطبيق الثيم بنجاح: " + selected.name);

        } catch (Exception e) {
            logMessage("❌ فشل في تطبيق الثيم: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * تحديث المعاينة
     */
    private void updatePreview() {
        SwingUtilities.updateComponentTreeUI(previewPanel);
        previewPanel.repaint();
    }

    /**
     * تحديث معاينة الخط
     */
    private void updateFontPreview() {
        // سيتم تنفيذها لاحقاً
    }

    /**
     * تحديث الشفافية
     */
    private void updateTransparency() {
        float alpha = transparencySlider.getValue() / 100.0f;
        logMessage("تحديث الشفافية: " + (int)(alpha * 100) + "%");
    }

    /**
     * حفظ الإعدادات في قاعدة البيانات
     */
    private void saveSettings() {
        try {
            logMessage("حفظ الإعدادات في قاعدة البيانات...");

            // حفظ الثيم المحدد
            ThemeInfo selectedTheme = (ThemeInfo) themeComboBox.getSelectedItem();
            if (selectedTheme != null) {
                saveSetting("CURRENT_THEME", selectedTheme.className, "STRING", "الثيم الحالي");
                saveSetting("CURRENT_THEME_NAME", selectedTheme.name, "STRING", "اسم الثيم الحالي");
            }

            // حفظ إعدادات الخط
            saveSetting("FONT_FAMILY", (String) fontFamilyCombo.getSelectedItem(), "STRING", "عائلة الخط");
            saveSetting("FONT_SIZE", fontSizeSpinner.getValue().toString(), "INTEGER", "حجم الخط");

            // حفظ إعدادات الواجهة
            saveSetting("ENABLE_ANIMATIONS", String.valueOf(enableAnimationsCheck.isSelected()), "BOOLEAN", "تفعيل الحركات");
            saveSetting("ENABLE_SOUNDS", String.valueOf(enableSoundsCheck.isSelected()), "BOOLEAN", "تفعيل الأصوات");
            saveSetting("ENABLE_TOOLTIPS", String.valueOf(enableTooltipsCheck.isSelected()), "BOOLEAN", "تفعيل التلميحات");
            saveSetting("RTL_SUPPORT", String.valueOf(rtlSupportCheck.isSelected()), "BOOLEAN", "دعم RTL");

            // حفظ الشفافية
            saveSetting("TRANSPARENCY_LEVEL", String.valueOf(transparencySlider.getValue()), "INTEGER", "مستوى الشفافية");

            // حفظ لون التمييز
            Color accentColor = accentColorChooser.getColor();
            String colorHex = String.format("#%02x%02x%02x", accentColor.getRed(), accentColor.getGreen(), accentColor.getBlue());
            saveSetting("ACCENT_COLOR", colorHex, "STRING", "لون التمييز");

            logMessage("✅ تم حفظ جميع الإعدادات بنجاح");

            JOptionPane.showMessageDialog(this,
                "تم حفظ الإعدادات بنجاح!\nسيتم تطبيق التغييرات عند إعادة تشغيل التطبيق.",
                "حفظ ناجح",
                JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            logMessage("❌ خطأ في حفظ الإعدادات: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "حدث خطأ أثناء حفظ الإعدادات:\n" + e.getMessage(),
                "خطأ",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * حفظ إعداد واحد في قاعدة البيانات
     */
    private void saveSetting(String settingId, String value, String type, String description) throws SQLException {
        String sql = """
            MERGE INTO UI_SETTINGS
            USING DUAL ON (SETTING_ID = ?)
            WHEN MATCHED THEN
                UPDATE SET SETTING_VALUE = ?, MODIFIED_DATE = SYSDATE
            WHEN NOT MATCHED THEN
                INSERT (SETTING_ID, SETTING_VALUE, SETTING_TYPE, DESCRIPTION)
                VALUES (?, ?, ?, ?)
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, settingId);
            stmt.setString(2, value);
            stmt.setString(3, settingId);
            stmt.setString(4, value);
            stmt.setString(5, type);
            stmt.setString(6, description);

            stmt.executeUpdate();
        }
    }

    /**
     * تحميل الإعدادات الحالية من قاعدة البيانات
     */
    private void loadCurrentSettings() {
        try {
            logMessage("تحميل الإعدادات من قاعدة البيانات...");

            // تحميل الثيم
            String currentTheme = loadSetting("CURRENT_THEME", "javax.swing.plaf.system.SystemLookAndFeel");
            for (int i = 0; i < themeComboBox.getItemCount(); i++) {
                ThemeInfo theme = themeComboBox.getItemAt(i);
                if (theme.className.equals(currentTheme)) {
                    themeComboBox.setSelectedIndex(i);
                    break;
                }
            }

            // تحميل إعدادات الخط
            String fontFamily = loadSetting("FONT_FAMILY", "Tahoma");
            fontFamilyCombo.setSelectedItem(fontFamily);

            int fontSize = Integer.parseInt(loadSetting("FONT_SIZE", "12"));
            fontSizeSpinner.setValue(fontSize);

            // تحميل إعدادات الواجهة
            enableAnimationsCheck.setSelected(Boolean.parseBoolean(loadSetting("ENABLE_ANIMATIONS", "true")));
            enableSoundsCheck.setSelected(Boolean.parseBoolean(loadSetting("ENABLE_SOUNDS", "true")));
            enableTooltipsCheck.setSelected(Boolean.parseBoolean(loadSetting("ENABLE_TOOLTIPS", "true")));
            rtlSupportCheck.setSelected(Boolean.parseBoolean(loadSetting("RTL_SUPPORT", "true")));

            // تحميل الشفافية
            int transparency = Integer.parseInt(loadSetting("TRANSPARENCY_LEVEL", "100"));
            transparencySlider.setValue(transparency);

            // تحميل لون التمييز
            String colorHex = loadSetting("ACCENT_COLOR", "#0078d4");
            try {
                Color accentColor = Color.decode(colorHex);
                accentColorChooser.setColor(accentColor);
            } catch (NumberFormatException e) {
                accentColorChooser.setColor(new Color(0, 120, 215));
            }

            logMessage("✅ تم تحميل الإعدادات بنجاح");

        } catch (Exception e) {
            logMessage("❌ خطأ في تحميل الإعدادات: " + e.getMessage());
        }
    }

    /**
     * تحميل إعداد واحد من قاعدة البيانات
     */
    private String loadSetting(String settingId, String defaultValue) {
        try {
            String sql = "SELECT SETTING_VALUE FROM UI_SETTINGS WHERE SETTING_ID = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, settingId);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getString("SETTING_VALUE");
                    }
                }
            }
        } catch (SQLException e) {
            logMessage("خطأ في تحميل الإعداد " + settingId + ": " + e.getMessage());
        }

        return defaultValue;
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    private void resetToDefaults() {
        int result = JOptionPane.showConfirmDialog(this,
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
            "تأكيد إعادة التعيين",
            JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            try {
                // حذف جميع الإعدادات من قاعدة البيانات
                String sql = "DELETE FROM UI_SETTINGS";
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    int deletedRows = stmt.executeUpdate();
                    logMessage("تم حذف " + deletedRows + " إعداد من قاعدة البيانات");
                }

                // إعادة تحميل الإعدادات الافتراضية
                loadCurrentSettings();

                logMessage("✅ تم إعادة تعيين الإعدادات للقيم الافتراضية");

                JOptionPane.showMessageDialog(this,
                    "تم إعادة تعيين الإعدادات بنجاح!",
                    "إعادة تعيين ناجحة",
                    JOptionPane.INFORMATION_MESSAGE);

            } catch (SQLException e) {
                logMessage("❌ خطأ في إعادة تعيين الإعدادات: " + e.getMessage());
            }
        }
    }

    /**
     * عارض قائمة الثيمات المخصص
     */
    private static class ThemeComboRenderer extends DefaultListCellRenderer {
        @Override
        public Component getListCellRendererComponent(JList<?> list, Object value,
                int index, boolean isSelected, boolean cellHasFocus) {

            super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

            if (value instanceof ThemeInfo) {
                ThemeInfo theme = (ThemeInfo) value;
                setText(theme.name + " (" + theme.category + ")");

                // تلوين حسب الفئة
                if (!isSelected) {
                    switch (theme.category) {
                        case "FlatLaf":
                            setForeground(new Color(0, 100, 200));
                            break;
                        case "Material":
                            setForeground(new Color(76, 175, 80));
                            break;
                        case "JTattoo":
                            setForeground(new Color(156, 39, 176));
                            break;
                        default:
                            setForeground(Color.BLACK);
                    }
                }
            }

            return this;
        }
    }
}
