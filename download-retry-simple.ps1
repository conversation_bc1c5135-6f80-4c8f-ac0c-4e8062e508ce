Write-Host "========================================" -ForegroundColor Green
Write-Host "  COMPREHENSIVE THEME DOWNLOAD RETRY" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Set-Location "e:\ship_erp\java\lib"

function Download-Library {
    param(
        [string]$Name,
        [string]$Url,
        [string]$OutputFile
    )
    
    Write-Host "`n[$Name]" -ForegroundColor White
    
    # Check if file already exists and is valid
    if (Test-Path $OutputFile) {
        $size = (Get-Item $OutputFile).Length
        if ($size -gt 10000) {
            Write-Host "  Already exists: $OutputFile ($size bytes)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  Removing corrupted file ($size bytes)" -ForegroundColor Yellow
            Remove-Item $OutputFile -Force
        }
    }
    
    try {
        Write-Host "  Downloading from: $Url" -ForegroundColor Cyan
        Invoke-WebRequest -Uri $Url -OutFile $OutputFile -TimeoutSec 120
        
        if (Test-Path $OutputFile) {
            $size = (Get-Item $OutputFile).Length
            if ($size -gt 10000) {
                Write-Host "  SUCCESS: $OutputFile ($size bytes)" -ForegroundColor Green
                return $true
            } else {
                Write-Host "  FAILED: File too small ($size bytes)" -ForegroundColor Red
                Remove-Item $OutputFile -Force -ErrorAction SilentlyContinue
            }
        }
    }
    catch {
        Write-Host "  ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    return $false
}

# Library definitions
$libraries = @(
    @{ Name = "FlatLaf Core"; Url = "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar"; File = "flatlaf-3.2.5.jar" },
    @{ Name = "FlatLaf Extras"; Url = "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-extras/3.2.5/flatlaf-extras-3.2.5.jar"; File = "flatlaf-extras-3.2.5.jar" },
    @{ Name = "FlatLaf IntelliJ Themes"; Url = "https://search.maven.org/remotecontent?filepath=com/formdev/flatlaf-intellij-themes/3.2.5/flatlaf-intellij-themes-3.2.5.jar"; File = "flatlaf-intellij-themes-3.2.5.jar" },
    @{ Name = "JTattoo"; Url = "https://search.maven.org/remotecontent?filepath=com/jtattoo/JTattoo/1.6.13/JTattoo-1.6.13.jar"; File = "jtattoo-1.6.13.jar" },
    @{ Name = "DarkLaf Core"; Url = "https://search.maven.org/remotecontent?filepath=com/github/weisj/darklaf-core/3.0.2/darklaf-core-3.0.2.jar"; File = "darklaf-core-3.0.2.jar" },
    @{ Name = "DarkLaf Theme"; Url = "https://search.maven.org/remotecontent?filepath=com/github/weisj/darklaf-theme/3.0.2/darklaf-theme-3.0.2.jar"; File = "darklaf-theme-3.0.2.jar" },
    @{ Name = "Material UI"; Url = "https://search.maven.org/remotecontent?filepath=io/github/vincenzopalazzo/material-ui-swing/1.1.4/material-ui-swing-1.1.4.jar"; File = "material-ui-swing-1.1.4.jar" },
    @{ Name = "Seaglass"; Url = "https://search.maven.org/remotecontent?filepath=com/seaglasslookandfeel/seaglasslookandfeel/0.2.1/seaglasslookandfeel-0.2.1.jar"; File = "seaglass-0.2.1.jar" },
    @{ Name = "JSON"; Url = "https://search.maven.org/remotecontent?filepath=org/json/json/20230227/json-20230227.jar"; File = "json-20230227.jar" },
    @{ Name = "Commons IO"; Url = "https://search.maven.org/remotecontent?filepath=commons-io/commons-io/2.11.0/commons-io-2.11.0.jar"; File = "commons-io-2.11.0.jar" },
    @{ Name = "Commons Lang"; Url = "https://search.maven.org/remotecontent?filepath=org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar"; File = "commons-lang3-3.12.0.jar" }
)

# Alternative URLs for failed downloads
$alternatives = @(
    @{ Name = "Substance (v7.3.04)"; Url = "https://repo1.maven.org/maven2/org/pushingpixels/substance/7.3.04/substance-7.3.04.jar"; File = "substance-7.3.04.jar" },
    @{ Name = "Trident (v1.4.00)"; Url = "https://repo1.maven.org/maven2/org/pushingpixels/trident/1.4.00/trident-1.4.00.jar"; File = "trident-1.4.00.jar" },
    @{ Name = "WebLaF Core (v2.1.0)"; Url = "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-core/2.1.0/weblaf-core-2.1.0.jar"; File = "weblaf-core-2.1.0.jar" },
    @{ Name = "WebLaF UI (v2.1.0)"; Url = "https://repo1.maven.org/maven2/com/weblookandfeel/weblaf-ui/2.1.0/weblaf-ui-2.1.0.jar"; File = "weblaf-ui-2.1.0.jar" },
    @{ Name = "Synthetica (v2.29.0)"; Url = "https://repo1.maven.org/maven2/de/javasoft/synthetica/2.29.0/synthetica-2.29.0.jar"; File = "synthetica-2.29.0.jar" },
    @{ Name = "BeautyEye (v3.6)"; Url = "https://repo1.maven.org/maven2/org/jb2011/beautyeye/3.6/beautyeye-3.6.jar"; File = "beautyeye-3.6.jar" }
)

$successCount = 0
$failCount = 0

Write-Host "Downloading primary libraries..." -ForegroundColor White

foreach ($lib in $libraries) {
    $result = Download-Library -Name $lib.Name -Url $lib.Url -OutputFile $lib.File
    if ($result) {
        $successCount++
    } else {
        $failCount++
    }
}

Write-Host "`nDownloading alternative versions..." -ForegroundColor Yellow

foreach ($alt in $alternatives) {
    $result = Download-Library -Name $alt.Name -Url $alt.Url -OutputFile $alt.File
    if ($result) {
        $successCount++
    } else {
        $failCount++
    }
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "  DOWNLOAD SUMMARY" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

$totalAttempts = $libraries.Count + $alternatives.Count
Write-Host "Total Attempts: $totalAttempts" -ForegroundColor White
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red

Write-Host "`nVerifying all JAR files in lib directory..." -ForegroundColor Yellow

$allJars = Get-ChildItem -Path "." -Filter "*.jar" | Where-Object { $_.Length -gt 10000 }
$validJars = $allJars.Count

Write-Host "Valid JAR files found: $validJars" -ForegroundColor Green

foreach ($jar in $allJars) {
    $sizeKB = [math]::Round($jar.Length / 1024, 1)
    Write-Host "  $($jar.Name) - $sizeKB KB" -ForegroundColor Cyan
}

if ($validJars -ge 10) {
    Write-Host "`nEXCELLENT! Sufficient libraries for comprehensive theme support!" -ForegroundColor Green
} elseif ($validJars -ge 7) {
    Write-Host "`nGOOD! Enough libraries for basic theme functionality!" -ForegroundColor Yellow
} else {
    Write-Host "`nLIMITED! Some themes may not be available!" -ForegroundColor Red
}

Write-Host "`nPress any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
