package com.shipment.erp.view;

import com.shipment.erp.model.Role;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * نموذج إدخال/تعديل بيانات الدور
 * Role Form Dialog
 */
public class RoleFormDialog extends JDialog {
    
    private Font arabicFont;
    private Role role;
    private boolean confirmed = false;
    
    // حقول الإدخال
    private JTextField nameField;
    private JTextField nameEnField;
    private JTextArea descriptionArea;
    private JCheckBox activeCheckBox;
    
    public RoleFormDialog(Dialog parent, String title, Role role) {
        super(parent, title, true);
        
        this.role = role;
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        initializeComponents();
        setupLayout();
        loadData();
        
        setSize(500, 400);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }
    
    private void initializeComponents() {
        // اسم الدور بالعربية
        nameField = new JTextField(30);
        nameField.setFont(arabicFont);
        nameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // اسم الدور بالإنجليزية
        nameEnField = new JTextField(30);
        nameEnField.setFont(arabicFont);
        nameEnField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        
        // الوصف
        descriptionArea = new JTextArea(5, 30);
        descriptionArea.setFont(arabicFont);
        descriptionArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);
        
        // الحالة
        activeCheckBox = new JCheckBox("نشط");
        activeCheckBox.setFont(arabicFont);
        activeCheckBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        activeCheckBox.setSelected(true);
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;
        
        int row = 0;
        
        // اسم الدور بالعربية
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("اسم الدور (عربي):*"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(nameField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;
        
        // اسم الدور بالإنجليزية
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("اسم الدور (إنجليزي):"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(nameEnField, gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;
        
        // الوصف
        gbc.gridx = 1; gbc.gridy = row;
        gbc.anchor = GridBagConstraints.NORTHEAST;
        mainPanel.add(createLabel("الوصف:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.BOTH;
        gbc.anchor = GridBagConstraints.EAST;
        mainPanel.add(new JScrollPane(descriptionArea), gbc);
        gbc.fill = GridBagConstraints.NONE;
        row++;
        
        // الحالة
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("الحالة:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(activeCheckBox, gbc);
        row++;
        
        // أزرار التحكم
        JPanel buttonsPanel = createButtonsPanel();
        
        add(mainPanel, BorderLayout.CENTER);
        add(buttonsPanel, BorderLayout.SOUTH);
    }
    
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }
    
    private JPanel createButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JButton saveButton = new JButton("حفظ");
        saveButton.setFont(arabicFont);
        saveButton.setPreferredSize(new Dimension(100, 30));
        saveButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (validateInput()) {
                    saveData();
                    confirmed = true;
                    dispose();
                }
            }
        });
        
        JButton cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.setPreferredSize(new Dimension(100, 30));
        cancelButton.addActionListener(e -> dispose());
        
        panel.add(saveButton);
        panel.add(cancelButton);
        
        return panel;
    }
    
    private void loadData() {
        if (role != null) {
            nameField.setText(role.getName());
            nameEnField.setText(role.getNameEn());
            descriptionArea.setText(role.getDescription());
            activeCheckBox.setSelected(role.isActive());
        }
    }
    
    private boolean validateInput() {
        if (nameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم الدور", "خطأ", JOptionPane.ERROR_MESSAGE);
            nameField.requestFocus();
            return false;
        }
        
        return true;
    }
    
    private void saveData() {
        if (role == null) {
            role = new Role();
        }
        
        role.setName(nameField.getText().trim());
        role.setNameEn(nameEnField.getText().trim());
        role.setDescription(descriptionArea.getText().trim());
        role.setActive(activeCheckBox.isSelected());
    }
    
    public Role getRole() {
        return role;
    }
    
    public boolean isConfirmed() {
        return confirmed;
    }
}
