import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Date;
import java.util.Properties;

/**
 * أداة إنشاء مستخدم SHIP_ERP في قاعدة البيانات
 * Tool to Create SHIP_ERP User in Oracle Database
 */
public class CreateShipERPUser {
    
    private Connection connection;
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  CREATE SHIP_ERP USER TOOL");
        System.out.println("  أداة إنشاء مستخدم SHIP_ERP");
        System.out.println("========================================");
        
        CreateShipERPUser creator = new CreateShipERPUser();
        creator.createUser();
    }
    
    /**
     * إنشاء مستخدم SHIP_ERP
     */
    public void createUser() {
        try {
            // الاتصال كـ SYSTEM أو DBA
            if (connectAsSystemUser()) {
                System.out.println("✅ Connected as SYSTEM user");
                
                // التحقق من وجود المستخدم
                if (userExists("SHIP_ERP")) {
                    System.out.println("⚠️ User SHIP_ERP already exists");
                    System.out.println("⚠️ المستخدم SHIP_ERP موجود بالفعل");
                    
                    // إعادة تعيين كلمة المرور
                    resetUserPassword("SHIP_ERP", "ship_erp_password");
                } else {
                    // إنشاء المستخدم الجديد
                    createNewUser();
                }
                
                // منح الصلاحيات
                grantPermissions();
                
                // إنشاء الجداول الأساسية
                createBasicTables();
                
                // اختبار الاتصال الجديد
                testNewUserConnection();
                
            } else {
                System.out.println("❌ Failed to connect as SYSTEM user");
                System.out.println("❌ فشل الاتصال كمستخدم SYSTEM");
                
                // محاولة الاتصال كـ ias20251
                tryConnectAsIAS20251();
            }
            
        } catch (Exception e) {
            System.out.println("❌ Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeConnection();
        }
    }
    
    /**
     * الاتصال كمستخدم SYSTEM
     */
    private boolean connectAsSystemUser() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            // محاولة الاتصال كـ SYSTEM
            String[] systemUsers = {"SYSTEM", "SYS"};
            String[] passwords = {"oracle", "sys", "password", "admin", "system"};
            
            for (String user : systemUsers) {
                for (String password : passwords) {
                    try {
                        Properties props = new Properties();
                        props.setProperty("user", user);
                        props.setProperty("password", password);
                        if ("SYS".equals(user)) {
                            props.setProperty("internal_logon", "sysdba");
                        }
                        
                        String url = "*************************************";
                        connection = DriverManager.getConnection(url, props);
                        
                        if (connection != null && !connection.isClosed()) {
                            System.out.println("✅ Connected as: " + user);
                            return true;
                        }
                    } catch (SQLException e) {
                        // تجاهل الخطأ ومحاولة التالي
                    }
                }
            }
            
            return false;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * محاولة الاتصال كـ ias20251
     */
    private void tryConnectAsIAS20251() {
        try {
            Properties props = new Properties();
            props.setProperty("user", "ias20251");
            props.setProperty("password", "ys123");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            String url = "*************************************";
            connection = DriverManager.getConnection(url, props);
            
            if (connection != null && !connection.isClosed()) {
                System.out.println("✅ Connected as ias20251");
                System.out.println("⚠️ Cannot create users with ias20251 account");
                System.out.println("⚠️ لا يمكن إنشاء مستخدمين بحساب ias20251");
                
                // فحص الجداول الموجودة
                checkExistingTables();
            }
            
        } catch (SQLException e) {
            System.out.println("❌ Failed to connect as ias20251: " + e.getMessage());
        }
    }
    
    /**
     * التحقق من وجود المستخدم
     */
    private boolean userExists(String username) {
        try {
            String query = "SELECT COUNT(*) FROM DBA_USERS WHERE USERNAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(query)) {
                stmt.setString(1, username.toUpperCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getInt(1) > 0;
                    }
                }
            }
        } catch (SQLException e) {
            // إذا فشل DBA_USERS، جرب ALL_USERS
            try {
                String query = "SELECT COUNT(*) FROM ALL_USERS WHERE USERNAME = ?";
                try (PreparedStatement stmt = connection.prepareStatement(query)) {
                    stmt.setString(1, username.toUpperCase());
                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            return rs.getInt(1) > 0;
                        }
                    }
                }
            } catch (SQLException e2) {
                System.out.println("⚠️ Cannot check if user exists: " + e2.getMessage());
            }
        }
        return false;
    }
    
    /**
     * إنشاء مستخدم جديد
     */
    private void createNewUser() {
        try {
            System.out.println("🔄 Creating new user SHIP_ERP...");
            System.out.println("🔄 إنشاء مستخدم جديد SHIP_ERP...");
            
            String createUserSQL = "CREATE USER SHIP_ERP IDENTIFIED BY ship_erp_password " +
                                  "DEFAULT TABLESPACE USERS " +
                                  "TEMPORARY TABLESPACE TEMP " +
                                  "QUOTA UNLIMITED ON USERS";
            
            try (Statement stmt = connection.createStatement()) {
                stmt.execute(createUserSQL);
                System.out.println("✅ User SHIP_ERP created successfully");
                System.out.println("✅ تم إنشاء المستخدم SHIP_ERP بنجاح");
            }
            
        } catch (SQLException e) {
            System.out.println("❌ Error creating user: " + e.getMessage());
            System.out.println("❌ خطأ في إنشاء المستخدم: " + e.getMessage());
        }
    }
    
    /**
     * إعادة تعيين كلمة مرور المستخدم
     */
    private void resetUserPassword(String username, String password) {
        try {
            System.out.println("🔄 Resetting password for " + username + "...");
            
            String alterUserSQL = "ALTER USER " + username + " IDENTIFIED BY " + password;
            
            try (Statement stmt = connection.createStatement()) {
                stmt.execute(alterUserSQL);
                System.out.println("✅ Password reset successfully for " + username);
                System.out.println("✅ تم إعادة تعيين كلمة المرور بنجاح لـ " + username);
            }
            
        } catch (SQLException e) {
            System.out.println("❌ Error resetting password: " + e.getMessage());
        }
    }
    
    /**
     * منح الصلاحيات
     */
    private void grantPermissions() {
        try {
            System.out.println("🔄 Granting permissions to SHIP_ERP...");
            System.out.println("🔄 منح الصلاحيات لـ SHIP_ERP...");
            
            String[] permissions = {
                "GRANT CONNECT TO SHIP_ERP",
                "GRANT RESOURCE TO SHIP_ERP",
                "GRANT CREATE SESSION TO SHIP_ERP",
                "GRANT CREATE TABLE TO SHIP_ERP",
                "GRANT CREATE VIEW TO SHIP_ERP",
                "GRANT CREATE SEQUENCE TO SHIP_ERP",
                "GRANT CREATE PROCEDURE TO SHIP_ERP",
                "GRANT CREATE TRIGGER TO SHIP_ERP",
                "GRANT CREATE SYNONYM TO SHIP_ERP"
            };
            
            try (Statement stmt = connection.createStatement()) {
                for (String permission : permissions) {
                    try {
                        stmt.execute(permission);
                        System.out.println("  ✅ " + permission);
                    } catch (SQLException e) {
                        System.out.println("  ⚠️ " + permission + " - " + e.getMessage());
                    }
                }
            }
            
            System.out.println("✅ Permissions granted successfully");
            System.out.println("✅ تم منح الصلاحيات بنجاح");
            
        } catch (Exception e) {
            System.out.println("❌ Error granting permissions: " + e.getMessage());
        }
    }
    
    /**
     * إنشاء الجداول الأساسية
     */
    private void createBasicTables() {
        System.out.println("🔄 Creating basic tables for SHIP_ERP...");
        System.out.println("🔄 إنشاء الجداول الأساسية لـ SHIP_ERP...");
        
        // سيتم إنشاء الجداول عند الاتصال الأول بالمستخدم الجديد
        System.out.println("ℹ️ Tables will be created on first connection");
        System.out.println("ℹ️ سيتم إنشاء الجداول عند الاتصال الأول");
    }
    
    /**
     * اختبار الاتصال بالمستخدم الجديد
     */
    private void testNewUserConnection() {
        System.out.println("🔄 Testing connection with new user...");
        System.out.println("🔄 اختبار الاتصال بالمستخدم الجديد...");
        
        Connection testConnection = null;
        try {
            Properties props = new Properties();
            props.setProperty("user", "SHIP_ERP");
            props.setProperty("password", "ship_erp_password");
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            
            String url = "*************************************";
            testConnection = DriverManager.getConnection(url, props);
            
            if (testConnection != null && !testConnection.isClosed()) {
                System.out.println("✅ SHIP_ERP user connection successful!");
                System.out.println("✅ اتصال المستخدم SHIP_ERP ناجح!");
                
                // اختبار استعلام بسيط
                try (Statement stmt = testConnection.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT SYSDATE FROM DUAL")) {
                    
                    if (rs.next()) {
                        System.out.println("📅 Current date: " + rs.getTimestamp(1));
                    }
                }
            }
            
        } catch (SQLException e) {
            System.out.println("❌ Test connection failed: " + e.getMessage());
            System.out.println("❌ فشل اختبار الاتصال: " + e.getMessage());
        } finally {
            if (testConnection != null) {
                try {
                    testConnection.close();
                } catch (SQLException e) {
                    // تجاهل
                }
            }
        }
    }
    
    /**
     * فحص الجداول الموجودة
     */
    private void checkExistingTables() {
        try {
            System.out.println("🔍 Checking existing tables...");
            System.out.println("🔍 فحص الجداول الموجودة...");
            
            String query = "SELECT TABLE_NAME FROM USER_TABLES ORDER BY TABLE_NAME";
            try (Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery(query)) {
                
                System.out.println("\nExisting tables:");
                System.out.println("الجداول الموجودة:");
                
                while (rs.next()) {
                    System.out.println("  📋 " + rs.getString("TABLE_NAME"));
                }
            }
            
        } catch (SQLException e) {
            System.out.println("❌ Error checking tables: " + e.getMessage());
        }
    }
    
    /**
     * إغلاق الاتصال
     */
    private void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("🔒 Connection closed");
            }
        } catch (SQLException e) {
            System.out.println("Error closing connection: " + e.getMessage());
        }
    }
}
