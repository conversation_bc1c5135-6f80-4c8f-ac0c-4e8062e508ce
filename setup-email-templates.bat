@echo off
echo ========================================
echo   Setup Email Templates Management System
echo   إعداد نظام إدارة قوالب البريد الإلكتروني
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Creating Email Templates Database Tables...
echo إنشاء جداول نظام إدارة القوالب...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CreateEmailTemplatesTables.java
if %errorlevel% neq 0 (
    echo Failed to compile CreateEmailTemplatesTables
    pause
    exit /b 1
)

java -cp "lib\*;." CreateEmailTemplatesTables
if %errorlevel% neq 0 (
    echo Failed to create email templates tables
    pause
    exit /b 1
)

echo.
echo [2] Compiling Email Templates Window...
echo تجميع نافذة إدارة القوالب...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\EmailTemplatesWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile EmailTemplatesWindow
    pause
    exit /b 1
)

echo.
echo [3] Updating TreeMenuPanel...
echo تحديث لوحة القائمة الرئيسية...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo Failed to compile TreeMenuPanel
    pause
    exit /b 1
)

echo.
echo [4] Testing Email Templates Window...
echo اختبار نافذة إدارة القوالب...
start /min java -cp "lib\*;." EmailTemplatesWindow

echo.
echo [5] Email Templates System Setup Complete!
echo تم إعداد نظام إدارة القوالب بنجاح!
echo.
echo Features included:
echo - Template categories management
echo - Template creation and editing
echo - Online templates browser
echo - Template variables system
echo - Template usage statistics
echo - Template sharing and permissions
echo - Template history and versioning
echo.
echo الميزات المتضمنة:
echo - إدارة فئات القوالب
echo - إنشاء وتحرير القوالب
echo - تصفح القوالب من الإنترنت
echo - نظام متغيرات القوالب
echo - إحصائيات استخدام القوالب
echo - مشاركة القوالب والصلاحيات
echo - تاريخ وإصدارات القوالب
echo.

echo [6] Starting Main System...
echo تشغيل النظام الرئيسي...
start .\start-system.bat

echo.
echo Setup completed successfully!
echo تم الإعداد بنجاح!
pause
