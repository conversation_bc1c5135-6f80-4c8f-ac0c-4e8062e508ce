package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.text.SimpleDateFormat;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSeparator;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableColumnModel;
import com.shipment.erp.model.Role;
// import com.shipment.erp.service.RoleService; // TODO: Implement RoleService
import com.shipment.erp.service.RoleService;

/**
 * نافذة إدارة الأدوار Role Management Window
 */
public class RoleManagementWindow extends JDialog {

    private Font arabicFont;
    private JTable rolesTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> statusFilterCombo;

    private RoleService roleService;
    private List<Role> rolesList;

    public RoleManagementWindow(JFrame parent) {
        super(parent, "إدارة الأدوار", true);

        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeServices();
        initializeComponents();
        setupLayout();
        loadRolesData();

        setSize(1000, 700);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(true);
        setMinimumSize(new Dimension(800, 500));
    }

    private void initializeServices() {
        // TODO: Initialize RoleService through dependency injection
        // roleService = ApplicationContext.getBean(RoleService.class);
    }

    private void initializeComponents() {
        // شريط الأدوات العلوي
        JPanel toolbarPanel = createToolbarPanel();

        // جدول الأدوار
        createRolesTable();
        JScrollPane tableScrollPane = new JScrollPane(rolesTable);
        tableScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();

        // تخطيط النافذة
        setLayout(new BorderLayout());
        add(toolbarPanel, BorderLayout.NORTH);
        add(tableScrollPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }

    private JPanel createToolbarPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // أزرار العمليات
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addButton = new JButton("إضافة دور جديد");
        addButton.setFont(arabicFont);
        addButton.addActionListener(e -> addRole());

        JButton editButton = new JButton("تعديل");
        editButton.setFont(arabicFont);
        editButton.addActionListener(e -> editRole());

        JButton deleteButton = new JButton("حذف");
        deleteButton.setFont(arabicFont);
        deleteButton.addActionListener(e -> deleteRole());

        JButton permissionsButton = new JButton("إدارة الصلاحيات");
        permissionsButton.setFont(arabicFont);
        permissionsButton.addActionListener(e -> managePermissions());

        JButton refreshButton = new JButton("تحديث");
        refreshButton.setFont(arabicFont);
        refreshButton.addActionListener(e -> loadRolesData());

        buttonsPanel.add(addButton);
        buttonsPanel.add(editButton);
        buttonsPanel.add(deleteButton);
        buttonsPanel.add(new JSeparator(SwingConstants.VERTICAL));
        buttonsPanel.add(permissionsButton);
        buttonsPanel.add(new JSeparator(SwingConstants.VERTICAL));
        buttonsPanel.add(refreshButton);

        // شريط البحث والفلترة
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);

        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                filterRoles();
            }
        });

        JLabel statusLabel = new JLabel("الحالة:");
        statusLabel.setFont(arabicFont);

        statusFilterCombo = new JComboBox<>(new String[] {"الكل", "نشط", "غير نشط"});
        statusFilterCombo.setFont(arabicFont);
        statusFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusFilterCombo.addActionListener(e -> filterRoles());

        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        searchPanel.add(Box.createHorizontalStrut(10));
        searchPanel.add(statusLabel);
        searchPanel.add(statusFilterCombo);

        panel.add(buttonsPanel, BorderLayout.EAST);
        panel.add(searchPanel, BorderLayout.WEST);

        return panel;
    }

    private void createRolesTable() {
        String[] columnNames = {"الرقم", "اسم الدور", "اسم الدور (إنجليزي)", "الوصف",
                "عدد المستخدمين", "الحالة", "تاريخ الإنشاء"};

        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        rolesTable = new JTable(tableModel);
        rolesTable.setFont(arabicFont);
        rolesTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        rolesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        rolesTable.setRowHeight(25);

        // تخصيص عرض الأعمدة
        TableColumnModel columnModel = rolesTable.getColumnModel();
        columnModel.getColumn(0).setPreferredWidth(60); // الرقم
        columnModel.getColumn(1).setPreferredWidth(150); // اسم الدور
        columnModel.getColumn(2).setPreferredWidth(150); // اسم الدور (إنجليزي)
        columnModel.getColumn(3).setPreferredWidth(250); // الوصف
        columnModel.getColumn(4).setPreferredWidth(100); // عدد المستخدمين
        columnModel.getColumn(5).setPreferredWidth(80); // الحالة
        columnModel.getColumn(6).setPreferredWidth(120); // تاريخ الإنشاء

        // تخصيص رأس الجدول
        JTableHeader header = rolesTable.getTableHeader();
        header.setFont(arabicFont);
        header.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إضافة مستمع النقر المزدوج
        rolesTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    editRole();
                }
            }
        });
    }

    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setBorder(BorderFactory.createEtchedBorder());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);

        panel.add(statusLabel);
        return panel;
    }

    private void loadRolesData() {
        // TODO: Load data from RoleService
        // rolesList = roleService.findAll();

        // بيانات تجريبية
        rolesList = createSampleData();
        updateTableData();
    }

    private List<Role> createSampleData() {
        Role role1 = new Role();
        role1.setId(1L);
        role1.setName("مدير عام");
        role1.setNameEn("System Administrator");
        role1.setDescription("صلاحيات كاملة للنظام");
        role1.setActive(true);
        role1.setCreatedDate(java.time.LocalDateTime.now());

        Role role2 = new Role();
        role2.setId(2L);
        role2.setName("مدير فرع");
        role2.setNameEn("Branch Manager");
        role2.setDescription("إدارة الفرع والعمليات اليومية");
        role2.setActive(true);
        role2.setCreatedDate(java.time.LocalDateTime.now());

        Role role3 = new Role();
        role3.setId(3L);
        role3.setName("موظف");
        role3.setNameEn("Employee");
        role3.setDescription("صلاحيات محدودة للعمليات الأساسية");
        role3.setActive(true);
        role3.setCreatedDate(java.time.LocalDateTime.now());

        return java.util.Arrays.asList(role1, role2, role3);
    }

    private void updateTableData() {
        tableModel.setRowCount(0);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (Role role : rolesList) {
            Object[] row = {role.getId(), role.getName(), role.getNameEn(), role.getDescription(),
                    "0", // TODO: Get actual users count
                    role.isActive() ? "نشط" : "غير نشط",
                    role.getCreatedDate() != null ? dateFormat.format(role.getCreatedDate()) : ""};
            tableModel.addRow(row);
        }
    }

    private void filterRoles() {
        // TODO: Implement filtering logic
        updateTableData();
    }

    private void addRole() {
        RoleFormDialog dialog = new RoleFormDialog(this, "إضافة دور جديد", null);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            Role newRole = dialog.getRole();
            // TODO: Save using RoleService
            // roleService.save(newRole);
            loadRolesData();
        }
    }

    private void editRole() {
        int selectedRow = rolesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار دور للتعديل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Role selectedRole = rolesList.get(selectedRow);
        RoleFormDialog dialog = new RoleFormDialog(this, "تعديل الدور", selectedRole);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            Role updatedRole = dialog.getRole();
            // TODO: Update using RoleService
            // roleService.save(updatedRole);
            loadRolesData();
        }
    }

    private void deleteRole() {
        int selectedRow = rolesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار دور للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Role selectedRole = rolesList.get(selectedRow);
        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف الدور: " + selectedRole.getName() + "؟\n"
                        + "تحذير: سيتم حذف جميع الصلاحيات المرتبطة بهذا الدور",
                "تأكيد الحذف", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            // TODO: Delete using RoleService
            // roleService.delete(selectedRole.getId());
            loadRolesData();
        }
    }

    private void managePermissions() {
        int selectedRow = rolesTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار دور لإدارة صلاحياته", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        Role selectedRole = rolesList.get(selectedRow);
        // TODO: Open PermissionManagementDialog
        JOptionPane.showMessageDialog(this,
                "نافذة إدارة الصلاحيات للدور: " + selectedRole.getName() + "\nقيد التطوير",
                "إدارة الصلاحيات", JOptionPane.INFORMATION_MESSAGE);
    }
}
