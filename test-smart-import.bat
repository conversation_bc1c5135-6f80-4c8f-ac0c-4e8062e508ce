@echo off
echo ========================================
echo 🧠 اختبار الاستيراد الذكي من IAS20251
echo Testing Smart Import from IAS20251
echo ========================================

cd /d "d:\java\java"

echo.
echo [1/3] التحقق من الجداول...
echo Checking Tables...
echo ========================================

echo التحقق من جدول ERP_SUPPLIERS...
sqlplus -s ship_erp/ship_erp_password@localhost:1521/ORCL << EOF
SELECT 'ERP_SUPPLIERS: ' || COUNT(*) || ' records' FROM ERP_SUPPLIERS;
EXIT;
EOF

echo.
echo [2/3] تجميع النافذة المحدثة...
echo Compiling Updated Window...
echo ========================================

echo تجميع SuppliersManagementWindow مع الاستيراد الذكي...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\SuppliersManagementWindow.java
if %errorlevel% equ 0 (
    echo ✅ تم تجميع النافذة بنجاح
) else (
    echo ❌ فشل في تجميع النافذة
    pause
    exit /b 1
)

echo.
echo [3/3] تشغيل النافذة...
echo Running Window...
echo ========================================

echo تشغيل النافذة مع الاستيراد الذكي...
start "Smart Import - Suppliers Management" java -cp "lib\*;." SuppliersManagementWindow

echo.
echo ========================================
echo 🧠 الاستيراد الذكي جاهز!
echo Smart Import Ready!
echo ========================================

echo.
echo 🎯 الميزات الجديدة:
echo ==================

echo.
echo 🔍 فحص ذكي للأعمدة:
echo -------------------
echo ✅ فحص الأعمدة الموجودة في V_DETAILS
echo ✅ فحص الأعمدة الموجودة في ERP_SUPPLIERS
echo ✅ تحديد الأعمدة المشتركة تلقائياً
echo ✅ إنشاء استعلام ديناميكي

echo.
echo 🔄 نسخ ذكي للبيانات:
echo --------------------
echo ✅ نسخ الأعمدة المشتركة فقط
echo ✅ تجاهل الأعمدة غير الموجودة
echo ✅ معالجة أنواع البيانات المختلفة
echo ✅ تقرير مفصل عن العملية

echo.
echo 🛡️ معالجة الأخطاء:
echo ------------------
echo ✅ حل مشكلة ORA-00904 (معرف غير صالح)
echo ✅ التعامل مع الأعمدة المفقودة
echo ✅ استمرار العملية عند حدوث أخطاء
echo ✅ تقرير شامل عن الأخطاء

echo.
echo 📋 خطوات الاستخدام:
echo ===================

echo.
echo 1️⃣ فتح النافذة:
echo • تم تشغيل النافذة تلقائياً
echo • ابحث عن زر "استيراد من IAS20251"

echo.
echo 2️⃣ إدخال بيانات الاتصال:
echo • انقر على زر الاستيراد
echo • أدخل بيانات الاتصال الصحيحة:
echo   - المستخدم: IAS20251 (أو الصحيح)
echo   - كلمة المرور: IAS20251 (أو الصحيحة)
echo   - الخادم: localhost
echo   - المنفذ: 1521
echo   - SID: ORCL

echo.
echo 3️⃣ مراقبة العملية:
echo • راقب فحص الأعمدة
echo • تحقق من الأعمدة المشتركة
echo • راقب تقدم النسخ
echo • راجع التقرير النهائي

echo.
echo 🔧 حل المشاكل:
echo ===============

echo.
echo ❌ إذا ظهر خطأ اتصال:
echo • تحقق من تشغيل Oracle
echo • تأكد من بيانات الاتصال
echo • جرب مستخدمين مختلفين

echo.
echo ❌ إذا لم توجد أعمدة مشتركة:
echo • تحقق من وجود جدول V_DETAILS
echo • تأكد من بنية الجدول
echo • راجع أسماء الأعمدة

echo.
echo ❌ إذا فشل في النسخ:
echo • تحقق من مساحة قاعدة البيانات
echo • تأكد من صلاحيات الكتابة
echo • راجع رسائل الخطأ المفصلة

echo.
echo 📊 التقارير المتوقعة:
echo =====================

echo.
echo 🔍 تقرير فحص الأعمدة:
echo • عدد الأعمدة في V_DETAILS
echo • عدد الأعمدة في ERP_SUPPLIERS
echo • عدد الأعمدة المشتركة
echo • قائمة الأعمدة المشتركة

echo.
echo 📈 تقرير الاستيراد:
echo • إجمالي السجلات المقروءة
echo • عدد السجلات المنسوخة بنجاح
echo • عدد السجلات التي فشلت
echo • تفاصيل الأخطاء إن وجدت

echo.
echo 🎉 النتائج المتوقعة:
echo ====================
echo ✅ اتصال ناجح بقاعدة IAS20251
echo ✅ فحص ناجح لبنية الجداول
echo ✅ تحديد الأعمدة المشتركة
echo ✅ نسخ ناجح للبيانات المتوافقة
echo ✅ تقرير شامل عن العملية
echo ✅ تحديث تلقائي للجدول

echo.
echo 💡 نصائح مهمة:
echo ===============
echo • لا تقلق من رسائل الأعمدة المفقودة
echo • النظام سينسخ الأعمدة المتوافقة فقط
echo • راقب التقرير النهائي للتأكد من النجاح
echo • يمكن إعادة تشغيل العملية عند الحاجة
echo • البيانات المكررة ستظهر كأخطاء

echo.
echo 🚀 ابدأ الآن:
echo ============
echo 1. افتح النافذة (تم تشغيلها)
echo 2. انقر "استيراد من IAS20251"
echo 3. أدخل بيانات الاتصال
echo 4. راقب العملية
echo 5. راجع النتائج

echo.
pause
