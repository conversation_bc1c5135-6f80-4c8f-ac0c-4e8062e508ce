@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 📦 تحميل المكتبات الإضافية لأدوات الإصلاح
echo    Additional Libraries Download
echo ================================================
echo.

cd /d "d:\java\java\lib"

echo 🔍 فحص المكتبات الموجودة...
echo.

echo [1] مكتبات الأيقونات والواجهة:
echo ================================

echo 📥 تحميل FontAwesome Icons...
if not exist "fontawesome-6.4.0.jar" (
    echo • تحميل fontawesome-6.4.0.jar...
    curl -L -o fontawesome-6.4.0.jar "https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-fontawesome-pack/12.3.1/ikonli-fontawesome-pack-12.3.1.jar"
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم تحميل FontAwesome بنجاح
    ) else (
        echo ❌ فشل في تحميل FontAwesome
    )
) else (
    echo ✅ FontAwesome موجود مسبقاً
)

echo.
echo 📥 تحميل Material Design Icons...
if not exist "material-design-icons-6.4.0.jar" (
    echo • تحميل material-design-icons-6.4.0.jar...
    curl -L -o material-design-icons-6.4.0.jar "https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-material-pack/12.3.1/ikonli-material-pack-12.3.1.jar"
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم تحميل Material Design Icons بنجاح
    ) else (
        echo ❌ فشل في تحميل Material Design Icons
    )
) else (
    echo ✅ Material Design Icons موجود مسبقاً
)

echo.
echo 📥 تحميل Ikonli Core...
if not exist "ikonli-core-12.3.1.jar" (
    echo • تحميل ikonli-core-12.3.1.jar...
    curl -L -o ikonli-core-12.3.1.jar "https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-core/12.3.1/ikonli-core-12.3.1.jar"
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم تحميل Ikonli Core بنجاح
    ) else (
        echo ❌ فشل في تحميل Ikonli Core
    )
) else (
    echo ✅ Ikonli Core موجود مسبقاً
)

echo.
echo 📥 تحميل Ikonli Swing...
if not exist "ikonli-swing-12.3.1.jar" (
    echo • تحميل ikonli-swing-12.3.1.jar...
    curl -L -o ikonli-swing-12.3.1.jar "https://repo1.maven.org/maven2/org/kordamp/ikonli/ikonli-swing/12.3.1/ikonli-swing-12.3.1.jar"
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم تحميل Ikonli Swing بنجاح
    ) else (
        echo ❌ فشل في تحميل Ikonli Swing
    )
) else (
    echo ✅ Ikonli Swing موجود مسبقاً
)

echo.
echo [2] مكتبات التقارير والتصدير:
echo ==============================

echo 📥 تحميل Apache Commons CSV...
if not exist "commons-csv-1.10.0.jar" (
    echo • تحميل commons-csv-1.10.0.jar...
    curl -L -o commons-csv-1.10.0.jar "https://repo1.maven.org/maven2/org/apache/commons/commons-csv/1.10.0/commons-csv-1.10.0.jar"
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم تحميل Commons CSV بنجاح
    ) else (
        echo ❌ فشل في تحميل Commons CSV
    )
) else (
    echo ✅ Commons CSV موجود مسبقاً
)

echo.
echo 📥 تحميل OpenCSV...
if not exist "opencsv-5.8.jar" (
    echo • تحميل opencsv-5.8.jar...
    curl -L -o opencsv-5.8.jar "https://repo1.maven.org/maven2/com/opencsv/opencsv/5.8/opencsv-5.8.jar"
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم تحميل OpenCSV بنجاح
    ) else (
        echo ❌ فشل في تحميل OpenCSV
    )
) else (
    echo ✅ OpenCSV موجود مسبقاً
)

echo.
echo [3] مكتبات التحقق والتحليل:
echo ===========================

echo 📥 تحميل Apache Commons Validator...
if not exist "commons-validator-1.7.jar" (
    echo • تحميل commons-validator-1.7.jar...
    curl -L -o commons-validator-1.7.jar "https://repo1.maven.org/maven2/commons-validator/commons-validator/1.7/commons-validator-1.7.jar"
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم تحميل Commons Validator بنجاح
    ) else (
        echo ❌ فشل في تحميل Commons Validator
    )
) else (
    echo ✅ Commons Validator موجود مسبقاً
)

echo.
echo [4] مكتبات النسخ الاحتياطي:
echo ==========================

echo 📥 تحميل H2 Database (للنسخ الاحتياطي)...
if not exist "h2-2.2.224.jar" (
    echo • تحميل h2-2.2.224.jar...
    curl -L -o h2-2.2.224.jar "https://repo1.maven.org/maven2/com/h2database/h2/2.2.224/h2-2.2.224.jar"
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم تحميل H2 Database بنجاح
    ) else (
        echo ❌ فشل في تحميل H2 Database
    )
) else (
    echo ✅ H2 Database موجود مسبقاً
)

echo.
echo ================================================
echo ✅ تم إكمال تحميل المكتبات الإضافية
echo ================================================
echo.

echo 📋 ملخص المكتبات المضافة:
echo • FontAwesome Icons - للأيقونات المتقدمة
echo • Material Design Icons - لأيقونات Material
echo • Ikonli Core & Swing - لإدارة الأيقونات
echo • Commons CSV & OpenCSV - لتصدير التقارير
echo • Commons Validator - للتحقق من البيانات
echo • H2 Database - للنسخ الاحتياطي
echo.

echo 💡 الخطوة التالية:
echo • تشغيل أدوات الإصلاح المتقدمة
echo • استخدام الأيقونات الجديدة
echo • إنشاء تقارير مفصلة
echo.

pause
