import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;
import javax.swing.BorderFactory;
import javax.swing.Icon;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JTree;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.border.EmptyBorder;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeCellRenderer;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;

/**
 * لوحة القائمة الشجرية المتقدمة Advanced Tree Menu Panel
 */
public class TreeMenuPanel extends JPanel {

    private JTree menuTree;
    private DefaultMutableTreeNode rootNode;
    private DefaultTreeModel treeModel;
    private Font arabicFont;
    private JFrame parentFrame;
    private Map<String, Runnable> menuActions;
    private SystemTreeManager systemTreeManager;

    public TreeMenuPanel(JFrame parentFrame) {
        this.parentFrame = parentFrame;

        // إعداد خصائص النظام للنص العربي
        setupArabicSystemProperties();

        // تهيئة الخط العربي
        this.arabicFont = createArabicFont();
        this.menuActions = new HashMap<>();

        // تهيئة مدير شجرة النظام
        initializeSystemTreeManager();

        initializeMenuActions();
        createTreeStructureFromDatabase();
        setupTreeComponents();
        setupLayout();
        setupEventHandlers();
    }

    /**
     * تهيئة مدير شجرة النظام
     */
    private void initializeSystemTreeManager() {
        try {
            systemTreeManager = SystemTreeManager.getInstance();
            System.out.println("✅ تم تهيئة مدير شجرة النظام في TreeMenuPanel");
        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة مدير شجرة النظام: " + e.getMessage());
            systemTreeManager = null;
        }
    }

    /**
     * إنشاء هيكل الشجرة من قاعدة البيانات
     */
    private void createTreeStructureFromDatabase() {
        try {
            if (systemTreeManager != null) {
                treeModel = systemTreeManager.getSystemTreeModel();
                rootNode = (DefaultMutableTreeNode) treeModel.getRoot();
                System.out.println("✅ تم تحميل شجرة النظام من قاعدة البيانات");
            } else {
                // استخدام الشجرة الافتراضية في حالة عدم توفر قاعدة البيانات
                createTreeStructure();
                System.out.println("⚠️ تم استخدام الشجرة الافتراضية");
            }
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحميل شجرة النظام من قاعدة البيانات: " + e.getMessage());
            // استخدام الشجرة الافتراضية كبديل
            createTreeStructure();
        }
    }

    /**
     * إعداد خصائص النظام للنص العربي
     */
    private void setupArabicSystemProperties() {
        try {
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            System.setProperty("user.language", "ar");
            System.setProperty("user.country", "SA");
            System.setProperty("awt.useSystemAAFontSettings", "lcd");
            System.setProperty("swing.aatext", "true");
            System.setProperty("swing.useSystemAAFontSettings", "lcd");
        } catch (Exception e) {
            System.err.println("خطأ في إعداد خصائص النظام: " + e.getMessage());
        }
    }

    /**
     * إنشاء خط عربي محسن
     */
    private Font createArabicFont() {
        String[] arabicFonts = {"Tahoma", "Arial Unicode MS", "Segoe UI", "Arial", "SansSerif"};

        for (String fontName : arabicFonts) {
            try {
                Font font = new Font(fontName, Font.PLAIN, 12);
                if (font.canDisplayUpTo("النص العربي") == -1) {
                    System.out.println("تم اختيار الخط: " + fontName);
                    return font;
                }
            } catch (Exception e) {
                continue;
            }
        }

        return new Font("Dialog", Font.PLAIN, 12);
    }

    /**
     * تهيئة إجراءات القائمة
     */
    private void initializeMenuActions() {
        // إجراءات إدارة الشحنات
        menuActions.put("قائمة الشحنات", () -> showMessage("فتح قائمة الشحنات"));
        menuActions.put("شحنة جديدة", () -> showMessage("إنشاء شحنة جديدة"));
        menuActions.put("تتبع الشحنات", () -> showMessage("فتح نظام تتبع الشحنات"));
        menuActions.put("تقارير الشحن", () -> showMessage("عرض تقارير الشحن"));

        // إجراءات إدارة العملاء
        menuActions.put("قائمة العملاء", () -> showMessage("فتح قائمة العملاء"));
        menuActions.put("عميل جديد", () -> showMessage("إضافة عميل جديد"));
        menuActions.put("تقارير العملاء", () -> showMessage("عرض تقارير العملاء"));

        // إجراءات المحاسبة
        menuActions.put("الفواتير", () -> showMessage("فتح نظام الفواتير"));
        menuActions.put("المدفوعات", () -> showMessage("إدارة المدفوعات"));
        menuActions.put("التقارير المالية", () -> showMessage("عرض التقارير المالية"));
        menuActions.put("الحسابات", () -> showMessage("إدارة الحسابات"));

        // إجراءات المخزون
        menuActions.put("قائمة المخزون", () -> showMessage("فتح قائمة المخزون"));
        menuActions.put("إضافة عنصر", () -> showMessage("إضافة عنصر جديد للمخزون"));
        menuActions.put("جرد المخزون", () -> showMessage("تنفيذ جرد المخزون"));
        menuActions.put("تقارير المخزون", () -> showMessage("عرض تقارير المخزون"));

        // إجراءات ربط الأنظمة
        menuActions.put("ربط النظام واستيراد البيانات", () -> openSystemIntegrationWindow());

        // إجراءات الإعدادات العامة
        menuActions.put("إدارة الربط والاستيراد", () -> openSystemIntegrationManagementWindow());

        // إجراءات التقارير
        menuActions.put("تقارير المبيعات", () -> showMessage("عرض تقارير المبيعات"));
        menuActions.put("تقارير الأرباح", () -> showMessage("عرض تقارير الأرباح"));
        menuActions.put("تقارير الأداء", () -> showMessage("عرض تقارير الأداء"));
        menuActions.put("تقارير مخصصة", () -> showMessage("إنشاء تقارير مخصصة"));

        // إجراءات الإعدادات العامة
        menuActions.put("الإعدادات العامة", () -> {
            showMessage("فئة الإعدادات العامة - اختر إعداد محدد من القائمة الفرعية");
        });

        menuActions.put("المتغيرات العامة", () -> {
            try {
                GlobalVariablesWindow variablesWindow = new GlobalVariablesWindow();
                variablesWindow.setVisible(true);
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة المتغيرات العامة: " + e.getMessage());
                showMessage("خطأ في فتح نافذة المتغيرات العامة: " + e.getMessage());
            }
        });

        menuActions.put("إدارة العملات", () -> {
            try {
                System.out.println("🔄 فتح نافذة إدارة العملات الشاملة من menuActions...");
                CurrencyManagementWindow currencyWindow = new CurrencyManagementWindow();
                currencyWindow.setVisible(true);
                System.out.println("✅ تم فتح نافذة إدارة العملات الشاملة بنجاح");
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة إدارة العملات: " + e.getMessage());
                e.printStackTrace();
                showMessage("خطأ في فتح نافذة إدارة العملات: " + e.getMessage());
            }
        });

        menuActions.put("إعدادات النظام العامة", () -> {
            showMessage("نافذة إعدادات النظام العامة قيد التطوير");
        });

        menuActions.put("إعدادات الأمان", () -> {
            showMessage("نافذة إعدادات الأمان قيد التطوير");
        });

        // إضافة نافذة إعدادات الواجهة والمظهر المتطورة
        menuActions.put("إعدادات الواجهة والمظهر", () -> {
            System.out.println("🎨 فتح نافذة إعدادات الواجهة والمظهر المتطورة من القائمة الرئيسية...");
            try {
                // AdvancedUISettingsWindow uiSettingsWindow = new AdvancedUISettingsWindow(parentFrame); // معطل مؤقتاً
                // uiSettingsWindow.setVisible(true); // معطل مؤقتاً
                showMessage("نافذة إعدادات الواجهة قيد التطوير");
                System.out.println("✅ تم فتح نافذة إعدادات الواجهة والمظهر المتطورة بنجاح");
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة إعدادات الواجهة والمظهر: " + e.getMessage());
                e.printStackTrace();

                // عرض رسالة خطأ للمستخدم
                JOptionPane.showMessageDialog(null,
                        "خطأ في فتح نافذة إعدادات الواجهة والمظهر:\n" + e.getMessage()
                                + "\n\nيرجى التأكد من تجميع النظام بشكل صحيح.",
                        "خطأ في إعدادات الواجهة - UI Settings Error",
                        JOptionPane.ERROR_MESSAGE);
            }
        });
        menuActions.put("إدارة المستخدمين", () -> {
            // UserManagementWindow usersWindow = new UserManagementWindow(parentFrame); // معطل مؤقتاً
            // usersWindow.setVisible(true); // معطل مؤقتاً
            showMessage("نافذة إدارة المستخدمين قيد التطوير");
        });
        menuActions.put("المتغيرات العامة", () -> {
            // GlobalVariablesWindow variablesWindow = new GlobalVariablesWindow(); // معطل مؤقتاً
            // variablesWindow.setVisible(true); // معطل مؤقتاً
            showMessage("نافذة المتغيرات العامة قيد التطوير");
        });
        menuActions.put("إعدادات الواجهة والمظهر", () -> {
            System.out.println("🎨 فتح نظام المظاهر العملي من القائمة الرئيسية...");
            try {
                WorkingThemeWindow themeWindow = new WorkingThemeWindow();
                themeWindow.setVisible(true);
                System.out.println("✅ تم فتح نظام المظاهر العملي بنجاح");
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نظام المظاهر العملي: " + e.getMessage());
                e.printStackTrace();

                // عرض رسالة خطأ للمستخدم
                JOptionPane.showMessageDialog(null,
                        "خطأ في فتح نظام المظاهر:\n" + e.getMessage()
                                + "\n\nيرجى التأكد من تجميع النظام بشكل صحيح.",
                        "خطأ في نظام المظاهر - Theme System Error", JOptionPane.ERROR_MESSAGE);
            }
        });

        // إضافة نافذة إعدادات الشركات الشاملة
        menuActions.put("إعدادات الشركات الشاملة", () -> {
            System.out.println("🏢 فتح نافذة إعدادات الشركات الشاملة من القائمة الرئيسية...");
            try {
                AdvancedCompanySettingsWindow companyWindow = new AdvancedCompanySettingsWindow();
                companyWindow.setVisible(true);
                System.out.println("✅ تم فتح نافذة إعدادات الشركات الشاملة بنجاح");
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة إعدادات الشركات الشاملة: " + e.getMessage());
                e.printStackTrace();

                // عرض رسالة خطأ للمستخدم
                JOptionPane.showMessageDialog(null,
                        "خطأ في فتح نافذة إعدادات الشركات:\n" + e.getMessage()
                                + "\n\nيرجى التأكد من تجميع النظام بشكل صحيح.",
                        "خطأ في إعدادات الشركات - Company Settings Error",
                        JOptionPane.ERROR_MESSAGE);
            }
        });

        // إجراءات إدارة الشركات والفروع
        menuActions.put("إدارة الشركات", () -> openCompanyManagementWindow());
        menuActions.put("إدارة الفروع", () -> openBranchManagementWindow());
        menuActions.put("إدارة الأدوار", () -> openRoleManagementWindow());
        menuActions.put("إدارة الصلاحيات", () -> openPermissionManagementWindow());
        // تم نقل "إدارة العملات" إلى قسم الإعدادات العامة
        menuActions.put("إدارة السنوات المالية", () -> openFiscalYearManagementWindow());
        menuActions.put("سجل التدقيق", () -> openAuditLogWindow());

        // إجراءات إدارة الأصناف
        menuActions.put("بيانات الأصناف الحقيقية", () -> openRealItemDataWindow());
        menuActions.put("بيانات الأصناف الشاملة", () -> openComprehensiveItemDataWindow());
        menuActions.put("مجموعات الأصناف", () -> openItemGroupsManagementWindow());
        menuActions.put("وحدات القياس", () -> openMeasurementUnitsWindow());

        menuActions.put("إعدادات النظام", () -> showMessage("فتح إعدادات النظام"));
        menuActions.put("النسخ الاحتياطية", () -> showMessage("إدارة النسخ الاحتياطية"));

        // إجراءات المساعدة
        menuActions.put("دليل المستخدم", () -> showMessage("فتح دليل المستخدم"));
        menuActions.put("الدعم الفني", () -> showMessage("الاتصال بالدعم الفني"));
        menuActions.put("حول البرنامج", () -> showAboutDialog());
    }

    /**
     * إنشاء هيكل الشجرة
     */
    private void createTreeStructure() {
        rootNode = new DefaultMutableTreeNode("نظام إدارة الشحنات");

        // إدارة الشحنات
        DefaultMutableTreeNode shippingNode = new DefaultMutableTreeNode("إدارة الشحنات");
        shippingNode.add(new DefaultMutableTreeNode("قائمة الشحنات"));
        shippingNode.add(new DefaultMutableTreeNode("شحنة جديدة"));
        shippingNode.add(new DefaultMutableTreeNode("تتبع الشحنات"));
        shippingNode.add(new DefaultMutableTreeNode("تقارير الشحن"));
        rootNode.add(shippingNode);

        // إدارة العملاء
        DefaultMutableTreeNode customersNode = new DefaultMutableTreeNode("إدارة العملاء");
        customersNode.add(new DefaultMutableTreeNode("قائمة العملاء"));
        customersNode.add(new DefaultMutableTreeNode("عميل جديد"));
        customersNode.add(new DefaultMutableTreeNode("تقارير العملاء"));
        rootNode.add(customersNode);

        // إدارة الموردين
        DefaultMutableTreeNode suppliersNode = new DefaultMutableTreeNode("إدارة الموردين");
        suppliersNode.add(new DefaultMutableTreeNode("إدارة الموردين الشاملة"));
        suppliersNode.add(new DefaultMutableTreeNode("طلبات الشراء"));
        suppliersNode.add(new DefaultMutableTreeNode("أوامر الشراء"));
        suppliersNode.add(new DefaultMutableTreeNode("تقارير الموردين"));
        rootNode.add(suppliersNode);

        // المحاسبة والمالية
        DefaultMutableTreeNode accountingNode = new DefaultMutableTreeNode("المحاسبة والمالية");
        accountingNode.add(new DefaultMutableTreeNode("الفواتير"));
        accountingNode.add(new DefaultMutableTreeNode("المدفوعات"));
        accountingNode.add(new DefaultMutableTreeNode("التقارير المالية"));
        accountingNode.add(new DefaultMutableTreeNode("الحسابات"));
        rootNode.add(accountingNode);

        // إدارة المخزون والأصناف
        DefaultMutableTreeNode inventoryNode = new DefaultMutableTreeNode("إدارة المخزون والأصناف");
        inventoryNode.add(new DefaultMutableTreeNode("بيانات الأصناف الحقيقية"));
        inventoryNode.add(new DefaultMutableTreeNode("بيانات الأصناف الشاملة"));
        inventoryNode.add(new DefaultMutableTreeNode("مجموعات الأصناف"));
        inventoryNode.add(new DefaultMutableTreeNode("وحدات القياس"));
        inventoryNode.add(new DefaultMutableTreeNode("قائمة المخزون"));
        inventoryNode.add(new DefaultMutableTreeNode("إضافة عنصر"));
        inventoryNode.add(new DefaultMutableTreeNode("جرد المخزون"));
        inventoryNode.add(new DefaultMutableTreeNode("تقارير المخزون"));
        rootNode.add(inventoryNode);

        // ربط الأنظمة
        DefaultMutableTreeNode integrationNode = new DefaultMutableTreeNode("ربط الأنظمة");
        integrationNode.add(new DefaultMutableTreeNode("ربط النظام واستيراد البيانات"));
        rootNode.add(integrationNode);

        // التقارير والإحصائيات
        DefaultMutableTreeNode reportsNode = new DefaultMutableTreeNode("التقارير والإحصائيات");
        reportsNode.add(new DefaultMutableTreeNode("تقارير المبيعات"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير الأرباح"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير الأداء"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير مخصصة"));
        rootNode.add(reportsNode);

        // إدارة الشركات والفروع
        DefaultMutableTreeNode companyNode = new DefaultMutableTreeNode("إدارة الشركات والفروع");
        companyNode.add(new DefaultMutableTreeNode("إدارة الشركات"));
        companyNode.add(new DefaultMutableTreeNode("إدارة الفروع"));
        companyNode.add(new DefaultMutableTreeNode("إدارة الأدوار"));
        companyNode.add(new DefaultMutableTreeNode("إدارة الصلاحيات"));
        rootNode.add(companyNode);

        // الإعدادات المالية والنظام
        DefaultMutableTreeNode financialNode =
                new DefaultMutableTreeNode("الإعدادات المالية والنظام");
        financialNode.add(new DefaultMutableTreeNode("إدارة السنوات المالية"));
        financialNode.add(new DefaultMutableTreeNode("سجل التدقيق"));
        rootNode.add(financialNode);

        // الإعدادات والإدارة
        DefaultMutableTreeNode settingsNode = new DefaultMutableTreeNode("الإعدادات والإدارة");

        // الإعدادات العامة مع العقد الفرعية
        DefaultMutableTreeNode generalSettingsNode = new DefaultMutableTreeNode("الإعدادات العامة");
        generalSettingsNode.add(new DefaultMutableTreeNode("المتغيرات العامة"));
        generalSettingsNode.add(new DefaultMutableTreeNode("إعدادات العملة"));
        generalSettingsNode.add(new DefaultMutableTreeNode("إعدادات النظام العامة"));
        generalSettingsNode.add(new DefaultMutableTreeNode("إعدادات الأمان"));
        settingsNode.add(generalSettingsNode);

        settingsNode.add(new DefaultMutableTreeNode("إدارة المستخدمين"));
        settingsNode.add(new DefaultMutableTreeNode("إدارة الربط والاستيراد"));
        settingsNode.add(new DefaultMutableTreeNode("إعدادات النظام"));
        settingsNode.add(new DefaultMutableTreeNode("النسخ الاحتياطية"));
        rootNode.add(settingsNode);

        // المساعدة والدعم
        DefaultMutableTreeNode helpNode = new DefaultMutableTreeNode("المساعدة والدعم");
        helpNode.add(new DefaultMutableTreeNode("دليل المستخدم"));
        helpNode.add(new DefaultMutableTreeNode("الدعم الفني"));
        helpNode.add(new DefaultMutableTreeNode("حول البرنامج"));
        rootNode.add(helpNode);

        treeModel = new DefaultTreeModel(rootNode);
    }

    /**
     * إعداد مكونات الشجرة
     */
    private void setupTreeComponents() {
        menuTree = new JTree(treeModel);

        // إعداد الشجرة للنص العربي
        menuTree.setFont(arabicFont);
        menuTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        menuTree.setRootVisible(true);
        menuTree.setShowsRootHandles(true);
        menuTree.setRowHeight(25);

        // تخصيص مظهر الشجرة
        menuTree.setCellRenderer(new CustomTreeCellRenderer());

        // توسيع العقد الرئيسية
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }

        // تحديد العقدة الجذر
        menuTree.setSelectionRow(0);
    }

    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setBackground(new Color(248, 249, 250));
        setBorder(new EmptyBorder(10, 10, 10, 10));

        // رأس القائمة
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);

        // الشجرة مع شريط التمرير
        JScrollPane scrollPane = new JScrollPane(menuTree);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createTitledBorder("القائمة الرئيسية"),
                BorderFactory.createEmptyBorder(5, 5, 5, 5)));
        scrollPane.setPreferredSize(new Dimension(250, 400)); // تقليل الحجم
        scrollPane.setMinimumSize(new Dimension(200, 300)); // حد أدنى للحجم

        add(scrollPane, BorderLayout.CENTER);

        // تذييل مع معلومات المستخدم
        JPanel footerPanel = createFooterPanel();
        add(footerPanel, BorderLayout.SOUTH);
    }

    /**
     * إنشاء رأس القائمة
     */
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(52, 152, 219));
        panel.setBorder(new EmptyBorder(15, 15, 15, 15));

        JLabel titleLabel = new JLabel("القائمة الرئيسية");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);

        panel.add(titleLabel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء تذييل القائمة
     */
    private JPanel createFooterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(new EmptyBorder(10, 15, 10, 15));

        JLabel userLabel = new JLabel("المستخدم: admin");
        userLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        userLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel timeLabel = new JLabel(
                "الوقت: " + new java.text.SimpleDateFormat("HH:mm").format(new java.util.Date()));
        timeLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        timeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        panel.add(userLabel, BorderLayout.NORTH);
        panel.add(timeLabel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        menuTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode =
                    (DefaultMutableTreeNode) menuTree.getLastSelectedPathComponent();

            if (selectedNode != null && selectedNode.isLeaf() && !selectedNode.isRoot()) {
                String menuItem = selectedNode.toString();
                handleMenuItemClick(menuItem);
            }
        });

        // إضافة قائمة سياق (Right-click menu)
        menuTree.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (SwingUtilities.isRightMouseButton(e)) {
                    showContextMenu(e);
                }
            }
        });
    }

    /**
     * عرض قائمة السياق
     */
    private void showContextMenu(MouseEvent e) {
        TreePath path = menuTree.getPathForLocation(e.getX(), e.getY());
        if (path != null) {
            menuTree.setSelectionPath(path);
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();

            JPopupMenu contextMenu = new JPopupMenu();
            contextMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            if (node.isLeaf() && !node.isRoot()) {
                JMenuItem openItem = new JMenuItem("فتح");
                openItem.setFont(arabicFont);
                openItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                openItem.addActionListener(ev -> {
                    String menuItem = node.toString();
                    handleMenuItemClick(menuItem);
                });
                contextMenu.add(openItem);

                contextMenu.addSeparator();

                JMenuItem infoItem = new JMenuItem("معلومات");
                infoItem.setFont(arabicFont);
                infoItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                infoItem.addActionListener(ev -> showItemInfo(node.toString()));
                contextMenu.add(infoItem);
            } else {
                JMenuItem expandItem = new JMenuItem(menuTree.isExpanded(path) ? "طي" : "توسيع");
                expandItem.setFont(arabicFont);
                expandItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                expandItem.addActionListener(ev -> {
                    if (menuTree.isExpanded(path)) {
                        menuTree.collapsePath(path);
                    } else {
                        menuTree.expandPath(path);
                    }
                });
                contextMenu.add(expandItem);
            }

            contextMenu.show(menuTree, e.getX(), e.getY());
        }
    }

    /**
     * عرض معلومات العنصر
     */
    private void showItemInfo(String itemName) {
        String info = getItemDescription(itemName);
        JOptionPane.showMessageDialog(this, info, "معلومات: " + itemName,
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * الحصول على وصف العنصر
     */
    private String getItemDescription(String itemName) {
        Map<String, String> descriptions = new HashMap<>();

        descriptions.put("قائمة الشحنات", "عرض وإدارة جميع الشحنات في النظام");
        descriptions.put("شحنة جديدة", "إنشاء شحنة جديدة وإدخال بياناتها");
        descriptions.put("تتبع الشحنات", "تتبع حالة الشحنات ومعرفة موقعها");
        descriptions.put("تقارير الشحن", "عرض تقارير مفصلة عن عمليات الشحن");

        descriptions.put("قائمة العملاء", "عرض وإدارة بيانات جميع العملاء");
        descriptions.put("عميل جديد", "إضافة عميل جديد إلى النظام");
        descriptions.put("تقارير العملاء", "عرض تقارير عن نشاط العملاء");

        descriptions.put("الفواتير", "إدارة الفواتير وإنشاء فواتير جديدة");
        descriptions.put("المدفوعات", "تسجيل ومتابعة المدفوعات");
        descriptions.put("التقارير المالية", "عرض التقارير المالية والمحاسبية");
        descriptions.put("الحسابات", "إدارة الحسابات المالية");

        descriptions.put("الإعدادات العامة", "تكوين الإعدادات العامة للنظام");
        descriptions.put("إدارة المستخدمين", "إدارة المستخدمين وصلاحياتهم");

        return descriptions.getOrDefault(itemName, "لا توجد معلومات متاحة لهذا العنصر");
    }

    /**
     * معالج النقر على عنصر القائمة
     */
    private void handleMenuItemClick(String menuItem) {
        System.out.println("تم النقر على: " + menuItem);

        // البحث عن الإجراء المطابق
        System.out.println("🔍 البحث عن الإجراء للعنصر: " + menuItem);

        // 🔥 حل فوري لنافذة إعدادات العملة
        if ("إعدادات العملة".equals(menuItem) || "إدارة العملات".equals(menuItem)) {
            System.out.println("🔥 فتح نافذة إعدادات العملة مباشرة - حل فوري!");
            try {
                CurrencyManagementWindow currencyWindow = new CurrencyManagementWindow();
                currencyWindow.setVisible(true);
                System.out.println("✅ تم فتح نافذة إعدادات العملة بنجاح!");
                return;
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة إعدادات العملة: " + e.getMessage());
                e.printStackTrace();
                showMessage("خطأ في فتح نافذة إعدادات العملة: " + e.getMessage());
                return;
            }
        }

        // 📋 حل فوري لنافذة طلبات الشراء
        if ("طلبات الشراء".equals(menuItem)) {
            System.out.println("📋 فتح نافذة طلبات الشراء مباشرة - حل فوري!");
            try {
                PurchaseRequestMasterDetailWindow purchaseWindow = new PurchaseRequestMasterDetailWindow();
                purchaseWindow.setVisible(true);
                System.out.println("✅ تم فتح نافذة طلبات الشراء بنجاح!");
                return;
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة طلبات الشراء: " + e.getMessage());
                e.printStackTrace();
                showMessage("خطأ في فتح نافذة طلبات الشراء: " + e.getMessage());
                return;
            }
        }

        Runnable action = menuActions.get(menuItem);
        if (action != null) {
            System.out.println("✅ تم العثور على الإجراء في menuActions: " + menuItem);
            try {
                action.run();
            } catch (Exception e) {
                System.err.println("خطأ في تنفيذ الإجراء: " + e.getMessage());
                e.printStackTrace();
                showMessage("خطأ في فتح النافذة: " + e.getMessage());
            }
        } else {
            System.out.println("⚠️ لم يتم العثور على الإجراء في menuActions، محاولة البحث في قاعدة البيانات: " + menuItem);
            // محاولة فتح النافذة ديناميكياً من قاعدة البيانات
            if (tryOpenWindowFromDatabase(menuItem)) {
                System.out.println("✅ تم فتح النافذة ديناميكياً: " + menuItem);
            } else {
                System.out.println("❌ فشل في فتح النافذة ديناميكياً: " + menuItem);
                showMessage("لم يتم تنفيذ هذه الوظيفة بعد: " + menuItem);
            }
        }
    }

    /**
     * محاولة فتح النافذة ديناميكياً من قاعدة البيانات
     */
    private boolean tryOpenWindowFromDatabase(String nodeName) {
        try {
            // البحث عن النافذة في قاعدة البيانات
            String windowClass = getWindowClassFromDatabase(nodeName);
            if (windowClass != null && !windowClass.isEmpty()) {
                return openWindowByClassName(windowClass);
            }
        } catch (Exception e) {
            System.err.println("خطأ في فتح النافذة ديناميكياً: " + e.getMessage());
        }
        return false;
    }

    /**
     * الحصول على كلاس النافذة من قاعدة البيانات
     */
    private String getWindowClassFromDatabase(String nodeName) {
        try {
            // TNSConnectionManager tnsManager = TNSConnectionManager.getInstance(); // معطل مؤقتاً
            // Connection connection = tnsManager.getShipErpConnection(); // معطل مؤقتاً

            // استخدام اتصال مباشر
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";
            Connection connection = DriverManager.getConnection(url, username, password);

            String sql =
                    "SELECT WINDOW_CLASS FROM ERP_SYSTEM_TREE WHERE NODE_NAME_AR = ? AND WINDOW_CLASS IS NOT NULL";

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, nodeName);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getString("WINDOW_CLASS");
                    }
                }
            }

            connection.close();
        } catch (Exception e) {
            System.err.println("خطأ في البحث عن النافذة في قاعدة البيانات: " + e.getMessage());
        }
        return null;
    }

    /**
     * فتح النافذة باستخدام اسم الكلاس
     */
    private boolean openWindowByClassName(String className) {
        try {
            System.out.println("🔄 محاولة فتح النافذة: " + className);

            // قائمة النوافذ المعروفة
            switch (className) {
                case "AdvancedCompanySettingsWindow":
                    // AdvancedCompanySettingsWindow companyWindow = new AdvancedCompanySettingsWindow(); // معطل مؤقتاً
                    // companyWindow.setVisible(true); // معطل مؤقتاً
                    showMessage("نافذة إعدادات الشركات قيد التطوير");
                    return true;

                case "EmailAccountsWindow":
                case "CompleteEmailAccountsWindow":
                    try {
                        // التأكد من وجود مكتبة JavaMail
                        Class.forName("javax.mail.Session");
                        CompleteEmailAccountsWindow emailWindow = new CompleteEmailAccountsWindow();
                        emailWindow.setVisible(true);
                        return true;
                    } catch (ClassNotFoundException e) {
                        showMessage(
                                "خطأ: مكتبة JavaMail غير موجودة. يرجى التأكد من وجود javax.mail-1.6.2.jar في مجلد lib");
                        return false;
                    } catch (Exception e) {
                        System.err.println("❌ خطأ في فتح نافذة إدارة حسابات البريد: " + e.getMessage());
                        e.printStackTrace();
                        showMessage("خطأ في فتح نافذة إدارة حسابات البريد: " + e.getMessage());
                        return false;
                    }

                    // نوافذ إدارة الأصناف والمخزون الحقيقية
                case "ItemsManagementWindow":
                case "ItemGroupsManagementWindow":
                    ItemGroupsManagementWindow itemsWindow = new ItemGroupsManagementWindow();
                    itemsWindow.setVisible(true);
                    return true;

                case "ItemCategoriesWindow":
                case "NewItemCategoryWindow":
                    showMessage(
                            "نافذة تصنيفات الأصناف - قيد التطوير\nItem Categories Window - Under Development");
                    return true;

                case "UnitsOfMeasureWindow":
                case "MeasurementUnitsWindow":
                    MeasurementUnitsWindow unitsWindow = new MeasurementUnitsWindow();
                    unitsWindow.setVisible(true);
                    return true;

                case "ItemPricesWindow":
                    showMessage(
                            "نافذة أسعار الأصناف قيد التطوير\nItem Prices Window Under Development");
                    return true;

                case "ItemInventoryWindow":
                    showMessage(
                            "نافذة مخزون الأصناف قيد التطوير\nItem Inventory Window Under Development");
                    return true;

                case "InventoryTransactionsWindow":
                    showMessage(
                            "نافذة حركة المخزون قيد التطوير\nInventory Transactions Window Under Development");
                    return true;

                case "InventoryValuationWindow":
                    showMessage(
                            "نافذة تقييم المخزون قيد التطوير\nInventory Valuation Window Under Development");
                    return true;

                case "InventorySettingsWindow":
                    showMessage(
                            "نافذة إعدادات المخزون قيد التطوير\nInventory Settings Window Under Development");
                    return true;

                case "WorkingThemeWindow":
                    WorkingThemeWindow themeWindow = new WorkingThemeWindow();
                    themeWindow.setVisible(true);
                    return true;

                case "GlobalVariablesWindow":
                    GlobalVariablesWindow variablesWindow = new GlobalVariablesWindow();
                    variablesWindow.setVisible(true);
                    return true;

                case "GeneralSettingsWindow":
                case "GeneralSystemSettingsWindow":
                    SwingUtilities.invokeLater(() -> {
                        GeneralSettingsWindow generalWindow = new GeneralSettingsWindow(
                                (JFrame) SwingUtilities.getWindowAncestor(this));
                        generalWindow.setVisible(true);
                    });
                    return true;

                case "CurrencySettingsWindow":
                case "CurrencyManagementWindow":
                    SwingUtilities.invokeLater(() -> {
                        try {
                            CurrencyManagementWindow currencyWindow = new CurrencyManagementWindow();
                            currencyWindow.setVisible(true);
                            System.out.println("✅ تم فتح نافذة إدارة العملات الشاملة");
                        } catch (Exception e) {
                            System.err.println("❌ خطأ في فتح نافذة إدارة العملات: " + e.getMessage());
                            showMessage("خطأ في فتح نافذة إدارة العملات: " + e.getMessage());
                        }
                    });
                    return true;

                case "AdvancedUISettingsWindow":
                case "UISettingsWindow":
                    SwingUtilities.invokeLater(() -> {
                        try {
                            AdvancedUISettingsWindow uiWindow = new AdvancedUISettingsWindow(
                                    (JFrame) SwingUtilities.getWindowAncestor(this));
                            uiWindow.setVisible(true);
                            System.out.println("✅ تم فتح نافذة إعدادات الواجهة والمظهر المتطورة");
                        } catch (Exception e) {
                            System.err.println("❌ خطأ في فتح نافذة إعدادات الواجهة: " + e.getMessage());
                            e.printStackTrace();
                        }
                    });
                    return true;

                // نوافذ الإعدادات العامة الحقيقية
                case "DatabaseSettingsWindow":
                    showMessage(
                            "نافذة إعدادات قاعدة البيانات - قيد التطوير\nDatabase Settings Window - Under Development");
                    return true;

                case "SecuritySettingsWindow":
                    UserManagementWindow userWindow = new UserManagementWindow(
                            (JFrame) SwingUtilities.getWindowAncestor(this));
                    userWindow.setVisible(true);
                    return true;

                case "BackupSettingsWindow":
                    showMessage(
                            "نافذة إعدادات النسخ الاحتياطي - قيد التطوير\nBackup Settings Window - Under Development");
                    return true;

                case "ReportsSettingsWindow":
                    showMessage(
                            "نافذة إعدادات التقارير قيد التطوير\nReports Settings Window Under Development");
                    return true;

                // نوافذ أدوات النظام
                case "SystemMonitorWindow":
                    showMessage(
                            "نافذة مراقب النظام قيد التطوير\nSystem Monitor Window Under Development");
                    return true;

                case "SystemLogsWindow":
                    showMessage(
                            "نافذة سجلات النظام قيد التطوير\nSystem Logs Window Under Development");
                    return true;

                case "BackupManagerWindow":
                    showMessage(
                            "نافذة النسخ الاحتياطي قيد التطوير\nBackup Manager Window Under Development");
                    return true;

                case "DataRecoveryWindow":
                    showMessage(
                            "نافذة استعادة البيانات قيد التطوير\nData Recovery Window Under Development");
                    return true;

                case "DatabaseMaintenanceWindow":
                    showMessage(
                            "نافذة صيانة قاعدة البيانات قيد التطوير\nDatabase Maintenance Window Under Development");
                    return true;

                case "TaskManagerWindow":
                    showMessage(
                            "نافذة مدير المهام قيد التطوير\nTask Manager Window Under Development");
                    return true;

                case "DiagnosticToolsWindow":
                    showMessage(
                            "نافذة أدوات التشخيص قيد التطوير\nDiagnostic Tools Window Under Development");
                    return true;

                // نوافذ التقارير
                case "ReportDesignerWindow":
                    showMessage(
                            "نافذة مصمم التقارير قيد التطوير\nReport Designer Window Under Development");
                    return true;

                case "ReportsLibraryWindow":
                    showMessage(
                            "نافذة مكتبة التقارير قيد التطوير\nReports Library Window Under Development");
                    return true;

                case "ReportSchedulerWindow":
                    showMessage(
                            "نافذة جدولة التقارير قيد التطوير\nReport Scheduler Window Under Development");
                    return true;

                case "ReportDistributionWindow":
                    showMessage(
                            "نافذة توزيع التقارير قيد التطوير\nReport Distribution Window Under Development");
                    return true;

                case "ReportsArchiveWindow":
                    showMessage(
                            "نافذة أرشيف التقارير قيد التطوير\nReports Archive Window Under Development");
                    return true;

                // نوافذ إدارة الشحنات
                case "ShipmentManagementWindow":
                    showMessage(
                            "نافذة إدارة الشحنات قيد التطوير\nShipment Management Window Under Development");
                    return true;

                case "ShipmentTrackingWindow":
                    showMessage(
                            "نافذة تتبع الشحنات قيد التطوير\nShipment Tracking Window Under Development");
                    return true;

                case "ContainerManagementWindow":
                    showMessage(
                            "نافذة إدارة الحاويات قيد التطوير\nContainer Management Window Under Development");
                    return true;

                case "ShippingScheduleWindow":
                    showMessage(
                            "نافذة جدولة الشحن قيد التطوير\nShipping Schedule Window Under Development");
                    return true;

                // نوافذ إدارة العملاء
                case "CustomerManagementWindow":
                    showMessage(
                            "نافذة إدارة العملاء قيد التطوير\nCustomer Management Window Under Development");
                    return true;

                case "CustomerContractsWindow":
                    showMessage(
                            "نافذة عقود العملاء قيد التطوير\nCustomer Contracts Window Under Development");
                    return true;

                case "CustomerInvoicesWindow":
                    showMessage(
                            "نافذة فواتير العملاء قيد التطوير\nCustomer Invoices Window Under Development");
                    return true;

                case "CustomerServiceWindow":
                    showMessage(
                            "نافذة خدمة العملاء قيد التطوير\nCustomer Service Window Under Development");
                    return true;

                // نوافذ إدارة الموردين
                case "SupplierManagementWindow":
                    try {
                        new SuppliersManagementWindow().setVisible(true);
                        System.out.println("🏢 تم فتح نافذة إدارة الموردين الشاملة من القائمة الرئيسية");
                        return true;
                    } catch (Exception e) {
                        System.err.println("❌ خطأ في فتح نافذة إدارة الموردين: " + e.getMessage());
                        e.printStackTrace();
                        showMessage("خطأ في فتح نافذة إدارة الموردين:\n" + e.getMessage());
                        return false;
                    }

                case "PurchaseRequestWindow":
                    try {
                        new PurchaseRequestMasterDetailWindow().setVisible(true);
                        System.out.println("📋 تم فتح نافذة طلبات الشراء المتقدمة من القائمة الرئيسية");
                        return true;
                    } catch (Exception e) {
                        System.err.println("❌ خطأ في فتح نافذة طلبات الشراء المتقدمة: " + e.getMessage());
                        e.printStackTrace();
                        showMessage("خطأ في فتح نافذة طلبات الشراء المتقدمة:\n" + e.getMessage());
                        return false;
                    }

                case "PurchaseOrdersWindow":
                    showMessage(
                            "نافذة طلبات الشراء قيد التطوير\nPurchase Orders Window Under Development");
                    return true;

                case "SupplierInvoicesWindow":
                    showMessage(
                            "نافذة فواتير الموردين قيد التطوير\nSupplier Invoices Window Under Development");
                    return true;

                case "SupplierEvaluationWindow":
                    showMessage(
                            "نافذة تقييم الموردين قيد التطوير\nSupplier Evaluation Window Under Development");
                    return true;

                // نوافذ إدارة المخازن
                case "WarehouseManagementWindow":
                    showMessage(
                            "نافذة إدارة المخازن قيد التطوير\nWarehouse Management Window Under Development");
                    return true;

                case "InventoryManagementWindow":
                    showMessage(
                            "نافذة إدارة المخزون قيد التطوير\nInventory Management Window Under Development");
                    return true;

                case "InventoryMovementWindow":
                    showMessage(
                            "نافذة حركة المخزون قيد التطوير\nInventory Movement Window Under Development");
                    return true;

                case "InventoryCountWindow":
                    showMessage(
                            "نافذة جرد المخزون قيد التطوير\nInventory Count Window Under Development");
                    return true;

                // نوافذ الحسابات والمالية
                case "ChartOfAccountsWindow":
                    showMessage(
                            "نافذة دليل الحسابات قيد التطوير\nChart of Accounts Window Under Development");
                    return true;

                case "JournalEntriesWindow":
                    showMessage(
                            "نافذة القيود المحاسبية قيد التطوير\nJournal Entries Window Under Development");
                    return true;

                case "BalanceSheetWindow":
                    showMessage(
                            "نافذة الميزانية العمومية قيد التطوير\nBalance Sheet Window Under Development");
                    return true;

                case "IncomeStatementWindow":
                    showMessage(
                            "نافذة قائمة الدخل قيد التطوير\nIncome Statement Window Under Development");
                    return true;

                // نوافذ إدارة الموظفين
                case "EmployeeManagementWindow":
                    showMessage(
                            "نافذة إدارة الموظفين قيد التطوير\nEmployee Management Window Under Development");
                    return true;

                case "AttendanceWindow":
                    showMessage(
                            "نافذة الحضور والانصراف قيد التطوير\nAttendance Window Under Development");
                    return true;

                case "PayrollWindow":
                    showMessage(
                            "نافذة الرواتب والأجور قيد التطوير\nPayroll Window Under Development");
                    return true;

                case "LeaveManagementWindow":
                    showMessage(
                            "نافذة الإجازات قيد التطوير\nLeave Management Window Under Development");
                    return true;

                // نوافذ التقارير والإحصائيات
                case "ShipmentReportsWindow":
                    showMessage(
                            "نافذة تقارير الشحنات قيد التطوير\nShipment Reports Window Under Development");
                    return true;

                case "CustomerReportsWindow":
                    showMessage(
                            "نافذة تقارير العملاء قيد التطوير\nCustomer Reports Window Under Development");
                    return true;

                case "FinancialReportsWindow":
                    showMessage(
                            "نافذة التقارير المالية قيد التطوير\nFinancial Reports Window Under Development");
                    return true;

                case "InventoryReportsWindow":
                    showMessage(
                            "نافذة تقارير المخزون قيد التطوير\nInventory Reports Window Under Development");
                    return true;

                case "DashboardWindow":
                    showMessage(
                            "نافذة لوحة المعلومات قيد التطوير\nDashboard Window Under Development");
                    return true;

                // نوافذ البريد الإلكتروني الأخرى
                case "EmailInboxWindow":
                    SwingUtilities.invokeLater(() -> {
                        try {
                            // فتح النافذة العاملة الجديدة (جاهزة لإضافة JavaMail لاحقاً)
                            WorkingRealEmailInbox inboxWindow = new WorkingRealEmailInbox();
                            inboxWindow.setVisible(true);
                            System.out.println("✅ تم فتح نافذة صندوق البريد الوارد العاملة بنجاح");
                        } catch (Exception e) {
                            System.err.println("❌ خطأ في فتح نافذة صندوق البريد الوارد: " + e.getMessage());
                            e.printStackTrace();
                            showMessage("خطأ في فتح نافذة صندوق البريد الوارد: " + e.getMessage());
                        }
                    });
                    return true;

                case "ComprehensiveEmailInboxWindow":
                    SwingUtilities.invokeLater(() -> {
                        try {
                            // فتح النافذة المتقدمة الجديدة
                            AdvancedEmailInboxWindow inboxWindow = new AdvancedEmailInboxWindow();
                            inboxWindow.setVisible(true);
                            System.out.println("✅ تم فتح نافذة صندوق البريد الوارد المتقدمة والشاملة");
                        } catch (Exception e) {
                            System.err.println("❌ خطأ في فتح نافذة صندوق البريد الوارد الشاملة: " + e.getMessage());
                            e.printStackTrace();
                            showMessage("خطأ في فتح نافذة صندوق البريد الوارد: " + e.getMessage());
                        }
                    });
                    return true;

                case "EmailComposeWindow":
                    showMessage(
                            "نافذة إنشاء رسالة جديدة قيد التطوير\nEmail Compose Window Under Development");
                    return true;

                case "EmailTemplatesWindow":
                    try {
                        // التأكد من وجود مكتبة JavaMail
                        Class.forName("javax.mail.Session");
                        EmailTemplatesWindow templatesWindow = new EmailTemplatesWindow();
                        templatesWindow.setVisible(true);
                        return true;
                    } catch (ClassNotFoundException e) {
                        showMessage(
                                "خطأ: مكتبة JavaMail غير موجودة. يرجى التأكد من وجود javax.mail-1.6.2.jar في مجلد lib");
                        return false;
                    }

                case "EmailCampaignsWindow":
                    showMessage(
                            "نافذة إدارة الحملات قيد التطوير\nEmail Campaigns Window Under Development");
                    return true;

                case "EmailAddressBookWindow":
                    showMessage(
                            "نافذة دفتر العناوين قيد التطوير\nEmail Address Book Window Under Development");
                    return true;

                case "EmailFiltersWindow":
                    showMessage(
                            "نافذة المرشحات والقواعد قيد التطوير\nEmail Filters Window Under Development");
                    return true;

                case "EmailSignaturesWindow":
                    showMessage(
                            "نافذة التوقيعات قيد التطوير\nEmail Signatures Window Under Development");
                    return true;

                case "EmailReportsWindow":
                    showMessage(
                            "نافذة إحصائيات البريد الإلكتروني قيد التطوير\nEmail Reports Window Under Development");
                    return true;

                case "EmailLogsWindow":
                    showMessage(
                            "نافذة سجلات البريد الإلكتروني قيد التطوير\nEmail Logs Window Under Development");
                    return true;

                case "EmailSettingsWindow":
                    showMessage(
                            "نافذة إعدادات البريد الإلكتروني قيد التطوير\nEmail Settings Window Under Development");
                    return true;

                default:
                    System.err.println("❌ النافذة غير معروفة: " + className);
                    return false;
            }

        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح النافذة " + className + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * عرض رسالة
     */
    private void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "إشعار", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * تسجيل نافذة جديدة في شجرة النظام
     */
    public void registerNewWindow(String windowClass, String nameAr, String nameEn,
            String description, String category) {
        try {
            if (systemTreeManager != null) {
                boolean success = systemTreeManager.registerNewWindow(windowClass, nameAr, nameEn,
                        description, category);

                if (success) {
                    // إعادة تحميل الشجرة
                    refreshTreeFromDatabase();
                    System.out.println("✅ تم تسجيل النافذة الجديدة: " + nameAr);
                } else {
                    System.err.println("❌ فشل في تسجيل النافذة: " + nameAr);
                }
            } else {
                System.err.println("❌ مدير شجرة النظام غير متاح");
            }
        } catch (Exception e) {
            System.err.println("❌ خطأ في تسجيل النافذة الجديدة: " + e.getMessage());
        }
    }

    /**
     * إعادة تحميل الشجرة من قاعدة البيانات
     */
    public void refreshTreeFromDatabase() {
        try {
            if (systemTreeManager != null) {
                DefaultTreeModel newModel = systemTreeManager.getSystemTreeModel();
                menuTree.setModel(newModel);

                // توسيع العقد الرئيسية
                for (int i = 0; i < menuTree.getRowCount(); i++) {
                    menuTree.expandRow(i);
                }

                System.out.println("✅ تم تحديث شجرة النظام");
            } else {
                System.out.println("⚠️ مدير شجرة النظام غير متاح");
            }
        } catch (Exception e) {
            System.err.println("❌ خطأ في تحديث شجرة النظام: " + e.getMessage());
        }
    }

    /**
     * طباعة شجرة النظام الحالية
     */
    public void printCurrentTree() {
        if (systemTreeManager != null) {
            systemTreeManager.printSystemTree();
        } else {
            System.out.println("⚠️ مدير شجرة النظام غير متاح");
        }
    }



    /**
     * فتح نافذة ربط النظام واستيراد البيانات
     */
    private void openSystemIntegrationWindow() {
        showMessage("نافذة ربط النظام قيد التطوير\nSystem Integration Window under development");
    }

    /**
     * فتح نافذة إدارة الربط والاستيراد
     */
    private void openSystemIntegrationManagementWindow() {
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("🔄 فتح نافذة إدارة الربط والاستيراد...");

                showMessage(
                        "نافذة إدارة الربط والاستيراد قيد التطوير\nImport/Export Integration Window under development");

                System.out.println("✅ تم عرض رسالة التطوير بنجاح!");

            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة إدارة الربط والاستيراد: " + e.getMessage());
                e.printStackTrace();

                String errorMessage = "فشل في فتح نافذة إدارة الربط والاستيراد:\n" + e.getMessage();

                if (e.getMessage() != null) {
                    if (e.getMessage().contains("oracle") || e.getMessage().contains("ORA-")) {
                        errorMessage += "\n\nتأكد من:\n" + "• تشغيل قاعدة البيانات Oracle\n"
                                + "• وجود المستخدم ship_erp\n"
                                + "• تنفيذ سكريبت إنشاء جداول النظام";
                    } else if (e.getMessage().contains("ClassNotFoundException")) {
                        errorMessage += "\n\nتأكد من وجود ملف ojdbc11.jar في مجلد lib";
                    }
                }

                showMessage(errorMessage);
            }
        });
    }



    /**
     * فتح نافذة إدارة الشركات
     */
    private void openCompanyManagementWindow() {
        try {
            showMessage("نافذة إدارة الشركات قيد التطوير");
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة إدارة الشركات: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * فتح نافذة إدارة الفروع
     */
    private void openBranchManagementWindow() {
        try {
            showMessage("نافذة إدارة الفروع قيد التطوير");
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة إدارة الفروع: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * فتح نافذة إدارة الأدوار
     */
    private void openRoleManagementWindow() {
        try {
            showMessage(
                    "نافذة إدارة الأدوار قيد التطوير\nRole Management Window under development");
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة إدارة الأدوار: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * فتح نافذة إدارة الصلاحيات
     */
    private void openPermissionManagementWindow() {
        try {
            // TODO: Create PermissionManagementWindow
            showMessage("نافذة إدارة الصلاحيات قيد التطوير");
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة إدارة الصلاحيات: " + e.getMessage());
        }
    }

    /**
     * فتح نافذة المتغيرات العامة
     */
    private void openGlobalVariablesWindow() {
        try {
            GlobalVariablesWindow variablesWindow = new GlobalVariablesWindow();
            variablesWindow.setVisible(true);
            System.out.println("✅ تم فتح نافذة المتغيرات العامة");
        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح نافذة المتغيرات العامة: " + e.getMessage());
            showMessage("خطأ في فتح نافذة المتغيرات العامة: " + e.getMessage());
        }
    }



    /**
     * فتح نافذة إدارة السنوات المالية
     */
    private void openFiscalYearManagementWindow() {
        try {
            showMessage(
                    "نافذة إدارة السنوات المالية قيد التطوير\nFiscal Year Management Window under development");
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة إدارة السنوات المالية: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * فتح نافذة سجل التدقيق
     */
    private void openAuditLogWindow() {
        try {
            showMessage("نافذة سجل التدقيق قيد التطوير\nAudit Log Window under development");
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة سجل التدقيق: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * فتح نافذة بيانات الأصناف الحقيقية
     */
    private void openRealItemDataWindow() {
        try {
            System.out.println("🔄 فتح نافذة بيانات الأصناف الحقيقية...");

            RealItemDataWindow window = new RealItemDataWindow();
            window.setVisible(true);

            System.out.println("✅ تم فتح نافذة بيانات الأصناف الحقيقية بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح نافذة بيانات الأصناف الحقيقية: " + e.getMessage());
            e.printStackTrace();
            showMessage("خطأ في فتح نافذة بيانات الأصناف الحقيقية: " + e.getMessage());
        }
    }

    /**
     * فتح نافذة بيانات الأصناف الشاملة المتقدمة
     */
    private void openComprehensiveItemDataWindow() {
        try {
            System.out.println("🔄 فتح نافذة بيانات الأصناف الشاملة المتقدمة...");

            // استخدام النافذة الشاملة المتقدمة الموجودة
            ComprehensiveItemDataWindow window = new ComprehensiveItemDataWindow();
            window.setVisible(true);

            System.out.println("✅ تم فتح نافذة بيانات الأصناف الشاملة المتقدمة بنجاح!");

        } catch (Exception e) {
            System.err.println(
                    "❌ خطأ في فتح نافذة بيانات الأصناف الشاملة المتقدمة: " + e.getMessage());
            e.printStackTrace();
            showMessage("خطأ في فتح نافذة بيانات الأصناف الشاملة المتقدمة:\n" + e.getMessage());
        }
    }

    /**
     * فتح نافذة مجموعات الأصناف
     */
    private void openItemGroupsManagementWindow() {
        try {
            System.out.println("🔄 فتح نافذة مجموعات الأصناف...");

            ItemGroupsManagementWindow window = new ItemGroupsManagementWindow();
            window.setVisible(true);

            System.out.println("✅ تم فتح نافذة مجموعات الأصناف بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح نافذة مجموعات الأصناف: " + e.getMessage());
            e.printStackTrace();
            showMessage("خطأ في فتح نافذة مجموعات الأصناف: " + e.getMessage());
        }
    }

    /**
     * فتح نافذة وحدات القياس
     */
    private void openMeasurementUnitsWindow() {
        try {
            System.out.println("🔄 فتح نافذة وحدات القياس...");

            // محاولة فتح نافذة وحدات القياس المتقدمة
            try {
                Class<?> windowClass = Class.forName("MeasurementUnitsWindow");
                Object window = windowClass.getDeclaredConstructor().newInstance();
                java.lang.reflect.Method setVisibleMethod =
                        windowClass.getMethod("setVisible", boolean.class);
                setVisibleMethod.invoke(window, true);
                System.out.println("✅ تم فتح نافذة وحدات القياس المتقدمة بنجاح!");
            } catch (ClassNotFoundException e) {
                // إذا لم توجد النافذة المتقدمة، استخدم النافذة البسيطة
                System.out.println("⚠️ النافذة المتقدمة غير متاحة، فتح النافذة البسيطة...");
                try {
                    Class<?> simpleWindowClass = Class.forName("SimpleMeasurementWindow");
                    Object simpleWindow = simpleWindowClass.getDeclaredConstructor().newInstance();
                    java.lang.reflect.Method setVisibleMethod =
                            simpleWindowClass.getMethod("setVisible", boolean.class);
                    setVisibleMethod.invoke(simpleWindow, true);
                    System.out.println("✅ تم فتح نافذة وحدات القياس البسيطة بنجاح!");
                } catch (Exception ex) {
                    // إذا لم توجد أي نافذة، اعرض رسالة
                    showMessage("نافذة وحدات القياس غير متاحة حالياً\n"
                            + "يرجى التأكد من وجود ملفات:\n" + "- MeasurementUnitsWindow.class\n"
                            + "- SimpleMeasurementWindow.class");
                }
            }

        } catch (Exception e) {
            System.err.println("❌ خطأ في فتح نافذة وحدات القياس: " + e.getMessage());
            e.printStackTrace();
            showMessage("خطأ في فتح نافذة وحدات القياس: " + e.getMessage());
        }
    }

    /**
     * عرض نافذة حول البرنامج
     */
    private void showAboutDialog() {
        String aboutText = "نظام إدارة الشحنات المتقدم\n" + "الإصدار: 1.0\n"
                + "تطوير: فريق التطوير\n" + "حقوق الطبع محفوظة © 2024";

        JOptionPane.showMessageDialog(this, aboutText, "حول البرنامج",
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * فئة مخصصة لعرض خلايا الشجرة
     */
    private class CustomTreeCellRenderer extends DefaultTreeCellRenderer {

        public CustomTreeCellRenderer() {
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        }

        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value, boolean selected,
                boolean expanded, boolean leaf, int row, boolean hasFocus) {

            super.getTreeCellRendererComponent(tree, value, selected, expanded, leaf, row,
                    hasFocus);

            // إعداد الخط العربي
            setFont(arabicFont);
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            setHorizontalAlignment(SwingConstants.RIGHT);

            // تخصيص الأيقونات من قاعدة البيانات
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;

            // محاولة الحصول على الأيقونة من بيانات العقدة
            Icon nodeIcon = getNodeIcon(node);
            if (nodeIcon != null) {
                setIcon(nodeIcon);
            } else {
                // استخدام الأيقونات الافتراضية كبديل
                String nodeText = node.toString();
                if (node.isRoot()) {
                    setIcon(createColoredIcon(new Color(52, 152, 219)));
                } else if (!leaf) {
                    // أيقونات المجلدات الرئيسية
                    if (nodeText.contains("الشحنات")) {
                        setIcon(createColoredIcon(new Color(40, 167, 69)));
                    } else if (nodeText.contains("العملاء")) {
                        setIcon(createColoredIcon(new Color(255, 193, 7)));
                    } else if (nodeText.contains("المحاسبة")) {
                        setIcon(createColoredIcon(new Color(220, 53, 69)));
                    } else if (nodeText.contains("المخزون")) {
                        setIcon(createColoredIcon(new Color(111, 66, 193)));
                    } else if (nodeText.contains("التقارير")) {
                        setIcon(createColoredIcon(new Color(23, 162, 184)));
                    } else if (nodeText.contains("الإعدادات")) {
                        setIcon(createColoredIcon(new Color(108, 117, 125)));
                    } else if (nodeText.contains("المساعدة")) {
                        setIcon(createColoredIcon(new Color(255, 87, 34)));
                    } else {
                        setIcon(createColoredIcon(new Color(76, 175, 80)));
                    }
                } else {
                    // أيقونات العناصر الفرعية
                    setIcon(createColoredIcon(new Color(158, 158, 158)));
                }
            }

            // تخصيص الألوان
            if (selected) {
                setBackgroundSelectionColor(new Color(52, 152, 219, 100));
                setTextSelectionColor(Color.BLACK);
            } else {
                setBackgroundNonSelectionColor(Color.WHITE);
                setTextNonSelectionColor(Color.BLACK);
            }

            return this;
        }

        @Override
        protected void paintComponent(Graphics g) {
            // تفعيل مكافحة التشويش للنص العربي
            if (g instanceof Graphics2D) {
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
                        RenderingHints.VALUE_TEXT_ANTIALIAS_LCD_HRGB);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING,
                        RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS,
                        RenderingHints.VALUE_FRACTIONALMETRICS_ON);
            }

            super.paintComponent(g);
        }

        /**
         * الحصول على أيقونة العقدة من قاعدة البيانات
         */
        private Icon getNodeIcon(DefaultMutableTreeNode node) {
            try {
                Object userObject = node.getUserObject();
                if (userObject instanceof SystemTreeManager.SystemTreeNode) {
                    SystemTreeManager.SystemTreeNode treeNode = (SystemTreeManager.SystemTreeNode) userObject;
                    if (treeNode.iconPath != null && !treeNode.iconPath.trim().isEmpty()) {
                        return loadIconFromPath(treeNode.iconPath);
                    }
                }
            } catch (Exception e) {
                System.err.println("خطأ في تحميل أيقونة العقدة: " + e.getMessage());
            }
            return null;
        }

        /**
         * تحميل الأيقونة من المسار
         */
        private Icon loadIconFromPath(String iconPath) {
            try {
                // محاولة تحميل الأيقونة من الملف
                java.io.File iconFile = new java.io.File(iconPath);
                if (iconFile.exists()) {
                    java.awt.image.BufferedImage image = javax.imageio.ImageIO.read(iconFile);
                    if (image != null) {
                        // تغيير حجم الأيقونة إذا لزم الأمر
                        java.awt.Image scaledImage = image.getScaledInstance(16, 16, java.awt.Image.SCALE_SMOOTH);
                        return new javax.swing.ImageIcon(scaledImage);
                    }
                }
            } catch (Exception e) {
                System.err.println("خطأ في تحميل الأيقونة من " + iconPath + ": " + e.getMessage());
            }
            return null;
        }

        /**
         * إنشاء أيقونة ملونة
         */
        private Icon createColoredIcon(Color color) {
            return new Icon() {
                @Override
                public void paintIcon(Component c, Graphics g, int x, int y) {
                    Graphics2D g2d = (Graphics2D) g.create();
                    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
                            RenderingHints.VALUE_ANTIALIAS_ON);
                    g2d.setColor(color);
                    g2d.fillOval(x + 2, y + 2, getIconWidth() - 4, getIconHeight() - 4);
                    g2d.setColor(color.darker());
                    g2d.drawOval(x + 2, y + 2, getIconWidth() - 4, getIconHeight() - 4);
                    g2d.dispose();
                }

                @Override
                public int getIconWidth() {
                    return 16;
                }

                @Override
                public int getIconHeight() {
                    return 16;
                }
            };
        }
    }

    /**
     * توسيع جميع العقد
     */
    public void expandAllNodes() {
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }
    }

    /**
     * طي جميع العقد
     */
    public void collapseAllNodes() {
        for (int i = menuTree.getRowCount() - 1; i >= 1; i--) {
            menuTree.collapseRow(i);
        }
    }

    /**
     * البحث في القائمة
     */
    public void searchInMenu(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            expandAllNodes();
            return;
        }

        collapseAllNodes();
        searchAndExpand(rootNode, searchText.toLowerCase());
    }

    /**
     * البحث وتوسيع العقد المطابقة
     */
    private boolean searchAndExpand(DefaultMutableTreeNode node, String searchText) {
        boolean found = false;

        if (node.toString().toLowerCase().contains(searchText)) {
            TreePath path = new TreePath(node.getPath());
            menuTree.expandPath(path);
            found = true;
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) node.getChildAt(i);
            if (searchAndExpand(child, searchText)) {
                TreePath path = new TreePath(node.getPath());
                menuTree.expandPath(path);
                found = true;
            }
        }

        return found;
    }

    /**
     * نقطة البداية الرئيسية للتطبيق Main entry point for the application
     */
    public static void main(String[] args) {
        // إعداد خصائص النظام للنص العربي
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("sun.jnu.encoding", "UTF-8");
        System.setProperty("console.encoding", "UTF-8");
        System.setProperty("user.language", "ar");
        System.setProperty("user.country", "SA");

        SwingUtilities.invokeLater(() -> {
            try {
                // إنشاء النافذة الرئيسية
                JFrame frame =
                        new JFrame("نظام إدارة الشحنات المتقدم - Advanced Shipment ERP System");
                frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
                frame.setSize(1200, 800);
                frame.setLocationRelativeTo(null);
                frame.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

                // إنشاء لوحة القائمة
                TreeMenuPanel menuPanel = new TreeMenuPanel(frame);
                frame.add(menuPanel, BorderLayout.CENTER);

                // عرض النافذة
                frame.setVisible(true);

                System.out.println("✅ تم تشغيل نظام إدارة الشحنات بنجاح!");

            } catch (Exception e) {
                System.err.println("❌ خطأ في تشغيل النظام: " + e.getMessage());
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, "خطأ في تشغيل النظام:\n" + e.getMessage(),
                        "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
