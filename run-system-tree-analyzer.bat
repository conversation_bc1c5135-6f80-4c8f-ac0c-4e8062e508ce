@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 📊 محلل شجرة الأنظمة - System Tree Analyzer
echo    تحليل شامل لجدول ERP_SYSTEM_TREE
echo ================================================
echo.

cd /d "d:\java\java"

echo 🔍 فحص المتطلبات...
echo.

echo [1] فحص المكتبات المطلوبة...
if not exist "lib\ojdbc11.jar" (
    echo ❌ ojdbc11.jar مفقود!
    pause
    exit /b 1
)

if not exist "lib\orai18n.jar" (
    echo ❌ orai18n.jar مفقود!
    pause
    exit /b 1
)

if not exist "lib\flatlaf-3.2.5.jar" (
    echo ❌ flatlaf-3.2.5.jar مفقود!
    pause
    exit /b 1
)

echo ✅ جميع المكتبات متوفرة
echo.

echo [2] فحص الملفات المطلوبة...
if not exist "src\main\java\SystemTreeAnalyzer.java" (
    echo ❌ SystemTreeAnalyzer.java مفقود!
    pause
    exit /b 1
)

if not exist "src\main\java\TNSConnectionManager.java" (
    echo ❌ TNSConnectionManager.java مفقود!
    pause
    exit /b 1
)

echo ✅ جميع الملفات متوفرة
echo.

echo [3] تجميع محلل شجرة الأنظمة...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TNSConnectionManager.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع TNSConnectionManager
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\SystemTreeAnalyzer.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تجميع SystemTreeAnalyzer
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح
echo.

echo [4] معلومات النظام:
echo • قاعدة البيانات: Oracle ORCL (localhost:1521)
echo • المستخدم: SHIP_ERP
echo • الجدول: ERP_SYSTEM_TREE
echo • الميزات: تحليل شامل، إحصائيات، إصلاح تلقائي
echo.

echo [5] تشغيل محلل شجرة الأنظمة...
echo.

java -Doracle.net.tns_admin=d:\java\java\network\admin -Doracle.jdbc.defaultNChar=true -Dfile.encoding=UTF-8 -cp "lib\*;." SystemTreeAnalyzer

echo.
echo 📊 تم إغلاق محلل شجرة الأنظمة
echo    System Tree Analyzer Closed
echo.

echo ================================================
echo 📋 ملخص الميزات المتاحة:
echo ================================================
echo.
echo 🔍 التحليل الشامل:
echo • فحص العقد اليتيمة (بدون أب صحيح)
echo • اكتشاف الأسماء المكررة
echo • فحص النوافذ المفقودة (بدون كلاس)
echo • تحليل هيكل الشجرة والمستويات
echo.
echo 📊 الإحصائيات التفصيلية:
echo • توزيع أنواع العقد (CATEGORY, WINDOW, TOOL, etc.)
echo • توزيع المستويات في الشجرة
echo • نسب العقد النشطة والمرئية
echo • إحصائيات الإنشاء والتحديث
echo.
echo 🛠️ أدوات الإصلاح:
echo • إصلاح العقد اليتيمة تلقائياً
echo • تحديث مستويات الشجرة
echo • إعادة ترتيب العقد
echo • تصحيح البيانات المفقودة
echo.
echo 📄 التقارير:
echo • تقرير نصي شامل
echo • جدول تفاعلي للشجرة
echo • جدول الإحصائيات المفصلة
echo • تصدير التقارير إلى ملفات نصية
echo.
echo 💡 التوصيات:
echo • اقتراحات لتحسين هيكل الشجرة
echo • تحديد العقد غير المستخدمة
echo • نصائح لتحسين الأداء
echo • إرشادات الصيانة الدورية
echo.

pause
