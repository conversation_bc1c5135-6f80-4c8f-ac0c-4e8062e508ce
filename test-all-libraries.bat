@echo off
echo ========================================
echo   COMPREHENSIVE LIBRARY TEST
echo   اختبار شامل لجميع المكتبات
echo ========================================

cd /d "d:\java\java"

echo.
echo [INFO] Starting comprehensive library test...
echo [INFO] بدء الاختبار الشامل للمكتبات...
echo.

echo ========================================
echo    1. Oracle Database Libraries Test
echo    اختبار مكتبات Oracle
echo ========================================

echo [1.1] Testing Oracle JDBC Driver...
java -cp "lib\ojdbc11.jar" oracle.jdbc.OracleDriver
if %errorlevel% equ 0 (
    echo ✅ Oracle JDBC Driver - Working
) else (
    echo ❌ Oracle JDBC Driver - Failed
)

echo.
echo [1.2] Testing Oracle i18n Support...
java -cp "lib\orai18n.jar;lib\ojdbc11.jar" -Dfile.encoding=UTF-8 -Doracle.jdbc.defaultNChar=true oracle.jdbc.OracleDriver
if %errorlevel% equ 0 (
    echo ✅ Oracle i18n Support - Working
) else (
    echo ❌ Oracle i18n Support - Failed
)

echo.
echo ========================================
echo    2. UI Libraries Test
echo    اختبار مكتبات الواجهة
echo ========================================

echo [2.1] Testing FlatLaf...
java -cp "lib\flatlaf-3.2.5.jar" -Djava.awt.headless=true com.formdev.flatlaf.FlatLightLaf
if %errorlevel% equ 0 (
    echo ✅ FlatLaf - Working
) else (
    echo ❌ FlatLaf - Failed
)

echo.
echo [2.2] Testing Material UI...
java -cp "lib\material-ui-swing-1.1.4.jar" -Djava.awt.headless=true -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Material UI - Available
) else (
    echo ❌ Material UI - Failed
)

echo.
echo [2.3] Testing JTattoo...
java -cp "lib\jtattoo-1.6.13.jar" -Djava.awt.headless=true -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ JTattoo - Available
) else (
    echo ❌ JTattoo - Failed
)

echo.
echo ========================================
echo    3. Spring Framework Test
echo    اختبار Spring Framework
echo ========================================

echo [3.1] Testing Spring Core...
java -cp "lib\spring-core-6.0.0.jar;lib\spring-beans-6.0.0.jar;lib\spring-context-6.0.0.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Spring Framework - Available
) else (
    echo ❌ Spring Framework - Failed
)

echo.
echo ========================================
echo    4. Email Libraries Test
echo    اختبار مكتبات البريد الإلكتروني
echo ========================================

echo [4.1] Testing JavaMail...
java -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ JavaMail - Available
) else (
    echo ❌ JavaMail - Failed
)

echo.
echo [4.2] Testing Jakarta Mail...
java -cp "lib\jakarta.mail-2.0.1.jar;lib\jakarta.activation-api-2.1.0.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Jakarta Mail - Available
) else (
    echo ❌ Jakarta Mail - Failed
)

echo.
echo ========================================
echo    5. Additional Libraries Test
echo    اختبار المكتبات الإضافية
echo ========================================

echo [5.1] Testing Hibernate...
java -cp "lib\hibernate-core-5.6.15.Final.jar;lib\hibernate-commons-annotations-5.1.2.Final.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Hibernate - Available
) else (
    echo ❌ Hibernate - Failed
)

echo.
echo [5.2] Testing HikariCP...
java -cp "lib\HikariCP-5.0.1.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ HikariCP - Available
) else (
    echo ❌ HikariCP - Failed
)

echo.
echo [5.3] Testing SLF4J...
java -cp "lib\slf4j-api-2.0.7.jar;lib\slf4j-simple-2.0.7.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ SLF4J - Available
) else (
    echo ❌ SLF4J - Failed
)

echo.
echo [5.4] Testing Apache POI...
java -cp "lib\poi-5.2.4.jar;lib\poi-ooxml-5.2.4.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Apache POI - Available
) else (
    echo ❌ Apache POI - Failed
)

echo.
echo [5.5] Testing Jackson...
java -cp "lib\jackson-core-2.15.2.jar;lib\jackson-databind-2.15.2.jar;lib\jackson-annotations-2.15.2.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Jackson - Available
) else (
    echo ❌ Jackson - Failed
)

echo.
echo [5.6] Testing iText...
java -cp "lib\itext-kernel-7.2.5.jar;lib\itext-layout-7.2.5.jar;lib\itext-io-7.2.5.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ iText - Available
) else (
    echo ❌ iText - Failed
)

echo.
echo [5.7] Testing BCrypt...
java -cp "lib\jbcrypt-0.4.jar" -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ BCrypt - Available
) else (
    echo ❌ BCrypt - Failed
)

echo.
echo ========================================
echo    6. Library Count Summary
echo    ملخص عدد المكتبات
echo ========================================

for /f %%i in ('dir /b lib\*.jar 2^>nul ^| find /c /v ""') do echo Total JAR files: %%i
echo.

echo ========================================
echo    LIBRARY TEST COMPLETED
echo    تم الانتهاء من اختبار المكتبات
echo ========================================
echo.
echo All essential libraries have been tested.
echo تم اختبار جميع المكتبات الأساسية.
echo.
pause
