@echo off
echo ========================================
echo   Download Email System Libraries
echo   تحميل مكتبات نظام البريد الإلكتروني
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo Creating lib directory if not exists...
if not exist "lib" mkdir lib

echo.
echo [1] Downloading JavaMail API...
echo This library is required for email functionality
echo.
echo Please download the following libraries manually:
echo.
echo 1. JavaMail API (javax.mail):
echo    URL: https://github.com/eclipse-ee4j/mail/releases
echo    File: jakarta.mail-2.0.1.jar
echo    Save to: lib\jakarta.mail-2.0.1.jar
echo.
echo 2. Jakarta Activation:
echo    URL: https://github.com/eclipse-ee4j/jaf/releases  
echo    File: jakarta.activation-2.0.1.jar
echo    Save to: lib\jakarta.activation-2.0.1.jar
echo.
echo 3. Apache Commons Email:
echo    URL: https://commons.apache.org/proper/commons-email/download_email.cgi
echo    File: commons-email-1.5.jar
echo    Save to: lib\commons-email-1.5.jar
echo.
echo 4. Apache Commons IO:
echo    URL: https://commons.apache.org/proper/commons-io/download_io.cgi
echo    File: commons-io-2.11.0.jar
echo    Save to: lib\commons-io-2.11.0.jar
echo.
echo 5. JSON Processing (for email templates):
echo    URL: https://github.com/stleary/JSON-java/releases
echo    File: json-20230227.jar
echo    Save to: lib\json-20230227.jar
echo.
echo 6. Apache Commons Lang (for string utilities):
echo    URL: https://commons.apache.org/proper/commons-lang/download_lang.cgi
echo    File: commons-lang3-3.12.0.jar
echo    Save to: lib\commons-lang3-3.12.0.jar
echo.
echo Alternative: Create mock libraries for development
echo.
set /p choice="Do you want to create mock libraries for development? (y/n): "
if /i "%choice%"=="y" goto create_mock
if /i "%choice%"=="yes" goto create_mock
goto end

:create_mock
echo.
echo [2] Creating mock libraries for development...

echo Creating mock jakarta.mail...
echo // Mock Jakarta Mail > lib\jakarta-mail-mock.txt
echo This is a mock file for jakarta.mail >> lib\jakarta-mail-mock.txt

echo Creating mock jakarta.activation...
echo // Mock Jakarta Activation > lib\jakarta-activation-mock.txt
echo This is a mock file for jakarta.activation >> lib\jakarta-activation-mock.txt

echo Creating mock commons-email...
echo // Mock Commons Email > lib\commons-email-mock.txt
echo This is a mock file for commons-email >> lib\commons-email-mock.txt

echo Creating mock commons-io...
echo // Mock Commons IO > lib\commons-io-mock.txt
echo This is a mock file for commons-io >> lib\commons-io-mock.txt

echo Creating mock json...
echo // Mock JSON > lib\json-mock.txt
echo This is a mock file for json processing >> lib\json-mock.txt

echo Creating mock commons-lang3...
echo // Mock Commons Lang3 > lib\commons-lang3-mock.txt
echo This is a mock file for commons-lang3 >> lib\commons-lang3-mock.txt

echo.
echo Mock libraries created for development.
echo Note: These are just placeholder files.
echo For full functionality, download the actual JAR files.

:end
echo.
echo ========================================
echo   Library Setup Instructions Complete
echo   تم إكمال تعليمات إعداد المكتبات
echo ========================================
echo.
echo NEXT STEPS:
echo 1. Download the actual JAR files from the URLs above
echo 2. Place them in the lib directory
echo 3. Run the email system creation script
echo.
echo Current lib directory contents:
dir lib\*.jar 2>nul
if %errorlevel% neq 0 (
    echo No JAR files found in lib directory
    echo Please download the required libraries
)
echo.
pause
