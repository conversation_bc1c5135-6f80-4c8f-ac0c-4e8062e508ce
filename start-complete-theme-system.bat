@echo off
echo ========================================
echo   Complete Theme System (Working Version)
echo ========================================

cd /d "e:\ship_erp\java"

echo.
echo [1] Checking available libraries...
echo Checking for FlatLaf...
if exist "lib\flatlaf-*.jar" (
    echo Found FlatLaf library
) else (
    echo FlatLaf not found - using system themes only
)

echo Checking for JTattoo...
if exist "lib\jtattoo-*.jar" (
    echo Found JTattoo library
) else (
    echo JTattoo not found - using system themes only
)

echo.
echo [2] Creating database (optional)...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\CreateCompleteThemeDatabase.java 2>nul
if %errorlevel% equ 0 (
    java -cp "lib\*;." CreateCompleteThemeDatabase 2>nul
    if %errorlevel% equ 0 (
        echo Database created successfully
    ) else (
        echo Database creation failed - using fallback
    )
) else (
    echo Database creation skipped
)

echo.
echo [3] Compiling working theme manager...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\WorkingThemeManager.java
if %errorlevel% neq 0 (
    echo Failed to compile WorkingThemeManager
    pause
    exit /b 1
)

echo.
echo [4] Compiling working theme window...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\WorkingThemeWindow.java
if %errorlevel% neq 0 (
    echo Failed to compile WorkingThemeWindow
    pause
    exit /b 1
)

echo.
echo [5] Updating TreeMenuPanel...
javac -encoding UTF-8 -cp "lib\*;." -d . src\main\java\TreeMenuPanel.java
if %errorlevel% neq 0 (
    echo Failed to compile TreeMenuPanel - continuing anyway
)

echo.
echo [6] Starting working theme system...
java -cp "lib\*;." WorkingThemeWindow

echo.
echo Theme system finished
pause
