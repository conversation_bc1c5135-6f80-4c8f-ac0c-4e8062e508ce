@echo off
echo ========================================
echo    SHIP ERP SYSTEM - UNIFIED VERSION
echo ========================================

cd /d "e:\ship_erp\java"

echo [INFO] Starting Ship ERP System...
echo.

echo Features enabled:
echo - Dynamic system tree from database
echo - Automatic window registration
echo - TNS connection management
echo - Arabic language support
echo - Real-time tree updates
echo - Security management
echo - Configuration management
echo - Global variables management
echo - UI theme and appearance settings
echo - Modern FlatLaf themes
echo - Color customization
echo - Email management system
echo - Complete Oracle integration
echo.

echo [1] Checking libraries...
if not exist "lib\ojdbc11.jar" (
    echo ERROR: ojdbc11.jar missing!
    pause
    exit /b 1
)

if not exist "lib\orai18n.jar" (
    echo ERROR: orai18n.jar missing!
    pause
    exit /b 1
)

if not exist "lib\javax.mail-1.6.2.jar" (
    echo ERROR: javax.mail-1.6.2.jar missing!
    pause
    exit /b 1
)

if not exist "lib\activation.jar" (
    echo ERROR: activation.jar missing!
    pause
    exit /b 1
)

if not exist "lib\flatlaf-3.2.5.jar" (
    echo ERROR: flatlaf-3.2.5.jar missing!
    pause
    exit /b 1
)

echo OK: All libraries found (including JavaMail and FlatLaf)
echo.

echo [2] System Information:
echo - Database: Oracle ORCL (localhost:1521)
echo - SHIP_ERP: ship_erp_password
echo - IAS20251: ys123
echo - Items: 4647 records
echo - Measurement Units: 18 units
echo - Item Groups: 70+ groups
echo - Email Accounts: Configured
echo - UI Theme: Modern FlatLaf
echo.

echo [3] Starting Enhanced Main Window with Unified Tree Menu...
echo.

java -Doracle.net.tns_admin=e:\ship_erp\java\network\admin -Doracle.jdbc.defaultNChar=true -Dfile.encoding=UTF-8 -cp "lib\javax.mail-1.6.2.jar;lib\activation.jar;lib\ojdbc11.jar;lib\orai18n.jar;lib\flatlaf-3.2.5.jar;lib\flatlaf-extras-3.2.5.jar;lib\miglayout-core-11.0.jar;lib\miglayout-swing-11.0.jar;lib\*;." CompleteOracleSystemTest

echo.
echo System shutdown complete.
pause
