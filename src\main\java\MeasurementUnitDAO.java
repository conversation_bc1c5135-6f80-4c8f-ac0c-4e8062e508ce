import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * DAO لإدارة وحدات القياس Data Access Object for Measurement Units
 */
public class MeasurementUnitDAO {

    private Connection connection;

    public MeasurementUnitDAO(Connection connection) {
        this.connection = connection;
    }

    /**
     * إنشاء جدول وحدات القياس
     */
    public void createTable() throws SQLException {
        String createTableSQL = """
                    CREATE TABLE ERP_MEASUREMENT (
                        MEASURE_CODE VARCHAR2(10) NOT NULL,
                        MEASURE_NAME VARCHAR2(100) NOT NULL,
                        MEASURE_NAME_EN VARCHAR2(100),
                        MEASURE_CODE_GB VARCHAR2(10),
                        MEASURE_TYPE NUMBER(1) DEFAULT 1,
                        MEASURE_WT_TYPE NUMBER(2),
                        MEASURE_WT_CONN NUMBER(1) DEFAULT 0,
                        DFLT_SIZE NUMBER(22,4),
                        ALLOW_UPD NUMBER(1) DEFAULT 1,
                        UNT_SALE_TYP NUMBER(2) DEFAULT 3,
                        IS_ACTIVE NUMBER(1) DEFAULT 1,
                        DESCRIPTION VARCHAR2(500),
                        SYMBOL VARCHAR2(10),
                        CONVERSION_FACTOR NUMBER(22,6) DEFAULT 1,
                        AD_U_ID NUMBER(5),
                        AD_DATE DATE DEFAULT SYSDATE,
                        UP_U_ID NUMBER(5),
                        UP_DATE DATE,
                        UP_CNT NUMBER(10) DEFAULT 0,
                        AD_TRMNL_NM VARCHAR2(50),
                        UP_TRMNL_NM VARCHAR2(50),
                        CONSTRAINT PK_ERP_MEASUREMENT PRIMARY KEY (MEASURE_CODE)
                    )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createTableSQL);
        }

        // إضافة التعليقات
        addTableComments();

        // إنشاء الفهارس
        createIndexes();
    }

    /**
     * إضافة التعليقات للجدول والأعمدة
     */
    private void addTableComments() throws SQLException {
        String[] comments = {"COMMENT ON TABLE ERP_MEASUREMENT IS 'جدول وحدات القياس'",
                "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_CODE IS 'كود وحدة القياس'",
                "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_NAME IS 'اسم وحدة القياس بالعربية'",
                "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_NAME_EN IS 'اسم وحدة القياس بالإنجليزية'",
                "COMMENT ON COLUMN ERP_MEASUREMENT.SYMBOL IS 'رمز وحدة القياس'",
                "COMMENT ON COLUMN ERP_MEASUREMENT.IS_ACTIVE IS 'حالة النشاط (1=نشط، 0=غير نشط)'",
                "COMMENT ON COLUMN ERP_MEASUREMENT.CONVERSION_FACTOR IS 'معامل التحويل للوحدة الأساسية'"};

        try (Statement stmt = connection.createStatement()) {
            for (String comment : comments) {
                stmt.execute(comment);
            }
        }
    }

    /**
     * إنشاء الفهارس
     */
    private void createIndexes() throws SQLException {
        String[] indexes = {"CREATE INDEX IDX_ERP_MEASURE_NAME ON ERP_MEASUREMENT(MEASURE_NAME)",
                "CREATE INDEX IDX_ERP_MEASURE_ACTIVE ON ERP_MEASUREMENT(IS_ACTIVE)",
                "CREATE INDEX IDX_ERP_MEASURE_TYPE ON ERP_MEASUREMENT(MEASURE_TYPE)"};

        try (Statement stmt = connection.createStatement()) {
            for (String index : indexes) {
                try {
                    stmt.execute(index);
                } catch (SQLException e) {
                    // تجاهل خطأ الفهرس الموجود
                    if (!e.getMessage().contains("already exists")) {
                        throw e;
                    }
                }
            }
        }
    }

    /**
     * إضافة وحدة قياس جديدة
     */
    public void insert(MeasurementUnit unit) throws SQLException {
        String sql = """
                    INSERT INTO ERP_MEASUREMENT (
                        MEASURE_CODE, MEASURE_NAME, MEASURE_NAME_EN, MEASURE_CODE_GB,
                        MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE,
                        ALLOW_UPD, UNT_SALE_TYP, IS_ACTIVE, DESCRIPTION, SYMBOL,
                        CONVERSION_FACTOR, AD_U_ID, AD_DATE, AD_TRMNL_NM
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, unit.getMeasureCode());
            stmt.setString(2, unit.getMeasureName());
            stmt.setString(3, unit.getMeasureNameEn());
            stmt.setString(4, unit.getMeasureCodeGlobal());
            stmt.setObject(5, unit.getMeasureType());
            stmt.setObject(6, unit.getMeasureWeightType());
            stmt.setObject(7, unit.getMeasureWeightConn());
            stmt.setObject(8, unit.getDefaultSize());
            stmt.setObject(9, unit.getAllowUpdate());
            stmt.setObject(10, unit.getUnitSaleType());
            stmt.setInt(11, unit.isActive() ? 1 : 0);
            stmt.setString(12, unit.getDescription());
            stmt.setString(13, unit.getSymbol());
            stmt.setObject(14, unit.getConversionFactor());
            stmt.setObject(15, unit.getAddUserId());
            stmt.setTimestamp(16,
                    unit.getAddDate() != null ? new Timestamp(unit.getAddDate().getTime()) : null);
            stmt.setString(17, unit.getAddTerminalName());

            stmt.executeUpdate();
        }
    }

    /**
     * تحديث وحدة قياس
     */
    public void update(MeasurementUnit unit) throws SQLException {
        String sql = """
                    UPDATE ERP_MEASUREMENT SET
                        MEASURE_NAME = ?, MEASURE_NAME_EN = ?, MEASURE_CODE_GB = ?,
                        MEASURE_TYPE = ?, MEASURE_WT_TYPE = ?, MEASURE_WT_CONN = ?,
                        DFLT_SIZE = ?, ALLOW_UPD = ?, UNT_SALE_TYP = ?, IS_ACTIVE = ?,
                        DESCRIPTION = ?, SYMBOL = ?, CONVERSION_FACTOR = ?,
                        UP_U_ID = ?, UP_DATE = ?, UP_CNT = NVL(UP_CNT, 0) + 1, UP_TRMNL_NM = ?
                    WHERE MEASURE_CODE = ?
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, unit.getMeasureName());
            stmt.setString(2, unit.getMeasureNameEn());
            stmt.setString(3, unit.getMeasureCodeGlobal());
            stmt.setObject(4, unit.getMeasureType());
            stmt.setObject(5, unit.getMeasureWeightType());
            stmt.setObject(6, unit.getMeasureWeightConn());
            stmt.setObject(7, unit.getDefaultSize());
            stmt.setObject(8, unit.getAllowUpdate());
            stmt.setObject(9, unit.getUnitSaleType());
            stmt.setInt(10, unit.isActive() ? 1 : 0);
            stmt.setString(11, unit.getDescription());
            stmt.setString(12, unit.getSymbol());
            stmt.setObject(13, unit.getConversionFactor());
            stmt.setObject(14, unit.getUpdateUserId());
            stmt.setTimestamp(15, new Timestamp(new Date().getTime()));
            stmt.setString(16, unit.getUpdateTerminalName());
            stmt.setString(17, unit.getMeasureCode());

            stmt.executeUpdate();
        }
    }

    /**
     * حذف وحدة قياس
     */
    public void delete(String measureCode) throws SQLException {
        String sql = "DELETE FROM ERP_MEASUREMENT WHERE MEASURE_CODE = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, measureCode);
            stmt.executeUpdate();
        }
    }

    /**
     * البحث عن وحدة قياس بالكود
     */
    public MeasurementUnit findByCode(String measureCode) throws SQLException {
        String sql = "SELECT * FROM ERP_MEASUREMENT WHERE MEASURE_CODE = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, measureCode);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToUnit(rs);
                }
            }
        }

        return null;
    }

    /**
     * الحصول على جميع وحدات القياس
     */
    public List<MeasurementUnit> findAll() throws SQLException {
        String sql = "SELECT * FROM ERP_MEASUREMENT ORDER BY MEASURE_NAME";

        List<MeasurementUnit> units = new ArrayList<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql);
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                units.add(mapResultSetToUnit(rs));
            }
        }

        return units;
    }

    /**
     * البحث في وحدات القياس
     */
    public List<MeasurementUnit> search(String searchTerm) throws SQLException {
        String sql = """
                    SELECT * FROM ERP_MEASUREMENT
                    WHERE UPPER(MEASURE_NAME) LIKE UPPER(?)
                       OR UPPER(MEASURE_CODE) LIKE UPPER(?)
                       OR UPPER(MEASURE_NAME_EN) LIKE UPPER(?)
                    ORDER BY MEASURE_NAME
                """;

        List<MeasurementUnit> units = new ArrayList<>();
        String searchPattern = "%" + searchTerm + "%";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, searchPattern);
            stmt.setString(2, searchPattern);
            stmt.setString(3, searchPattern);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    units.add(mapResultSetToUnit(rs));
                }
            }
        }

        return units;
    }

    /**
     * تحويل ResultSet إلى MeasurementUnit
     */
    private MeasurementUnit mapResultSetToUnit(ResultSet rs) throws SQLException {
        MeasurementUnit unit = new MeasurementUnit();

        unit.setMeasureCode(rs.getString("MEASURE_CODE"));
        unit.setMeasureName(rs.getString("MEASURE_NAME"));
        unit.setMeasureNameEn(rs.getString("MEASURE_NAME_EN"));
        unit.setMeasureCodeGlobal(rs.getString("MEASURE_CODE_GB"));
        unit.setMeasureType(rs.getObject("MEASURE_TYPE", Integer.class));
        unit.setMeasureWeightType(rs.getObject("MEASURE_WT_TYPE", Integer.class));
        unit.setMeasureWeightConn(rs.getObject("MEASURE_WT_CONN", Integer.class));
        unit.setDefaultSize(rs.getObject("DFLT_SIZE", Double.class));
        unit.setAllowUpdate(rs.getObject("ALLOW_UPD", Integer.class));
        unit.setUnitSaleType(rs.getObject("UNT_SALE_TYP", Integer.class));
        unit.setActive(rs.getInt("IS_ACTIVE") == 1);
        unit.setDescription(rs.getString("DESCRIPTION"));
        unit.setSymbol(rs.getString("SYMBOL"));
        unit.setConversionFactor(rs.getObject("CONVERSION_FACTOR", Double.class));
        unit.setAddUserId(rs.getObject("AD_U_ID", Integer.class));
        unit.setAddDate(rs.getTimestamp("AD_DATE"));
        unit.setUpdateUserId(rs.getObject("UP_U_ID", Integer.class));
        unit.setUpdateDate(rs.getTimestamp("UP_DATE"));
        unit.setUpdateCount(rs.getObject("UP_CNT", Integer.class));
        unit.setAddTerminalName(rs.getString("AD_TRMNL_NM"));
        unit.setUpdateTerminalName(rs.getString("UP_TRMNL_NM"));

        return unit;
    }

    /**
     * استيراد البيانات من جدول MEASUREMENT الأصلي
     */
    public int importFromOriginalTable() throws SQLException {
        String selectSQL = "SELECT * FROM IAS20251.MEASUREMENT";
        String insertSQL = """
                    INSERT INTO ERP_MEASUREMENT (
                        MEASURE_CODE, MEASURE_NAME, MEASURE_NAME_EN, MEASURE_CODE_GB,
                        MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE,
                        ALLOW_UPD, UNT_SALE_TYP, IS_ACTIVE, AD_U_ID, AD_DATE,
                        UP_U_ID, UP_DATE, UP_CNT, AD_TRMNL_NM, UP_TRMNL_NM
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, ?, ?, ?, ?)
                """;

        int importedCount = 0;

        try (PreparedStatement selectStmt = connection.prepareStatement(selectSQL);
                PreparedStatement insertStmt = connection.prepareStatement(insertSQL);
                ResultSet rs = selectStmt.executeQuery()) {

            while (rs.next()) {
                insertStmt.setString(1, rs.getString("MEASURE_CODE"));
                insertStmt.setString(2, rs.getString("MEASURE"));
                insertStmt.setString(3, rs.getString("MEASURE_F_NM"));
                insertStmt.setString(4, rs.getString("MEASURE_CODE_GB"));
                insertStmt.setObject(5, rs.getObject("MEASURE_TYPE"));
                insertStmt.setObject(6, rs.getObject("MEASURE_WT_TYPE"));
                insertStmt.setObject(7, rs.getObject("MEASURE_WT_CONN"));
                insertStmt.setObject(8, rs.getObject("DFLT_SIZE"));
                insertStmt.setObject(9, rs.getObject("ALLOW_UPD"));
                insertStmt.setObject(10, rs.getObject("UNT_SALE_TYP"));
                insertStmt.setObject(11, rs.getObject("AD_U_ID"));
                insertStmt.setTimestamp(12, rs.getTimestamp("AD_DATE"));
                insertStmt.setObject(13, rs.getObject("UP_U_ID"));
                insertStmt.setTimestamp(14, rs.getTimestamp("UP_DATE"));
                insertStmt.setObject(15, rs.getObject("UP_CNT"));
                insertStmt.setString(16, rs.getString("AD_TRMNL_NM"));
                insertStmt.setString(17, rs.getString("UP_TRMNL_NM"));

                try {
                    insertStmt.executeUpdate();
                    importedCount++;
                } catch (SQLException e) {
                    // تجاهل الأخطاء المكررة
                    if (!e.getMessage().contains("unique constraint")) {
                        throw e;
                    }
                }
            }
        }

        return importedCount;
    }
}
