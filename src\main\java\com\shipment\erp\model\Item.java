package com.shipment.erp.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * نموذج الصنف
 * Item Model
 */
@Entity
@Table(name = "items")
public class Item extends BaseEntity {

    @Column(name = "code", unique = true, nullable = false, length = 50)
    @NotBlank(message = "كود الصنف مطلوب")
    @Size(max = 50, message = "كود الصنف يجب ألا يزيد عن 50 حرف")
    private String code;

    @Column(name = "name", nullable = false, length = 200)
    @NotBlank(message = "اسم الصنف مطلوب")
    @Size(max = 200, message = "اسم الصنف يجب ألا يزيد عن 200 حرف")
    private String name;

    @Column(name = "name_en", length = 200)
    @Size(max = 200, message = "الاسم الإنجليزي يجب ألا يزيد عن 200 حرف")
    private String nameEn;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "description_en", columnDefinition = "TEXT")
    private String descriptionEn;

    @Column(name = "category", length = 100)
    @Size(max = 100, message = "الفئة يجب ألا تزيد عن 100 حرف")
    private String category;

    @Column(name = "unit", length = 50)
    @Size(max = 50, message = "الوحدة يجب ألا تزيد عن 50 حرف")
    private String unit;

    @Column(name = "weight", precision = 10, scale = 3)
    @DecimalMin(value = "0.0", message = "الوزن يجب أن يكون أكبر من أو يساوي صفر")
    private BigDecimal weight;

    @Column(name = "dimensions", length = 100)
    @Size(max = 100, message = "الأبعاد يجب ألا تزيد عن 100 حرف")
    private String dimensions;

    @Column(name = "price", precision = 15, scale = 2)
    @DecimalMin(value = "0.0", message = "السعر يجب أن يكون أكبر من أو يساوي صفر")
    private BigDecimal price;

    @Column(name = "cost", precision = 15, scale = 2)
    @DecimalMin(value = "0.0", message = "التكلفة يجب أن تكون أكبر من أو تساوي صفر")
    private BigDecimal cost;

    @Column(name = "barcode", length = 100)
    @Size(max = 100, message = "الباركود يجب ألا يزيد عن 100 حرف")
    private String barcode;

    @Column(name = "sku", length = 100)
    @Size(max = 100, message = "SKU يجب ألا يزيد عن 100 حرف")
    private String sku;

    @Column(name = "min_stock_level")
    @Min(value = 0, message = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")
    private Integer minStockLevel;

    @Column(name = "max_stock_level")
    @Min(value = 0, message = "الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي صفر")
    private Integer maxStockLevel;

    @Column(name = "current_stock")
    @Min(value = 0, message = "المخزون الحالي يجب أن يكون أكبر من أو يساوي صفر")
    private Integer currentStock;

    @Column(name = "is_trackable")
    private Boolean isTrackable = true;

    @Column(name = "is_perishable")
    private Boolean isPerishable = false;

    @Column(name = "shelf_life_days")
    @Min(value = 0, message = "مدة الصلاحية يجب أن تكون أكبر من أو تساوي صفر")
    private Integer shelfLifeDays;

    @Column(name = "supplier_name", length = 200)
    @Size(max = 200, message = "اسم المورد يجب ألا يزيد عن 200 حرف")
    private String supplierName;

    @Column(name = "supplier_code", length = 50)
    @Size(max = 50, message = "كود المورد يجب ألا يزيد عن 50 حرف")
    private String supplierCode;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    // Constructors
    public Item() {
        super();
        this.isTrackable = true;
        this.isPerishable = false;
        this.currentStock = 0;
        this.minStockLevel = 0;
        this.maxStockLevel = 0;
    }

    public Item(String code, String name) {
        this();
        this.code = code;
        this.name = name;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescriptionEn() {
        return descriptionEn;
    }

    public void setDescriptionEn(String descriptionEn) {
        this.descriptionEn = descriptionEn;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getDimensions() {
        return dimensions;
    }

    public void setDimensions(String dimensions) {
        this.dimensions = dimensions;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getMinStockLevel() {
        return minStockLevel;
    }

    public void setMinStockLevel(Integer minStockLevel) {
        this.minStockLevel = minStockLevel;
    }

    public Integer getMaxStockLevel() {
        return maxStockLevel;
    }

    public void setMaxStockLevel(Integer maxStockLevel) {
        this.maxStockLevel = maxStockLevel;
    }

    public Integer getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(Integer currentStock) {
        this.currentStock = currentStock;
    }

    public Boolean getIsTrackable() {
        return isTrackable;
    }

    public void setIsTrackable(Boolean isTrackable) {
        this.isTrackable = isTrackable;
    }

    public Boolean getIsPerishable() {
        return isPerishable;
    }

    public void setIsPerishable(Boolean isPerishable) {
        this.isPerishable = isPerishable;
    }

    public Integer getShelfLifeDays() {
        return shelfLifeDays;
    }

    public void setShelfLifeDays(Integer shelfLifeDays) {
        this.shelfLifeDays = shelfLifeDays;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    // Compatibility methods for UI
    public boolean isTrackable() {
        return Boolean.TRUE.equals(isTrackable);
    }

    public void setTrackable(boolean trackable) {
        this.isTrackable = trackable;
    }

    public boolean isPerishable() {
        return Boolean.TRUE.equals(isPerishable);
    }

    public void setPerishable(boolean perishable) {
        this.isPerishable = perishable;
    }

    // Business methods
    public boolean isLowStock() {
        return currentStock != null && minStockLevel != null && currentStock <= minStockLevel;
    }

    public boolean isOverStock() {
        return currentStock != null && maxStockLevel != null && currentStock >= maxStockLevel;
    }

    public String getStockStatus() {
        if (isLowStock()) {
            return "مخزون منخفض";
        } else if (isOverStock()) {
            return "مخزون زائد";
        } else {
            return "مخزون طبيعي";
        }
    }

    @Override
    public String toString() {
        return "Item{" +
                "id=" + getId() +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", category='" + category + '\'' +
                ", currentStock=" + currentStock +
                ", price=" + price +
                '}';
    }
}
